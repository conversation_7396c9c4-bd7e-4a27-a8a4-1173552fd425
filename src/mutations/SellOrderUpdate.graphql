mutation UpdateSellOrder($input: UpdateSellOrderInput!) {
  updateSellOrder(input: $input) {
    id
  }
}

mutation SellOrderCancelMutation($sellOrderId: Int!) {
  cancelSellOrder(sellOrderId: $sellOrderId) {
    id
  }
}

mutation SellOrderCashOutMutation($sellOrderId: Int!) {
  executeShareBuybackSellOrder(sellOrderId: $sellOrderId) {
    id
  }
}

mutation SellOrderRedeemMutation(
  $sellOrderId: Int!
  $manuallyCloseFlg: Boolean
) {
  redeemSellOrder(
    sellOrderId: $sellOrderId
    manuallyCloseFlg: $manuallyCloseFlg
  ) {
    id
  }
}
