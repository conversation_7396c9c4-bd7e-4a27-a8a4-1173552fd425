mutation UpdateUser($input: UpdateUserInput!) {
  updateUser(input: $input) {
    id
  }
}

mutation SuspendUser($id: Int!) {
  suspendUser(id: $id) {
    id
  }
}

mutation DeactivateUser($id: Int!) {
  deactivateUser(id: $id) {
    id
  }
}

mutation ReactivateUser($id: Int!) {
  reactivateUser(id: $id) {
    id
  }
}

mutation SyncOktaUserFirstLastName {
  syncOktaUserFirstLastName {
    id
  }
}

mutation SyncOktaUserEmail {
  syncOktaUserEmail {
    id
  }
}

mutation SyncOktaUserSSN {
  syncOktaUserSSN {
    id
  }
}

mutation SyncUserVerifiedDt {
  syncUserVerifiedDt {
    id
  }
}

mutation ManuallyConfirmEmail($id: Int!) {
  manuallyConfirmEmail(id: $id) {
    id
  }
}
