const { constants } = require('./global');

const roleMatrix = {
  AccountantLedger: {
    readAccess: ['ITRead', 'AccountingRead'],
    writeAccess: ['ITWrite', 'AccountingWrite'],
  },
  AccreditedQualification: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  AlertType: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  AnnualReportNumbers: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  Article: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'FinanceWrite',
    ],
  },
  ArticleCategory: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite'],
  },
  AuthenticationDocument: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  AuthMatrix: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  AutoInvestSubscription: {
    readAccess: ['ITRead', 'FinanceRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'FinanceWrite', 'CustomerRelationsWrite'],
  },
  AutoReinvestIndicator: {
    readAccess: ['ITRead', 'FinanceWrite', 'CustomerRelationsWrite'],
    writeAccess: ['ITWrite', 'FinanceWrite', 'CustomerRelationsWrite'],
  },
  BeneficiaryType: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  BlendedProduct: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  BoardMember: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  BrBannerPhrase: {
    readAccess: ['ITRead', 'MarketingRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CreditMgmtWrite'],
  },
  BrBenefit: {
    readAccess: ['ITRead', 'MarketingRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CreditMgmtWrite'],
  },
  BrFaqCategory: {
    readAccess: ['ITRead', 'MarketingRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CreditMgmtWrite'],
  },
  BrFaqEntry: {
    readAccess: ['ITRead', 'MarketingRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CreditMgmtWrite'],
  },
  BrBillingCycle: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrBillingCycleLite: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrConsortium: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  BrConsumerUnit: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrConsumerUnitLite: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrContact: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrContactsBrCustomer: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrCreditAdjustment: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrCreditCompensation: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrCreditCompensationLite: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrCustomer: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrInvoice: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrInvoiceLite: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrNote: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrOnboardingDocument: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrPowerPlan: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrPromoDiscount: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrRateio: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrRateioLineItem: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrSalesPartnerStatus: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  BrSalesPerson: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrSelfConsumptionOfftaker: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrTariffClass: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrTermsOfAdhesion: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrUtilityBill: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrVoltagePhase: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  BudgetLineItem: {
    readAccess: ['ITRead', 'MarketingRead', 'FinanceRead', 'AccountingRead'],
    writeAccess: [
      'ITWrite',
      'MarketingWrite',
      'FinanceWrite',
      'AccountingWrite',
    ],
  },
  BuyDirection: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'FinanceRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite'],
  },
  CommunicationGroup: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  Country: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  Dashboard: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
      'HumanResourcesRead',
      'AccountingRead',
      'EnhancedCapital',
      'Admin', // Temporarily using this as something of an "employee" group
    ],
    writeAccess: [],
  },
  CMSDashboardData: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
      'HumanResourcesRead',
      'AccountingRead',
    ],
    writeAccess: [],
  },
  CMSDashboardInvestmentGraphData: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
      'HumanResourcesRead',
      'AccountingRead',
    ],
    writeAccess: [],
  },
  Constant: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  CreditManagementDashboard2: {
    readAccess: ['ITRead', 'CreditMgmtRead', 'Lattice'],
    writeAccess: [],
  },
  CreditManagementSalesDashboard: {
    readAccess: [
      'ITRead',
      'CreditMgmtRead',
      'CustomerRelationsRead',
      'Lattice',
    ],
    writeAccess: [],
  },
  DailyInverterGeneration: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  DeveloperProject: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  Device: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  Dividend: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite'],
  },
  DtcParticipantNumber: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  DwollaDocument: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  DwollaEstimatedMonthlyCost: {
    readAccess: ['ITRead', 'AccountingRead'],
    writeAccess: [],
  },
  DwollaTransfer: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  DwollaWebhookEvent: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  Employee: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  EmployeeLite: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: [],
  },
  EmployeeShareAward: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  EmployeeType: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  EmploymentAgreement: {
    readAccess: ['ITRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  EnergeaGlobalSharePrice: {
    readAccess: ['Admin'], // everyone internal
    writeAccess: ['ITWrite', 'FinanceWrite', 'HumanResourcesWrite'],
  },
  EquipmentItem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  EquipmentType: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  Event: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  EventAuthor: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  EventEmail: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  EventType: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  EversignDocument: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  ExpectedProductionPeriod: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  FaqCategory: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  FaqEntry: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  GiftCard: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  GreenAntMeter: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  HubSpotContact: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  HubSpotDeal: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  HubSpotSocialChannel: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  HubSpotSocialMediaPost: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  HubSpotLeadSource: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  InstallationType: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  InstitutionalInvestment: {
    readAccess: ['ITRead', 'FinanceRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  InstitutionalInvestor: {
    readAccess: ['ITRead', 'FinanceRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  InsurancePolicyType: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  InternetAccount: {
    readAccess: [
      'ITRead',
      // 'CustomerRelationsRead',
      // 'MarketingRead',
      // 'FinanceRead',
      // 'HumanResourcesRead',
      'AccountingRead',
      'OMRead',
    ],
    writeAccess: ['ITWrite', 'OMWrite', 'AccountingWrite'],
  },
  InternetServiceProvider: {
    readAccess: [
      'ITRead',
      // 'CustomerRelationsRead',
      // 'MarketingRead',
      // 'FinanceRead',
      // 'HumanResourcesRead',
      'AccountingRead',
      'OMRead',
    ],
    writeAccess: ['ITWrite', 'OMWrite', 'AccountingWrite'],
  },
  Inverter: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  InverterProductionPeriod: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  InvestmentLite: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'AccountingRead',
    ],
    writeAccess: [],
  },
  Investment: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite'],
  },
  InvestorResource: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  InvestorsCached: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
    ],
    writeAccess: [],
  },
  InvestorResourceType: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  InvestorReturn: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite'],
  },
  IpAddress: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: [],
  },
  IPCamera: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  IRAAccountStatus: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  JobApplication: {
    readAccess: [
      'ITRead',
      'HumanResourcesRead',
      'CustomerRelationsRead',
      'FinanceRead',
      'MarketingRead',
    ],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  JobApplicationStatus: {
    readAccess: [
      'ITRead',
      'HumanResourcesRead',
      'CustomerRelationsRead',
      'FinanceRead',
      'MarketingRead',
    ],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  JobPosting: {
    readAccess: [
      'ITRead',
      'HumanResourcesRead',
      'CustomerRelationsRead',
      'FinanceRead',
      'MarketingRead',
    ],
    writeAccess: ['ITWrite', 'HumanResourcesWrite'],
  },
  MonthlySharePrice: {
    readAccess: ['ITRead', 'FinanceRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  KnownContact: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'MarketingRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite', 'MarketingWrite'],
  },
  LeadSourceCategory: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  Market: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'MarketingRead',
    ],
    writeAccess: ['ITWrite'],
  },
  MarketingDashboard: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
      'EnhancedCapital',
    ],
    writeAccess: [],
  },
  MarketingPortfolio: {
    readAccess: ['ITRead', 'MarketingRead'],
    writeAccess: [],
  },
  MillenniumTrustAuthSession: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  MillenniumTrustFundingSession: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  MonitoringAlarm: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  MonitoringAlarmCode: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  MonitoringStatusEmail: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  MonitoringDashboard: {
    readAccess: [
      'ITRead',
      'VictoryHill',
      'Lattice',
      'OMRead',
      'MarketingRead',
      'FinanceRead',
      'Laerskool',
      'Eventide',
      'Connaught',
      'Fresno',
    ],
    writeAccess: ['ITWrite', 'OMWrite', 'FinanceWrite'],
  },
  MonitoringProject: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  MonthlyDividendEmail: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  MonthlyPortfolioFinancialActual: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  MonthlyPortfolioFinancialProjection: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite'],
  },
  Notification: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  OMFieldDashboard: {
    readAccess: [
      'ITRead',
      'OMRead',
      'OMPartnerSolRen',
      'OMPartnerI9',
      'OMPartnerRun',
    ],
    writeAccess: [
      'ITWrite',
      'OMWrite',
      'OMPartnerSolRen',
      'OMPartnerI9',
      'OMPartnerRun',
    ],
  },
  OMMonthlyReport: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMReport: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMReportImage: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMReportType: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMTicket: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMTicketType: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  OMTruck: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  PendingDwollaDividendPayment: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  PlaidWebhookEvent: {
    readAccess: ['ITRead'],
    writeAccess: [],
  },
  Portfolio: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'AccountingRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'HumanResourcesRead',
      'CreditMgmtRead',
    ],
    writeAccess: [
      'ITWrite',
      'FinanceWrite',
      'AccountingWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'HumanResourcesWrite',
    ],
  },
  PortfolioDocument: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  PortfolioDocumentType: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  PortfolioLite: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'AccountingRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'HumanResourcesRead',
    ],
    writeAccess: [],
  },
  PortfolioMedia: {
    readAccess: ['ITRead', 'MarketingRead'],
    writeAccess: ['ITWrite', 'MarketingWrite'],
  },
  PortfolioMonthReconciliation: {
    readAccess: ['ITRead', 'AccountingRead'],
    writeAccess: [],
  },
  PortfolioRisk: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  PortfolioSharePrice: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'FinanceRead'],
    writeAccess: ['ITWrite'],
  },
  PortfolioShareClass: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'FinanceRead'],
    writeAccess: ['ITWrite'],
  },
  PortfolioStatus: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  PortfolioTaxYear: {
    readAccess: [
      'ITRead',
      'AccountingRead',
      'HumanResourcesRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite', 'AccountingWrite'],
  },
  Post: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'FinanceWrite',
    ],
  },
  PostBannerImage: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'FinanceWrite',
    ],
  },
  PostCategory: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite'],
  },
  PostVideo: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'FinanceWrite',
    ],
  },
  PotentialCorporateInvestor: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'MarketingRead',
    ],
    writeAccess: [
      'ITWrite',
      'FinanceWrite',
      'CustomerRelationsWrite',
      'MarketingWrite',
    ],
  },
  PowerFactorSystem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  ProductionPeriod: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  ProjectLite: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'AccountingRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'HumanResourcesRead',
      'CreditMgmtRead',
      'OMRead',
    ],
    writeAccess: [],
  },
  Project: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'AccountingRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'HumanResourcesRead',
      'CreditMgmtRead',
      'OMRead',
    ],
    writeAccess: [
      'ITWrite',
      'FinanceWrite',
      'AccountingWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'HumanResourcesWrite',
      'OMWrite',
      'CreditMgmtWrite',
    ],
  },
  ProjectContact: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  ProjectDocument: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'AccountingRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'HumanResourcesRead',
    ],
    writeAccess: [
      'ITWrite',
      'FinanceWrite',
      'AccountingWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'HumanResourcesWrite',
    ],
  },
  ProjectInsurancePolicy: {
    readAccess: ['ITRead', 'OMRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'OMWrite', 'HumanResourcesWrite'],
  },
  ProjectInvestmentStatus: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  ProjectMedia: {
    readAccess: ['ITRead', 'MarketingRead'],
    writeAccess: ['ITWrite', 'MarketingWrite'],
  },
  ProjectMonitoringRequirement: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: [],
  },
  ProjectMonitoringStatus: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  PromoCode: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  PushNotification: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  PushNotificationToken: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  QuarterlyUserFinancialStatement: {
    readAccess: ['ITRead', 'AccountingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  Referral: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  Regulation: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  SalesDashboard: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'MarketingRead'],
    writeAccess: [],
  },
  SalesforceProject: {
    readAccess: ['ITRead', 'CreditMgmtRead', 'FinanceRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  ScadaSystem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  SellOrder: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  SellOrderLite: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  SendgridEmailTemplate: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  Sensor: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  SensorDataPeriod: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  SensorType: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  SgdSystem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  ShareTransfer: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'CustomerRelationsRead',
      'AccountingRead',
    ],
    writeAccess: ['ITWrite'],
  },
  SolarEdgeSite: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  SolisSystem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  StatePortfolioTaxInfo: {
    readAccess: [
      'ITRead',
      'AccountingRead',
      'HumanResourcesRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'AccountingWrite',
      'HumanResourcesWrite',
      'FinanceWrite',
    ],
  },
  StatePortfolioTaxStatus: {
    readAccess: [
      'ITRead',
      'AccountingRead',
      'HumanResourcesRead',
      'FinanceRead',
    ],
    writeAccess: [],
  },
  SubAccount: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  SubAccountLite: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'FinanceRead',
    ],
    writeAccess: [],
  },
  SubAccountType: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  SunExchangeSite: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  SustainabilityStory: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite'],
  },
  TransEmail: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  Transfer: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'AccountingRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'CustomerRelationsWrite',
      'AccountingWrite',
      'FinanceWrite',
    ],
  },
  TransferType: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite'],
  },
  TUSDInvoice: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'OMRead',
      'AccountingRead',
      'CreditMgmtRead',
    ],
    writeAccess: [
      'ITRead',
      'FinanceWrite',
      'OMWrite',
      'AccountingWrite',
      'CreditMgmtWrite',
    ],
  },
  UniqueMonthlyPortfolioFinancialProjectionCreatedAtDates: {
    readAccess: [
      'ITRead',
      'FinanceRead',
      'AccountingRead',
      'MarketingRead',
      'CustomerRelationsRead',
    ],
    writeAccess: [],
  },
  User: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'AccountingRead',
      'FinanceRead',
    ],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite', 'MarketingWrite'],
  },
  UserEventTypeCommunicationPreference: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'MarketingRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  UserImage: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'MarketingRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  UserLite: {
    readAccess: [
      'ITRead',
      'CustomerRelationsRead',
      'MarketingRead',
      'AccountingRead',
    ],
    writeAccess: [],
  },
  UserLogin: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITRead'],
  },
  UserMarketing: {
    readAccess: ['ITRead', 'MarketingRead'],
    writeAccess: ['ITWrite'],
  },
  UserMerch: {
    readAccess: ['ITRead', 'MarketingRead'],
    writeAccess: [],
  },
  UserMilestone: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'MarketingRead'],
    writeAccess: ['ITWrite'],
  },
  UserPortfolioTaxDocument: {
    readAccess: ['ITRead', 'CustomerRelationsRead', 'AccountingRead'],
    writeAccess: ['ITWrite'],
  },
  UserPortfolioTaxSummary: {
    readAccess: ['ITRead', 'AccountingRead', 'FinanceRead'],
    writeAccess: [],
  },
  UserReview: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  USState: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  UtilityScript: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  VictoryHillProject: {
    readAccess: ['VictoryHill'],
    writeAccess: [],
  },
  LatticeProject: {
    readAccess: ['Lattice'],
    writeAccess: [],
  },
  UtilityCompany: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  Video: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  WhitelistedEmail: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  // !Dont change me. I am used by the add-resource script!
  FlexOmSite: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  BrContactDocument: {
    readAccess: [],
    writeAccess: [],
  },
  ShareClass: {
    readAccess: [
      'ITRead',
      'MarketingRead',
      'CustomerRelationsRead',
      'FinanceRead',
    ],
    writeAccess: [
      'ITWrite',
      'MarketingWrite',
      'CustomerRelationsWrite',
      'FinanceRead',
    ],
  },
  Borrower: {
    readAccess: ['ITRead', 'MarketingRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'MarketingWrite', 'CustomerRelationsWrite'],
  },
  OMPmpChecklistItem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMChecklistItem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite', 'OMWrite'],
  },
  OMPmpPeriodicity: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  OMPmp: {
    readAccess: ['ITRead', 'OMRead', 'OMPartnerI9'],
    writeAccess: ['ITWrite', 'OMWrite', 'OMPartnerI9'],
  },
  SungrowSystem: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  ProjectGroup: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  SmaSite: {
    readAccess: ['ITRead', 'OMRead'],
    writeAccess: ['ITWrite'],
  },
  BrTicket: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  BrTicketDocument: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  BrTicketCommunicationChannel: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  BrTicketType: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  TodDocument: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  Beneficiary: {
    readAccess: ['ITRead', 'CustomerRelationsRead'],
    writeAccess: ['ITWrite', 'CustomerRelationsWrite'],
  },
  CorporateShareAllocation: {
    readAccess: ['ITRead', 'FinanceRead'],
    writeAccess: ['ITWrite', 'FinanceWrite'],
  },
  CorporateShareClass: {
    readAccess: ['ITRead', 'FinanceRead'],
    writeAccess: ['ITWrite', 'FinanceWrite'],
  },
  CorporateRound: {
    readAccess: ['ITRead', 'FinanceRead'],
    writeAccess: ['ITWrite', 'FinanceWrite'],
  },
  BrCommissionPayment: {
    readAccess: ['ITRead', 'CreditMgmtRead'],
    writeAccess: ['ITWrite', 'CreditMgmtWrite'],
  },
  SharePriceFiling: {
    readAccess: ['ITRead', 'FinanceRead'],
    writeAccess: ['ITWrite', 'FinanceWrite'],
  },
  BrConsumerUnitStage: {
    readAccess: ['ITRead'],
    writeAccess: ['ITWrite'],
  },
  InsuranceCarrier: {
    readAccess: ['ITRead', 'OMRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'OMWrite', 'HumanResourcesWrite'],
  },
  InsurancePolicyStatus: {
    readAccess: ['ITRead', 'OMRead', 'HumanResourcesRead'],
    writeAccess: ['ITWrite', 'OMWrite', 'HumanResourcesWrite'],
  },
};

const getEditable = (entityName, permissions) => {
  if (!permissions || !entityName) return null;

  // Get roles from user object (simplified structure)
  if (!permissions) return null;

  const lintedEntityName = entityName.replace(' ', '');
  if (!roleMatrix[String(lintedEntityName)]) {
    console.log(`Missing roleMatrix for ${lintedEntityName}`);
    return null;
  }

  const userRoles = permissions.roles?.map((role) => role.name) || [];
  return roleMatrix[String(lintedEntityName)].writeAccess.some((r) =>
    userRoles.includes(r)
  );
};

const applyRoleAuth = (permissions, aResources) => {
  const filteredResources = [];

  // Get roles from user object (simplified structure)
  const roles = permissions?.roles?.map((role) => role.name) || [];

  aResources.forEach((aResource) => {
    if (
      roleMatrix[aResource.props.name] &&
      roleMatrix[aResource.props.name].readAccess.some((r) => roles.includes(r))
    ) {
      filteredResources.push(aResource);
    } else {
      console.log(
        `DENIED ACCESS TO : ${aResource.props.name}`,
        roleMatrix[aResource.props.name]
      );
    }
  });
  return filteredResources;
};

module.exports = {
  applyRoleAuth,
  getEditable,
  roleMatrix,
};
