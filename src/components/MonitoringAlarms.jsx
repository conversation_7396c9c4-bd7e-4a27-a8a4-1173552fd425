import React from 'react';

import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateTimeInput,
  Edit,
  Filter,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { DetailField, LinkField } from './CustomFields';

const entityName = 'Monitoring Alarm';

export const MonitoringAlarmEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput label="Project" fullWidth optionText="name" />
            </ReferenceInput>
            <ReferenceInput source="alertType.id" reference="AlertType">
              <SelectInput label="AlertType" fullWidth optionText="name" />
            </ReferenceInput>
            <TextInput source="description" required fullWidth />
            <DateTimeInput source="openedDt" required fullWidth />
            <DateTimeInput source="acknowledgedDt" fullWidth />
            <DateTimeInput source="closedDt" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const MonitoringAlarmFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      source="project.id"
      reference="Project"
      label="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const MonitoringAlarmList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<MonitoringAlarmFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <LinkField
          label="AlertType"
          linkSource="alertType.id"
          labelSource="alertType.name"
          reference="AlertType"
        />
        <DetailField source="lintedDescription" />
        <DateField source="openedDt" showTime />
        <LinkField
          label="Acknowledged By"
          linkSource="acknowledgedByEmployee.id"
          labelSource="acknowledgedByEmployee.fullName"
          reference="Employee"
        />
        <DateField source="acknowledgedDt" showTime />
        <LinkField
          label="Closed By"
          linkSource="closedByEmployee.id"
          labelSource="closedByEmployee.fullName"
          reference="Employee"
        />
        <DateField source="closedDt" showTime />
      </Datagrid>
    </List>
  );
};

export const MonitoringAlarmCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            perPage={10000}
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput label="Project" fullWidth optionText="name" />
          </ReferenceInput>
          <ReferenceInput source="alertType.id" reference="AlertType">
            <SelectInput label="AlertType" fullWidth optionText="name" />
          </ReferenceInput>
          <TextInput source="description" required fullWidth />
          <DateTimeInput source="openedDt" required fullWidth />
          <DateTimeInput source="acknowledgedDt" fullWidth />
          <DateTimeInput source="closedDt" fullWidth />
          <TextField source="severity" />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
