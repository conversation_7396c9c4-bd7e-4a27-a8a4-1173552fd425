import React, { Component, useRef, useState, useEffect } from 'react';
import { withStyles } from '@mui/styles';
import { useTheme, useMediaQuery } from '@mui/material';
import mapboxgl from '!mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

mapboxgl.accessToken = process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;

const styles = (theme) => ({
  mapContainer: {
    minHeight: '600px',
  },
  // marker: {
  // display: 'block',
  // border: 'none',
  // borderRadius: '50%',
  // cursor: 'pointer',
  // padding: 0
  // }
});

function getHeight(height) {
  if (!height) return 400;
  if (typeof height === 'function') {
    return height();
  }
  return height;
}

function getFocusPoint(markers, fullScreen) {
  let latMax;
  let latMin;
  let longMax;
  let longMin;
  markers.forEach((marker) => {
    const { latitude, longitude } = marker;
    if (latitude === null || longitude === null) return;
    if (latitude >= latMax || typeof latMax === 'undefined') latMax = latitude;
    if (latitude <= latMin || typeof latMin === 'undefined') latMin = latitude;
    if (longitude >= longMax || typeof longMin === 'undefined')
      longMax = longitude;
    if (longitude <= longMin || typeof longMin === 'undefined')
      longMin = longitude;
  });
  const longDiff = Math.abs(longMax - longMin);
  const latDiff = Math.abs(longMax - longMin);
  const maxDiff = longDiff > latDiff ? longDiff : latDiff;
  let zoom;
  if (maxDiff > 100) {
    zoom = 1.25;
  } else if (maxDiff > 50) {
    zoom = 1.5;
  } else if (maxDiff > 24) {
    zoom = 2.5;
  } else if (maxDiff > 6) {
    zoom = 3;
  } else if (maxDiff > 2) {
    zoom = 4;
  } else if (maxDiff > 0.75) {
    zoom = 5;
  } else if (maxDiff > 0.2) {
    zoom = 6;
  } else if (maxDiff > 0.15) {
    zoom = 6;
  } else {
    zoom = 6;
  }
  if (fullScreen) {
    zoom *= 0.6;
  }
  zoom = Math.max(zoom, 1);
  return {
    latAvg: (latMax + latMin) / 2,
    longAvg: (longMax + longMin) / 2,
    zoom,
  };
}

const mapboxStyles = {
  street: 'mapbox://styles/mapbox/streets-v11',
  outdoors: 'mapbox://styles/mapbox/outdoors-v11',
  light: 'mapbox://styles/mapbox/light-v10',
  dark: 'mapbox://styles/mapbox/dark-v10',
  satellite: 'mapbox://styles/mapbox/satellite-v9',
  satelliteStreets: 'mapbox://styles/mapbox/satellite-streets-v11',
  navigationDay: 'mapbox://styles/mapbox/navigation-day-v1',
  navigationNight: 'mapbox://styles/mapbox/navigation-night-v1',
};

const mapboxProjections = {
  mercator: 'mercator',
  equalEarth: 'equalEarth',
  globe: 'globe',
};

class Map extends Component {
  constructor(props) {
    super(props);
    const focusPoint = getFocusPoint(props.markers, props.fullScreen);
    this.state = {
      longitude: props.longitude || focusPoint.longAvg,
      latitude: props.latitude || focusPoint.latAvg,
      zoom: 1, // props.zoom || focusPoint.zoom,
      width: props.width || 400,
      height: getHeight(props.height) || 400,
      portfolioId: props.portfolioId,
    };

    this.map = null;
    this.mapContainer = React.createRef();
    this.currentMarkers = [];

    this._onViewportChange = this._onViewportChange.bind(this);
    this._resize = this._resize.bind(this);
  }

  addMarkersToMap(markers) {
    markers.forEach((marker) => {
      // To add custom icons to pins see https://docs.mapbox.com/mapbox-gl-js/example/custom-marker-icons/
      // One way is to change 'div' to 'svg' and className to the fontawesome icon class.
      // Might be helpful to create our own if we want something like a lightnight bolt on the head of the pin
      const newMarker = new mapboxgl.Marker({
        color: marker.color,
        scale: marker.scale || 1,
      })
        .setLngLat([marker.longitude, marker.latitude])
        .addTo(this.map);
      // NOTE: If we add this instead of pin, remove when switch portfolio
      // const popUp = new mapboxgl.Popup({
      // 	closeButton: false,
      // 	closeOnClick: false
      // })
      // 	.setLngLat([marker.longitude, marker.latitude])
      // 	.setHTML(
      // 		'<strong>Salinas</strong>',
      // 	)
      // 	.addTo(this.map);
      if (marker.onMarkerClick) {
        const el = newMarker.getElement();
        el.addEventListener('click', (event) => {
          marker.onMarkerClick(event);
        });
      }
      this.currentMarkers.push(newMarker);
    });
  }

  componentDidUpdate() {
    const { markers, fullScreen } = this.props;
    // TODO: Marker update
    if (this.props.portfolioId !== this.state.portfolioId) {
      this.currentMarkers.forEach((marker) => {
        marker.remove();
      });
      this.currentMarkers = [];
      this.addMarkersToMap(markers);
      const focusPoint = getFocusPoint(markers, fullScreen);
      const target = {
        center: [focusPoint.longAvg, focusPoint.latAvg],
        zoom: focusPoint.zoom,
      };
      this.map.flyTo({
        ...target, // Fly to the selected target
        duration: 3000, // Animate over 6 seconds
        essential: true, // This animation is considered essential with
        // Respect to prefers-reduced-motion
      });
      this.setState({ portfolioId: this.props.portfolioId });
    }
    if (markers && markers.length !== this.currentMarkers.length) {
      this.currentMarkers.forEach((marker) => {
        marker.remove();
      });
      this.currentMarkers = [];
      this.addMarkersToMap(markers);
      const focusPoint = getFocusPoint(markers, fullScreen);
      const target = {
        center: [focusPoint.longAvg || 0, focusPoint.latAvg || 0],
        zoom: focusPoint.zoom,
      };
      this.map.flyTo({
        ...target, // Fly to the selected target
        duration: 3000, // Animate over 6 seconds
        essential: true, // This animation is considered essential with
        // Respect to prefers-reduced-motion
      });
      this.setState({ portfolioId: this.props.portfolioId });
    }
  }
  componentDidMount() {
    const { markers, nightTime, fullScreen } = this.props;
    const { longitude, latitude, zoom } = this.state;

    this.map = new mapboxgl.Map({
      container: this.mapContainer.current,
      style: nightTime ? mapboxStyles.dark : mapboxStyles.light,
      center: [longitude || 0, latitude || 0],
      zoom: zoom,
      // Comment me out to use the default view
      projection: {
        name: this.props.projection || mapboxProjections.globe,
      },
    });

    this.addMarkersToMap(markers);

    this.map.on('move', () => {
      this.setState({
        longitude: this.map.getCenter().lng.toFixed(4),
        latitude: this.map.getCenter().lat.toFixed(4),
        zoom: this.map.getZoom().toFixed(2),
      });
    });

    this.map.on('load', () => {
      // Day fog
      // this.map.setFog({
      // 	'range': [-1, 2],
      // 	'horizon-blend': 0.3,
      // 	'color': 'white',
      // 	'high-color': '#add8e6',
      // 	'space-color': '#d8f2ff',
      // 	'star-intensity': 0.0
      // });

      // Night fog
      this.map.setFog({
        range: [-1, 2],
        'horizon-blend': 0.3,
        color: '#242B4B',
        'high-color': '#161B36',
        'space-color': '#0B1026',
        'star-intensity': 0.8,
      });
    });

    // Add navigation control (the +/- zoom buttons)
    this.map.addControl(new mapboxgl.NavigationControl(), 'top-right');

    const target = {
      zoom: getFocusPoint(markers, fullScreen).zoom,
    };
    this.map.flyTo({
      ...target, // Fly to the selected target
      duration: 3000, // Animate over 6 seconds
      essential: true, // This animation is considered essential with
      // Respect to prefers-reduced-motion
    });

    window.addEventListener('resize', this._resize);
    this._resize();
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this._resize);
  }

  _resize() {
    const { props } = this;
    let { latitude, longitude } = props;
    if (latitude === 0) {
      latitude = 0.000001;
    }
    if (longitude === 0) {
      longitude = 0.000001;
    }
    this._onViewportChange({
      width: props.width || 400,
      height: getHeight(props.height) || 400,
      latitude: props.latitude || 0,
      longitude: props.longitude || 0,
      zoom: props.zoom || 8,
    });
  }

  _onViewportChange(viewport) {
    if (viewport.zoom < 1 || viewport.zoom > 20) return;
    this.setState(viewport);
  }

  render() {
    const { classes } = this.props;
    return (
      <div>
        <div ref={this.mapContainer} className={classes.mapContainer} />
      </div>
    );
  }
}

function withMobileDialog(WrappedComponent) {
  return (props) => (
    <WrappedComponent
      {...props}
      fullScreen={useMediaQuery(useTheme().breakpoints.down('sm'))}
    />
  );
}

export default withStyles(styles)(withMobileDialog(Map));
