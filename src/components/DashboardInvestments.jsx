// cachebust 2
import React, { Component, useState, useEffect, useRef } from 'react';
import jsonExport from 'jsonexport/dist';

import {
  Button,
  ButtonGroup,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Radio,
  RadioGroup,
  Skeleton,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { downloadCSV, useDataProvider, usePermissions } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment';
import { Bar, Line } from 'react-chartjs-2';
// import zoomPlugin from 'chartjs-plugin-zoom';
// import { Chart } from 'chart.js';
import 'chart.js/auto';
import 'chartjs-adapter-moment';

import theme from '../theme';
import { interpolateColors } from '../utils/global';
import { Settings } from '@mui/icons-material';
import MuiButton from '@mui/material/Button';
import { getGradient } from '../utils/global';

// utility functions
const minDate = (dataList, dateField) => {
  // if (!sortedList || !sortedList[0]) return null;
  const sortedList = dataList.sort((a, b) =>
    a[String(dateField)] > b[String(dateField)] ? 1 : -1
  );
  return sortedList[0][String(dateField)];
};

const generateDateRange = (startDate, endDate, timeSpan = 'day') => {
  const dates = [];
  const currentDate = moment(startDate);
  while (currentDate <= endDate) {
    dates.push(moment(currentDate));
    currentDate.add(1, timeSpan);
  }
  return dates;
};

const getBorderColorsArray = (numColors) => {
  return interpolateColors(numColors, null, null, 1);
};

const getColorsArray = (numColors) => {
  return interpolateColors(numColors, null, null, 0.8);
};

const getCumulativesOverTime = (valuesPerDayMap) => {
  let cumulativeValuesOverTime = [];
  let sum = 0;
  for (const val of valuesPerDayMap.values()) {
    cumulativeValuesOverTime.push((sum += val));
  }
  return cumulativeValuesOverTime;
};

const getInvestmentBreakdownData = (investors, crowdInvestmentTotal) => {
  const investmentRanges = [
    '<500',
    '500-2,500',
    '2,500-10,000',
    '10,000-50,000',
    '50,000-250,000',
    '250,000+',
  ];
  const investorBuckets = {};
  investmentRanges.forEach((range) => (investorBuckets[String(range)] = []));
  investors.forEach((investor) => {
    if (investor.investmentSum === 0 || investor.id === 76) {
      // Pass
    } else if (investor.investmentSum < 500) {
      investorBuckets['<500'].push(investor.investmentSum);
    } else if (investor.investmentSum < 2500) {
      investorBuckets['500-2,500'].push(investor.investmentSum);
    } else if (investor.investmentSum < 10000) {
      investorBuckets['2,500-10,000'].push(investor.investmentSum);
    } else if (investor.investmentSum < 50000) {
      investorBuckets['10,000-50,000'].push(investor.investmentSum);
    } else if (investor.investmentSum < 250000) {
      investorBuckets['50,000-250,000'].push(investor.investmentSum);
    } else {
      investorBuckets['250,000+'].push(investor.investmentSum);
    }
  });
  const investorBreakdownData = {
    medians: [],
    means: [],
    counts: [],
    percentOfTotals: [],
    totals: [],
  };
  investmentRanges.forEach((range) => {
    const investments = investorBuckets[String(range)].sort();
    const bucketSum = investments.reduce((a, b) => a + b, 0);
    const mid = Math.floor(investments.length / 2);

    investorBreakdownData.medians.push(
      numeral(
        investments.length % 2 !== 0
          ? investments[Number(mid)]
          : (investments[Number(mid - 1)] + investments[Number(mid)]) / 2
      ).format('$0,0')
    );
    investorBreakdownData.means.push(
      numeral(bucketSum / investments.length).format('$0,0')
    );
    investorBreakdownData.counts.push(investments.length);
    investorBreakdownData.percentOfTotals.push(
      numeral(bucketSum / crowdInvestmentTotal).format('0.00%')
    );
    investorBreakdownData.totals.push(bucketSum);
  });
  return { data: investorBreakdownData, labels: investmentRanges };
};

export default (args) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const { open } = args;
  const { permissions } = usePermissions();

  const exporter = (rows) => {
    const rowsForExport = rows?.map((row) => {
      const returnRow = {};
      const financial = [
        'crowdValue',
        'energeaValue',
        'institutionalValue',
        'retailDebtValue',
      ];
      const attrs = [
        'month',
        'crowdValue',
        'energeaValue',
        'institutionalValue',
        'retailDebtValue',
      ];
      Object.keys(row).forEach((attr) => {
        if (attrs.indexOf(attr) === -1) {
          return;
        }
        const data = row[String(attr)];
        if (financial.indexOf(attr) > -1) {
          returnRow[String(attr)] = numeral(data).format('$0,0.00');
          return;
        }
        returnRow[String(attr)] = data;
      });
      return returnRow;
    });
    return () => {
      jsonExport(
        rowsForExport,
        {
          headers: ['id'],
        },
        (err, csv) => {
          downloadCSV(
            csv,
            `${moment().format('YYYY-MM-DD')}_Total AUM Breakdown`
          );
        }
      );
    };
  };

  const fetchData = () => {
    setLoading(true);
    dataProvider.getOne('CMSDashboardInvestmentData', { data: null }).then(
      (resp) => {
        setData(resp.data);
        setLoading(false);
      },
      (e) => {
        setLoading(false);
        console.error('HIT AN ERROR', e);
        return new Error(e);
      }
    );
  };

  if (!data && !loading) {
    fetchData();
  }

  if (!open) return null;
  // if (!data || loading)
  //   return (
  //     <Grid container>
  //       <Grid item xs={12}>
  //         <Skeleton
  //           animation="wave"
  //           variant="rectangular"
  //           height="16rem"
  //           style={{
  //             borderRadius: theme.shape.borderRadius,
  //           }}
  //         />
  //       </Grid>
  //     </Grid>
  //   );

  const { totalAUMByMonth, totalInvestedByAccountTypeByMonth } = data || {};

  // let aumChartColors = interpolateColors(4, null, null, 0.8);
  let aumBorderColors = interpolateColors(4, null, null, 1);
  // let investedByTypeChartColors = interpolateColors(5, null, null, 0.8);
  let investedByTypeBorderColors = interpolateColors(5, null, null, 1);

  return (
    <Collapse in={open}>
      <Grid container>
        <InvestmentLineChartHOC permissions={permissions} />
        <Divider
          style={{
            width: '100%',
            marginTop: '2em',
            marginBottom: '2em',
          }}
        />
        <Grid item container xs={12}>
          <Grid container justifyContent="space-between">
            <Grid item>
              <Typography variant="h6">Total Investments : </Typography>
              <Typography gutterBottom variant="body2">
                Principal invested minus sold positions
              </Typography>
            </Grid>
            <Grid item>
              <Button
                size="small"
                variant="outlined"
                onClick={exporter(totalAUMByMonth)}
              >
                Export to CSV
              </Button>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            {!data || loading ? (
              <Skeleton
                animation="wave"
                variant="rectangular"
                height="16rem"
                style={{
                  borderRadius: theme.shape.borderRadius,
                }}
              />
            ) : (
              <Line
                key={`aum-line-chart`}
                height={360}
                responsive="true"
                data={{
                  labels: totalAUMByMonth.map((el) => el.month),
                  datasets: [
                    {
                      label: `Crowd`,
                      fill: true,
                      data: totalAUMByMonth.map((el) => el.crowdValue),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: aumBorderColors[0],
                      backgroundColor: getGradient(aumBorderColors[0], 0.5),
                    },
                    {
                      label: `Energea`,
                      fill: true,
                      data: totalAUMByMonth.map((el) => el.energeaValue),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: aumBorderColors[1],
                      backgroundColor: getGradient(aumBorderColors[1], 0.5),
                    },
                    {
                      label: `Institutional`,
                      fill: true,
                      backgroundColor: getGradient(aumBorderColors[2], 0.5),
                      data: totalAUMByMonth.map((el) => el.institutionalValue),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: aumBorderColors[2],
                    },
                    {
                      label: `Retail Debt`,
                      fill: true,
                      data: totalAUMByMonth.map((el) => el.retailDebtValue),
                      pointRadius: 0,
                      borderWidth: 3,
                      backgroundColor: getGradient(aumBorderColors[3], 0.5),
                      borderColor: aumBorderColors[3],
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        label: (tooltipItem) => {
                          return `${tooltipItem.dataset.label}: ${numeral(
                            tooltipItem.formattedValue
                          ).format('$0,0')}`;
                        },
                        footer: (tooltipItem) => {
                          const totalAum = tooltipItem.reduce(function (
                            sum,
                            cur
                          ) {
                            return sum + cur.raw;
                          },
                          0);
                          return `AUM: ${numeral(totalAum).format('$0,0')}`;
                        },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MM-DD-YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      stacked: true,
                      title: {
                        text: 'AUM',
                        display: true,
                      },
                      ticks: {
                        callback: (value) => numeral(value).format('$0,0.[0]a'),
                      },
                    },
                  },
                }}
              />
            )}
          </Grid>
        </Grid>
        <Divider
          style={{
            width: '100%',
            marginTop: '2em',
            marginBottom: '2em',
          }}
        />
        <Grid item container xs={12}>
          <Grid container justifyContent="space-between">
            <Grid item>
              <Typography variant="h6">
                Total Retail Investments by Type :
              </Typography>
              <Typography gutterBottom variant="body2">
                Investments minus sold positions
              </Typography>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            {!data || loading ? (
              <Skeleton
                animation="wave"
                variant="rectangular"
                height="16rem"
                style={{
                  borderRadius: theme.shape.borderRadius,
                }}
              />
            ) : (
              <Line
                key={`aum-line-chart`}
                height={360}
                responsive="true"
                data={{
                  labels: totalInvestedByAccountTypeByMonth.map(
                    (el) => el.month
                  ),
                  datasets: [
                    {
                      label: `Personal (non-IRA)`,
                      fill: true,
                      data: totalInvestedByAccountTypeByMonth.map(
                        (el) => el.personalTotalInvested
                      ),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: investedByTypeBorderColors[0],
                      backgroundColor: getGradient(
                        investedByTypeBorderColors[0],
                        0.5
                      ),
                    },
                    {
                      label: `Business (non-IRA)`,
                      fill: true,
                      data: totalInvestedByAccountTypeByMonth.map(
                        (el) => el.businessTotalInvested
                      ),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: investedByTypeBorderColors[1],
                      backgroundColor: getGradient(
                        investedByTypeBorderColors[1],
                        0.5
                      ),
                    },
                    {
                      label: `Entrust IRA`,
                      fill: true,
                      data: totalInvestedByAccountTypeByMonth.map(
                        (el) => el.entrustTotalInvested
                      ),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: investedByTypeBorderColors[2],
                      backgroundColor: getGradient(
                        investedByTypeBorderColors[2],
                        0.5
                      ),
                    },
                    {
                      label: `Millennium Trust IRA`,
                      fill: true,
                      data: totalInvestedByAccountTypeByMonth.map(
                        (el) => el.mtcTotalInvested
                      ),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: investedByTypeBorderColors[3],
                      backgroundColor: getGradient(
                        investedByTypeBorderColors[3],
                        0.5
                      ),
                    },
                    {
                      label: `Other IRA`,
                      fill: true,
                      data: totalInvestedByAccountTypeByMonth.map(
                        (el) => el.otherIRATotalInvested
                      ),
                      pointRadius: 0,
                      borderWidth: 3,
                      borderColor: investedByTypeBorderColors[4],
                      backgroundColor: getGradient(
                        investedByTypeBorderColors[4],
                        0.5
                      ),
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    // legend: { display: false },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        label: (tooltipItem) => {
                          return `${tooltipItem.dataset.label}: ${numeral(
                            tooltipItem.formattedValue
                          ).format('$0,0')}`;
                        },
                        footer: (tooltipItem) => {
                          const totalAum = tooltipItem.reduce(function (
                            sum,
                            cur
                          ) {
                            return sum + cur.raw;
                          },
                          0);
                          return `AUM: ${numeral(totalAum).format('$0,0')}`;
                        },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MM-DD-YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      stacked: true,
                      title: {
                        text: 'AUM',
                        display: true,
                      },
                      ticks: {
                        callback: (value) => numeral(value).format('$0,0.[0]a'),
                      },
                    },
                  },
                }}
              />
            )}
          </Grid>
        </Grid>
        <Divider
          style={{
            width: '100%',
            marginTop: '2em',
            marginBottom: '2em',
          }}
        />
        {!data || loading ? (
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="16rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        ) : (
          <InvestorBreakdownChart data={data} />
        )}
      </Grid>
    </Collapse>
  );
};

const InvestmentLineChartHOC = ({ permissions, that }) => {
  const [cmsDashboardInvestmentGraphData, setCMSDashboardInvestmentGraphData] =
    useState(null);
  const dataProvider = useDataProvider();
  const isMountedRef = useRef(true);

  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  const getInvestmentGraphData = (data) => {
    const { allInvestedInPortfolios } = data;
    let minInvestmentDate = moment();
    allInvestedInPortfolios.forEach((p) => {
      if (
        p.crowdInvestmentDailyTotals &&
        p.crowdInvestmentDailyTotals.length > 0
      ) {
        let minDt = minDate(p.crowdInvestmentDailyTotals, 'date');
        minInvestmentDate =
          moment(minDt) < minInvestmentDate ? moment(minDt) : minInvestmentDate;
      }
    });
    const investmentDays = generateDateRange(
      minInvestmentDate.subtract(1, 'day').startOf('day'),
      moment()
    );

    let borderColorsArray = getBorderColorsArray(
      allInvestedInPortfolios.length
    );
    let colorsArray = getColorsArray(allInvestedInPortfolios.length);

    let moneyRaisedDatasets = [];
    allInvestedInPortfolios.forEach((portfolio, i) => {
      const netInvestmentDateBuckets = {};
      const grossInvestmentDateBuckets = {};
      const soldDateBuckets = {};
      investmentDays.forEach((d) => {
        netInvestmentDateBuckets[d.format('MM-DD-YYYY')] = null;
        grossInvestmentDateBuckets[d.format('MM-DD-YYYY')] = null;
        soldDateBuckets[d.format('MM-DD-YYYY')] = null;
      });
      portfolio.crowdInvestmentDailyTotals.forEach((investmentDt) => {
        const dt = moment(investmentDt.date, 'YYYY-MM-DD').format('MM-DD-YYYY');
        netInvestmentDateBuckets[String(dt)] += investmentDt.value;
        grossInvestmentDateBuckets[String(dt)] += investmentDt.value;
      });
      portfolio.crowdDailyValueSold.forEach((sellDt) => {
        const dt = moment(sellDt.date, 'YYYY-MM-DD').format('MM-DD-YYYY');
        netInvestmentDateBuckets[String(dt)] -= sellDt.value;
        soldDateBuckets[String(dt)] += sellDt.value;
      });
      moneyRaisedDatasets.push({
        label: portfolio.subtitle,
        netData: Object.values(netInvestmentDateBuckets),
        grossData: Object.values(grossInvestmentDateBuckets),
        soldData: Object.values(soldDateBuckets),
        pointRadius: 0,
        borderWidth: 3,
        borderColor: borderColorsArray[i],
        backgroundColor: colorsArray[i],
        fill: true,
      });
    });

    return { data: moneyRaisedDatasets, labels: investmentDays };
  };

  useEffect(() => {
    if (!cmsDashboardInvestmentGraphData) {
      dataProvider
        .getOne('CMSDashboardInvestmentGraphData', {
          data: null,
        })
        .then(
          (resp) => {
            // Only update state if component is still mounted
            if (isMountedRef.current) {
              const lintedData = getInvestmentGraphData(resp.data);
              Object.keys(lintedData).forEach(
                (key) => (resp.data[String(key)] = lintedData[String(key)])
              );
              setCMSDashboardInvestmentGraphData(resp.data);
            }
          },
          (e) => {
            // Only log error if component is still mounted
            if (isMountedRef.current) {
              console.error('HIT AN ERROR', e);
            }
          }
        );
    }

    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
    };
  }, [cmsDashboardInvestmentGraphData, dataProvider]);
  let jsx;
  if (!(cmsDashboardInvestmentGraphData && permissions)) {
    jsx = (
      <Grid item xs={12}>
        <Skeleton
          animation="wave"
          variant="rectangular"
          height="23rem"
          style={{
            width: '100%',
            borderRadius: theme.shape.borderRadius,
          }}
        />
      </Grid>
    );
  } else {
    jsx = (
      <InvestmentLineChart
        data={cmsDashboardInvestmentGraphData}
        fullScreen={fullScreen}
      />
    );
  }
  return (
    <>
      <Grid
        container
        justifyContent="space-between"
        alignItems="center"
        spacing={3}
      >
        <Grid item>
          <Typography variant="h6">Crowd Investments Received : </Typography>
        </Grid>
        {jsx}
      </Grid>
      <Grid container justifyContent="flex-end">
        <Typography variant="caption">
          <i>Data does not include Energea Global investments</i>
        </Typography>
      </Grid>
    </>
  );
};

class InvestmentLineChart extends Component {
  constructor(props) {
    super(props);
    this.chartData = props.data.data;
    this.chartLabels = props.data.labels;
    this.state = {
      timePeriod: 'all', // 'all', 'year', 'ytd', '3month', '6month'
      chartType: 'new', // 'total', 'new'
      barChartBucketSize: 'month', // 'day', 'week', 'month', 'quarter', 'year'
      investmentTotalType: 'gross', // 'net', 'gross', 'sold'
      dialogOpen: false,
    };

    // Investment chart customize state
    this.state.selectTimePeriod = this.state.timePeriod;
    this.state.selectChartType = this.state.chartType;
    this.state.selectBarChartBucketSize = this.state.barChartBucketSize;
    this.state.selectInvestmentTotalType = this.state.investmentTotalType;
  }

  getMinInvestmentDate() {
    switch (this.state.timePeriod) {
      case 'all':
        return this.chartLabels[0];
      case 'year':
        return moment().subtract(1, 'year');
      case 'ytd':
        return moment().startOf('year');
      case '6month':
        return moment().subtract(6, 'months');
      case '3month':
        return moment().subtract(3, 'months');
      default:
        return moment();
    }
  }

  render() {
    const minInvestmentDate = this.getMinInvestmentDate();
    let filteredChartLabels = [];
    let filteredChartData = [];

    for (
      let portfolioIndex = 0;
      portfolioIndex < this.chartData.length;
      portfolioIndex++
    ) {
      let data = null;
      if (this.state.investmentTotalType === 'net') {
        data = this.chartData[Number(portfolioIndex)].netData;
      } else if (this.state.investmentTotalType === 'sold') {
        data = this.chartData[Number(portfolioIndex)].soldData;
      } else {
        data = this.chartData[Number(portfolioIndex)].grossData;
      }
      const investmentBuckets = {};
      for (let dayIndex = 0; dayIndex < data.length; dayIndex++) {
        if (this.chartLabels[Number(dayIndex)] >= minInvestmentDate) {
          const newInvestmentAmt = data[Number(dayIndex)] ?? 0;
          const startOfTimePeriod =
            this.state.chartType === 'total'
              ? moment(this.chartLabels[Number(dayIndex)]).format('MM-DD-YYYY')
              : moment(this.chartLabels[Number(dayIndex)])
                  .startOf(this.state.barChartBucketSize)
                  .format('MM-DD-YYYY');
          const bucket = investmentBuckets[String(startOfTimePeriod)];
          investmentBuckets[String(startOfTimePeriod)] = bucket
            ? bucket + newInvestmentAmt
            : newInvestmentAmt;
        }
      }
      filteredChartData.push({
        ...this.chartData[Number(portfolioIndex)],
      });
      filteredChartData[Number(portfolioIndex)].data =
        Object.values(investmentBuckets);
      filteredChartLabels = Object.keys(investmentBuckets).map((dtStr) =>
        moment(dtStr, 'MM-DD-YYYY').toDate()
      );
    }

    let investmentData = [];
    const investmentLabels = filteredChartLabels;

    if (this.state.chartType === 'total') {
      filteredChartData.forEach((portfolio) => {
        const portfolioData = {
          ...portfolio,
          backgroundColor: getGradient(portfolio.borderColor, 0.5),
        };
        portfolioData.data = getCumulativesOverTime(portfolioData.data);
        // Only add portfolios with non-zero values
        if (portfolioData.data.some((value) => value > 0)) {
          investmentData.push(portfolioData);
        }
      });
    } else {
      // Filter out portfolios with all zeros before adding to investmentData
      investmentData = filteredChartData.filter((portfolio) =>
        portfolio.data.some((value) => value > 0)
      );

      // Add projection for the final time bucket
      if (
        investmentLabels.length > 0 &&
        this.state.barChartBucketSize !== 'day'
      ) {
        const lastBucketDate = moment(
          investmentLabels[investmentLabels.length - 1]
        );
        const now = moment();

        // Only add projection if we're in the current time bucket
        if (lastBucketDate.isSame(now, this.state.barChartBucketSize)) {
          // Calculate days elapsed and remaining in current bucket
          const startOfBucket = moment(lastBucketDate).startOf(
            this.state.barChartBucketSize
          );
          const endOfBucket = moment(startOfBucket).endOf(
            this.state.barChartBucketSize
          );
          const daysElapsed = now.diff(startOfBucket, 'days');
          const daysRemaining = endOfBucket.diff(now, 'days', true);

          // Only project if we have days remaining in the bucket
          if (daysRemaining > 0 && daysElapsed > 0) {
            // Calculate total current investment across all portfolios for the last bucket
            let totalCurrentValue = 0;
            investmentData.forEach((portfolio) => {
              totalCurrentValue +=
                portfolio.data[portfolio.data.length - 1] || 0;
            });

            // Calculate daily average using the exact same method as in the tooltip
            // This ensures consistency between the projection and the tooltip
            const startOfBucket = moment(lastBucketDate).startOf(
              this.state.barChartBucketSize
            );
            const endOfBucket = moment(startOfBucket).endOf(
              this.state.barChartBucketSize
            );
            const exactDayCount = now.diff(startOfBucket, 'days', true);
            const dailyAverage = totalCurrentValue / Math.max(exactDayCount, 1);

            const projectedAdditional = dailyAverage * daysRemaining;

            // console.log('Projection details:', {
            //   totalCurrentValue,
            //   exactDayCount,
            //   daysRemaining,
            //   dailyAverage,
            //   projectedAdditional,
            // });

            // Create a single projected dataset for all portfolios combined
            if (projectedAdditional > 0) {
              const projectedDataset = {
                label: 'Projected Additional',
                data: Array(investmentLabels.length).fill(0),
                backgroundColor: 'rgba(76, 175, 80, 0.3)',
                borderColor: 'rgba(76, 175, 80, 0.6)',
                borderWidth: 2,
                borderDash: [5, 5], // Add dashed border
                // Add diagonal line pattern to indicate projection
                hoverBackgroundColor: 'rgba(76, 175, 80, 0.8)',
                label: 'Projected Additional',
              };

              // Set only the last bar to the projected additional amount
              projectedDataset.data[projectedDataset.data.length - 1] =
                projectedAdditional;

              // Add the single projected dataset to the chart data
              investmentData.push(projectedDataset);
            }
          }
        }
      }
    }

    const data = {
      labels: investmentLabels,
      datasets: investmentData,
    };

    const chartOptions = {
      maintainAspectRatio: false,
      borderRadius: 4,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
          position: 'nearest',
          callbacks: {
            label: (tooltipItem) => {
              // Skip datasets with zero value
              if (tooltipItem.raw === 0) {
                return null;
              }

              // Skip the projected dataset for tooltip calculations
              if (tooltipItem.dataset.label === 'Projected Additional') {
                return `${tooltipItem.dataset.label}: ${numeral(
                  tooltipItem.formattedValue
                ).format('$0,0')}`;
              }

              return `${tooltipItem.dataset.label}: ${numeral(
                tooltipItem.formattedValue
              ).format('$0,0')}`;
            },
            footer: (tooltipItem) => {
              // Calculate total excluding the projected dataset
              const totalMoneyRaised = tooltipItem.reduce(function (sum, cur) {
                // Skip the projected dataset in total calculation
                if (cur.dataset.label === 'Projected Additional') {
                  return sum;
                }
                return sum + cur.raw;
              }, 0);
              const totalIncludingProjected = tooltipItem.reduce(function (
                sum,
                cur
              ) {
                return sum + cur.raw;
              },
              0);

              const footer = [
                `Total: ${numeral(totalMoneyRaised).format('$0,0.00')}`,
              ];
              if (totalIncludingProjected !== totalMoneyRaised) {
                footer.push(
                  `Total including projection: ${numeral(
                    totalIncludingProjected
                  ).format('$0,0.00')}`
                );
              }

              if (this.state.chartType === 'new') {
                let avgDailyInvestment = 0;
                if (tooltipItem.length > 0) {
                  const startDt = moment(tooltipItem[0].label);
                  const endDt = moment(startDt).endOf(
                    this.state.barChartBucketSize
                  );
                  const now = moment();

                  // Use the exact same calculation as in the projection
                  const dayCount = startDt.isSame(
                    now,
                    this.state.barChartBucketSize
                  )
                    ? now.diff(
                        startDt.startOf(this.state.barChartBucketSize),
                        'days',
                        true
                      )
                    : endDt.diff(
                        startDt.startOf(this.state.barChartBucketSize),
                        'days',
                        true
                      );

                  avgDailyInvestment = totalMoneyRaised / Math.max(dayCount, 1);
                }
                footer.push(
                  `Avg daily invested: ${numeral(avgDailyInvestment).format(
                    '$0,0'
                  )}`
                );
              }
              return footer;
            },
          },
        },
      },
      scales: {
        x: {
          stacked: true,
          type: 'time',
          time: {
            tooltipFormat: 'MM-DD-YYYY',
            unit: this.state.barChartBucketSize,
          },
        },
        y: {
          stacked: true,
          ticks: {
            callback: (value) => numeral(value).format('$0,0.[0]a'),
          },
        },
      },
    };

    if (!this.props.fullScreen) {
      const toggleButtonStyle = { textTransform: 'none' };
      return (
        <>
          <Grid item style={{ margin: '4px' }}>
            <ButtonGroup size="small" aria-label="investment toggle">
              <Button
                variant={
                  this.state.chartType === 'total' ? 'contained' : 'outlined'
                }
                aria-label="total invested per project over time"
                style={{ ...toggleButtonStyle }}
                onClick={() => this.setState({ chartType: 'total' })}
              >
                Total Invested
              </Button>
              <Button
                variant={
                  this.state.chartType === 'new' ? 'contained' : 'outlined'
                }
                aria-label="total invested per project per time frame"
                style={{ ...toggleButtonStyle }}
                onClick={() => this.setState({ chartType: 'new' })}
              >
                New Investments
              </Button>
            </ButtonGroup>
          </Grid>
          <Grid item style={{ margin: '4px' }}>
            <ButtonGroup size="small" aria-label="new users time-frame">
              <Button
                variant={
                  this.state.barChartBucketSize === 'day'
                    ? 'contained'
                    : 'outlined'
                }
                aria-label="daily new investments"
                style={{ ...toggleButtonStyle }}
                onClick={() =>
                  this.setState(
                    this.state.chartType === 'total'
                      ? null
                      : { barChartBucketSize: 'day' }
                  )
                }
                disabled={this.state.chartType === 'total'}
              >
                Per Day
              </Button>
              <Button
                variant={
                  this.state.barChartBucketSize === 'week'
                    ? 'contained'
                    : 'outlined'
                }
                aria-label="weekly new investments"
                style={{ ...toggleButtonStyle }}
                onClick={() =>
                  this.setState(
                    this.state.chartType === 'total'
                      ? null
                      : { barChartBucketSize: 'week' }
                  )
                }
                disabled={this.state.chartType === 'total'}
              >
                Per Week
              </Button>
              <Button
                variant={
                  this.state.barChartBucketSize === 'month'
                    ? 'contained'
                    : 'outlined'
                }
                aria-label="monthly new investments"
                style={{ ...toggleButtonStyle }}
                onClick={() =>
                  this.setState(
                    this.state.chartType === 'total'
                      ? null
                      : { barChartBucketSize: 'month' }
                  )
                }
                disabled={this.state.chartType === 'total'}
              >
                Per Month
              </Button>
              <Button
                variant={
                  this.state.barChartBucketSize === 'quarter'
                    ? 'contained'
                    : 'outlined'
                }
                aria-label="quarterly new investments"
                style={{ ...toggleButtonStyle }}
                onClick={() =>
                  this.setState(
                    this.state.chartType === 'total'
                      ? null
                      : { barChartBucketSize: 'quarter' }
                  )
                }
                disabled={this.state.chartType === 'total'}
              >
                Per Quarter
              </Button>
              <Button
                variant={
                  this.state.barChartBucketSize === 'year'
                    ? 'contained'
                    : 'outlined'
                }
                aria-label="yearly new investments"
                style={{ ...toggleButtonStyle }}
                onClick={() =>
                  this.setState(
                    this.state.chartType === 'total'
                      ? null
                      : { barChartBucketSize: 'year' }
                  )
                }
                disabled={this.state.chartType === 'total'}
              >
                Per Year
              </Button>
            </ButtonGroup>
          </Grid>
          <Grid item style={{ margin: '4px' }}>
            <ButtonGroup size="small" aria-label="time period filter">
              <Button
                variant={
                  this.state.timePeriod === 'all' ? 'contained' : 'outlined'
                }
                aria-label="all investments"
                style={{ ...toggleButtonStyle }}
                onClick={() => this.setState({ timePeriod: 'all' })}
              >
                All Time
              </Button>
              {/* <ToggleButton value="ytd" aria-label="year to date investments">
              YTD
            </ToggleButton> */}
              <Button
                variant={
                  this.state.timePeriod === 'year' ? 'contained' : 'outlined'
                }
                aria-label="1 year filter"
                style={{ ...toggleButtonStyle }}
                onClick={() => this.setState({ timePeriod: 'year' })}
              >
                1 Year
              </Button>
              {/* <ToggleButton value="6month" aria-label="6 month filter">
              6 Months
            </ToggleButton> */}
              <Button
                variant={
                  this.state.timePeriod === '3month' ? 'contained' : 'outlined'
                }
                aria-label="3 month filter"
                style={{ ...toggleButtonStyle }}
                onClick={() => this.setState({ timePeriod: '3month' })}
              >
                3 Months
              </Button>
            </ButtonGroup>
          </Grid>
          <Grid item style={{ margin: '4px' }}>
            <ButtonGroup size="small" aria-label="investments-total-type">
              <Tooltip title="Total invested net of crowd resold shares (not net of shares sold by Energea). Energea investments are not included.">
                <Button
                  variant={
                    this.state.investmentTotalType === 'net'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="net invested"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ investmentTotalType: 'net' })}
                >
                  Net Invested
                </Button>
              </Tooltip>
              <Tooltip title="Total invested by crowd. Energea investments are not included, and sold shares by the crowd are not deducted.">
                <Button
                  variant={
                    this.state.investmentTotalType === 'gross'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="gross invested"
                  style={{ ...toggleButtonStyle }}
                  onClick={() =>
                    this.setState({ investmentTotalType: 'gross' })
                  }
                >
                  Gross Invested
                </Button>
              </Tooltip>
              <Tooltip title="Sold positions by the crowd. Early exits are counted with the penalty applied. Positions sold by Energea's account are not included.">
                <Button
                  variant={
                    this.state.investmentTotalType === 'sold'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="amount sold"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ investmentTotalType: 'sold' })}
                >
                  Sold
                </Button>
              </Tooltip>
            </ButtonGroup>
          </Grid>
          <Grid item xs={12}>
            {this.state.chartType === 'total' ? (
              <Line height={360} data={data} options={chartOptions} />
            ) : (
              <Bar height={360} data={data} options={chartOptions} />
            )}
          </Grid>
        </>
      );
    } else {
      return (
        <>
          <Grid item>
            <IconButton
              variant="contained"
              color="primary"
              onClick={() => this.setState({ dialogOpen: true })}
            >
              <Settings />
            </IconButton>
          </Grid>
          <Grid item xs={12}>
            {this.state.chartType === 'total' ? (
              <Line height={360} data={data} options={chartOptions} />
            ) : (
              <Bar height={360} data={data} options={chartOptions} />
            )}
          </Grid>
          <Dialog open={this.state.dialogOpen} fullWidth>
            <DialogTitle>
              <Typography variant="h5">Chart Customization</Typography>
            </DialogTitle>
            <DialogContent>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Chart Type:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectChartType"
                    defaultValue={this.state.selectChartType}
                    onChange={(event) => {
                      this.setState({ selectChartType: event.target.value });
                    }}
                  >
                    <FormControlLabel
                      value="total"
                      control={<Radio />}
                      label="Total Invested"
                    />
                    <FormControlLabel
                      value="new"
                      control={<Radio />}
                      label="New Investments"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Bar Chart Bucket Size:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectBarChartBucketSize"
                    defaultValue={this.state.selectBarChartBucketSize}
                    onChange={(event) => {
                      this.setState({
                        selectBarChartBucketSize: event.target.value,
                      });
                    }}
                  >
                    <FormControlLabel
                      value="day"
                      control={<Radio />}
                      label="Per Day"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="week"
                      control={<Radio />}
                      label="Per Week"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="month"
                      control={<Radio />}
                      label="Per Month"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="quarter"
                      control={<Radio />}
                      label="Per Quarter"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="year"
                      control={<Radio />}
                      label="Per Year"
                      disabled={this.state.selectChartType === 'total'}
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Time Period:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectTimePeriod"
                    defaultValue={this.state.selectTimePeriod}
                    onChange={(event) => {
                      this.setState({ selectTimePeriod: event.target.value });
                    }}
                  >
                    <FormControlLabel
                      value="all"
                      control={<Radio />}
                      label="All Time"
                    />
                    <FormControlLabel
                      value="year"
                      control={<Radio />}
                      label="1 Year"
                    />
                    <FormControlLabel
                      value="3month"
                      control={<Radio />}
                      label="3 Months"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Investment Total Type:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectInvestmentTotalType"
                    defaultValue={this.state.selectInvestmentTotalType}
                    onChange={(event) => {
                      this.setState({
                        selectInvestmentTotalType: event.target.value,
                      });
                    }}
                  >
                    <FormControlLabel
                      value="net"
                      control={<Radio />}
                      label="Net Invested"
                    />
                    <FormControlLabel
                      value="gross"
                      control={<Radio />}
                      label="Gross Invested"
                    />
                    <FormControlLabel
                      value="sold"
                      control={<Radio />}
                      label="Amount Sold"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <MuiButton
                onClick={() => this.setState({ dialogOpen: false })}
                color="primary"
              >
                Cancel
              </MuiButton>
              <MuiButton
                onClick={() => {
                  this.setState({
                    chartType: this.state.selectChartType,
                    barChartBucketSize:
                      this.state.selectChartType === 'total'
                        ? 'month'
                        : this.state.selectBarChartBucketSize,
                    timePeriod: this.state.selectTimePeriod,
                    investmentTotalType: this.state.selectInvestmentTotalType,
                    dialogOpen: false,
                  });
                }}
                color="primary"
                variant="contained"
              >
                Go
              </MuiButton>
            </DialogActions>
          </Dialog>
        </>
      );
    }
  }
}

class InvestorBreakdownChart extends Component {
  constructor(props) {
    super(props);
    const formattedData = getInvestmentBreakdownData(
      props.data.allInvestors,
      props.data.getCMSDashboardData.crowdInvestmentTotal
    );
    this.chartData = formattedData.data;
    this.chartLabels = formattedData.labels;
  }

  render() {
    return (
      <>
        <Typography gutterBottom variant="h6">
          Investor Breakdown by Amount Invested :{' '}
        </Typography>
        <Grid item xs={12}>
          <Bar
            height={360}
            data={{
              labels: this.chartLabels,
              datasets: [
                {
                  label: 'Total Invested',
                  data: this.chartData.totals,
                  backgroundColor: theme.palette.green.main,
                  yAxisID: 'yTotals',
                },
                {
                  label: 'Investor Count',
                  data: this.chartData.counts,
                  backgroundColor: theme.palette.primary.main,
                  yAxisID: 'yCounts',
                },
              ],
            }}
            options={{
              maintainAspectRatio: false,
              borderRadius: 4,
              plugins: {
                legend: { display: false },
                tooltip: {
                  mode: 'index',
                  intersect: false,
                  filter: (tooltipItem) => tooltipItem?.datasetIndex === 0,
                  callbacks: {
                    title: (tooltipItem) => {
                      return `$${tooltipItem[0].label}`;
                    },
                    label: (tooltipItem) => {
                      return [
                        `Investors: ${numeral(
                          this.chartData.counts[tooltipItem.dataIndex]
                        ).format('0,0')}`,
                        `Total Invested: ${numeral(
                          this.chartData.totals[tooltipItem.dataIndex]
                        ).format('$0,0')}`,
                        `Median: ${
                          this.chartData.medians[tooltipItem.dataIndex]
                        }`,
                        `Mean: ${this.chartData.means[tooltipItem.dataIndex]}`,
                        `Percent of Total: ${
                          this.chartData.percentOfTotals[tooltipItem.dataIndex]
                        }`,
                      ];
                    },
                  },
                },
              },
              scales: {
                yTotals: {
                  title: {
                    text: `Total Invested`,
                    display: true,
                  },
                  position: 'right',
                  grid: {
                    display: false,
                  },
                },
                yCounts: {
                  title: {
                    text: `# of Investors`,
                    display: true,
                  },
                  position: 'left',
                },
              },
            }}
          />
        </Grid>
      </>
    );
  }
}
