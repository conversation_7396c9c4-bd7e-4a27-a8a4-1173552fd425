import React from 'react';
import { useParams } from 'react-router-dom';

import {
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  FunctionField,
  List,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  Toolbar,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Investor Return';

const InvestorReturnCreateToolbar = (props) => {
  const { data } = props;
  return (
    <Toolbar {...props}>
      <SaveButton
        label={'Batch Create InvestorReturns'}
        redirect="list"
        submitOnEnter
      />
    </Toolbar>
  );
};

export const InvestorReturnEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="irr" fullWidth />
            <DateInput source="firstInvestmentDt" fullWidth />
            <ReferenceInput
              source="user.id"
              reference="UserLite"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="fullName"
                label="User"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const styleRow = (record, index) => {
  const { showIRR } = record;
  const inactiveStyle = {
    backgroundColor: '#ddd',
    fontStyle: 'italic',
  };
  return showIRR ? {} : inactiveStyle;
};

export const InvestorReturnList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <FunctionField label="IRR" render={(record) => `${record.irr}%`} />
        <LinkField
          label="User"
          linkSource="user.id"
          labelSource="user.fullName"
          reference="User"
        />
        <DateField source="firstInvestmentDt" />
      </Datagrid>
    </List>
  );
};

export const InvestorReturnCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm toolbar={<InvestorReturnCreateToolbar />}>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <BooleanInput source="batchCreate" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
