import React, { useState } from 'react';

import {
  Card,
  CardActionArea,
  CardContent,
  CircularProgress,
  <PERSON>lapse,
  Divider,
  Grid,
  TextField,
  Typography,
} from '@mui/material';

import { Alert } from '@mui/lab';
import { Title, useDataProvider } from 'react-admin';
import { ArrowDropDown, ArrowDropUp } from '@mui/icons-material';
import numeral from 'numeral';
import moment from 'moment';
import 'chart.js/auto';

import { Line } from 'react-chartjs-2';

import theme from '../theme';

const renderOppListItem = (prevToDt) => {
  return (opportunity) => {
    const color = '#666';
    const disabled =
      opportunity.isClosedBoolean &&
      moment(opportunity.closeDate).isSameOrBefore(prevToDt);
    return (
      <>
        <Grid container justifyContent="center" direction="column">
          <Grid item style={{ textAlign: 'center' }}>
            <Typography
              style={{ color: disabled ? '#ccc' : 'inherit' }}
              variant="h6"
            >
              <b>
                {`${opportunity.opportunityName} - ${numeral(
                  opportunity.amount
                ).format('$0,0.[00]')}`}
              </b>
            </Typography>
          </Grid>
          <Grid item style={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              style={{ color: disabled ? '#ccc' : color }}
            >
              <b>User : </b>
              {opportunity.primaryLeadUser &&
                opportunity.primaryLeadUser.fullName}
            </Typography>
          </Grid>
          <Grid item style={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              style={{ color: disabled ? '#ccc' : color }}
            >
              <b>Created At : </b>
              {moment(opportunity.createTimestamp).format('ll')}
            </Typography>
          </Grid>
          <Grid item style={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              style={{ color: disabled ? '#ccc' : color }}
            >
              <b>
                {opportunity.isClosedBoolean
                  ? 'Closed Date'
                  : 'Estimated Closed Date'}{' '}
                :{' '}
              </b>
              {moment(opportunity.closeDate).format('ll')}
            </Typography>
          </Grid>
          <Grid item style={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              style={{ color: disabled ? '#ccc' : color }}
            >
              <b>Probability of close : </b>
              {opportunity.probability}%
            </Typography>
          </Grid>
          <Grid item style={{ textAlign: 'center' }}>
            <Typography
              variant="body2"
              style={{ color: disabled ? '#ccc' : color }}
            >
              <b>Amount actually invested to date : </b>
              {(opportunity.primaryLeadUser &&
                numeral(opportunity.primaryLeadUser.investmentSum).format(
                  '$0,0.00'
                )) ||
                '$0'}
            </Typography>
          </Grid>
        </Grid>
        <Grid item style={{ textAlign: 'center' }} xs={12}>
          <Divider
            style={{
              margin: 'auto',
              marginTop: '1rem',
              marginBottom: '1rem',
              maxWidth: '12rem',
            }}
          />
        </Grid>
      </>
    );
  };
};

export default (args) => {
  const [cmsDashboardData, setCMSDashboardData] = useState(null);
  const [visibleChart, setVisibleChart] = useState(null);
  const [fromDt, setFromDt] = useState(
    moment().subtract(30, 'days').format('YYYY-MM-DD')
  );
  const [toDt, setToDt] = useState(moment().format('YYYY-MM-DD'));
  const dataAttrs = [
    'totalLostOpportunities',
    'prevTotalLostOpportunities',
    'totalLostOpportunitiesValue',
    'prevTotalLostOpportunitiesValue',
    'totalWonOpportunities',
    'prevTotalWonOpportunities',
    'totalWonOpportunitiesValue',
    'prevTotalWonOpportunitiesValue',
    'totalOpenOpportunities',
    'prevTotalOpenOpportunities',
    'totalWeightedOpenOpportunitiesValue',
    'prevTotalWeightedOpenOpportunitiesValue',
    'totalNewOpportunities',
    'prevTotalNewOpportunities',
    'totalNewOpportunitiesValue',
    'prevTotalNewOpportunitiesValue',
    'totalTouchPoints',
    'totalPhoneCalls',
    'totalEmails',
    'totalEmailsOpened',
    'prevTotalTouchPoints',
    'prevTotalPhoneCalls',
    'prevTotalEmails',
    'prevTotalEmailsOpened',
  ];
  const dataProvider = useDataProvider();
  const { permissions } = args;

  const fetchDashboardData = (dates = {}) => {
    const startDt = dates.fromDt ?? fromDt;
    const endDt = dates.toDt ?? toDt;
    dataProvider
      .getOne('CMSSalesDashboardData', {
        input: { toDt: endDt, fromDt: startDt },
      })
      .then(
        (resp) => {
          setCMSDashboardData(resp.data.getCMSSalesDashboardData);
        },
        (e) => {
          // eslint disable-next-line no-console
          console.error('Error retrieving dashboard data', e);
        }
      );
  };

  if (!cmsDashboardData) {
    fetchDashboardData();
  }

  if (!(cmsDashboardData && permissions))
    return (
      <Grid
        style={{
          position: 'fixed',
          top: '50%',
          width: '100%',
          textAlign: 'center',
        }}
      >
        <CircularProgress />
      </Grid>
    );
  const { roles } = permissions;
  const touchPointDataStructure = [
    {
      label: 'All Outreach',
      attr: 'totalTouchPoints',
      prevAttr: 'prevTotalTouchPoints',
      dailyAttr: 'dailyTotalTouchPoints',
      prevDailyAttr: 'prevDailyTotalTouchPoints',
    },
    {
      label: 'Phone Calls',
      attr: 'totalPhoneCalls',
      prevAttr: 'prevTotalPhoneCalls',
      dailyAttr: 'dailyTotalPhoneCalls',
      prevDailyAttr: 'prevDailyTotalPhoneCalls',
    },
    {
      label: 'Emails',
      attr: 'totalEmails',
      prevAttr: 'prevTotalEmails',
      dailyAttr: 'dailyTotalEmails',
      prevDailyAttr: 'prevDailyTotalEmails',
    },
    {
      label: 'Emails Opened',
      attr: 'totalEmailsOpened',
      prevAttr: 'prevTotalEmailsOpened',
      dailyAttr: 'dailyTotalEmailsOpened',
      prevDailyAttr: 'prevDailyTotalEmailsOpened',
    },
  ];
  const dataStructure = [
    {
      label: 'Open',
      attr: 'totalOpenOpportunities',
      prevAttr: 'prevTotalOpenOpportunities',
      valAttr: 'totalWeightedOpenOpportunitiesValue',
      prevValAttr: 'prevTotalWeightedOpenOpportunitiesValue',
      valLabel: 'Weighted Value',
    },
    {
      label: 'Won',
      attr: 'totalWonOpportunities',
      prevAttr: 'prevTotalWonOpportunities',
    },
    {
      label: 'Lost',
      attr: 'totalLostOpportunities',
      prevAttr: 'prevTotalLostOpportunities',
    },
    {
      label: 'New',
      attr: 'totalNewOpportunities',
      prevAttr: 'prevTotalNewOpportunities',
    },
  ];

  const daysBetween = moment(toDt).diff(moment(fromDt), 'days');
  const prevFromDt = moment(fromDt).subtract(daysBetween + 1, 'days');
  const prevToDt = moment(fromDt).subtract(1, 'days');
  const openOpps = [];
  const wonOpps = [];
  const lostOpps = [];
  const newOpps = [];
  return (
    <Card>
      <Title title="Sales Dashboard" />
      <CardContent>
        <Grid container justifyContent="space-between">
          <Grid item>
            <Typography variant="h4">Sales</Typography>
          </Grid>
          <Grid item>
            <TextField
              id="fromDate"
              label="From"
              type="date"
              value={fromDt}
              onChange={(event) => {
                const val = event.target.value;
                if (moment(val) < moment(toDt)) {
                  fetchDashboardData({ fromDt: val });
                }
                setFromDt(val);
              }}
              InputLabelProps={{
                shrink: true,
              }}
              style={{ marginRight: '2rem' }}
            />
            <TextField
              id="toDate"
              label="To"
              type="date"
              value={toDt}
              onChange={(event) => {
                const val = event.target.value;
                if (moment(val) > moment(fromDt)) {
                  fetchDashboardData({ toDt: val });
                }
                setToDt(val);
              }}
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
        </Grid>
        <Divider
          style={{ width: '100%', marginTop: '2em', marginBottom: '2em' }}
        />
        <Grid
          container
          style={{ width: '100%' }}
          justifyContent="space-evenly"
          spacing={1}
        >
          {fromDt && toDt && fromDt < toDt ? (
            <Grid style={{ textAlign: 'center' }} item xs={12}>
              <Typography
                variant="h5"
                style={{ color: theme.palette.secondary.main }}
              >
                {moment(fromDt).format('MMMM Do')} -{' '}
                {moment(toDt).format('MMMM Do')}
              </Typography>
              <Typography
                style={{
                  color: 'rgba(0,0,0,.6)',
                }}
                variant="body2"
              >
                vs
              </Typography>
              <Typography
                style={{
                  color: 'rgba(0,0,0,.6)',
                  fontWeight: 'bold',
                }}
                variant="body1"
              >
                {prevFromDt.format('MMMM Do')} -{' '}
                {moment(prevToDt).format('MMMM Do')}
              </Typography>
            </Grid>
          ) : (
            <Grid item style={{ textAlign: 'center' }}>
              <Alert severity="warning">
                Select a 'from' date that is before the 'to' date
              </Alert>
            </Grid>
          )}
          <Divider style={{ width: '100%', margin: '2rem 0' }} />
          <Grid item xs={12}>
            <Typography gutterBottom variant="h5">
              Outreach :
            </Typography>
          </Grid>
          {cmsDashboardData
            ? touchPointDataStructure.map((dataEl, index) => {
                const selected = dataEl.label === visibleChart;
                const change =
                  (cmsDashboardData[dataEl.attr] -
                    cmsDashboardData[dataEl.prevAttr]) /
                  cmsDashboardData[dataEl.prevAttr];
                let icon;
                let color;
                if (change > 0) {
                  color = theme.palette.green.main;
                  icon = <ArrowDropUp />;
                } else if (change < 0) {
                  color = 'red';
                  icon = <ArrowDropDown />;
                } else {
                  color = '#666';
                  icon = <ArrowDropUp />;
                }
                return (
                  <Grid
                    key={`kpi-container-${dataEl.attr}`}
                    item
                    lg={3}
                    sm={6}
                    xs={12}
                  >
                    <Card
                      elevation={selected ? 2 : 0}
                      style={{
                        height: '14rem',
                        boxSizing: 'border-box',
                        backgroundColor: selected ? '#fafafa' : null,
                      }}
                    >
                      <CardActionArea
                        onClick={() => setVisibleChart(dataEl.label)}
                        aria-label={`${dataEl.label} chart`}
                        style={{
                          cursor: 'pointer',
                          height: '100%',
                        }}
                      >
                        <CardContent>
                          <Grid
                            container
                            direction="column"
                            justifyContent="center"
                            alignItems="center"
                            item
                            xs={12}
                          >
                            <Grid item>
                              <Typography
                                style={{
                                  color: theme.palette.primary.main,
                                  fontWeight: 'normal',
                                  textAlign: 'center',
                                }}
                                variant="h5"
                              >
                                {dataEl.label}
                              </Typography>
                            </Grid>
                            <Grid
                              style={{
                                marginTop: '1rem',
                              }}
                              item
                            >
                              <Grid
                                container
                                alignItems="center"
                                style={{ position: 'relative' }}
                              >
                                <Grid item>
                                  <Typography
                                    style={{
                                      fontWeight: 'bold',
                                      textAlign: 'center',
                                      color: theme.palette.green.main,
                                    }}
                                    variant="h3"
                                  >
                                    {numeral(
                                      cmsDashboardData[dataEl.attr]
                                    ).format('0,0')}
                                  </Typography>
                                </Grid>
                                <Grid
                                  item
                                  container
                                  alignItems="center"
                                  style={{
                                    position: 'absolute',
                                    right: '-6.5rem',
                                    width: '6rem',
                                    color,
                                  }}
                                >
                                  <Grid item>
                                    <Typography variant="body2">
                                      <span
                                        style={{ fontWeight: 'bold', color }}
                                      >
                                        {!isFinite(change) ? (
                                          <span>&infin;</span>
                                        ) : (
                                          numeral(change).format('0,0.0%')
                                        )}
                                      </span>
                                    </Typography>
                                  </Grid>
                                  <Grid item>{icon}</Grid>
                                </Grid>
                              </Grid>
                            </Grid>
                            <Grid item>
                              <Typography
                                style={{
                                  color: 'rgba(0,0,0,.6)',
                                }}
                                variant="body1"
                              >
                                Previously{' '}
                                {numeral(
                                  cmsDashboardData[dataEl.prevAttr]
                                ).format('0,0')}
                              </Typography>
                            </Grid>
                          </Grid>
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  </Grid>
                );
              })
            : null}
          <Grid container style={{ width: '100%' }}>
            {cmsDashboardData
              ? touchPointDataStructure.map((dataEl) => {
                  return (
                    <Grid item xs={12}>
                      <Collapse in={visibleChart === dataEl.label}>
                        <Typography gutterBottom variant="h6">
                          {dataEl.label} :{' '}
                        </Typography>
                        <Grid item xs={12}>
                          <Line
                            height={300}
                            data={{
                              labels: cmsDashboardData[dataEl.dailyAttr].map(
                                (day) => day.date
                              ),
                              datasets: [
                                {
                                  label: 'Current Period',
                                  data: cmsDashboardData[dataEl.dailyAttr].map(
                                    (day) => day.value
                                  ),
                                  backgroundColor: 'rgba(21, 48, 76, 0.3)',
                                  borderColor: theme.palette.primary.main,
                                  fill: true,
                                  tension: 0.5,
                                },
                                {
                                  label: 'Previous Period',
                                  data: cmsDashboardData[
                                    dataEl.prevDailyAttr
                                  ].map((day) => day.value),
                                  borderColor: theme.palette.green.main,
                                  tension: 0.5,
                                },
                              ],
                            }}
                            options={{
                              maintainAspectRatio: false,
                              plugins: {
                                tooltip: {
                                  mode: 'index',
                                  intersect: false,
                                  callbacks: {
                                    title: (tooltipItem) => {
                                      const daysBetween = moment(toDt).diff(
                                        moment(fromDt),
                                        'days'
                                      );
                                      const currLabelDt = new Date(
                                        tooltipItem[0].label
                                      );
                                      const prevLabelDt = new Date(currLabelDt);
                                      prevLabelDt.setDate(
                                        prevLabelDt.getDate() -
                                          (daysBetween + 1)
                                      );

                                      const prevLabel = prevLabelDt
                                        .toUTCString()
                                        .substr(5, 11);
                                      const currLabel = currLabelDt
                                        .toUTCString()
                                        .substr(5, 11);
                                      return `${prevLabel} vs. ${currLabel}`;
                                    },
                                    label: (tooltipItem) => {
                                      if (tooltipItem.datasetIndex === 1)
                                        return null;

                                      const label = tooltipItem.dataset.label;
                                      const currValue =
                                        tooltipItem.chart.data.datasets[0].data[
                                          tooltipItem.dataIndex
                                        ];
                                      const prevValue =
                                        tooltipItem.chart.data.datasets[1].data[
                                          tooltipItem.dataIndex
                                        ];
                                      const percentChg =
                                        (currValue - prevValue) / prevValue;
                                      const percentChgStr = isFinite(percentChg)
                                        ? numeral(percentChg).format('0,0.0% ')
                                        : '-%';
                                      return `${label}: ${currValue} (${percentChgStr} ${
                                        isFinite(percentChg) && percentChg < 0
                                          ? 'down'
                                          : 'up'
                                      } from ${prevValue})`;
                                    },
                                  },
                                },
                              },
                              scales: {
                                y: {
                                  beginAtZero: true,
                                },
                              },
                            }}
                          />
                        </Grid>
                      </Collapse>
                    </Grid>
                  );
                })
              : null}
          </Grid>
          <Grid item xs={12}>
            <Typography gutterBottom variant="h5">
              Opportunities :
            </Typography>
          </Grid>
          {cmsDashboardData
            ? dataStructure.map((dataEl, index) => {
                const valAttr = dataEl.valAttr || `${dataEl.attr}Value`;
                const prevValAttr =
                  dataEl.prevValAttr || `${dataEl.prevAttr}Value`;
                const change =
                  (cmsDashboardData[dataEl.attr] -
                    cmsDashboardData[dataEl.prevAttr]) /
                  cmsDashboardData[dataEl.prevAttr];
                const valChange =
                  (cmsDashboardData[String(valAttr)] -
                    cmsDashboardData[String(prevValAttr)]) /
                  cmsDashboardData[String(prevValAttr)];
                let icon;
                let color;
                let opps;
                switch (index) {
                  case 0:
                    opps = openOpps.sort((a, b) =>
                      a.createTimestamp > b.createTimestamp ? -1 : 1
                    );
                    break;
                  case 1:
                    opps = wonOpps.sort((a, b) =>
                      a.closeDate > b.closeDate ? -1 : 1
                    );
                    break;
                  case 2:
                    opps = lostOpps.sort((a, b) =>
                      a.closeDate > b.closeDate ? -1 : 1
                    );
                    break;
                  case 3:
                    opps = newOpps.sort((a, b) =>
                      a.createTimestamp > b.createTimestamp ? -1 : 1
                    );
                    break;
                  default:
                    opps = [];
                    break;
                }
                if (change > 0) {
                  color = theme.palette.green.main;
                  icon = <ArrowDropUp />;
                } else if (change < 0) {
                  color = 'red';
                  icon = <ArrowDropDown />;
                } else {
                  color = '#666';
                  icon = <ArrowDropUp />;
                }
                let valIcon;
                let valColor;
                if (valChange > 0) {
                  valColor = theme.palette.green.main;
                  valIcon = <ArrowDropUp />;
                } else if (valChange < 0) {
                  valColor = 'red';
                  valIcon = <ArrowDropDown />;
                } else {
                  valColor = '#666';
                  valIcon = <ArrowDropUp />;
                }
                return (
                  <Grid
                    key={`kpi-container-${dataEl.attr}`}
                    item
                    lg={3}
                    sm={6}
                    xs={12}
                  >
                    <Card
                      elevation={1}
                      style={{
                        margin: '1rem',
                        boxSizing: 'border-box',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="center"
                          alignItems="center"
                          item
                          style={{ marginBottom: '2rem' }}
                          xs={12}
                        >
                          <Grid item>
                            <Typography
                              style={{
                                color: theme.palette.primary.main,
                                fontWeight: 'normal',
                                textAlign: 'center',
                              }}
                              variant="h5"
                            >
                              {dataEl.label}
                            </Typography>
                          </Grid>
                          <Grid container item>
                            <Grid
                              container
                              item
                              direction="column"
                              justifyContent="center"
                              alignItems="center"
                              style={{
                                marginTop: '1rem',
                              }}
                            >
                              <Grid item>
                                <Typography variant="body1">
                                  <b>Count</b>
                                </Typography>
                              </Grid>
                              <Grid item>
                                <Grid
                                  container
                                  alignItems="center"
                                  style={{ position: 'relative' }}
                                >
                                  <Grid item>
                                    <Typography
                                      style={{
                                        fontWeight: 'bold',
                                        textAlign: 'center',
                                        color: theme.palette.green.main,
                                      }}
                                      variant="h3"
                                    >
                                      {numeral(
                                        cmsDashboardData[dataEl.attr]
                                      ).format('0,0')}
                                    </Typography>
                                  </Grid>
                                  <Grid
                                    item
                                    container
                                    alignItems="center"
                                    style={{
                                      position: 'absolute',
                                      right: '-6.5rem',
                                      width: '6rem',
                                      color,
                                    }}
                                  >
                                    <Grid item>
                                      <Typography variant="body2">
                                        <span
                                          style={{ fontWeight: 'bold', color }}
                                        >
                                          {!isFinite(change) ? (
                                            <span>&infin;</span>
                                          ) : (
                                            numeral(change).format('0,0.0%')
                                          )}
                                        </span>
                                      </Typography>
                                    </Grid>
                                    <Grid item>{icon}</Grid>
                                  </Grid>
                                </Grid>
                              </Grid>

                              <Grid item>
                                <Typography
                                  style={{
                                    color: 'rgba(0,0,0,.6)',
                                  }}
                                  variant="body1"
                                >
                                  Previously{' '}
                                  {numeral(
                                    cmsDashboardData[dataEl.prevAttr]
                                  ).format('0,0')}
                                </Typography>
                              </Grid>
                            </Grid>
                            {/* VALUE */}
                            <Grid
                              container
                              item
                              direction="column"
                              justifyContent="center"
                              alignItems="center"
                              style={{
                                marginTop: '1rem',
                              }}
                            >
                              <Grid item>
                                <Typography variant="body1">
                                  <b>{dataEl.valLabel || 'Value'}</b>
                                </Typography>
                              </Grid>
                              <Grid item>
                                <Grid
                                  container
                                  alignItems="center"
                                  style={{ position: 'relative' }}
                                >
                                  <Grid item>
                                    <Typography
                                      style={{
                                        fontWeight: 'bold',
                                        textAlign: 'center',
                                        color: theme.palette.green.main,
                                      }}
                                      variant="h3"
                                    >
                                      {numeral(
                                        cmsDashboardData[String(valAttr)]
                                      ).format('$0.0a')}
                                    </Typography>
                                  </Grid>
                                  <Grid
                                    item
                                    container
                                    alignItems="center"
                                    style={{
                                      position: 'absolute',
                                      right: '-6.5rem',
                                      width: '6rem',
                                      color,
                                    }}
                                  >
                                    <Grid item>
                                      <Typography variant="body2">
                                        <span
                                          style={{
                                            fontWeight: 'bold',
                                            color: valColor,
                                          }}
                                        >
                                          {!isFinite(valChange) ? (
                                            <span>&infin;</span>
                                          ) : (
                                            numeral(valChange).format('0,0.0%')
                                          )}
                                        </span>
                                      </Typography>
                                    </Grid>
                                    <Grid item>{valIcon}</Grid>
                                  </Grid>
                                </Grid>
                              </Grid>

                              <Grid item>
                                <Typography
                                  style={{
                                    color: 'rgba(0,0,0,.6)',
                                  }}
                                  variant="body1"
                                >
                                  Previously{' '}
                                  {numeral(
                                    cmsDashboardData[String(prevValAttr)]
                                  ).format('$0.0a')}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                        {opps.map(renderOppListItem(prevToDt))}
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })
            : null}
        </Grid>
      </CardContent>
    </Card>
  );
};
