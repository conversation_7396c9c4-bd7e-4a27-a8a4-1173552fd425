import React, { useState } from 'react';
import { CircularProgress, Grid, Typography } from '@mui/material';
import { Alert } from '@mui/lab';
import { useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';
import 'chartjs-adapter-moment';
import { Line } from 'react-chartjs-2';
import moment from 'moment';

import theme from '../theme';
import { getGradient } from '../utils/global';

export default (props) => {
  const { salesforceProjectId, name } = props;
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(null);
  const [error, setError] = useState(null);

  const notify = useNotify();
  const dataProvider = useDataProvider();

  const fetchData = () => {
    dataProvider
      .getOne('CreditMgmtProjectSalesData', {
        id: salesforceProjectId,
      })
      .then(
        (res) => {
          setLoading(false);
          setError(null);
          setData(res.data);
        },
        (err) => {
          console.error(err);
          setError(err);
          setLoading(false);
          notify('Failed to fetch list of projects', {
            type: 'error',
          });
        }
      );
  };

  if (!loading && !data && !error) {
    fetchData();
  }

  const getFillCapacityColor = (val) => {
    if (val >= 1) return theme.palette.green.dark;
    if (val >= 0.8) return theme.palette.orange.main;
    if (val >= 0.5) return theme.palette.warning.dark;
    return theme.palette.error.dark;
  };

  const renderProjectSalesData = () => {
    if (error) {
      return <Alert severity="error">Error loading project data</Alert>;
    }

    if (loading || !data) {
      return (
        <Grid
          container
          style={{ width: '100%', height: '261px', margin: '1rem 0' }}
          justifyContent="center"
          alignItems="center"
        >
          <CircularProgress />
        </Grid>
      );
    }

    const systemSizeDataset = [];
    let endingValue = 0;
    const sortedProjects = data.projects;
    sortedProjects.sort((a, b) => (a.actualCOD < b.actualCOD ? -1 : 1));
    sortedProjects.forEach((project) => {
      const previousValue = endingValue;
      if (project.actualCOD) {
        systemSizeDataset.push({
          date: new Date(project.actualCOD),
          value: previousValue + project.avgP50Prod,
        });
        endingValue += project.avgP50Prod;
      }
    });
    systemSizeDataset.push({
      date: new Date(),
      value: endingValue,
    });

    systemSizeDataset.sort((a, b) => (a.date < b.date ? -1 : 1));
    // NOTE: This ^ sort and the one below are needed
    if (data.capacityOverTimeChartData.length > 0) {
      const firstCUDate = new Date(data.capacityOverTimeChartData[0].date);
      const firstCODDate = new Date(systemSizeDataset[0].date);
      if (firstCUDate < firstCODDate) {
        systemSizeDataset.push({
          date: firstCUDate,
          value: 0,
        });
      } else if (firstCUDate > firstCODDate) {
        data.capacityOverTimeChartData.push({
          date: firstCODDate,
          adjustedAverageMonthlyConsumption: 0,
          averageMonthlyConsumption: 0,
        });
      }
    }

    // NOTE: This sort and the one above are needed
    systemSizeDataset.sort((a, b) => (a.date < b.date ? -1 : 1));
    data.capacityOverTimeChartData.sort((a, b) =>
      new Date(a.date) < new Date(b.date) ? -1 : 1
    );

    const currentCapacity =
      systemSizeDataset[systemSizeDataset.length - 1].value;
    let currentCUSizeAdjusted = 0;
    if (data.capacityOverTimeChartData?.length) {
      currentCUSizeAdjusted =
        data.capacityOverTimeChartData[
          data.capacityOverTimeChartData?.length - 1
        ].adjustedAverageMonthlyConsumption || 0;
    }

    // Calculate total projected capacity and check for projects without actualCOD
    const totalProjectedCapacity =
      data.projects?.reduce((acc, project) => {
        return acc + (project.avgP50Prod || 0);
      }, 0) || 0;

    const projectsWithoutCOD =
      data.projects?.filter(
        (project) => !project.actualCOD && project.projectedCOD
      ) || [];
    const hasProjectedProjects = projectsWithoutCOD.length > 0;

    // Also check if we have any projects at all when currentCapacity is 0
    const hasAnyProjects = totalProjectedCapacity > 0;

    // Find the earliest projected COD date among projects without actualCOD
    let earliestProjectedCOD = null;
    if (hasProjectedProjects) {
      earliestProjectedCOD = projectsWithoutCOD.reduce((earliest, project) => {
        if (
          !earliest ||
          (project.projectedCOD &&
            new Date(project.projectedCOD) < new Date(earliest))
        ) {
          return project.projectedCOD;
        }
        return earliest;
      }, null);
    }

    return (
      <Grid container style={{ width: '100%', margin: '1rem 0' }}>
        <Grid item xs={12}>
          <Typography variant="h5">{name}</Typography>
          <Typography
            gutterBottom
            style={{
              color:
                currentCapacity > 0
                  ? getFillCapacityColor(
                      currentCUSizeAdjusted / currentCapacity
                    )
                  : hasAnyProjects
                  ? 'grey'
                  : getFillCapacityColor(0),
            }}
          >
            {currentCapacity > 0 ? (
              <>
                {numeral(currentCUSizeAdjusted / currentCapacity).format(
                  '0,0[.]0%'
                )}{' '}
                filled of {numeral(currentCapacity / 1000).format('0,0')} MWh
              </>
            ) : hasAnyProjects ? (
              <>
                {numeral(currentCUSizeAdjusted / totalProjectedCapacity).format(
                  '0,0[.]0%'
                )}{' '}
                filled of {numeral(totalProjectedCapacity / 1000).format('0,0')}{' '}
                MWh{' '}
                {hasProjectedProjects && earliestProjectedCOD && (
                  <em>
                    *Proj. COD of{' '}
                    {moment(earliestProjectedCOD).format('M/D/YY')}
                  </em>
                )}
              </>
            ) : (
              <span>&infin;</span>
            )}
          </Typography>
        </Grid>
        {/* xs={12} results in chart forever expanding to the right */}
        <Grid item xs={11}>
          <Line
            key={`project-capacity-line-chart-${salesforceProjectId}`}
            height={340}
            data={{
              datasets: [
                {
                  label: 'Consumer Unit Adjusted Average Monthly Consumption',
                  data: data
                    ? data.capacityOverTimeChartData.map((pt) => ({
                        date: moment(pt.date, 'YYYY-MM-DD'),
                        value: pt.adjustedAverageMonthlyConsumption,
                        averageMonthlyConsumption: pt.averageMonthlyConsumption,
                      }))
                    : [],
                  backgroundColor: getGradient(theme.palette.green.main, 0.4),
                  fill: true,
                  pointRadius: 0,
                  borderColor: theme.palette.green.main,
                  stepped: 'before',
                },
                {
                  label: 'Average P50 Generation',
                  data: systemSizeDataset,
                  fill: false,
                  borderColor: '#777777', // Color of the dotted line
                  borderDash: [5, 5], // Creates the dotted effect: [dash length, space length]
                  tension: 0, // Disable any line curvature to make it straight
                  borderWidth: 2,
                  stepped: 'before',
                },
              ],
            }}
            options={{
              maintainAspectRatio: false,
              parsing: {
                xAxisKey: 'date',
                yAxisKey: 'value',
              },
              plugins: {
                legend: { display: false },
                tooltip: {
                  mode: 'nearest',
                  intersect: false,
                  callbacks: {
                    label: (tooltipItem) => {
                      if (tooltipItem.datasetIndex === 1) {
                        return `Capacity: ${numeral(
                          tooltipItem.raw.value
                        ).format('0,0')} kWh`;
                      }
                      return [
                        `Adjusted average monthly consumption: ${numeral(
                          tooltipItem.raw.value
                        ).format('0,0')} kWh`,
                        `Average monthly consumption: ${numeral(
                          tooltipItem.raw.averageMonthlyConsumption
                        ).format('0,0')} kWh`,
                      ];
                    },
                  },
                },
              },
              scales: {
                x: {
                  type: 'time',
                  time: {
                    tooltipFormat: 'MMM D, YYYY',
                    unit: 'day',
                  },
                },
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Capacity (kWh)',
                  },
                },
              },
            }}
          />
        </Grid>
      </Grid>
    );
  };

  return renderProjectSalesData();
};
