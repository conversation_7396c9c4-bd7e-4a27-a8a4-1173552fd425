import React, { useState } from 'react';

import {
  <PERSON><PERSON>y<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  SaveButton,
  Filter,
  FunctionField,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  Show,
  SingleFieldList,
  SimpleForm,
  SimpleShowLayout,
  TextField,
  TextInput,
  Toolbar,
  UrlField,
  useDataProvider,
  useNotify,
  useRefresh,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Button, Chip } from '@mui/material';
import numeral from 'numeral';

import theme from '../theme';
import { CustomTopToolbar } from './CustomTopToolbar';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Transfer';

const CustomToolbar = () => (
  <Toolbar>
    <SaveButton />
    <SaveButton
      redirect={false}
      label="Create Dwolla Transfer"
      transform={(data) => ({
        ...data,
        createDwollaTransfer: true,
      })}
      // submitOnEnter={false}
    />
  </Toolbar>
);

export const TransferEdit = () => {
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const cancelTransfer = (transferId) => {
    setLoading(true);
    dataProvider
      .update('DwollaTransfer', {
        data: {
          transferId,
          cancelTransfer: true,
        },
      })
      .then(
        (record) => {
          notify(`Successfully cancelled transfer ${transferId}`);
          setLoading(false);
          refresh();
        },
        (e) => {
          console.log('HIT ERROR', e);
          notify(e.message, { type: 'error' });
          setLoading(false);
        }
      );
  };
  return (
    <Edit title={`${entityName} #${id}`}>
      <SimpleForm toolbar={<CustomToolbar />}>
        <TextField fullWidth source="type" />
        <DateInput fullWidth source="completedDt" />
        <DateField fullWidth source="updatedAt" />
        <DateField fullWidth source="createdAt" />
        <ArrayField fullWidth source="dwollaTransfers">
          <Datagrid>
            <TextField label="Id" source="id" />
            <TextField label="Status" source="status" />
            <NumberField
              label="Amount"
              source="amount.value"
              options={{ style: 'currency', currency: 'USD' }}
            />
            <FunctionField
              label="Cancel Action"
              style={{ width: '100%' }}
              render={(record) => (
                <Button
                  color="primary"
                  variant="contained"
                  disabled={loading || record.status !== 'pending'}
                  style={{
                    background:
                      record.status === 'pending'
                        ? theme.palette.error.main
                        : null,
                  }}
                  onClick={() => cancelTransfer(record.id)}
                >
                  Cancel Transfer
                </Button>
              )}
            />
          </Datagrid>
        </ArrayField>
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          reference="Investment"
          linkSource="investment.id"
          labelSource="investment.label"
          label="Investment"
        />
        <LinkField
          label="Buy Direction"
          linkSource="buyDirection.id"
          labelSource="buyDirection.label"
          reference="BuyDirection"
        />
        <LinkField
          reference="Dividend"
          linkSource="dividend.id"
          labelSource="dividend.label"
          label="Dividend"
        />
        <LinkField
          reference="ShareTransfer"
          linkSource="id"
          labelSource="label"
          label="Share Transfer"
        />
        <TextInput source="fromFundingSourceId" />
        <TextInput source="toFundingSourceId" />
        <CustomNumberInput source="amount" />
        {/* 
      <FunctionField
        label="Create Dwolla Transfer"
        style={{ width: '100%' }}
        render={(record) => {
          // const amount = (record.amount && record.amount.value) || null;
          // return `ID: ${record.id}, Status: ${record.status
          //   }, Amount: ${numeral(amount).format('$0,0.00')}`;
          console.log('HIT record', record);
          const hasTransfers =
            record.dwollaTransferIds && record.dwollaTransferIds.length > 0;
          return (
            <>
              
            </>
          );
        }}
      /> */}
      </SimpleForm>
    </Edit>
  );
};

export const TransferShow = () => (
  <Show>
    <SimpleShowLayout>
      <TextField source="id" />
      <TextField source="type" />
      <LinkField
        reference="User"
        linkSource="user.id"
        labelSource="user.fullName"
        label="User"
      />
      <LinkField
        reference="Investment"
        linkSource="investment.id"
        labelSource="investment.label"
        label="Investment"
      />
      <LinkField
        label="Buy Direction"
        linkSource="buyDirection.id"
        labelSource="buyDirection.label"
        reference="BuyDirection"
      />
      <LinkField
        reference="Dividend"
        linkSource="dividend.id"
        labelSource="dividend.label"
        label="Dividend"
      />
      <LinkField
        reference="ShareTransfer"
        linkSource="shareTransfer.id"
        labelSource="shareTransfer.label"
        label="Share Transfer"
      />
      <ArrayField source="dwollaTransfers">
        <Datagrid>
          <TextField source="id" />
          <UrlField target="_blank" label="Dwolla Link" source="dwollaUrl" />
          <TextField source="status" />
          <NumberField
            options={{ style: 'currency', currency: 'USD' }}
            label="Amount"
            source="amount.value"
          />
        </Datagrid>
      </ArrayField>
      <FunctionField
        label="Millennium Trust Transfer"
        style={{
          width: '100%',
        }}
        render={(record) => {
          if (record.millenniumTrustInvestmentTransfer) {
            return (
              <span>{`ID: ${
                record.millenniumTrustInvestmentTransfer.id
              }, Status: ${
                record.millenniumTrustInvestmentTransfer.status
              }, Amount: ${numeral(
                record.millenniumTrustInvestmentTransfer.amount
              ).format('$0,0.00')}`}</span>
            );
          }
          return null;
        }}
      />
      <DateField source="completedDt" showTime={true} />
      <DateField source="updatedAt" showTime={true} />
      <DateField source="createdAt" showTime={true} />
    </SimpleShowLayout>
  </Show>
);

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);
const TransferFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      source="transferType.name"
      label="Type"
      reference="TransferType"
    >
      <SelectInput label="Type" optionText="label" />
    </ReferenceInput>
  </Filter>
);
const styleRow = (record, index) => {
  const { dwollaTransfers, type, dbAmount, millenniumTrustInvestmentTransfer } =
    record;
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'orange',
    color: '#fff',
  };
  if (!dwollaTransfers && !millenniumTrustInvestmentTransfer) return errorStyle;
  if (millenniumTrustInvestmentTransfer) {
    if (millenniumTrustInvestmentTransfer.amount !== dbAmount)
      return errorStyle;
  } else if (
    dwollaTransfers &&
    ['deposit', 'withdrawal'].indexOf(type) === -1
  ) {
    let dwollaAmount = 0;
    dwollaTransfers.forEach((dwollaTransfer) => {
      dwollaAmount += parseFloat(
        dwollaTransfer.amount && dwollaTransfer.amount.value
      );
    });
    if (dwollaAmount !== dbAmount) return errorStyle;
  }
  if (
    record.dwollaTransfers.filter((el) => el.status !== 'processed').length ===
      0 &&
    !record.completedDt
  )
    return warningStyle;
  if (
    record.dwollaTransfers.filter((el) => el.status !== 'processed').length >
      0 &&
    record.completedDt
  )
    return warningStyle;
  return {};
};
export const TransferList = () => (
  <List
    actions={<CustomTopToolbar hideExportButton={true} />} // This list is long enough that the export button can cause heap errors
    perPage={25}
    pagination={<CustomPagination />}
    filters={<TransferFilter />}
    sort={{ field: 'id', order: 'DESC' }}
  >
    <Datagrid rowStyle={styleRow} rowClick="show">
      <TextField source="id" />
      <TextField source="type" />
      <LinkField
        reference="User"
        linkSource="user.id"
        labelSource="user.fullName"
        label="User"
      />
      <LinkField
        reference="Investment"
        linkSource="investment.id"
        labelSource="investment.label"
        label="Investment"
      />
      <LinkField
        label="Buy Direction"
        linkSource="buyDirection.id"
        labelSource="buyDirection.label"
        reference="BuyDirection"
      />
      <LinkField
        reference="Dividend"
        linkSource="dividend.id"
        labelSource="dividend.label"
        label="Dividend"
      />
      <LinkField
        reference="ShareTransfer"
        linkSource="shareTransfer.id"
        labelSource="shareTransfer.label"
        label="Share Transfer"
      />
      <ArrayField fullWidth source="dbDwollaWebhookEvents" sortable={false}>
        <SingleFieldList>
          <FunctionField
            label="Dwolla Webhooks"
            render={(record) => <Chip label={record?.label} />}
          />
        </SingleFieldList>
      </ArrayField>
      <ArrayField fullWidth source="dwollaTransfers" sortable={false}>
        <SingleFieldList>
          <FunctionField
            label="Dwolla Transfers"
            style={{
              width: '100%',
            }}
            render={(record) => {
              const amount = (record.amount && record.amount.value) || null;
              return (
                <span
                  style={{
                    color: record.balanceToBankFailure ? 'darkred' : null,
                  }}
                >{`ID: ${record.id}, Status: ${
                  record.status
                }, Amount: ${numeral(amount).format('$0,0.00')}`}</span>
              );
            }}
          />
        </SingleFieldList>
      </ArrayField>
      <FunctionField
        label="Millennium Trust Transfer"
        style={{
          width: '100%',
        }}
        render={(record) => {
          if (record.millenniumTrustInvestmentTransfer) {
            return (
              <span>{`ID: ${
                record.millenniumTrustInvestmentTransfer.id
              }, Status: ${
                record.millenniumTrustInvestmentTransfer.status
              }, Amount: ${numeral(
                record.millenniumTrustInvestmentTransfer.amount
              ).format('$0,0.00')}`}</span>
            );
          }
          return null;
        }}
      />
      <DateField source="completedDt" showTime={true} />
      <DateField source="updatedAt" showTime={true} />
      <DateField source="createdAt" showTime={true} />
    </Datagrid>
  </List>
);

export const TransferCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <TextInput source="name" />
    </SimpleForm>
  </Create>
);
