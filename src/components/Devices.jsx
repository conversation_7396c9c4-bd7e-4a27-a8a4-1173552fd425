import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  List,
  NumberField,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, CustomReferenceField } from './CustomFields';

const entityName = 'Device';

export const DeviceEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="visitorId" required disabled fullWidth />
            <TextInput source="os" fullWidth />
            <TextInput source="browser" fullWidth />
            <TextInput source="device" fullWidth />
            <CustomNumberInput source="fraudScore" fullWidth />
            <DateTimeInput source="firstSeenDt" disabled fullWidth />
            <DateTimeInput source="lastSeenDt" disabled fullWidth />
            <TextInput source="fingerprintSource" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const DeviceList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'desc' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="visitorId" />
        <TextField source="os" />
        <TextField source="browser" />
        <TextField source="device" />
        <NumberField source="fraudScore" />
        <DateField source="firstSeenDt" showTime />
        <DateField source="lastSeenDt" showTime />
        <ArrayField source="userLogins">
          <SingleFieldList>
            <CustomReferenceField
              color={(resource) => 'primary'}
              source="label"
            />
          </SingleFieldList>
        </ArrayField>
        <TextField source="fingerprintSource" />
      </Datagrid>
    </List>
  );
};

export const DeviceCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="visitorId" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
