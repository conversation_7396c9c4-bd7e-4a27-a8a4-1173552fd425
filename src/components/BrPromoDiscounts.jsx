import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SimpleForm,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import {
  CustomBooleanField,
  LinkField,
  PercentageInput,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Promo Discount';

export const BrPromoDiscountEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              perPage={10_000}
              source="brTermsOfAdhesion.id"
              sortable={false}
              reference="BrTermsOfAdhesion"
            >
              <AutocompleteInput
                label="Terms of Adhesion"
                required
                fullWidth
                optionText="label"
                shouldRenderSuggestions={(value) => true}
              />
            </ReferenceInput>
            <DateInput source="startDt" required fullWidth />
            <DateInput source="endDt" fullWidth />
            <PercentageInput
              source="discountRate"
              required
              fullWidth
              disabled
            />
            <BooleanInput source="renegotiationFlg" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrPromoDiscountList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrTermsOfAdhesion"
          linkSource="brTermsOfAdhesion.id"
          labelSource="brTermsOfAdhesion.label"
          label="Terms of Adhesion"
        />
        <DateField source="startDt" />
        <DateField source="endDt" />
        <NumberField source="discountRate" />
        <CustomBooleanField source="renegotiationFlg" />
      </Datagrid>
    </List>
  );
};

export const BrPromoDiscountCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={10_000}
            source="brTermsOfAdhesion.id"
            sortable={false}
            reference="BrTermsOfAdhesion"
          >
            <AutocompleteInput
              label="Terms of Adhesion"
              required
              fullWidth
              optionText="label"
              shouldRenderSuggestions={(value) => true}
            />
          </ReferenceInput>
          <DateInput source="startDt" required fullWidth />
          <DateInput source="endDt" fullWidth />
          <PercentageInput source="discountRate" required fullWidth />
          <BooleanInput source="renegotiationFlg" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
