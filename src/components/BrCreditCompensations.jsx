import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  Filter,
  Labeled,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Credit Compensation';

export const BrCreditCompensationEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <Labeled fullWidth>
              <LinkField
                reference="BrConsumerUnit"
                linkSource="brConsumerUnit.id"
                labelSource="brConsumerUnit.name"
                label="Consumer Unit"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                reference="BrBillingCycle"
                linkSource="brBillingCycle.id"
                labelSource="brBillingCycle.label"
                label="Billing Cycle"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                reference="BrInvoice"
                linkSource="brInvoice.id"
                labelSource="brInvoice.label"
                label="Invoice"
              />
            </Labeled>
            <CustomNumberInput source="allocationPercentage" fullWidth />
            <CustomNumberInput
              source="peakUCBalance"
              fullWidth
              label="Peak UC balance"
            />
            <CustomNumberInput
              source="offPeakUCBalance"
              fullWidth
              label="Off-peak UC balance"
            />
            <CustomNumberInput source="grossConsumption" fullWidth />
            <BooleanInput source="finalPaymentFlg" fullWidth />
            <CustomNumberInput source="utilityElectricitySupplied" fullWidth />
            <CustomNumberInput source="utilityElectricityPrice" fullWidth />
            <CustomNumberInput source="utilityElectricityValue" fullWidth />
            <CustomNumberInput source="injectedElectricity" fullWidth />
            <CustomNumberInput source="injectedElectricityPrice" fullWidth />
            <CustomNumberInput source="injectedElectricityTaxPrice" fullWidth />
            <CustomNumberInput source="injectedElectricityTaxValue" fullWidth />
            <CustomNumberInput source="discountRate" fullWidth />
            <CustomNumberInput source="savingsAmount" fullWidth disabled />
            <CustomNumberInput source="additionalUtilityCharges" fullWidth />
            <CustomNumberInput source="compensatedCreditsValue" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Consumer Unit"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="Billing Cycle"
      source="brBillingCycle.id"
      reference="BrBillingCycleLite"
      perPage={10_000}
      sort={{ field: 'billingMonth', order: 'desc' }}
      alwaysOn
    >
      <SelectInput label="Billing Cycle" optionText="label" />
    </ReferenceInput>
  </Filter>
);

export const BrCreditCompensationList = () => {
  const { permissions } = usePermissions();

  const styleRow = (record, index) => {
    const { brInvoice } = record;
    const cancelledStyle = {
      backgroundColor: '#ddd',
      fontStyle: 'italic',
    };
    if (brInvoice && brInvoice.cancelledDt) {
      return cancelledStyle;
    }
    return {};
  };

  return (
    <List
      title={entityName}
      perPage={25}
      filters={<CustomFilter />}
      sort={{ field: 'id', order: 'desc' }}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrConsumerUnit"
          linkSource="brConsumerUnit.id"
          labelSource="brConsumerUnit.name"
          label="Consumer Unit"
        />
        <LinkField
          reference="BrBillingCycle"
          linkSource="brBillingCycle.id"
          labelSource="brBillingCycle.label"
          label="Billing Cycle"
        />
        <LinkField
          reference="BrInvoice"
          linkSource="brInvoice.id"
          labelSource="brInvoice.label"
          label="Invoice"
        />
        <NumberField source="allocationPercentage" />
        <NumberField source="peakUCBalance" label="Peak UC balance" />
        <NumberField source="offPeakUCBalance" label="Off-peak UC balance" />
        <NumberField source="grossConsumption" />
        <BooleanField source="finalPaymentFlg" />
        <NumberField source="utilityElectricitySupplied" />
        <NumberField source="utilityElectricityPrice" />
        <NumberField source="utilityElectricityValue" />
        <NumberField source="injectedElectricity" />
        <NumberField source="injectedElectricityPrice" />
        <NumberField source="injectedElectricityTaxPrice" />
        <NumberField source="injectedElectricityTaxValue" />
        <NumberField source="discountRate" />
        <NumberField source="savingsAmount" />
        <NumberField source="additionalUtilityCharges" />
        <NumberField source="compensatedCreditsValue" />
        <NumberField source="discountedInjectedElectricityPrice" />
      </Datagrid>
    </List>
  );
};

export const BrCreditCompensationCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={10_000}
            source="brConsumerUnit.id"
            sortable={false}
            reference="BrConsumerUnitLite"
          >
            <AutocompleteInput
              label="Consumer Unit"
              required
              fullWidth
              optionText="name"
              shouldRenderSuggestions={(value) => true}
            />
          </ReferenceInput>
          <ReferenceInput
            source="brBillingCycle.id"
            reference="BrBillingCycleLite"
            perPage={10_000}
            sort={{ field: 'billingMonth', order: 'DESC' }}
          >
            <SelectInput optionText="label" label="Billing Cycle" fullWidth />
          </ReferenceInput>
          <CustomNumberInput source="allocationPercentage" fullWidth />
          <CustomNumberInput
            source="peakUCBalance"
            fullWidth
            label="Peak UC balance"
          />
          <CustomNumberInput
            source="offPeakUCBalance"
            fullWidth
            label="Off-peak UC balance"
          />
          <CustomNumberInput source="grossConsumption" fullWidth />
          <BooleanInput source="finalPaymentFlg" fullWidth />
          <CustomNumberInput source="utilityElectricitySupplied" fullWidth />
          <CustomNumberInput source="utilityElectricityPrice" fullWidth />
          <CustomNumberInput source="utilityElectricityValue" fullWidth />
          <CustomNumberInput source="injectedElectricity" fullWidth />
          <CustomNumberInput source="injectedElectricityPrice" fullWidth />
          <CustomNumberInput source="injectedElectricityTaxPrice" fullWidth />
          <CustomNumberInput source="injectedElectricityTaxValue" fullWidth />
          <CustomNumberInput source="discountRate" fullWidth />
          <CustomNumberInput source="additionalUtilityCharges" fullWidth />
          <CustomNumberInput source="compensatedCreditsValue" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
