import React from 'react';
import {  useParams,} from 'react-router-dom'
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Accredited Qualification';

export const AccreditedQualificationEdit = () => {
  const {id} = useParams()
  return (
  <Edit title={`${entityName} #${id}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="label" required fullWidth />
          <CustomNumberInput source="orderNo" step={1} />
          <BooleanInput
            source="individualFlg"
            helperText="Set this to true if you want this option to show up in the USER onboarding process."
          />
          <BooleanInput
            source="businessFlg"
            helperText="Set this to true if you want this option to show up in the BUSINESS onboarding process."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Edit>
);
  }

export const AccreditedQualificationList = () => {
  const {permissions} = usePermissions();
  return (
  <List title={entityName} perPage={25}>
    <Datagrid
      rowClick={
        getEditable(useResourceDefinition().name, permissions) ? 'edit' : 'show'
      }
    >
      <NumberField source="id" />
      <TextField source="label" />
      <NumberField source="orderNo" />
      <BooleanField source="individualFlg" />
      <BooleanField source="businessFlg" />
    </Datagrid>
  </List>
);
    }

export const AccreditedQualificationCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="label" required fullWidth />
          <BooleanInput
            source="individualFlg"
            defaultValue={true}
            helperText="This must be set to true to show up in the USER onboarding process. Set to false to hide it from the USER onboarding process."
          />
          <BooleanInput
            source="businessFlg"
            defaultValue={true}
            helperText="This must be set to true to show up in the BUSINESS onboarding process. Set to false to hide it from the BUSINESS onboarding process."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);