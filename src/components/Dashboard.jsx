// cachebust 3
import React, { Component, useState } from 'react';
import CountUp from 'react-countup';
// import jsonExport from 'jsonexport/dist';

import {
  Avatar,
  Button,
  Card,
  CardActionArea,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  Hidden,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Title, useDataProvider, useNotify } from 'react-admin';
import {
  AccountBalanceWalletTwoTone,
  Close,
  // CheckCircle,
  Info,
  MonetizationOnTwoTone,
  PeopleAltTwoTone,
  VerifiedUser,
  // Warning,
} from '@mui/icons-material';
import numeral from 'numeral';
// import moment from 'moment';

import theme from '../theme';
import { beerMatrix } from '../utils/global';
import DashboardInvestments from './DashboardInvestments';
import DashboardUsers from './DashboardUsers';
import DashboardDividends from './DashboardDividends';
import DashboardFees from './DashboardFees';

export default (args) => {
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  const [data, setData] = useState(null);
  const [showChart, setShowChart] = useState('crowdInvestmentTotal');
  const [loading, setLoading] = useState(false);
  const [
    manuallyTriggerAutoInvestmentsDialogOpen,
    setManuallyTriggerAutoInvestmentsDialogOpen,
  ] = useState(false);
  const [
    manuallyTriggerAutoInvestmentsDayOfMonth,
    setManuallyTriggerAutoInvestmentsDayOfMonth,
  ] = useState(null);
  const dataProvider = useDataProvider();
  const showNotification = useNotify();
  const jsx = [];
  const { permissions } = args;
  // const permissionsLoading = !permissions || !permissions.oktaUser;
  const isVictoryHill = permissions?.roles
    ?.map((role) => role.name)
    .includes('VictoryHill');
  if (isVictoryHill) {
    return null;
  }

  const fetchData = () => {
    dataProvider.getOne('CMSDashboardData', { data: null }).then(
      (resp) => {
        setData(resp.data);
      },
      (e) => {
        console.error('HIT AN ERROR', e);
        return new Error(e);
      }
    );
  };

  if (!data) {
    fetchData();
  }

  if (!(data && permissions))
    return (
      <Grid
        container
        style={{
          // position: 'fixed',
          top: '50%',
          width: '100%',
          height: '100vh',
          textAlign: 'center',
        }}
        justifyContent="center"
        alignItems="center"
      >
        <Grid item>
          <CircularProgress />
        </Grid>
      </Grid>
    );

  const { roles } = permissions;
  const {
    getCMSDashboardData: CMSDashboardData,
    getInvestorCount: investorCount,
  } = data;

  const dataStructure = [
    {
      attr: 'crowdInvestmentTotal',
      formatter: (val) => numeral(val).format('$0,0.00'),
      label: 'Net Crowd Investments Received',
      icon: (
        <MonetizationOnTwoTone
          style={{
            fontSize: '2.5rem',
            color:
              showChart === 'crowdInvestmentTotal'
                ? theme.palette.primary.main
                : 'inherit',
          }}
        />
      ),
    },
    {
      attr: 'investorCount',
      formatter: (val) => numeral(val).format('0,0'),
      label: 'Number of Investors',
      icon: (
        <PeopleAltTwoTone
          style={{
            fontSize: '2.5rem',
            color:
              showChart === 'investorCount'
                ? theme.palette.primary.main
                : 'inherit',
          }}
        />
      ),
    },
    {
      attr: 'dividendTotal',
      formatter: (val) => numeral(val).format('$0,0.00'),
      label: 'Total Dividends Paid Out',
      icon: (
        <AccountBalanceWalletTwoTone
          style={{
            fontSize: '2.5rem',
            color:
              showChart === 'dividendTotal'
                ? theme.palette.primary.main
                : 'inherit',
          }}
        />
      ),
    },
    {
      attr: 'feesAndCarryTotal',
      formatter: (val) => numeral(Math.round(val)).format('$0,0[.]00'),
      label: 'Total Fees & Carry',
      icon: (
        <MonetizationOnTwoTone
          style={{
            fontSize: '2.5rem',
            color:
              showChart === 'feesAndCarryTotal'
                ? theme.palette.primary.main
                : 'inherit',
          }}
        />
      ),
    },
  ];
  roles &&
    roles.forEach((role) => {
      jsx.push(
        <ListItem key={`role-list-item-${role.name}`}>
          <ListItemAvatar>
            <Avatar>
              <VerifiedUser />
            </Avatar>
          </ListItemAvatar>
          <ListItemText primary={role.name} secondary={role.description} />
        </ListItem>
      );
    });

  const beerData = beerMatrix[permissions.email];
  return (
    <div style={{ padding: '1rem', width: '100%' }}>
      <Title title="Platform Dashboard" />
      {/* <CardContent> */}
      <Hidden only={['xs', 'sm']}>
        <Typography gutterBottom variant="h4">
          {permissions.firstName}, welcome to Starlight.
        </Typography>
        {beerData
          ? beerData.map((el, i) => (
              <Typography variant="body2" key={`beer-data-${i}`}>
                {el.name} owes you <b>{el.number}</b> {el.item}
                {el.number > 1 ? 's' : ''}.
              </Typography>
            ))
          : null}

        <Divider style={{ width: '100%', marginTop: '2em' }} />
      </Hidden>
      <Grid container style={{ width: '100%', marginTop: '2em' }}>
        <Grid
          style={{ textAlign: 'center', margin: '2rem 0' }}
          item
          xs={12}
          lg={6}
        >
          <Typography
            style={{
              color: theme.palette.green.main,
              fontSize: fullScreen && '3rem',
            }}
            variant="h2"
          >
            <b>
              <CountUp
                useEasing={false}
                decimals={0}
                // delay={1}
                duration={2}
                end={CMSDashboardData && CMSDashboardData.aum}
                formattingFn={(num) => `${numeral(num).format('$0,0')}`}
              />
            </b>
          </Typography>
          <Grid container style={{ width: '100%' }} justifyContent="center">
            <Grid item>
              <Typography
                style={{
                  fontWeight: 'bold',
                  color: 'rgba(0,0,0,.6)',
                }}
                variant="body1"
              >
                Total Invested
              </Typography>
            </Grid>
            <Grid item>
              <Tooltip
                arrow
                title={
                  <>
                    <Typography style={{ fontWeight: 'bold' }}>
                      Breakdown:
                    </Typography>
                    <Typography variant="body2">
                      {`Gross Crowd Investments: ${numeral(
                        CMSDashboardData.crowdAum
                      ).format('$0,0')}`}
                    </Typography>
                    <Typography variant="body2">
                      {`Gross Energea Investments: ${numeral(
                        CMSDashboardData.energeaAum
                      ).format('$0,0')}`}
                    </Typography>
                    <Typography variant="body2">
                      {`Institutional Investments: ${numeral(
                        CMSDashboardData.institutionalInvestmentTotal
                      ).format('$0,0')}`}
                    </Typography>
                    <Typography variant="body2">
                      {`Retail Debt Investments: ${numeral(
                        CMSDashboardData.retailDebtInvestmentTotal
                      ).format('$0,0')}`}
                    </Typography>
                  </>
                }
              >
                <Info color="secondary" style={{ marginLeft: '5px' }} />
              </Tooltip>
            </Grid>
          </Grid>
        </Grid>
        <Grid
          style={{ textAlign: 'center', margin: '2rem 0' }}
          item
          xs={12}
          lg={6}
        >
          <Typography
            style={{
              color: theme.palette.green.main,
              fontSize: fullScreen && '3rem',
            }}
            variant="h2"
          >
            <b>
              <CountUp
                useEasing={false}
                decimals={3}
                // delay={1}
                duration={2}
                end={
                  CMSDashboardData &&
                  CMSDashboardData.latestNAVBasedIRR &&
                  CMSDashboardData.latestNAVBasedIRR.navBasedIRR
                }
                formattingFn={(num) => `${numeral(num).format('0.000')}%`}
              />
            </b>
          </Typography>
          <Typography
            style={{
              fontWeight: 'bold',
              color: 'rgba(0,0,0,.6)',
            }}
            variant="body1"
          >
            Realized IRR (including FMV)
          </Typography>
        </Grid>
        {CMSDashboardData
          ? dataStructure.map((dataEl) => {
              const selected = dataEl.attr === showChart;
              return (
                <Grid
                  key={`kpi-container-${dataEl.attr}`}
                  item
                  lg={3}
                  sm={6}
                  xs={12}
                  style={{ padding: '1rem' }}
                >
                  <Card
                    elevation={selected ? 0 : 10}
                    style={{
                      height: '10rem',
                      border: selected
                        ? `solid ${theme.palette.primary.main} 3px`
                        : '',
                      backgroundColor: selected
                        ? 'rgba(230, 230, 230, 0.3)'
                        : '',
                      boxSizing: 'border-box',
                      borderRadius: theme.shape.borderRadius,
                    }}
                  >
                    <CardActionArea
                      onClick={() => setShowChart(dataEl.attr)}
                      aria-label={`${dataEl.attr} chart`}
                      style={{
                        cursor: 'pointer',
                        padding: selected ? null : '3px',
                        height: '100%',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="center"
                          alignItems="center"
                          item
                          xs={12}
                        >
                          <Grid style={{ marginTop: '1rem' }} item>
                            {dataEl.icon}
                          </Grid>
                          <Grid item>
                            <Typography
                              style={{
                                color: theme.palette.primary.main,
                                fontWeight: 'bold',
                                textAlign: 'center',
                              }}
                              variant="h5"
                            >
                              {dataEl.formatter(
                                dataEl.attr === 'investorCount'
                                  ? investorCount
                                  : CMSDashboardData[dataEl.attr]
                              )}
                            </Typography>
                          </Grid>
                          <Grid item>
                            <Typography
                              style={{
                                fontWeight: 'bold',
                                color: 'rgba(0,0,0,.6)',
                                minHeight: '40px',
                              }}
                              variant="body2"
                            >
                              {dataEl.label}
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              );
            })
          : null}
      </Grid>
      <Divider
        style={{
          width: '100%',
          marginTop: '2em',
          marginBottom: '2em',
        }}
      />
      <DashboardFees open={'feesAndCarryTotal' === showChart} />
      <DashboardDividends open={'dividendTotal' === showChart} />
      <DashboardInvestments open={'crowdInvestmentTotal' === showChart} />
      <DashboardUsers open={'investorCount' === showChart} />
      {roles.map((role) => role.name).indexOf('ITWrite') > -1 ? (
        <>
          <Divider
            style={{ width: '100%', marginTop: '2em', marginBottom: '2em' }}
          />
          {/* <Typography variant="h6">IT Status : </Typography>
            <List>
              <ListItem key={`it-list-item-webhooks`}>
                <ListItemAvatar>
                  <Avatar>
                    {CMSDashboardData.dwollaWebhookSubscriptionCount === 1 ? (
                      <CheckCircle />
                    ) : (
                      <Warning />
                    )}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={`Dwolla webhook count : ${CMSDashboardData.dwollaWebhookSubscriptionCount}`}
                  secondary="If webhook count exceeds 1, then multiple emails will be fired for Dwolla updates. Temporarily disable emails by setting environmental variable 'PREVENT_DWOLLA_EMAILS'= false"
                />
              </ListItem>
            </List> */}
          <Typography gutterBottom variant="h6">
            IT Utilities :{' '}
          </Typography>
          <Grid container spacing={2}>
            {[
              {
                name: 'Push Okta primaryPhone to DB',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update(
                      'OktaPrimaryPhoneSync',
                      {}
                      // zero id indicates that you are deleting all most recently uploaded entries
                    )
                    .then(
                      (returnObj) => {
                        showNotification(`Success ${returnObj?.data}`);
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Delete all Cancelled Eversign Docs',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .delete('EversignDocument', {
                      deleteAllCancelled: true, // zero id indicates that you are deleting all most recently uploaded entries
                    })
                    .then(() => setLoading(false))
                    .catch(() => setLoading(false));
                },
              },
              {
                name: 'Delete all Eversign Test Docs',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .delete('EversignDocument', {
                      deleteAllTests: true, // zero id indicates that you are deleting all most recently uploaded entries
                    })
                    .then(() => setLoading(false))
                    .catch(() => setLoading(false));
                },
              },
              {
                name: 'Delete all Test Okta Users',
                disabled: true, // originally disabled
                onClick: () => {
                  setLoading(true);
                  console.log('UNDER CONSTRUCTION');
                  setLoading(false);
                },
              },
              {
                // Previously commented out
                name: 'Sync HubSpot Ids with Platform Users',
                disabled: true,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update(
                      'HubSpotContact',
                      { data: { syncHubSpotContactIds: true } }
                      // zero id indicates that you are deleting all most recently uploaded entries
                    )
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully updated ${
                            returnObj &&
                            returnObj.data.successfullyTransferredIds
                          } users. Missing ${
                            returnObj && returnObj.data.missingHubSpotContacts
                          } HubSpot Contacts`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                // Previously commented out
                name: 'Push Platform data to HubSpot Contacts',
                disabled: true,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update(
                      'HubSpotContact',
                      { data: { syncHubSpotContactData: true } }
                      // zero id indicates that you are deleting all most recently uploaded entries
                    )
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully updated ${
                            returnObj &&
                            returnObj.data.successfullyTransferredIds
                          } users. Missing ${
                            returnObj && returnObj.data.missingHubSpotContacts
                          } HubSpot Contacts`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Pull Contact Lead Source from HubSpot',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update(
                      'HubSpotContact',
                      { data: { pullHubSpotContactLeadSource: true } }
                      // zero id indicates that you are deleting all most recently uploaded entries
                    )
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully pulled contact lead sources`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Backfill Investor Lat/Lng',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('InvestorLatitudeLongitudeBackfill', {})
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully backfilled investor latitude/longitude`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                // Previously commented out
                name: 'Push Platform data to HubSpot Deals',
                disabled: true,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .create(
                      'HubSpotDeal',
                      { data: { syncHubSpotDealData: true } }
                      // zero id indicates that you are deleting all most recently uploaded entries
                    )
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully created ${
                            returnObj && returnObj.data.successfullyCreatedCount
                          } deals. Skipped ${
                            returnObj && returnObj.data.alreadyExistingCount
                          } HubSpot Deals that already existed. Errored out on : ${
                            returnObj && returnObj.data.errorCount
                          }`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                // Previously commented out
                name: 'Archive All HubSpot Deals',
                disabled: true,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .delete(
                      'HubSpotDeal',
                      { data: { syncHubSpotDealData: true } }
                      // zero id indicates that you are deleting all most recently uploaded entries
                    )
                    .then(
                      () => {
                        showNotification(
                          'Successfully Archived All HubSpot Deals'
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Sync Okta First and Last Names with Users',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider.update('OktaUserNameSync', {}).then(
                    (returnObj) => {
                      showNotification('Successfully synced Okta user names');
                      setLoading(false);
                    },
                    (e) => {
                      showNotification(e.message);
                      setLoading(false);
                    }
                  );
                },
              },
              {
                name: 'Sync Okta Emails with Users',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider.update('OktaUserEmailSync', {}).then(
                    (returnObj) => {
                      showNotification(
                        'Successfully synced Okta emails with users'
                      );
                      setLoading(false);
                    },
                    (e) => {
                      showNotification(e.message);
                      setLoading(false);
                    }
                  );
                },
              },
              {
                name: 'Sync Okta SSNs with Users',
                disabled: true, // originally disabled
                onClick: () => {
                  setLoading(true);
                  dataProvider.update('OktaUserSSNSync', {}).then(
                    (returnObj) => {
                      showNotification(
                        'Successfully synced Okta SSNs with users'
                      );
                      setLoading(false);
                    },
                    (e) => {
                      showNotification(e.message);
                      setLoading(false);
                    }
                  );
                },
              },
              {
                name: 'Backfill share prices',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('PortfolioSharePrice', {
                      data: { id: 1, updateHistoricalPrices: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          'Successfully backfilled share prices'
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Backfill User Verified Dates',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider.update('SyncUserVerifiedDt', {}).then(
                    (returnObj) => {
                      showNotification(
                        'Successfully synced verifiedDt for users'
                      );
                      setLoading(false);
                    },
                    (e) => {
                      showNotification(e.message);
                      setLoading(false);
                    }
                  );
                },
              },
              {
                name: 'Backfill Transfer Completed Dates',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider.update('SyncTransferCompletedDt', {}).then(
                    (returnObj) => {
                      showNotification(
                        'Successfully synced transfer completed dates'
                      );
                      setLoading(false);
                    },
                    (e) => {
                      showNotification(e.message);
                      setLoading(false);
                    }
                  );
                },
              },
              {
                name: 'Send Monthly MTC Recon. Reports to IT',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('SubAccount', {
                      data: {
                        id: -1,
                        sendMonthlyMTCReconciliationReports: true,
                      },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully sent monthly MTC reconciliation reports`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                // Previously commented out
                name: 'Sync HubSpot Contacts Dwolla Status',
                disabled: true,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('HubSpotContact', {
                      data: { syncHubSpotContactDwollaStatus: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          'Successfully synced HubSpot Contacts Dwolla Status'
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Push Monthly Investor Recap KPIs to HubSpot',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('HubSpotContact', {
                      data: { pushMonthlyInvestorRecapKPIsToHubSpot: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully pushed monthly investor recap KPIs to HubSpot`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Sync User Investment KPIs with HubSpot',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('HubSpotContact', {
                      data: { syncUserInvestmentKPIsWithHubSpot: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully pushed updated user investment KPIs to HubSpot`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Create HubSpot SubAccount Records',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('HubSpotContact', {
                      data: { createAllHubSpotContactSubAccounts: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully created HubSpot subAccounts`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Sync Investments with HubSpot Deals',
                disabled: true, // originally disabled
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('HubSpotContact', {
                      data: { createSyncHubSpotContactDeals: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully initiated sync of investments table with HubSpot deals`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Sync HubSpot Contact Properties',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('HubSpotContact', {
                      data: { syncAllHubSpotContacts: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully initiated HubSpot contact sync`,
                          'success'
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Backfill Prev Year Capital Account',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('UserPortfolioTaxDocument', {
                      data: { syncEndingCapitalAccount: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          'Successfully synced Ending Capital Account'
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Remove duplicate power generation',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('ProductionPeriod', {
                      data: { removeDuplicateEntries: true, id: 0 },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully initiated production period duplicate removal.`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Apply Transformer Loss to Production Periods',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('ProductionPeriod', {
                      data: { applyTransformerLoss: true, id: 0 },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully initiated production period transformer loss application.`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Check for missing Dwolla webhooks',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('DwollaWebhookEvent', {
                      data: { backfillMissingEvents: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          `Successfully initiated check for missing Dwolla webhooks.`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Safely Obliterate Bull Queue',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('BullQueue', {
                      data: { obliterateBullQueues: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification(
                          'Successfully obliterated Bull Queue.'
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Create BR Okta Users',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('BrContacts', {
                      data: { id: 0, backfillOktaUsers: true },
                    })
                    .then(
                      (returnObj) => {
                        showNotification('Successfully backfilled Okta Users.');
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
              {
                name: 'Manually Trigger Auto-Investments',
                disabled: false,
                onClick: () => {
                  setManuallyTriggerAutoInvestmentsDialogOpen(true);
                },
              },
              {
                name: 'Run Auth0 Migration',
                disabled: false,
                onClick: () => {
                  setLoading(true);
                  dataProvider
                    .update('Auth0Migration', {
                      data: { dryRun: false, batchSize: 10 },
                    })
                    .then(
                      (returnObj) => {
                        const result = returnObj?.data;
                        showNotification(
                          `Auth0 Migration completed: Found ${
                            result?.found || 0
                          }, Created ${result?.created || 0}, Mapped ${
                            result?.mapped || 0
                          }, Emailed ${result?.emailed || 0}. Duration: ${
                            result?.duration || 'N/A'
                          }. ${
                            result?.errors?.length > 0
                              ? `Errors: ${result.errors.length}`
                              : 'No errors'
                          }`
                        );
                        setLoading(false);
                      },
                      (e) => {
                        showNotification(e.message);
                        setLoading(false);
                      }
                    );
                },
              },
            ].map((el) => (
              <Grid
                item
                xs={12}
                md={4}
                lg={3}
                xxl={2}
                key={`utility-btn-${el.name.replace(' ', '-')}`}
              >
                <Button
                  color="primary"
                  variant="contained"
                  disabled={loading}
                  fullWidth
                  style={{ background: theme.palette.error.main }}
                  onClick={el.onClick}
                >
                  {loading ? <CircularProgress /> : <b>{el.name}</b>}
                </Button>
              </Grid>
            ))}
            <Dialog open={manuallyTriggerAutoInvestmentsDialogOpen}>
              <DialogTitle>
                <Grid
                  container
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Grid item>Auto Invest Day of Current Month</Grid>
                  <Grid item>
                    <IconButton
                      onClick={() => {
                        setManuallyTriggerAutoInvestmentsDialogOpen(false);
                      }}
                    >
                      <Close />
                    </IconButton>
                  </Grid>
                </Grid>
              </DialogTitle>
              <DialogContent>
                <TextField
                  label="Day of current month"
                  fullWidth
                  value={manuallyTriggerAutoInvestmentsDayOfMonth || ''}
                  onChange={(event) =>
                    setManuallyTriggerAutoInvestmentsDayOfMonth(
                      event.target.value
                    )
                  }
                />
              </DialogContent>
              <DialogActions>
                <Button
                  variant="contained"
                  color="primary"
                  disabled={
                    !manuallyTriggerAutoInvestmentsDayOfMonth ||
                    (manuallyTriggerAutoInvestmentsDayOfMonth &&
                      (manuallyTriggerAutoInvestmentsDayOfMonth > 28 ||
                        manuallyTriggerAutoInvestmentsDayOfMonth < 1))
                  }
                  onClick={() => {
                    setLoading(true);
                    dataProvider
                      .update('AutoInvestSubscription', {
                        data: {
                          id: 1,
                          triggerAutoInvestmentsDayOfMonth: parseInt(
                            manuallyTriggerAutoInvestmentsDayOfMonth,
                            10
                          ),
                        },
                      })
                      .then(
                        (returnObj) => {
                          showNotification(
                            'Successfully triggered auto investment worker job.'
                          );
                          setLoading(false);
                        },
                        (e) => {
                          showNotification(e.message);
                          setLoading(false);
                        }
                      );
                  }}
                >
                  Submit
                </Button>
              </DialogActions>
            </Dialog>
          </Grid>
        </>
      ) : null}
      <Divider
        style={{ width: '100%', marginTop: '2em', marginBottom: '2em' }}
      />
      <Typography variant="h6">Your platform email notifications :</Typography>
      <Typography gutterBottom variant="caption">
        Turn these events on if you want to receive emails for each event
      </Typography>
      <List>...coming soon</List>
      <Divider
        style={{ width: '100%', marginTop: '2em', marginBottom: '2em' }}
      />
      <Typography variant="h6">Your current permission roles are :</Typography>
      <List>{jsx}</List>
      <Typography gutterBottom variant="caption">
        Use the list to the left to view/edit entities in the database. If you
        don't see an entity you need access to, contact <EMAIL> to
        update your permission roles.
      </Typography>
      {/* </CardContent> */}
    </div>
  );
};
