import React from 'react';
import { useParams } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';

import {
  ArrayField,
  Create,
  Datagrid,
  Edit,
  List,
  ReferenceInput,
  ReferenceArrayInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomReferenceField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Project Group';

const ProjectGroupEditForm = () => {
  const { watch } = useFormContext();
  const portfolioId = watch('portfolio.id');

  return (
    <Grid container style={{ width: '100%' }}>
      <Grid item xs={12} md={6}>
        <TextInput source="name" fullWidth />
        <ReferenceInput
          source="portfolio.id"
          reference="PortfolioLite"
          perPage={10_000}
          sort={{ field: 'name', order: 'ASC' }}
        >
          <SelectInput fullWidth label="Portfolio" optionText="name" />
        </ReferenceInput>
        <ReferenceArrayInput
          source="projects"
          reference="Project"
          fullWidth
          filter={{ portfolioId }}
          perPage={10_000}
          sort={{ field: 'name', order: 'ASC' }}
        >
          <SelectArrayInput fullWidth optionText="name" />
        </ReferenceArrayInput>
      </Grid>
    </Grid>
  );
};

export const ProjectGroupEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <ProjectGroupEditForm />
      </SimpleForm>
    </Edit>
  );
};

export const ProjectGroupList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <ArrayField source="projects" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="name" />
          </SingleFieldList>
        </ArrayField>
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
      </Datagrid>
    </List>
  );
};

export const ProjectGroupCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
