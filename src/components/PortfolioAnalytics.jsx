import React, { Component } from 'react';
import { Alert } from '@mui/lab';
import { ReferenceInput, SelectInput, useRecordContext } from 'react-admin';
import { useParams } from 'react-router-dom';

import { Chip, Divider, Grid, Typography } from '@mui/material';
import 'chart.js/auto';

import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-moment';

import numeral from 'numeral';
import moment from 'moment';

import { interpolateColors } from '../utils/global';

const getColorsArray = (numColors) =>
  interpolateColors(numColors, 'rgba(255,0,0,0.5)', 'rgba(255,200,0,0.5)');

class PortfolioAnalytics extends Component {
  constructor(props) {
    super(props);

    const projectionsCreatedAtReference = 1588286005799; // Initial value of 4/30/2020 6:33 PM

    // this.handleProjectionsChange = this.handleProjectionsChange.bind(this);
    this.initializeProjections = this.initializeProjections.bind(this);
    this.addProjection = this.addProjection.bind(this);
    this.removeProjection = this.removeProjection.bind(this);
    this.state = {
      projections: [],
      projectionVersions: [projectionsCreatedAtReference],
    };
    this.initializeProjections();
  }

  removeProjection(date) {
    const { projectionVersions, projections } = this.state;
    const indexToRemove = projectionVersions.indexOf(date);
    if (date !== -1) {
      projectionVersions.splice(indexToRemove, 1);
      projections.splice(indexToRemove, 1);
      this.setState({
        projectionVersions,
        projections,
      });
    }
  }

  addProjection(event) {
    const { projections, projectionVersions } = this.state;
    if (event?.target?.dataset?.value) {
      if (projectionVersions.includes(event.target.dataset.value)) {
        return;
      }

      const {
        dataProvider,
        record: { id },
      } = this.props;

      dataProvider
        .getList('MonthlyPortfolioFinancialProjection', {
          filter: {
            createdAtReference: {
              id: parseInt(event.target.dataset.value, 10),
            },
            portfolio: { id },
          },
          sort: {
            field: 'effectiveDt',
            order: 'ASC',
          },
          pagination: {
            page: 1,
            perPage: 10000,
          },
        })
        .then((res) => {
          const now = moment();
          const filteredProjections = res.data.filter(
            (proj) => moment(proj.effectiveDt) <= now
          );
          this.setState({
            projectionVersions: [...projectionVersions, event.target.value],
            projections: [...projections, filteredProjections],
          });
        });
    }
  }

  initializeProjections() {
    const { dataProvider, record } = this.props;
    const { projectionVersions } = this.state;
    dataProvider
      .getList('MonthlyPortfolioFinancialProjection', {
        filter: {
          // effectiveDt: []
          suppressFutureProjections: true,
          createdAtReference: { id: projectionVersions[0] },
          portfolio: { id: record.id },
        },
        sort: {
          field: 'effectiveDt',
          order: 'ASC',
        },
        pagination: {
          page: 1,
          perPage: 10000,
        },
      })
      .then((res) => {
        const now = moment().add(1, 'month');
        const filteredProjections = res.data.filter(
          (proj) => moment(proj.effectiveDt) <= now
        );
        this.setState({ projections: [filteredProjections] });
      });
  }

  render() {
    const { record } = this.props;
    const { projections, projectionVersions } = this.state;
    if (
      !record.monthlyPortfolioFinancialActuals ||
      record.monthlyPortfolioFinancialActuals.length === 0
    ) {
      return (
        <Alert severity="info">
          <Typography gutterBottom>
            Once the first set of projections and actuals are uploaded we will
            show analytics.
          </Typography>
        </Alert>
      );
    }
    const data = !record.portfolioSharePrices.length
      ? []
      : record.portfolioSharePrices
          .map((actual) => ({
            t: new Date(actual.date),
            y: actual.sharePrice,
          }))
          .concat([
            {
              t: new Date(),
              y:
                record.portfolioSharePrices[
                  record.portfolioSharePrices.length - 1
                ].sharePrice,
            },
          ]);
    const actualSharePriceData =
      record.investments &&
      record.investments.map((inv) => ({
        t: new Date(inv.startDt),
        y: inv.calculatedSharePrice,
      }));
    const cafdActualData = {
      data: [],
      borderColor: 'rgba(21, 48, 76, 1)',
      pointRadius: 4,
      fill: false,
      label: 'Actuals',
    };
    const productionActualData = {
      data: [],
      borderColor: 'rgba(21, 48, 76, 1)',
      pointRadius: 4,
      fill: false,
      label: 'Actuals',
    };
    const blankElement = {
      t: moment(),
      y: null,
    };

    if (record.monthlyPortfolioFinancialActuals.length > 0) {
      record.monthlyPortfolioFinancialActuals.forEach((actual) => {
        const startOfMonth = moment(actual.effectiveDt).startOf('month');
        cafdActualData.data.push({
          t: startOfMonth,
          y: actual.grossCafd || 0,
        });
        productionActualData.data.push({
          t: moment(startOfMonth),
          y: actual.production || 0,
        });
      });

      cafdActualData.data.push(blankElement);
      productionActualData.data.push(blankElement);
    }
    const datasetColors = getColorsArray(projectionVersions.length);

    const cafdProjectionDataSets = [];
    const cafdProjectionDifferentialDataSets = [];
    const productionProjectionDataSets = [];
    const productionProjectionDifferentialDataSets = [];

    if (projections.length > 0) {
      projections.forEach((version) => {
        const datasetColor = datasetColors.shift();
        const cafdProj = {
          borderColor: datasetColor,
          borderDash: [5, 8],
          data: [],
          fill: 'false',
          pointRadius: 4,
          label:
            version[0] &&
            moment(version[0].createdAtReference.date).format('MM/DD/YYYY LT'),
        };
        const prodProj = {
          borderColor: datasetColor,
          borderDash: [5, 8],
          data: [],
          fill: 'false',
          pointRadius: 4,
          label:
            version[0] &&
            moment(version[0].createdAtReference.date).format('MM/DD/YYYY LT'),
        };
        const cafdProjDiff = {
          data: [],
          borderColor: datasetColor,
          borderWidth: 1,
          type: 'bar',
          label: `Projections Differential ${
            version[0] &&
            moment(version[0].createdAtReference.date).format('MM/DD/YYYY LT')
          }`,
        };
        const prodProjDiff = {
          data: [],
          borderColor: datasetColor,
          borderWidth: 1,
          type: 'bar',
          label: `Projections Differential ${
            version[0] &&
            moment(version[0].createdAtReference.date).format('MM/DD/YYYY LT')
          }`,
        };

        version.forEach((projection) => {
          const startOfMonth = moment(projection.effectiveDt).startOf('month');
          const startOfMonthProd = moment(startOfMonth);
          cafdProj.data.push({
            t: startOfMonth,
            y: projection.grossCafd,
          });
          prodProj.data.push({
            t: startOfMonthProd,
            y: projection.production,
          });
          const actualCafd = cafdActualData.data.filter((actual) =>
            actual.t.isSame(startOfMonth)
          )[0];
          const actualCafdValue = actualCafd && actualCafd.y;
          cafdProjDiff.data.push({
            t: startOfMonth,
            y: Math.abs(actualCafdValue - projection.grossCafd),
          });
          const actualProduction = productionActualData.data.filter((actual) =>
            actual.t.isSame(startOfMonthProd)
          )[0];
          const actualProductionValue = actualProduction && actualProduction.y;
          prodProjDiff.data.push({
            t: startOfMonthProd,
            y: Math.abs(actualProductionValue - projection.production),
          });
        });
        cafdProj.data.push(blankElement);
        prodProj.data.push(blankElement);
        cafdProjDiff.data.push(blankElement);
        prodProjDiff.data.push(blankElement);

        cafdProjectionDataSets.push(cafdProj);
        productionProjectionDataSets.push(prodProj);
        cafdProjectionDifferentialDataSets.push(cafdProjDiff);
        productionProjectionDifferentialDataSets.push(prodProjDiff);
      });
    }
    return (
      <>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12}>
            <Typography variant="h6">Share Price Over Time : </Typography>
            <Grid item>
              <Line
                height={300}
                data={{
                  datasets: [
                    {
                      data,
                      backgroundColor: 'rgba(21, 48, 76, 0.5)',
                      fill: true,
                      pointRadius: 0,
                      stepped: true,
                    },
                    {
                      actualSharePriceData,
                      backgroundColor: 'rgba(21, 48, 76, 0.5)',
                      fill: true,
                      pointRadius: 0,
                      type: 'scatter',
                      borderColor: 'darkOrange',
                      backgroundColor: 'orange',
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  parsing: {
                    xAxisKey: 't',
                    yAxisKey: 'y', // default value
                  },
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MM-DD-YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      ticks: {
                        callback: (value) => numeral(value).format('$0,0.00'),
                      },
                    },
                  },
                }}
              />
            </Grid>
          </Grid>
        </Grid>
        <Divider
          style={{
            width: '100%',
            marginTop: '2em',
            marginBottom: '2em',
          }}
        />
        <Grid container style={{ width: '100%' }}>
          <Grid item style={{ display: 'flex', alignItems: 'center' }}>
            <Typography>Import Projections : </Typography>
          </Grid>
          <Grid
            item
            style={{
              display: 'flex',
              alignItems: 'center',
              marginLeft: '1rem',
            }}
          >
            <ReferenceInput
              source="createdAtReference.id"
              reference="UniqueMonthlyPortfolioFinancialProjectionCreatedAtDates"
            >
              <SelectInput
                label="Versions"
                optionText={(value) =>
                  moment(value.date).format('MM/DD/YYYY LT')
                }
                onClick={this.addProjection}
                filterToQuery={() => {
                  return { portfolioId: record.id };
                }}
              />
            </ReferenceInput>
          </Grid>
          <Grid item>
            {projectionVersions.map((date) => (
              <Chip
                key={`projection-version-chip-${moment(date).format(
                  'MM/DD/YYYY'
                )}`}
                label={moment(date).format('MM/DD/YYYY LT')}
                onDelete={() => this.removeProjection(date)}
                color="primary"
                style={{ marginTop: '1rem', marginLeft: '1rem' }}
              />
            ))}
          </Grid>
        </Grid>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={5}>
            <Typography variant="h6">CAFD : </Typography>
            <Grid item>
              <Line
                responsive="true"
                height={300}
                data={{
                  datasets: [
                    cafdActualData,
                    ...cafdProjectionDataSets,
                    ...cafdProjectionDifferentialDataSets,
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  parsing: {
                    xAxisKey: 't',
                    yAxisKey: 'y', // default value
                  },
                  plugins: {
                    legend: {
                      labels: {
                        filter: (item) =>
                          item.text &&
                          !item.text.startsWith('Projections Differential'),
                      },
                    },
                    tooltip: {
                      mode: 'x',
                      intersect: false,
                      position: 'average',
                      filter: (tooltipItem) =>
                        !tooltipItem.chart.data.datasets[
                          tooltipItem.datasetIndex
                        ].label.startsWith('Projections Differential'),
                      callbacks: {
                        label: (tooltipItem) => {
                          return (
                            tooltipItem.dataset &&
                            `${tooltipItem.dataset.label}: ${numeral(
                              tooltipItem.formattedValue
                            ).format('$0,0')}`
                          );
                        },
                        afterLabel: (tooltipItem) => {
                          const version =
                            tooltipItem.dataset && tooltipItem.dataset.label;
                          if (version && version !== 'Actuals') {
                            const difference = tooltipItem.chart.data.datasets.filter(
                              (dataset) =>
                                dataset.label ===
                                `Projections Differential ${version}`
                            )[0].data[tooltipItem.dataIndex].y;
                            return `Difference: ${numeral(difference).format(
                              '$0,0'
                            )}`;
                          }
                          return '';
                        },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MMM YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      ticks: {
                        callback: (value) => numeral(value).format('$0,0'),
                      },
                    },
                  },
                }}
              />
            </Grid>
          </Grid>
          <Grid item>
            <Divider
              orientation="vertical"
              style={{
                // width: '100%',
                marginLeft: '2em',
                marginRight: '2em',
              }}
            />
          </Grid>
          <Grid item xs={5}>
            <Typography variant="h6">Production : </Typography>
            <Grid item>
              <Line
                height={300}
                data={{
                  datasets: [
                    productionActualData,
                    ...productionProjectionDataSets,
                    ...productionProjectionDifferentialDataSets,
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  parsing: {
                    xAxisKey: 't',
                    yAxisKey: 'y', // default value
                  },
                  plugins: {
                    legend: {
                      labels: {
                        filter: (item) =>
                          item.text &&
                          !item.text.startsWith('Projections Differential'),
                      },
                    },
                    tooltip: {
                      mode: 'x',
                      intersect: false,
                      position: 'average',
                      filter: (tooltipItem) =>
                        !tooltipItem.chart.data.datasets[
                          tooltipItem.datasetIndex
                        ].label.startsWith('Projections Differential'),
                      callbacks: {
                        label: (tooltipItem) => {
                          return (
                            tooltipItem.dataset &&
                            `${tooltipItem.dataset.label}: ${numeral(
                              tooltipItem.formattedValue
                            ).format('0,0')} MWh`
                          );
                        },
                        afterLabel: (tooltipItem, labelData) => {
                          const version =
                            tooltipItem.dataset && tooltipItem.dataset.label;
                          if (version && version !== 'Actuals') {
                            const difference = tooltipItem.chart.data.datasets.filter(
                              (dataset) =>
                                dataset.label ===
                                `Projections Differential ${version}`
                            )[0].data[tooltipItem.dataIndex].y;
                            return `Difference: ${numeral(difference).format(
                              '0,0'
                            )} MWh`;
                          }
                          return null;
                        },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MMM YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0,0')} MWh`,
                      },
                    },
                  },
                }}
              />
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  }
}

function withRecord(WrappedComponent) {
  return (props) => <WrappedComponent {...props} record={useRecordContext()} />;
}

export default withRecord(PortfolioAnalytics);
