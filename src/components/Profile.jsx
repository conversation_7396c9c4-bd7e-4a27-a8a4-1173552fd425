import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Avatar,
  Button,
  Chip,
  CircularProgress,
  Grid,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { ArrowRight, Info, VerifiedUser } from '@mui/icons-material';
import { Alert } from '@mui/lab';

import { useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment-timezone';

import { withStyles } from '@mui/styles';

import theme from '../theme';
import { constants } from '../utils/global';
import { Scatter } from 'react-chartjs-2';

const styles = (theme) => ({});

const renderPermissionsSection = (roles) => {
  const jsx = [];
  roles &&
    roles.forEach((role) => {
      jsx.push(
        <ListItem key={`role-list-item-${role.name}`}>
          <ListItemAvatar>
            <Avatar>
              <VerifiedUser />
            </Avatar>
          </ListItemAvatar>
          <ListItemText primary={role.name} secondary={role.description} />
        </ListItem>
      );
    });
  return jsx;
};
export default withStyles(styles)((args) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  const fetchData = () => {
    setLoading(true);
    console.log('HIT THE FETCH');
    dataProvider.getOne('MyProfile', {}).then(
      (res) => {
        console.log('HIT THE RESP', res);
        setData(res.data);
        setLoading(false);
      },
      (e) => {
        console.error('Error retrieving data', e);
        notify('Error collecting data', { type: 'error' });
        setError(e);
        setLoading(false);
      }
    );
  };

  if (!data && !error && !loading) {
    fetchData();
  }

  if (!data && !error && !loading) {
    return null;
  }
  if (loading) {
    return (
      <Grid
        style={{
          position: 'fixed',
          top: '50%',
          width: '100%',
          textAlign: 'center',
        }}
      >
        <CircularProgress />
      </Grid>
    );
  }

  if (error) {
    return null;
  }

  return (
    <Grid container spacing={2} style={{ padding: '1rem' }}>
      <Grid item xs={12}>
        <Typography variant="h4" gutterBottom>
          My Profile
        </Typography>

        {/* <Alert>
          This page is coming soon. Included will be:
          <ul>
            <li>list of your permissions</li>
            <li>
              <li>Login history</li>
              <li>
                Equity information about your energea stock as per the employee
                equity ownership program
              </li>
              <li>List of software subscriptions</li>
              <li>List of company equipment under your ownership</li>
              <li>Access to employment contracts</li>
            </li>
          </ul>
        </Alert> */}
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          Profile
        </Typography>
        <Typography variant="body1">
          Name: {data?.firstName} {data?.lastName}
        </Typography>
        <Typography variant="body1">Email: {data?.email}</Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          Documents
        </Typography>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Download</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.user?.documents?.map((document) => (
              <TableRow key={document.id}>
                <TableCell>{document.title}</TableCell>
                <TableCell>
                  <Button
                    variant="contained"
                    color="primary"
                    endIcon={<CloudDownload />}
                    style={{ textTransform: 'none' }}
                    onClick={() => {
                      window.location.assign(document.downloadUrl);
                    }}
                  >
                    Download
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          Permissions
        </Typography>
        {data?.roles && renderPermissionsSection(data?.roles)}
      </Grid>
    </Grid>
  );
});
