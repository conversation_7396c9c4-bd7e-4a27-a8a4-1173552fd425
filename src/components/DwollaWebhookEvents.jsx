import React from 'react';

import {
  <PERSON><PERSON>,
  Datagrid,
  DateField,
  DateInput,
  Filter,
  FunctionField,
  List,
  Pagination,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextField,
  TextInput,
  useNotify,
  useRedirect,
  useRefresh,
  useDataProvider,
} from 'react-admin';

import { Button } from '@mui/material';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Dwolla Webhook Event';

export const DwollaWebhookEventShow = () => (
  <Show>
    <SimpleShowLayout>
      <TextField source="id" />
      <TextField source="status" />
      <TextField source="amount.value" />
      <DateField source="created" />
      <TextField source="correlationId" />
      <TextField source="individualAchId" />
    </SimpleShowLayout>
  </Show>
);

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);

const CustomFilter = (props) => (
  <Filter {...props}>
    <CustomNumberInput
      style={{ minWidth: '220px' }}
      label="Min Amount"
      source="startAmount"
    />
    <CustomNumberInput
      style={{ minWidth: '220px' }}
      label="Max Amount"
      source="endAmount"
    />
    <DateInput
      style={{ minWidth: '220px' }}
      label="Start Date Lower Bound"
      source="startDtLowerBound"
    />
    <DateInput
      style={{ minWidth: '220px' }}
      label="Start Date Upper Bound"
      source="startDtUpperBound"
    />
  </Filter>
);

export const DwollaWebhookEventList = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const handleRetryWebhook = (dwollaWebhookEventId) => {
    dataProvider
      .update('DwollaWebhookEvent', {
        data: {
          dwollaWebhookEventId,
          retryWebhookEvent: true,
        },
      })
      .then(
        () => {
          notify('Webhook event successfully retried.');
          refresh();
        },
        () =>
          notify(
            `Error retrying webhook event. Please try again later.`,
            'error'
          )
      );
  };
  return (
    <List
      perPage={25}
      pagination={<CustomPagination />}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<CustomFilter />}
    >
      <Datagrid rowClick="show">
        <TextField source="id" sortable={false} />
        <TextField source="resourceId" sortable={false} />
        <TextField source="topic" sortable={false} />
        <TextField source="dbDwollaWebhookEvent.label" sortable={false} />
        <DateField source="created" sortable={false} />
        <FunctionField
          label="Agreement"
          render={(record) => {
            return (
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  handleRetryWebhook(record.id);
                }}
                disabled={record.dbDwollaWebhookEvent}
              >
                Retry
              </Button>
            );
          }}
        />
        {/* <TextField source="status" sortable={false} />
      <NumberField
        source="amount.value"
        options={{ style: 'currency', currency: 'USD' }}
        sortable={false}
      />
      <LinkField
          reference="Investment"
          linkSource="investment.id"
          labelSource="investment.label"
          label="Investment"
        />
      <DateField source="created" sortable={false} />
      <TextField source="correlationId" sortable={false} />
      <TextField source="individualAchId" sortable={false} /> */}
      </Datagrid>
    </List>
  );
};

export const DwollaTransferCreate = () => {
  const notify = useNotify();
  const refresh = useRefresh();
  const redirect = useRedirect();
  const onSuccess = () => {
    notify(`Changes saved`, { type: 'success' });
    redirect('/DwollaTransfer');
    refresh();
  };
  return (
    <Create
      title={`Create ${entityName}`}
      mutationOptions={{ onSuccess: onSuccess }}
      undoable={false}
    >
      <SimpleForm>
        <TextInput source="fromAccountId" />
        <TextInput source="toAccountId" />
        <CustomNumberInput source="amount" />
        <TextInput source="correlationId" />
      </SimpleForm>
    </Create>
  );
};
