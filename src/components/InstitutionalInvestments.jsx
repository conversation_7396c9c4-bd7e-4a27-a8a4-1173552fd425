import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON>oleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  FormDataConsumer,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';
import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Institutional Investment';

export const InstitutionalInvestmentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={8}>
            <DateInput source="date" fullWidth />
            <ReferenceInput
              source="institutionalInvestor.id"
              reference="InstitutionalInvestor"
            >
              <SelectInput
                label="Institutional Investor"
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="portfolio.id" reference="PortfolioLite">
              <SelectInput
                label="Portfolio"
                required
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
            <CustomNumberInput label="Amount (USD)" source="amount" fullWidth />
            <CustomNumberInput
              label="Amount (BRL)"
              source="amountBRL"
              fullWidth
            />
            <BooleanInput source="isRetailProductDebt" fullWidth />
            <CustomNumberInput
              disabled
              label="USD To BRL Exchange Rate"
              source="usdToBRLExchangeRate"
              fullWidth
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const InstitutionalInvestmentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          label="Institutional Investor"
          linkSource="institutionalInvestor.id"
          labelSource="institutionalInvestor.name"
          reference="InstitutionalInvestor"
        />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <NumberField
          label="Amount (USD)"
          source="amount"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Amount (BRL)"
          source="amountBRL"
          options={{ style: 'currency', currency: 'BRL' }}
        />
        <BooleanField source="isRetailProductDebt" />
        <DateField source="date" />
      </Datagrid>
    </List>
  );
};

export const InstitutionalInvestmentCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={8}>
          <DateInput required source="date" fullWidth />
          <ReferenceInput
            source="institutionalInvestor.id"
            reference="InstitutionalInvestor"
          >
            <SelectInput
              required
              label="Institutional Investor"
              fullWidth
              optionText="name"
            />
          </ReferenceInput>
          <BooleanInput source="isRetailProductDebt" />
          <SelectInput
            source="currency"
            choices={[
              { id: 'USD', name: 'USD' },
              { id: 'BRL', name: 'BRL' },
            ]}
            fullWidth
          />
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (formData.currency) {
                return (
                  <CustomNumberInput
                    label={`Amount (${formData.currency})`}
                    source={`amount${formData.currency === 'USD' ? '' : 'BRL'}`}
                    fullWidth
                  />
                );
              }
            }}
          </FormDataConsumer>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
