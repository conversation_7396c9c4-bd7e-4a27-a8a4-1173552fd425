import React, { useState } from 'react';
import { CircularProgress, Grid, Typography } from '@mui/material';
import { Alert } from '@mui/lab';
import { useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';
import 'chartjs-adapter-moment';
import { Bar, Line } from 'react-chartjs-2';

import { interpolateColors } from '../utils/global';

export default (props) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(null);
  const [error, setError] = useState(null);

  const notify = useNotify();
  const dataProvider = useDataProvider();

  const getBorderColorsArray = (numColors) => {
    return interpolateColors(numColors, null, null, 1);
  };

  const getColorsArray = (numColors) => {
    return interpolateColors(numColors, null, null, 0.8);
  };

  const fetchData = () => {
    dataProvider.getOne('CreditMgmtSalesOverviewChartByStateData', {}).then(
      (res) => {
        setLoading(false);
        setError(null);
        setData(res.data.dailyBrSaleVolumeByState);
      },
      (err) => {
        console.error(err);
        setError(err);
        setLoading(false);
        notify('Failed to fetch list of projects', {
          type: 'error',
        });
      }
    );
  };

  if (!loading && !data && !error) {
    fetchData();
  }

  if (error) {
    return <Alert severity="error">Error loading data</Alert>;
  }

  if (!data || loading) {
    return (
      <Grid container style={{ width: '100%' }} justifyContent="center">
        <CircularProgress />
      </Grid>
    );
  }

  const stateNames = [];
  data.forEach((date) => {
    date.states.forEach((state) => {
      if (!stateNames.includes(state.name)) {
        stateNames.push(state.name);
      }
    });
  });
  const datasets = [];

  let borderColorsArray = getBorderColorsArray(stateNames.length);
  let colorsArray = getColorsArray(stateNames.length);

  stateNames.forEach((state) => {
    const dataValues = [];
    data.forEach((date) => {
      const stateData = date.states.find((s) => s.name === state);
      const previousValue =
        dataValues.length > 0 ? dataValues[dataValues.length - 1].y : 0;
      dataValues.push({
        x: date.date,
        y:
          previousValue + (stateData ? stateData.soldConsumptionKWh / 1000 : 0),
      });
    });
    datasets.push({
      label: state,
      data: dataValues,
      pointRadius: 0,
      fill: true,
      borderColor: borderColorsArray.shift(),
      backgroundColor: colorsArray.shift(),
    });
  });

  return (
    <Line
      height={400}
      data={{
        datasets,
      }}
      options={{
        maintainAspectRatio: false,
        borderRadius: 4,
        plugins: {
          // legend: { display: false },
          tooltip: {
            mode: 'index',
            intersect: false,
            position: 'nearest',
            callbacks: {
              label: (tooltipItem) => {
                if (tooltipItem?.raw?.y === 0) {
                  return null;
                }
                return `${tooltipItem.dataset.label}: ${numeral(
                  tooltipItem.formattedValue
                ).format('0,0[.][00]')} MWh`;
              },
            },
          },
        },
        scales: {
          x: {
            stacked: true,
            type: 'time',
            time: {
              tooltipFormat: 'YYYY-MM-DD',
              unit: 'month',
            },
          },
          y: {
            stacked: true,
            title: {
              display: true,
              text: 'Avg Monthly Cons. Signed (MWh)',
            },
            beginAtZero: true,
          },
        },
      }}
    />
  );
};
