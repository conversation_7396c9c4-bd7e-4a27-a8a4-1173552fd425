import React, { useState } from 'react';
import {
  BooleanInput,
  Datagrid,
  Filter,
  FunctionField,
  List,
  ReferenceInput,
  SelectInput,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Avatar, Box, Dialog, DialogContent, IconButton } from '@mui/material';
import { Close } from '@mui/icons-material';
import { Image, Transformation } from 'cloudinary-react';
import Config from '../config/config';
import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'Project Media';

// Filter component
const ProjectMediaFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      source="portfolio.id"
      reference="Portfolio"
      alwaysOn
      allowEmpty
    >
      <SelectInput optionText="name" label="Portfolio" />
    </ReferenceInput>
    <BooleanInput source="isPublic" label="Is Public" alwaysOn />
  </Filter>
);

// Image zoom dialog
const ImageZoomDialog = ({ open, onClose, imageUrl, title }) => {
  const getPublicId = (url) => {
    if (!url) return null;
    if (!url.includes('cloudinary.com')) return url;
    const match = url.match(/\/v\d+\/(.+?)(\.[^.]+)?$/);
    return match ? match[1] : url;
  };

  const publicId = getPublicId(imageUrl);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md">
      <DialogContent sx={{ position: 'relative', padding: 0 }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 1)',
            },
            zIndex: 1,
          }}
        >
          <Close />
        </IconButton>
        {publicId ? (
          <Image
            cloud_name={Config.cloud_name}
            publicId={publicId}
            style={{ width: '100%', height: 'auto', display: 'block' }}
          >
            <Transformation
              width="248"
              height="248"
              crop="fill"
              gravity="center"
            />
          </Image>
        ) : (
          <Box
            sx={{
              width: 248,
              height: 248,
              bgcolor: '#e0e0e0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            No Image
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export const ProjectMediaList = () => {
  const { permissions } = usePermissions();
  const [zoomDialogOpen, setZoomDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const handleImageClick = (event, imageUrl, projectName) => {
    event.stopPropagation(); // Prevent row click navigation
    setSelectedImage({ url: imageUrl, title: projectName });
    setZoomDialogOpen(true);
  };

  const handleCloseZoom = () => {
    setZoomDialogOpen(false);
    setSelectedImage(null);
  };

  return (
    <>
      <List
        title={entityName}
        perPage={25}
        resource="ProjectMedia"
        filters={<ProjectMediaFilter />}
        filterDefaultValues={{ isPublic: true }}
      >
        <Datagrid
          rowClick={
            getEditable('Project', permissions)
              ? (id) => `/Project/${id}/media`
              : (id) => `/Project/${id}/show`
          }
        >
          <TextField source="id" />
          <TextField source="name" label="Project Name" />
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />

          <FunctionField
            label="Avatar Image"
            sortable={false}
            render={(record) => {
              if (!record.avatarImageUrl) {
                return (
                  <Avatar
                    sx={{
                      width: 62,
                      height: 62,
                      bgcolor: '#e0e0e0',
                      color: '#999',
                    }}
                  >
                    ?
                  </Avatar>
                );
              }

              // Extract Cloudinary public ID from URL if it's a full URL
              const getPublicId = (url) => {
                if (!url) return null;
                // If it's already a public ID, return it
                if (!url.includes('cloudinary.com')) return url;
                // Extract public ID from Cloudinary URL
                const match = url.match(/\/v\d+\/(.+?)(\.[^.]+)?$/);
                return match ? match[1] : url;
              };

              const publicId = getPublicId(record.avatarImageUrl);

              return (
                <Box
                  onClick={(event) =>
                    handleImageClick(event, record.avatarImageUrl, record.name)
                  }
                  sx={{
                    width: 62,
                    height: 62,
                    borderRadius: '50%',
                    overflow: 'hidden',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    '&:hover': {
                      opacity: 0.8,
                    },
                  }}
                >
                  <Image
                    cloud_name={Config.cloud_name}
                    publicId={publicId}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  >
                    <Transformation
                      width="124"
                      height="124"
                      crop="fill"
                      gravity="center"
                    />
                  </Image>
                </Box>
              );
            }}
          />
        </Datagrid>
      </List>

      <ImageZoomDialog
        open={zoomDialogOpen}
        onClose={handleCloseZoom}
        imageUrl={selectedImage?.url}
        title={selectedImage?.title}
      />
    </>
  );
};
