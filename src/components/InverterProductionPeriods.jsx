import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON>reate,
  Datagrid,
  DateField,
  Edit,
  ReferenceInput,
  List,
  NumberField,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
  Filter,
} from 'react-admin';

import { Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import moment from 'moment';
import { InverterProductionPeriodsUpload } from './InverterProductionPeriodsUpload';

const entityName = 'Inverter Snapshot';

export const InverterProductionPeriodEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput source="inverter.id" reference="Inverter">
              <SelectInput
                label="Inverter"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <TextInput
              source="periodStartDt"
              required
              fullWidth
              helperText="Ex: 2021-11-08 18:14:00"
            />
            <TextInput
              source="timeUnit"
              defaultValue="HOUR"
              required
              fullWidth
            />
            <TextInput source="unit" required fullWidth />
            <CustomNumberInput source="production" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="project.id"
      reference="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Inverter"
      source="inverter.id"
      reference="Inverter"
      perPage={10000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <SelectInput label="Inverter" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const InverterProductionPeriodList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<CustomFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          label="Inverter"
          linkSource="inverter.id"
          labelSource="inverter.name"
          reference="Inverter"
        />
        <TextField source="periodStartDt" />
        <NumberField source="production" />
        <TextField source="timeUnit" />
        <TextField source="unit" />
        <DateField source="createdAt" showTime={true} />
        <DateField source="updatedAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

const attrs = [
  {
    name: 'periodStartDt',
    label: 'Period Start Dt (Snapshot timestamp)',
    align: 'center',
    dataFormat: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    name: 'production',
    label: 'Production (power)',
    align: 'center',
  },
  {
    name: 'timeUnit',
    label: "Time Unit ('5_MINUTES')",
    align: 'center',
  },
  {
    name: 'unit',
    label: "Unit ('W')",
    align: 'center',
  },
  {
    name: 'inverterId',
    label: 'Inverter Id',
    align: 'center',
  },
];

export const InverterProductionPeriodCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <Grid container>
      <Grid item>
        <Typography>Upload from CSV:</Typography>
        <InverterProductionPeriodsUpload attrs={attrs} />
      </Grid>
      <Grid item>
        <Typography>Create Manually:</Typography>
        <SimpleForm>
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <ReferenceInput source="inverter.id" reference="Inverter">
                <SelectInput
                  label="Inverter"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput
                source="periodStartDt"
                required
                fullWidth
                helperText="Ex: 2021-11-08 18:14:00"
              />
              <CustomNumberInput source="production" fullWidth />
              <TextInput
                source="timeUnit"
                defaultValue="HOUR"
                required
                fullWidth
              />
              <TextInput source="unit" required fullWidth />
            </Grid>
          </Grid>
        </SimpleForm>
      </Grid>
    </Grid>
  </Create>
);
