import React, { Component } from 'react';
import { Alert } from '@mui/lab';

import {
  useDataProvider,
  useNotify,
  useRecordContext,
  useRefresh,
} from 'react-admin';

import { Button, Chip, Divider, Grid, Typography } from '@mui/material';
import 'chart.js/auto';

import { Line } from 'react-chartjs-2';

import numeral from 'numeral';
import moment from 'moment';

import { interpolateColors } from '../utils/global';

export default () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const record = useRecordContext();

  const syncHubSpotProduct = () => {
    dataProvider
      .update('Portfolio', {
        data: {
          id: record.id,
          syncHubSpotProduct: true,
        },
      })
      .then(
        () => {
          notify('HubSpot product sucessfully updated.');
          refresh();
        },
        () =>
          notify(
            `Error syncing HubSpot product. Please try again later.`,
            'error'
          )
      );
  };
  return (
    <Grid>
      {record.hubSpotProductId ? (
        <Typography>
          <b>HubSpot Product ID : </b>
          {record.hubSpotProductId}
        </Typography>
      ) : (
        <Alert severity="warning">
          No HubSpot Product connected. Click the button below to create and
          sync a product to this Portfolio
        </Alert>
      )}
      <Button variant="contained" onClick={syncHubSpotProduct}>
        Sync HubSpot Product
      </Button>
    </Grid>
  );
};
