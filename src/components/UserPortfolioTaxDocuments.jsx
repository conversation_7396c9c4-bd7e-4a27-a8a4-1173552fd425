import React, { useState } from 'react';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  useRefresh,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';

import { Button, CircularProgress, Grid } from '@mui/material';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';

// import { uploadObject } from '../utils/aws';
import { getEditable } from '../utils/applyRoleAuth';

import { UserPortfolioTaxDocumentsUpload } from './UserPortfolioTaxDocumentsUpload';
import { CustomNumberInput, LinkField } from './CustomFields';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'User Portfolio Tax Document';

// const useStyles = makeStyles({
//   toolbar: {
//     display: 'flex',
//     justifyContent: 'space-between',
//   },
// });

export const UserPortfolioTaxDocumentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="id" disabled fullWidth />
            <ReferenceInput
              perPage={10000}
              source="user.id"
              sortable={false}
              reference="UserLite"
            >
              <AutocompleteInput
                label="User"
                required
                disabled
                fullWidth
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            <ReferenceInput source="subAccount.id" reference="SubAccountLite">
              <SelectInput
                label="SubAccount"
                required
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="portfolio.id" reference="PortfolioLite">
              <SelectInput
                label="Portfolio"
                required
                disabled
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
            <TextInput source="awsObjectKey" disabled fullWidth />
            <TextInput source="year" disabled fullWidth />
            <TextInput source="documentType" fullWidth />
            <CustomNumberInput source="endingCapitalAccount" fullWidth />
            <DateInput source="updatedAt" disabled fullWidth />
            <DateInput source="createdAt" disabled fullWidth />
          </Grid>
        </Grid>
        <FunctionField
          label="Statement URL"
          render={(record) => {
            return (
              <Button
                variant="contained"
                startIcon={<CloudDownloadIcon />}
                style={{ textTransform: 'none' }}
                onClick={() => window.location.assign(record.awsObjectUrl)}
              >
                Download PDF
              </Button>
            );
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

const UserPortfolioTaxDocumentFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <CustomNumberInput label="Year" source="year" step={1} />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
  </Filter>
);

const UserPortfolioTaxDocumentPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100]} />
);

export const UserPortfolioTaxDocumentList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      filters={<UserPortfolioTaxDocumentFilter />}
      sort={{ field: 'id', order: 'DESC' }}
      perPage={25}
      pagination={<UserPortfolioTaxDocumentPagination />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          reference="SubAccount"
          linkSource="subAccount.id"
          labelSource="subAccount.name"
          label="SubAccount"
        />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <TextField source="year" />
        <TextField source="documentType" />
        <NumberField source="endingCapitalAccount" />
        <FunctionField
          label="Statement URL"
          render={(record) => {
            return (
              <Button
                variant="contained"
                startIcon={<CloudDownloadIcon />}
                style={{ textTransform: 'none' }}
                onClick={(event) => {
                  window.location.assign(record.awsObjectUrl);
                  event.stopPropagation();
                }}
              >
                Download PDF
              </Button>
            );
          }}
        />
        <TextField source="awsObjectKey" sortable={false} />
        <DateField source="updatedAt" showTime />
        <DateField source="createdAt" showTime />
      </Datagrid>
    </List>
  );
};

// const getFileFromBlobUrl = async (url) => {
//   let file = await fetch(url)
//     .then(r => r.blob())
//     .then(blob => blob.stream())
//   //.then(blobFile => new File([blobFile], "fileNameGoesHere"));
//   return file;
// }
// const uploadTaxDocuments = (data) => {
//   // "TODO: Add overwrite option"
//   data.documents.forEach(taxDocument => {
//     const object = getFileFromBlobUrl(taxDocument.url);
//     const awsObjectKey = lintAwsObjectKey(`TaxDocuments/K1s/2020/${taxDocument.filename})`;

//     // const pass = new PassThrough();
//     // object.pipe(pass);
//     // pass.on('error', err => {
//     //   console.error('File Error', err);
//     // });

//     uploadObject(awsObjectKey, object);
//   })
//   // Upload to S3 in this folder: ./tax-documents/k1s/2020
//   // Send successful ones to server to update db
// }

// const UserPortfolioTaxDocumentCreateToolbar = (props) => {
//   return (
//     <Toolbar {...props} >
//       <SaveButton
//         label='Upload'
//         redirect="list"
//         submitOnEnter={true}
//         transform={uploadTaxDocuments}
//       />
//     </Toolbar>
//   )
// }

export const UserPortfolioTaxDocumentCreate = () => {
  // return (
  //   <Create title={`Create ${entityName}`}>
  //     <SimpleForm redirect="list">
  //       <Grid style={{ width: '100%' }} container spacing={5}>
  //         <Alert severity="warning" fullWidth>
  //           <Typography>
  //             Under construction: This is currently used to sync the database
  //             with what is stored on AWS S3.
  //           </Typography>
  //         </Alert>
  //         <Grid item md={6} xs={12}>
  //           {/* <FileInput source="documents" label="Tax Documents" accept="application/pdf" multiple={true}>
  //             <FileField source="url" title="filename" />
  //           </FileInput> */}
  //           <CustomNumberInput source="year" fullWidth />
  //         </Grid>
  //       </Grid>
  //     </SimpleForm>
  //   </Create>
  // );
  const attrs = [
    { name: 'ssnEinEnum', label: 'SSN (2) or EIN (1)', align: 'center' },
    { name: 'ssnEin', label: 'SSN or EIN', align: 'center' },
    { name: 'userName', label: 'User', align: 'center' },
    { name: 'portfolioId', label: 'Portfolio ID', align: 'center' },
    { name: 'userId', label: 'User ID', align: 'center' },
    // {
    //   name: 'transferDate',
    //   format: (val) => moment(val).format('MM/DD/YYYY'),
    //   label: 'Transfer Date',
    //   align: 'center',
    // },
    // {
    //   name: 'netTransfer',
    //   dataFormat: (val) => Math.round(val * 100) / 100,
    //   format: (val) => numeral(val).format('$0,0.00'),
    //   label: 'Net Transfer',
    //   align: 'center',
    // },
    // {
    //   name: 'category',
    //   dataFormat: (val) => val,
    //   format: (val) => val,
    //   label: 'Category',
    //   align: 'center',
    // },
  ];

  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const handlePullInAllTaxDocumentsFromS3 = () => {
    setLoading(true);
    dataProvider.create('UserPortfolioTaxDocument', {}).then(
      (res) => {
        notify('Tax documents uploaded', { type: 'success' });
        setLoading(false);
        refresh();
      },
      (e) => {
        console.error('ERROR', e);
        notify(e.message, { type: 'error' });
        setLoading(false);
        refresh();
      }
    );
  };

  return (
    <Create title={`Create ${entityName}`}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <Button
              color="primary"
              variant="contained"
              disabled={loading} // disable until next year
              onClick={() => handlePullInAllTaxDocumentsFromS3()}
              size="large"
            >
              {loading ? (
                <CircularProgress />
              ) : (
                'Pull in all Tax Documents from S3'
              )}
            </Button>
          </Grid>
        </Grid>
      </SimpleForm>
      {/* <UserPortfolioTaxDocumentsUpload attrs={attrs} /> */}
    </Create>
  );
};
