import {
  <PERSON>olean<PERSON>ield,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Monitoring Alarm Code';

export const MonitoringAlarmCodeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="code" fullWidth />
            <TextInput source="description" fullWidth />
            <BooleanInput source="hiddenFlg" fullWidth />
            <CustomNumberInput
              source="severity"
              fullWidth
              helperText="-1 is reserved for codes that will be used to close all open alarms"
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const MonitoringAlarmCodeList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="code" />
        <TextField source="description" />
        <NumberField source="severity" />
        <BooleanField source="hiddenFlg" />
      </Datagrid>
    </List>
  );
};

export const MonitoringAlarmCodeCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="code" required fullWidth />
          <TextInput source="description" required fullWidth />
          <BooleanInput source="hiddenFlg" fullWidth />
          <CustomNumberInput
            source="severity"
            required
            fullWidth
            helperText="-1 is reserved for codes that will be used to close all open alarms"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
