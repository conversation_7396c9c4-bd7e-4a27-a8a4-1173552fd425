import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDataProvider } from 'react-admin';
import {
  CircularProgress,
  Grid,
  IconButton,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
} from '@mui/material';
import { PictureAsPdf } from '@mui/icons-material';

import MuiList from '@mui/material/List';

export const UserDocuments = () => {
  const [userDocuments, setUserDocuments] = useState(null);
  const dataProvider = useDataProvider();

  const { id } = useParams();
  if (!userDocuments) {
    dataProvider.getOne('UserWithDocuments', { id: parseInt(id, 10) }).then(
      (resp) => {
        setUserDocuments(resp.data.documents);
      },
      (e) => {
        console.error('HIT AN ERROR', e);
        return new Error(e);
      }
    );
  }

  if (userDocuments) {
    return (
      <Grid item xs={12} md={6}>
        <MuiList>
          {userDocuments.map((document) => {
            if (document.downloadUrl) {
              return (
                <ListItem
                  style={{ paddingRight: '75px' }}
                  key={`user-document-desktop-list-item-${document.createdAt}`}
                  divider
                >
                  <ListItemText
                    primary={document.title}
                    secondary={document.subtitle}
                  />
                  <ListItemSecondaryAction>
                    <IconButton href={document.downloadUrl} size="large">
                      <PictureAsPdf />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              );
            }
            return null;
          })}
        </MuiList>
      </Grid>
    );
  } else {
    return <CircularProgress />;
  }
};
