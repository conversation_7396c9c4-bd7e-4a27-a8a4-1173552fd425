import React from 'react';
import {
  Datagrid,
  DatagridBody,
  Filter,
  FunctionField,
  List,
  RecordContextProvider,
  ReferenceInput,
  SelectInput,
  TextInput,
} from 'react-admin';
import { makeStyles } from '@mui/styles';
import {
  Grid,
  Icon,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';

import { LinkField } from './CustomFields';

const entityName = 'Project Monitoring PunchList';

const useStyles = makeStyles((theme) => ({
  sizeSmallHeader: {
    padding: '16px 0px',
  },
  sizeSmallBody: {
    padding: '0px',
  },
}));

const ProjectFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Project Name"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      source="country.id"
      reference="Country"
      label="Country"
      filter={{ occupiedOnly: true }}
    >
      <SelectInput label="Country" optionText="name" />
    </ReferenceInput>
  </Filter>
);

const MyDatagridRow = ({ record, id, children }) => {
  const classes = useStyles();
  return (
    <RecordContextProvider value={record}>
      <TableRow>
        {React.Children.map(children, (field, index) => (
          <TableCell
            classes={{
              sizeSmall: classes.sizeSmallBody,
            }}
            key={`${id}-${field.props.source}`}
            style={{ paddingLeft: index === 0 ? '1rem' : 0 }}
          >
            {field}
          </TableCell>
        ))}
      </TableRow>
    </RecordContextProvider>
  );
};
const MyDatagridHeader = ({ children }) => {
  const classes = useStyles();
  return (
    <TableHead>
      <TableRow>
        {React.Children.map(children, (child, index) => (
          <TableCell
            key={child.props.label || child.props.source}
            style={{ width: '50px', paddingLeft: index === 0 ? '1rem' : 0 }}
            classes={{
              sizeSmall: classes.sizeSmallHeader,
            }}
          >
            {child.props.label || child.props.source}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
};
const MyDatagridBody = (props) => (
  <DatagridBody {...props} row={<MyDatagridRow />} />
);
const MyDatagrid = (props) => (
  <Datagrid
    {...props}
    body={<MyDatagridBody />}
    header={<MyDatagridHeader />}
  />
);

export const ProjectMonitoringRequirementList = () => {
  const punchListItems = [
    //   {
    //   label: "2.3 Internet Status",
    //   attr: 'internetStatus'
    // },
    {
      label: '2.4 Inverter Power',
      attr: 'inverterPower',
    },
    {
      label: '2.9 Wind Speed',
      attr: 'windSpeed',
    },
    {
      label: '2.14.1 Direct Irradiance',
      attr: 'directIrradiance',
    },
    {
      label: '2.14.2 Horizontal Irradiance',
      attr: 'horizontalIrradiance',
    },
    {
      label: '2.14.3 Reflected Irradiance',
      attr: 'reflectedIrradiance',
    },
    {
      label: '2.16 Module Temperature',
      attr: 'moduleTemperature',
    },
    {
      label: '2.18 Revenue Grade Meter',
      attr: 'revenueGradeMeter',
    },
    {
      label: '2.23 Tracker Position',
      attr: 'trackerPosition',
    },
  ];
  return (
    <List title={entityName} filters={<ProjectFilter />}>
      <MyDatagrid>
        <LinkField
          reference="Project"
          linkSource="id"
          labelSource="name"
          label="Project"
        />
        {punchListItems.map((item) => (
          <FunctionField
            label={item.label}
            sortable={false}
            style={{ width: '50px' }}
            render={(record) => (
              <Grid
                container
                item
                alignItems="center"
                justifyContent="center"
                style={{
                  backgroundColor:
                    record.monitoringPunchList &&
                    record.monitoringPunchList[String(item.attr)]?.color,
                  height: '54px',
                }}
              >
                <Tooltip
                  arrow
                  title={
                    record.monitoringPunchList &&
                    record.monitoringPunchList[String(item.attr)]?.status
                  }
                >
                  <span>
                    <Icon
                      style={{ color: 'white' }}
                      className={
                        record.monitoringPunchList &&
                        record.monitoringPunchList[String(item.attr)]?.icon
                      }
                    />
                  </span>
                </Tooltip>
              </Grid>
            )}
          />
        ))}
      </MyDatagrid>
    </List>
  );
};
