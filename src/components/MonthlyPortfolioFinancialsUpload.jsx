import React, { Component } from 'react';
import { Alert } from '@mui/lab';

import {
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  FormControl,
  FormControlLabel,
  Grid,
  Input,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

import { CheckCircle, Error, GetApp } from '@mui/icons-material';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';

export class MonthlyPortfolioFinancialsUpload extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      data: null,
      loading: false,
      portfolios: null,
      selectedPortfolios: [],
    };
    this.getFilteredData = this.getFilteredData.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.handleData = this.handleData.bind(this);
    this.save = this.save.bind(this);
  }

  handleChange(event) {
    this.setState({ selectedPortfolios: event.target.value });
  }

  handleData(data) {
    const { attrs } = this.props;
    const lintedData = this.lintData(data, attrs);
    this.setState({ data: lintedData });
  }

  lintData(data, attrs) {
    return data.map((entry) => {
      const returnObj = {};
      Object.keys(entry).forEach((entryAttr) => {
        const entryAttrData = findWithAttr(attrs, 'name', entryAttr);
        if (!entryAttrData) {
          console.log('Missing attr detected', entryAttr);
        } else if (entryAttrData.dataFormat) {
          returnObj[String(entryAttr)] = entryAttrData.dataFormat(
            entry[String(entryAttr)]
          );
        } else if (entry[String(entryAttr)] === 'NA') {
          returnObj[String(entryAttr)] = null;
        } else {
          returnObj[String(entryAttr)] = entry[String(entryAttr)];
        }
      });
      return returnObj;
    });
  }

  getFilteredData() {
    const { data, selectedPortfolios } = this.state;
    let filteredData = data;
    if (selectedPortfolios && selectedPortfolios.length > 0) {
      const selectedPortfolioIds = selectedPortfolios.map((portfolioLabel) =>
        portfolioLabel.substring(0, 1)
      );
      filteredData = data.filter((row) =>
        selectedPortfolioIds.includes(String(row.portfolioId))
      );
    }
    return filteredData;
  }

  save() {
    this.setState({ loading: true });
    const { type, dataProvider } = this.props;

    const filteredData = this.getFilteredData();

    dataProvider
      .create(`MonthlyPortfolioFinancial${type}`, {
        data: filteredData,
      })
      .catch((e) => {
        this.setState({ loading: false });
        console.log('ERROR', e);
      })
      .then(() => {
        // TODO: throw visible error. Also this is not the right way to redirect to List view
        window.location.href = `/MonthlyPortfolioFinancial${type}`;
        this.setState({ loading: false });
      });
  }

  renderPortfolioFilter() {
    const { portfolios, selectedPortfolios } = this.state;
    return (
      <FormControl fullWidth>
        <InputLabel>Portfolios to upload</InputLabel>
        <Select
          labelId="multiple-portfolio-label"
          multiple
          value={selectedPortfolios}
          onChange={this.handleChange}
          input={<Input id="select-multiple-portfolio" />}
          renderValue={(selected) => (
            <div style={{ flexWrap: 'wrap', display: 'flex' }}>
              {selected.map((value, id) => (
                <Chip
                  key={`${value}-${id}`}
                  label={value}
                  style={{ margin: 2 }}
                />
              ))}
            </div>
          )}
        >
          {portfolios
            ? portfolios.map((portfolio) => {
                const label = `${portfolio.id} - ${portfolio.subtitle}`;
                return (
                  <MenuItem key={`${portfolio.id}-checkbox`} value={label}>
                    <Checkbox
                      checked={selectedPortfolios.indexOf(label) > -1}
                    />
                    {label}
                  </MenuItem>
                );
              })
            : null}
        </Select>
      </FormControl>
    );
  }

  renderData() {
    const { attrs } = this.props;
    // Note: the attrs only differ between actuals and projections in one field. MaxShares vs TotalShares
    const filteredData = this.getFilteredData();
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell
                key={`projections-header-cell-${attr.name}`}
                align={attr.align || 'center'}
              >
                {attr.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {filteredData.map((row, index) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell
                  key={`projections-val-cell-${attr.name}`}
                  align={attr.align || 'center'}
                >
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.portfolioId}-${row.effectiveDt}-${index}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  }

  renderSubmit() {
    const { reviewed, loading } = this.state;
    const { type } = this.props;
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <FormControlLabel
            // style={{ width: '100%' }}
            control={
              <Checkbox
                checked={!!reviewed}
                onChange={() => this.setState({ reviewed: !reviewed })}
              />
            }
            label={`I have checked and double checked the below numbers. ${
              type === 'Actual'
                ? '**These numbers will determine dividends!*'
                : ''
            }`}
          />
          <Button
            onClick={this.save}
            disabled={!reviewed || loading}
            variant="contained"
            size="large"
            color="secondary"
          >
            {loading ? <CircularProgress /> : 'Save'}
          </Button>
        </Alert>
      </>
    );
  }

  render() {
    // const parserOptions = {
    //   header: true,
    //   dynamicTyping: true,
    //   skipEmptyLines: true,
    //   transformHeader: (header) => header.toLowerCase().replace(/\W/g, '_'),
    // };

    const { data, portfolios } = this.state;
    const { type, dataProvider } = this.props;
    if (!portfolios) {
      dataProvider
        .getList('PortfolioLite', {
          sort: {
            field: 'id',
            order: 'ASC',
          },
          pagination: {
            page: 1,
            perPage: 10000,
          },
        })
        .then((portfolios) => this.setState({ portfolios: portfolios.data }));
    }

    return (
      <Grid container>
        <Grid xs={12} item style={{ padding: '1em' }}>
          <Button
            component="a"
            variant="contained"
            href={`/csv-templates/monthlyPortfolioFinancial${type}s.xlsx`}
            download
          >
            <GetApp />
            Click to download the csv template
          </Button>
        </Grid>
        <ExcelReader type={type} handleData={this.handleData} />
        <Grid item xs={12} style={{ margin: '1em' }}>
          {data ? this.renderSubmit() : null}
        </Grid>
        <Grid item xs={12} md={4} style={{ margin: '1em' }}>
          {data ? this.renderPortfolioFilter() : null}
        </Grid>
        <Grid item xs={12}>
          {data ? this.renderData() : null}
        </Grid>
      </Grid>
    );
  }
}
