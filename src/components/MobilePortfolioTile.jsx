import React from 'react';
import { Image } from 'cloudinary-react';

// @material-ui
import {
  Card,
  CardActionArea,
  CardActions,
  CardContent,
  CardMedia,
  Chip,
  Fab,
  Grid,
  Typography,
} from '@mui/material';

import {
  ControlPointTwoTone,
  CheckCircleTwoTone,
  HourglassFullTwoTone,
} from '@mui/icons-material';

import Config from '../config/config';
import theme from '../theme';

const PortfolioTile = (props) => {
  const { data, status } = props;
  if (!data) return null;
  const {
    // id,
    subtitle,
    summary,
    bannerImage,
    primaryImage,
    projectedCOCYield,
  } = data;

  const oStatus = {
    open: {
      label: 'Accepting Investments',
      icon: <ControlPointTwoTone />,
      color: theme.palette.success.main,
    },
    comingSoon: {
      label: 'Coming Soon',
      icon: <HourglassFullTwoTone />,
      color: theme.palette.yellow.main,
    },
    fullyFunded: {
      label: 'Fully Funded',
      icon: <CheckCircleTwoTone />,
      color: '#5292c3',
    },
  };
  const statusData = oStatus[String(status)] || {};
  return (
    <Card style={{ position: 'relative', width: '40%' }}>
      <CardActionArea>
        <Chip
          style={{
            zIndex: 9999,
            fontWeight: 'bold',
            marginTop: '10px',
            marginLeft: '-135px',
            position: 'absolute',
            backgroundColor: statusData.color,
          }}
          icon={statusData.icon}
          color="secondary"
          label={statusData.label}
        />
        <CardMedia
          component={Image}
          style={{
            objectFit: 'cover',
            width: '100%',
            height: '100%',
            minHeight: '30vh',
            maxHeight: '30vh',
          }}
          width="720"
          alt="portfolio banner image"
          crop="scale"
          cloud_name={Config.cloud_name}
          publicId={
            (primaryImage && primaryImage.public_id) ||
            (bannerImage && bannerImage.public_id) ||
            'site/missing_image'
          } // eslint-disable-line camelcase
        />
        <CardContent>
          <Grid style={{ textAlign: 'left' }}>
            <Typography style={{ fontSize: '1rem' }} variant="h5">
              <b>{subtitle}</b>
            </Typography>
            <Typography gutterBottom variant="body2">
              <b>{`${projectedCOCYield} estimated return`}</b>
            </Typography>
            <Typography style={{ minHeight: '64px' }} variant="body2">
              {summary}
            </Typography>
          </Grid>
        </CardContent>
      </CardActionArea>
      <CardActions style={{ textAlign: 'center' }}>
        <Fab
          style={{
            margin: 'auto',
            marginBottom: '.5rem',
            fontWeight: 'bold',
            textTransform: 'none',
            backgroundColor: '#5292c3',
          }}
          // to={`/investment/${id}`}
          // component={Link}
          size="medium"
          variant="extended"
          color="secondary"
        >
          Learn More
        </Fab>
      </CardActions>
    </Card>
  );
};

export default PortfolioTile;
