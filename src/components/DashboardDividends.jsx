// cachebust 2
import React, { Component, useState } from 'react';

import {
  Button,
  ButtonGroup,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Grid,
  IconButton,
  Radio,
  RadioGroup,
  Skeleton,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useDataProvider } from 'react-admin';

import numeral from 'numeral';
import moment from 'moment';
import { Bar, Line } from 'react-chartjs-2';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Chart } from 'chart.js';
import 'chart.js/auto';
import 'chartjs-adapter-moment';

import theme from '../theme';
import { interpolateColors } from '../utils/global';
import MuiButton from '@mui/material/Button';
import { Settings } from '@mui/icons-material';

// utility functions
const minDate = (dataList, dateField) => {
  // if (!sortedList || !sortedList[0]) return null;
  const sortedList = dataList.sort((a, b) =>
    a[String(dateField)] > b[String(dateField)] ? 1 : -1
  );
  return sortedList[0][String(dateField)];
};

const maxDate = (dataList, dateField) => {
  const sortedList = dataList.sort((a, b) =>
    a[String(dateField)] < b[String(dateField)] ? 1 : -1
  );
  return sortedList[0][String(dateField)];
};

const generateDateRange = (startDate, endDate, timeSpan = 'day') => {
  const dates = [];
  const currentDate = moment(startDate);
  while (currentDate <= endDate) {
    dates.push(moment(currentDate));
    currentDate.add(1, timeSpan);
  }
  return dates;
};

const getBorderColorsArray = (numColors) => {
  return interpolateColors(numColors, null, null, 1);
};

const getColorsArray = (numColors) => {
  return interpolateColors(numColors, null, null, 0.8);
};

const getCumulativesOverTime = (valuesPerDayMap) => {
  let cumulativeValuesOverTime = [];
  let sum = 0;
  for (const val of valuesPerDayMap.values()) {
    cumulativeValuesOverTime.push((sum += val));
  }
  return cumulativeValuesOverTime;
};

const getMonthlyActualsChartData = (actuals, type) => {
  const firstActualDt = minDate(actuals, 'effectiveDt');
  const lastActualDt = maxDate(actuals, 'effectiveDt');
  const actualMonths = generateDateRange(
    moment(firstActualDt).startOf('month'),
    moment(lastActualDt).startOf('month'),
    'month'
  );

  let portfolioActuals = {};
  actuals.forEach((actual) => {
    if (actual.portfolio) {
      let actualList = portfolioActuals[actual.portfolio.subtitle];
      if (!actualList) {
        portfolioActuals[actual.portfolio.subtitle] = [actual];
      } else {
        actualList.push(actual);
        portfolioActuals[actual.portfolio.subtitle] = actualList;
      }
    }
  });

  const borderColorsArray = getBorderColorsArray(
    Object.keys(portfolioActuals).length
  );
  const colorsArray = getColorsArray(Object.keys(portfolioActuals).length);

  let datasets = [];

  for (const [portfolio, actuals] of Object.entries(portfolioActuals)) {
    let dateBuckets = {};
    let total = 0;
    actualMonths.forEach((d) => (dateBuckets[moment(d).format('MM-YYYY')] = 0));
    actuals.forEach((actual) => {
      const key = moment(actual.effectiveDt).format('MM-YYYY');
      if (type === 'dividends') {
        if (!dateBuckets[String(key)]) {
          dateBuckets[String(key)] = parseFloat(actual.grossCafd || 0);
        } else {
          dateBuckets[String(key)] += parseFloat(actual.grossCafd || 0);
        }
        total += parseFloat(actual.grossCafd || 0);
      } else if (type === 'production') {
        if (!dateBuckets[String(key)]) {
          dateBuckets[String(key)] = parseFloat(actual.production || 0);
        } else {
          dateBuckets[String(key)] += parseFloat(actual.production || 0);
        }
        total += parseFloat(actual.production || 0);
      } else if (type === 'fees') {
        if (!dateBuckets[String(key)]) {
          dateBuckets[String(key)] =
            parseFloat(actual.fees || 0) + parseFloat(actual.carry || 0);
        } else {
          dateBuckets[String(key)] +=
            parseFloat(actual.fees || 0) + parseFloat(actual.carry || 0);
        }
        total += parseFloat(actual.fees || 0) + parseFloat(actual.carry || 0);
      }
    });

    const borderColor = borderColorsArray.shift();
    const fillColor = colorsArray.shift();

    if (total > 0) {
      datasets.push({
        label: portfolio,
        data: Object.values(dateBuckets),
        pointRadius: 0,
        borderWidth: 3,
        borderColor: borderColor,
        backgroundColor: fillColor,
        fill: true,
      });
    }
  }
  return { data: datasets, labels: actualMonths };
};

export default (args) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const { open } = args;
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  const fetchData = () => {
    setLoading(true);
    dataProvider.getOne('CMSDashboardDividendData', { data: null }).then(
      (resp) => {
        setData(resp.data.allMonthlyPortfolioFinancialActuals);
        setLoading(false);
      },
      (e) => {
        setLoading(false);
        console.error('HIT AN ERROR', e);
        return new Error(e);
      }
    );
  };

  if (!data && !loading) {
    fetchData();
  }

  if (!open) return null;
  if (!data || loading)
    return (
      <Grid container>
        <Grid item xs={12}>
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="16rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </Grid>
      </Grid>
    );

  return (
    <Collapse in={open}>
      <Grid container>
        <DividendChart data={data} fullScreen={fullScreen} />
        <Grid item xs={12} style={{ marginTop: '2rem' }}>
          <ProductionChart data={data} fullScreen={fullScreen} />
        </Grid>
      </Grid>
    </Collapse>
  );
};

class DividendChart extends Component {
  constructor(props) {
    super(props);
    const formattedData = getMonthlyActualsChartData(props.data, 'dividends');
    this.chartData = formattedData.data;
    this.chartLabels = formattedData.labels;
    this.state = {
      chartType: 'monthly', // 'total', 'monthly'

      dialogOpen: false,
    };

    this.state.selectChartType = this.state.chartType;
  }

  render() {
    const dividendLabels = [...this.chartLabels];
    const formattedLabels = dividendLabels.map((label) =>
      moment(label, 'MM-DD-YYYY').toDate()
    );
    let dividendData = [];
    if (this.state.chartType === 'total') {
      this.chartData.forEach((projectDividendData) => {
        const projectData = { ...projectDividendData };
        projectData.data = getCumulativesOverTime(projectData.data);
        dividendData.push(projectData);
      });
    } else {
      dividendData = [...this.chartData];
    }
    const data = {
      labels: formattedLabels,
      datasets: dividendData,
    };
    const chartOptions = {
      maintainAspectRatio: false,
      borderRadius: 4,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
          position: 'nearest',
          callbacks: {
            label: (tooltipItem) =>
              `${tooltipItem.dataset.label}: ${numeral(
                tooltipItem.formattedValue
              ).format('$0,0')}`,
            footer: (tooltipItem) => {
              const total = tooltipItem.reduce(function (sum, cur) {
                return sum + cur.raw;
              }, 0);
              return `Net CAFD: ${numeral(total).format('$0,0')}`;
            },
          },
        },
      },
      scales: {
        x: {
          stacked: true,
          type: 'time',
          time: {
            tooltipFormat: 'MMM YYYY',
            unit: 'month',
          },
        },
        y: {
          stacked: true,
          beginAtZero: true,
          ticks: {
            callback: (value) => numeral(value).format('$0,0.[0]a'),
          },
        },
      },
    };

    const chartJsx = (
      <Grid item xs={12}>
        {this.state.chartType === 'total' ? (
          <Line height={300} data={data} options={chartOptions} />
        ) : (
          <Bar height={300} data={data} options={chartOptions} />
        )}
      </Grid>
    );

    if (!this.props.fullScreen) {
      const toggleButtonStyle = { textTransform: 'none' };
      return (
        <>
          <Grid container justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Dividends Paid Out : </Typography>
            <Grid item style={{ margin: '4px' }}>
              <ButtonGroup aria-label="dividend chart type" size="small">
                <Button
                  variant={
                    this.state.chartType === 'total' ? 'contained' : 'outlined'
                  }
                  aria-label="total dividends paid"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ chartType: 'total' })}
                >
                  Total Dividends Paid
                </Button>
                <Button
                  variant={
                    this.state.chartType === 'monthly'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="monthly dividends paid"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ chartType: 'monthly' })}
                >
                  Monthly Dividends Paid
                </Button>
              </ButtonGroup>
            </Grid>
          </Grid>
          {chartJsx}
        </>
      );
    } else {
      return (
        <>
          <Grid container justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Dividends Paid Out : </Typography>
            <IconButton
              variant="contained"
              color="primary"
              onClick={() => this.setState({ dialogOpen: true })}
            >
              <Settings />
            </IconButton>
          </Grid>
          <Dialog open={this.state.dialogOpen} fullWidth>
            <DialogTitle>
              <Typography variant="h5">Chart Customization</Typography>
            </DialogTitle>
            <DialogContent>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Chart Type:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectChartType"
                    defaultValue={this.state.selectChartType}
                    onChange={(event) => {
                      this.setState({ selectChartType: event.target.value });
                    }}
                  >
                    <FormControlLabel
                      value="total"
                      control={<Radio />}
                      label="Total Dividends Paid"
                    />
                    <FormControlLabel
                      value="monthly"
                      control={<Radio />}
                      label="Monthly Dividends Paid"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <MuiButton
                onClick={() => this.setState({ dialogOpen: false })}
                color="primary"
              >
                Cancel
              </MuiButton>
              <MuiButton
                onClick={() => {
                  this.setState({
                    chartType: this.state.selectChartType,
                    dialogOpen: false,
                  });
                }}
                color="primary"
                variant="contained"
              >
                Go
              </MuiButton>
            </DialogActions>
          </Dialog>
          {chartJsx}
        </>
      );
    }
  }
}

class ProductionChart extends Component {
  constructor(props) {
    super(props);
    const formattedData = getMonthlyActualsChartData(props.data, 'production');
    this.chartData = formattedData.data;
    this.chartLabels = formattedData.labels;
    this.state = {
      chartType: 'monthly', // 'total', 'monthly'

      dialogOpen: false,
    };

    this.state.selectChartType = this.state.chartType;
  }

  render() {
    const productionLabels = [...this.chartLabels];
    const formattedLabels = productionLabels.map((label) =>
      moment(label, 'MM-DD-YYYY').toDate()
    );
    let productionData = [];
    if (this.state.chartType === 'total') {
      this.chartData.forEach((projectProductionData) => {
        const projectData = { ...projectProductionData };
        projectData.data = getCumulativesOverTime(projectData.data);
        productionData.push(projectData);
      });
    } else {
      productionData = [...this.chartData];
    }

    const data = {
      labels: formattedLabels,
      datasets: productionData,
    };

    const chartOptions = {
      maintainAspectRatio: false,
      borderRadius: 4,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
          position: 'nearest',
          callbacks: {
            label: (tooltipItem) =>
              `${tooltipItem.dataset.label}: ${numeral(
                tooltipItem.formattedValue
              ).format('0,0')} MWh`,
            footer: (tooltipItem) => {
              const total = tooltipItem.reduce(function (sum, cur) {
                return sum + cur.raw;
              }, 0);
              return `Total Production: ${numeral(total).format('0,0')} MWh`;
            },
          },
        },
      },
      scales: {
        x: {
          stacked: true,
          type: 'time',
          time: {
            tooltipFormat: 'MMM YYYY',
            unit: 'month',
          },
        },
        y: {
          stacked: true,
          ticks: {
            callback: (value) => `${numeral(value).format('0,0.[0]a')} MWh`,
          },
        },
      },
    };

    const chartJsx = (
      <Grid item xs={12}>
        {this.state.chartType === 'total' ? (
          <Line height={300} data={data} options={chartOptions} />
        ) : (
          <Bar height={300} data={data} options={chartOptions} />
        )}
      </Grid>
    );

    if (!this.props.fullScreen) {
      const toggleButtonStyle = { textTransform: 'none' };
      return (
        <>
          <Grid container justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Production : </Typography>
            <Grid item style={{ margin: '4px' }}>
              <ButtonGroup aria-label="dividend chart type" size="small">
                <Button
                  variant={
                    this.state.chartType === 'total' ? 'contained' : 'outlined'
                  }
                  aria-label="total energy produced"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ chartType: 'total' })}
                >
                  Total Production
                </Button>
                <Button
                  variant={
                    this.state.chartType === 'monthly'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="monthly energy produced"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ chartType: 'monthly' })}
                >
                  Monthly Production
                </Button>
              </ButtonGroup>
            </Grid>
          </Grid>
          {chartJsx}
        </>
      );
    } else {
      return (
        <>
          <Grid container justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Production : </Typography>
            <IconButton
              variant="contained"
              color="primary"
              onClick={() => this.setState({ dialogOpen: true })}
            >
              <Settings />
            </IconButton>
          </Grid>
          <Dialog open={this.state.dialogOpen} fullWidth>
            <DialogTitle>
              <Typography variant="h5">Chart Customization</Typography>
            </DialogTitle>
            <DialogContent>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Chart Type:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectChartType"
                    defaultValue={this.state.selectChartType}
                    onChange={(event) => {
                      this.setState({ selectChartType: event.target.value });
                    }}
                  >
                    <FormControlLabel
                      value="total"
                      control={<Radio />}
                      label="Total Production"
                    />
                    <FormControlLabel
                      value="monthly"
                      control={<Radio />}
                      label="Monthly Production"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <MuiButton
                onClick={() => this.setState({ dialogOpen: false })}
                color="primary"
              >
                Cancel
              </MuiButton>
              <MuiButton
                onClick={() => {
                  this.setState({
                    chartType: this.state.selectChartType,
                    dialogOpen: false,
                  });
                }}
                color="primary"
                variant="contained"
              >
                Go
              </MuiButton>
            </DialogActions>
          </Dialog>
          {chartJsx}
        </>
      );
    }
  }
}
