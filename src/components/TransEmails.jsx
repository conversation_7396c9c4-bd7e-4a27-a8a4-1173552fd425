import React from 'react';
import {
  <PERSON><PERSON>an<PERSON>ield,
  BooleanInput,
  Create,
  <PERSON>grid,
  DateField,
  Edit,
  List,
  Pagination,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Transactional Emails';

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100]} />
);

export const TransEmailList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      perPage={100}
      pagination={<CustomPagination />}
      title={entityName}
      sort={{ field: 'id', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <TextField source="sendgridTemplateId" />
        <TextField source="roles" />
        <BooleanField source="inactive" />
        <BooleanField source="itBccFlg" label="IT BCC Flg" />
        <BooleanField
          source="investorRelationsBccFlg"
          label="Investor Relations BCC Flg"
        />
        <BooleanField source="creditMgmtBccFlg" label="Credit Mgmt BCC Flg" />
        <BooleanField
          source="hubSpotTrackingFlg"
          label="HubSpot Tracking Flg"
        />
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const TransEmailEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput multiline source="description" fullWidth />
            <TextInput source="sendgridTemplateId" fullWidth />
          </Grid>
        </Grid>
        <TextField source="roles" />
        <BooleanInput source="inactive" />
        <BooleanInput source="itBccFlg" label="IT BCC Flg" />
        <BooleanInput
          source="investorRelationsBccFlg"
          label="Investor Relations BCC Flg"
        />
        <BooleanInput source="creditMgmtBccFlg" label="Credit Mgmt BCC Flg" />
        <BooleanInput
          source="hubSpotTrackingFlg"
          label="HubSpot Tracking Flg"
          helperText="In general, this should be set to true as long as the email is not internal."
        />
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </SimpleForm>
    </Edit>
  );
};

export const TransEmailCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
