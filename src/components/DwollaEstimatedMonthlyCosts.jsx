import React from 'react';
import numeral from 'numeral';
import {
  Datagrid,
  FunctionField,
  List,
  NumberField,
  TextField,
} from 'react-admin';
import 'chart.js/auto';

import { Doughnut } from 'react-chartjs-2';
import { Alert } from '@mui/lab';

import theme from '../theme';

const entityName = 'Dwolla Estimated Monthly Cost';

export const DwollaEstimatedMonthlyCostList = () => (
  <>
    <Alert severity="info">
      The below list is not the actual monthly fees charged by Dwolla but it
      should be very close. It uses the cost structure which began on October
      1st, 2022. This uses all of the dwolla webhook events we receive to total
      up an estimated cost.
    </Alert>
    <List>
      <Datagrid>
        <TextField source="month" sortable={false} />
        <FunctionField
          align="center"
          label="Cost Breakdown"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <div style={{ width: '10em' }}>
                <Doughnut
                  data={{
                    labels: ['Transfers', 'Verifications', 'Subscription'],
                    datasets: [
                      {
                        data: [
                          record.transferCost,
                          record.personalVerificationCost +
                            record.businessVerificationCost,
                          record.monthlySubscriptionCost,
                        ],
                        backgroundColor: [
                          theme.palette.green.main,
                          theme.palette.primary.main,
                          '#ccc',
                        ],
                      },
                    ],
                  }}
                  options={{
                    animation: {
                      duration: 0,
                    },
                    plugins: {
                      legend: { display: false },
                      tooltip: {
                        callbacks: {
                          label: (tooltipItem) =>
                            `${tooltipItem.label}: ${numeral(
                              tooltipItem.raw
                            ).format('$0,0')}`,
                        },
                      },
                    },
                    layout: {
                      padding: {
                        right: 50,
                      },
                    },
                  }}
                />
              </div>
            );
          }}
        />
        <NumberField
          source="total"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField
          source="transferCost"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField
          source="personalVerificationCost"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField
          source="businessVerificationCost"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField
          source="monthlySubscriptionCost"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
      </Datagrid>
    </List>
  </>
);
