import React from 'react';

import {
  Create,
  Datagrid,
  Edit,
  FileField,
  FileInput,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Utility Script';

export const UtilityScriptEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <FileInput
              source="files"
              label="Related files"
              // accept="application/html"
            >
              <FileField source="src" title="title" />
            </FileInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const UtilityScriptList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        {/* <DateField source="date" />
      <NumberField source="amount" /> */}
      </Datagrid>
    </List>
  );
};

export const UtilityScriptCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          {/* <DateInput source="date" required fullWidth />
          <CustomNumberInput source="amount" fullWidth /> */}
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
