import React, { useState } from 'react';
import { Link as ReactRouterLink, useParams } from 'react-router-dom';
import moment from 'moment';
import jsonExport from 'jsonexport/dist';
import {
  BooleanField,
  BooleanInput,
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  downloadCSV,
  Edit,
  ExportButton,
  Filter,
  FunctionField,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import { EmailExportButton } from './EmailExportButton';

const entityName = 'Dividend';

export const DividendEdit = () => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);

  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const createReinvestedDividend = () => {
    dataProvider
      .update('Dividend', {
        data: {
          id: parseInt(id),
          manuallyReinvestDividend: true,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Successfully reinvested dividend');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error reinvesting dividend', { type: 'error' });
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="id" fullWidth />
            <CustomNumberInput source="value" fullWidth />
            <DateInput source="date" fullWidth />
            <FunctionField
              label="Re-Investment"
              style={{ width: '100%' }}
              render={(record) => (
                <>
                  Re-Investment :{' '}
                  {record.investment ? (
                    <Typography
                      fullWidth
                      style={{ width: '100%' }}
                      component={ReactRouterLink}
                      to={`/investment/${record.investment.id}`}
                    >
                      {record.investment.label}
                    </Typography>
                  ) : null}
                  <br />
                </>
              )}
            />
            <FunctionField
              label="Re-Investment"
              style={{ width: '100%' }}
              render={(record) => (
                <>
                  Transfer :{' '}
                  {record.transfer ? (
                    <Typography
                      fullWidth
                      style={{ width: '100%' }}
                      component={ReactRouterLink}
                      to={`/transfer/${record.transfer.id}`}
                    >
                      {record.transfer.label}
                    </Typography>
                  ) : null}
                  <br />
                </>
              )}
            />
            <FunctionField
              align="center"
              label="User"
              style={{ width: '100%' }}
              render={(record) => (
                <>
                  User :{' '}
                  {record.user ? (
                    <Typography
                      fullWidth
                      style={{ width: '100%' }}
                      component={ReactRouterLink}
                      to={`/user/${record.user.id}`}
                    >
                      {record.user.fullName}
                    </Typography>
                  ) : null}
                  <br />
                </>
              )}
            />
            <FunctionField
              align="center"
              label="Sub-Account"
              style={{ width: '100%' }}
              render={(record) => (
                <>
                  Sub Account :{' '}
                  {record.subAccount ? (
                    <Typography
                      fullWidth
                      style={{ width: '100%' }}
                      component={ReactRouterLink}
                      to={`/subAccount/${record.subAccount.id}`}
                    >
                      {record.subAccount.label}
                    </Typography>
                  ) : null}
                  <br />
                </>
              )}
            />
            <FunctionField
              align="center"
              label="Monthly Actual"
              style={{ width: '100%' }}
              render={(record) => (
                <>
                  Monthly Actual :{' '}
                  {record.monthlyPortfolioFinancialActual ? (
                    <Typography
                      fullWidth
                      style={{ width: '100%' }}
                      component={ReactRouterLink}
                      to={`/monthlyPortfolioFinancialActual/${record.monthlyPortfolioFinancialActual.id}`}
                    >
                      {record.monthlyPortfolioFinancialActual.label}
                    </Typography>
                  ) : null}
                  <br />
                </>
              )}
            />
          </Grid>
        </Grid>
        <FunctionField
          align="left"
          label=""
          style={{ width: '100%' }}
          render={(record) => (
            <>
              {record.monthlyPortfolioFinancialActual &&
              record.subAccount?.subAccountType?.id === 37 &&
              !record.transfer &&
              !record.investment ? (
                <Button
                  variant="contained"
                  disabled={loading}
                  onClick={createReinvestedDividend}
                >
                  Manually Reinvest Dividend
                </Button>
              ) : null}
              <br />
            </>
          )}
        />
      </SimpleForm>
    </Edit>
  );
};
// cachebust2

const exporter = (rows) => {
  const rowsForExport = rows.map((row) => {
    const returnRow = {};
    const dates = ['date', 'completedDt', 'createdAt', 'updatedAt'];
    Object.keys(row).forEach((attr) => {
      if (attr === '__typename') return;
      const data = row[String(attr)];
      if (data && data.__typename) {
        delete data.__typename;
      }
      if (dates.indexOf(attr) > -1) {
        returnRow[String(attr)] = moment(data).format('MM/DD/YYYY');
      } else {
        returnRow[String(attr)] = data;
      }
    });
    return returnRow;
  });
  jsonExport(
    rowsForExport,
    {
      rowDelimiter: ';',
    },
    (err, csv) => {
      downloadCSV(`sep=;\n${csv}`, entityName); // download as 'posts.csv` file
    }
  );
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      {/* NOTE: ExportButton results in javascript heap error for this list. If we do want regular export, we can lower maxResults */}
      {/* <ExportButton maxResults={100000} /> */}
      <EmailExportButton
        {...props}
        resource={resource}
        filterValues={filterValues}
      />
    </TopToolbar>
  );
};

const DividendFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <DateInput label="Date Lower Bound" source="dateLowerBound" />
    <DateInput label="Date Upper Bound" source="dateUpperBound" />
    <TextInput
      style={{ minWidth: '200px' }}
      label="Investor's State (Ex: 'CT')"
      source="investorState"
    />
    <BooleanInput
      label="Millennium Trust Only"
      source="millenniumTrustDividendsFlg"
    />
    <BooleanInput label="Non-Reinvested Only" source="nonReinvestedOnlyFlg" />
  </Filter>
);
const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

const styleRow = (record, index) => {
  const { investment, transfer, date } = record;
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  const dividendTurnover = new Date('11/15/2021');
  if (investment && transfer && new Date(date) > dividendTurnover) {
    if (investment.value < record.value) {
      return warningStyle;
    }
    return errorStyle;
  }
  const webhookCount =
    (record?.transfer?.eventNotifications?.length || 0) +
    (record?.investment?.autoReinvestEventNotifications?.length || 0);
  if (record.investment && webhookCount !== 1) {
    return errorStyle;
  }
  if (record.completedDt && record.transfer && webhookCount !== 2) {
    return errorStyle;
  }
  if (!record.completedDt && record.transfer && webhookCount !== 1) {
    return errorStyle;
  }
  return {};
};

export const DividendList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      actions={<ListActions />}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<DividendFilter />}
      perPage={50}
      pagination={<CustomPagination />}
      exporter={exporter}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <DateField source="date" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          reference="SubAccount"
          linkSource="subAccount.id"
          labelSource="subAccount.name"
          label="SubAccount"
        />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          label="Value (USD)"
          source="value"
        />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <LinkField
          reference="Transfer"
          linkSource="transfer.id"
          labelSource="transfer.label"
          label="Transfer"
        />
        <LinkField
          reference="Investment"
          linkSource="investment.id"
          labelSource="investment.label"
          label="Investment"
        />
        <BooleanField source="shouldBeReinvested" />
        <FunctionField
          label="Notification Count"
          style={{ width: '100%' }}
          render={(record) => (
            <span>
              {(record?.transfer?.eventNotifications?.length || 0) +
                (record?.investment?.autoReinvestEventNotifications?.length ||
                  0)}
            </span>
          )}
        />
        <NumberField
          label="Webhook Count"
          source="transfer.dbDwollaWebhookEventCount"
        />
        <DateField source="completedDt" showTime={true} />
      </Datagrid>
    </List>
  );
};

export const DividendCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
