import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import numeral from 'numeral';
import { Link } from 'react-router-dom';
import {
  ArrayField,
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  List,
  NumberField,
  ReferenceArrayInput,
  ReferenceInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Alert,
  Avatar,
  Button,
  Checkbox,
  CircularProgress,
  Divider,
  FormControlLabel,
  Grid,
  Icon,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField as MuiTextField,
  Typography,
  FormControl,
} from '@mui/material';

import {
  CustomNumberInput,
  CustomR<PERSON>erence<PERSON>ield,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CheckCircle, Error, GetApp } from '@mui/icons-material';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';

const entityName = 'Billing Cycle';

export const BrBillingCycleEdit = () => {
  const [invoices, setInvoices] = useState(null);
  const [invoicesLoading, setInvoicesLoading] = useState(false);
  const { id } = useParams();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  const fetchInvoices = () => {
    setInvoicesLoading(true);
    dataProvider
      .getList('BrInvoiceLite', {
        pagination: { page: 1, perPage: 10_000 },
        filter: { brBillingCycle: { id: parseInt(id, 10) } },
        sort: { field: 'dueDt', order: 'DESC' },
      })
      .then(
        (res) => {
          setInvoices(res.data);
          setInvoicesLoading(false);
        },
        (err) => {
          console.error(err);
          setInvoicesLoading(false);
          notify('Error fetching invoices.', { type: 'error' });
        }
      );
  };

  if (!invoices && !invoicesLoading) {
    fetchInvoices();
  }

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="billingMonth" fullWidth />
              <TextInput source="generationMonth" fullWidth />
              <DateInput source="invoicedDt" fullWidth />
              <ReferenceInput
                source="salesforceProject.id"
                reference="SalesforceProject"
                perPage={10_000}
                sort={{ field: 'name', order: 'ASC' }}
              >
                <SelectInput optionText="name" label="Project" fullWidth />
              </ReferenceInput>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  return (
                    <ReferenceArrayInput
                      source="tusdInvoiceIds"
                      reference="TUSDInvoiceLite"
                      fullWidth
                      sort={{ field: 'id', order: 'DESC' }}
                      filter={{
                        salesforceProject: {
                          id: formData?.salesforceProject?.id,
                        },
                      }}
                      perPage={10_000}
                    >
                      <SelectArrayInput
                        optionText="label"
                        fullWidth
                        label="TUSD Invoices"
                      />
                    </ReferenceArrayInput>
                  );
                }}
              </FormDataConsumer>
              <ReferenceInput
                source="brRateio.id"
                reference="BrRateio"
                perPage={10_000}
                sort={{ field: 'id', order: 'ASC' }}
              >
                <SelectInput optionText="label" label="Rateio" fullWidth />
              </ReferenceInput>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Invoices">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Typography variant="h6">
                Invoices {invoicesLoading ? '' : `(${invoices?.length || 0})`}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              {invoicesLoading ? (
                <CircularProgress />
              ) : (
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <b>Consumer Unit</b>
                      </TableCell>
                      <TableCell>
                        <b>Due Date</b>
                      </TableCell>
                      <TableCell>
                        <b>Amount Due</b>
                      </TableCell>
                      <TableCell>
                        <b>Amount Paid</b>
                      </TableCell>
                      <TableCell>
                        <b>Status</b>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {invoices?.map((invoice) => (
                      <TableRow
                        component={Link}
                        to={`/BrInvoice/${invoice.id}`}
                        hover
                      >
                        <TableCell
                          component={Link}
                          to={`/BrConsumerUnit/${invoice.brConsumerUnit?.id}`}
                        >
                          {invoice.brConsumerUnit?.name}
                        </TableCell>
                        <TableCell>
                          {moment(invoice.dueDt).format('MMM D, YYYY')}
                        </TableCell>
                        <TableCell>
                          {numeral(invoice.amountDue).format('$0,0.00')}
                        </TableCell>
                        <TableCell>
                          {numeral(invoice.amountPaid).format('$0,0.00')}
                        </TableCell>
                        <TableCell>
                          <Grid
                            container
                            direction="column"
                            justifyContent="center"
                            alignItems="center"
                          >
                            <Grid item>
                              <Avatar
                                style={{
                                  backgroundColor:
                                    invoice.paymentStatus.iconColor,
                                }}
                              >
                                <Icon
                                  className={invoice.paymentStatus.iconClass}
                                  style={{
                                    color: '#fff',
                                    width: 'auto',
                                  }}
                                />
                              </Avatar>
                            </Grid>
                            <Grid item>
                              <Typography variant="body2" align="center">
                                {invoice.paymentStatus.label}
                              </Typography>
                            </Grid>
                          </Grid>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const BrBillingCycleFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="salesforceProject.id"
      reference="SalesforceProject"
      perPage={10_000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const BrBillingCycleList = () => {
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const refresh = useRefresh();
  const notify = useNotify();
  const [loading, setLoading] = useState(false);

  const isIT = permissions?.roles?.indexOf('ITWrite') > -1;

  const handleSendInvoiceEmails = async (id) => {
    setLoading(true);
    dataProvider
      .update('BrBillingCycle', {
        data: {
          id,
          sendInvoiceEmails: true,
        },
      })
      .then(
        () => {
          notify('Invoice emails sent', { type: 'success' });
          refresh();
          setLoading(false);
        },
        (err) => {
          console.error(err);
          notify('Error sending invoice emails', { type: 'error' });
          setLoading(false);
        }
      );
  };

  return (
    <List
      title={entityName}
      perPage={25}
      filters={<BrBillingCycleFilter />}
      sort={{ field: 'id', order: 'desc' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="SalesforceProject"
          linkSource="salesforceProject.id"
          labelSource="salesforceProject.name"
          label="Project"
        />
        <TextField source="generationMonth" />
        <TextField source="billingMonth" />
        <DateField source="invoicedDt" />
        <NumberField
          source="totalGrossConsumption"
          label="Total gross consumption"
        />
        <NumberField
          source="maxPotentialEnergeaGrossConsumption"
          label="Max potential energy purchased from Energea"
        />
        <ArrayField source="tusdInvoices" label="TUSD Invoices">
          <SingleFieldList>
            <CustomReferenceField source="label" />
          </SingleFieldList>
        </ArrayField>
        <LinkField
          reference="BrRateio"
          linkSource="brRateio.id"
          labelSource="brRateio.label"
          label="Rateio"
        />
        {/* {isIT ? ( */}
        <FunctionField
          label="Send invoice emails"
          render={(record) => {
            if (record.id < 561) return null;
            return (
              <Button
                onClick={(event) => {
                  event.stopPropagation();
                  event.preventDefault();
                  handleSendInvoiceEmails(record.id);
                }}
                color="primary"
                variant="contained"
                disabled={
                  loading ||
                  record.unsentStripeInvoiceCount === 0 ||
                  record.emailsPendingFlg
                }
              >
                {record.emailsPendingFlg
                  ? 'Email sending in progress'
                  : `Send ${record.unsentStripeInvoiceCount} Invoice Emails`}
              </Button>
            );
          }}
        />
        {/* ) : null} */}
      </Datagrid>
    </List>
  );
};

const attrs = [
  {
    name: 'installationCode',
    format: (val) => String(val),
    label: 'Código instalação',
    align: 'left',
    validate: (val) => !!val,
  },
  {
    name: 'injectedElectricity',
    format: (val) => val,
    label: 'Energia Injetada',
    align: 'right',
    validate: (val) => val || val === 0,
  },
  {
    name: 'grossConsumption',
    format: (val) => val,
    label: 'Consumo Total',
    align: 'right',
    validate: (val) => val || val === 0,
  },
  {
    name: 'utilityElectricitySupplied',
    format: (val) => val,
    label: 'Energia Fornecida',
    align: 'right',
    validate: (val) => val || val === 0,
  },
  {
    name: 'peakUCBalance',
    format: (val) => val,
    label: 'Saldo P',
    align: 'right',
    validate: (val) => val || val === 0,
  },
  {
    name: 'offPeakUCBalance',
    format: (val) => val,
    label: 'Saldo FP',
    align: 'right',
    validate: (val) => val || val === 0,
  },
];

const months = [
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];

export const BrBillingCycleCreate = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const redirect = useRedirect();
  const refresh = useRefresh();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [reviewed, setReviewed] = useState(false);
  const [
    selectedSelfConsumptionOffTakerId,
    setSelectedSelfConsumptionOffTakerId,
  ] = useState(null);
  const [uploadErrors, setUploadErrors] = useState(false);
  const [billingReferenceMonth, setBillingReferenceMonth] = useState(null);
  const [billingReferenceYear, setBillingReferenceYear] = useState(null);
  const [generationReferenceMonth, setGenerationReferenceMonth] =
    useState(null);
  const [generationReferenceYear, setGenerationReferenceYear] = useState(null);
  const [amountPaid, setAmountPaid] = useState(0);
  const [amountDue, setAmountDue] = useState(0);
  const [selfConsumptionOmRevenue, setSelfConsumptionOmRevenue] =
    useState(null);
  const [selfConsumptionLandRevenue, setSelfConsumptionLandRevenue] =
    useState(null);
  const [selfConsumptionEquipmentRevenue, setSelfConsumptionEquipmentRevenue] =
    useState(null);
  const [selfConsumptionUmbrellaRevenue, setSelfConsumptionUmbrellaRevenue] =
    useState(null);
  const [
    selfConsumptionOmRevenueApproved,
    setSelfConsumptionOmRevenueApproved,
  ] = useState(null);
  const [
    selfConsumptionLandRevenueApproved,
    setSelfConsumptionLandRevenueApproved,
  ] = useState(null);
  const [
    selfConsumptionEquipmentRevenueApproved,
    setSelfConsumptionEquipmentRevenueApproved,
  ] = useState(null);
  const [
    selfConsumptionUmbrellaRevenueApproved,
    setSelfConsumptionUmbrellaRevenueApproved,
  ] = useState(null);

  const calculatedAmountPaid =
    parseFloat(selfConsumptionOmRevenue || 0) +
    parseFloat(selfConsumptionLandRevenue || 0) +
    parseFloat(selfConsumptionEquipmentRevenue || 0) +
    parseFloat(selfConsumptionUmbrellaRevenue || 0);
  if (calculatedAmountPaid !== amountPaid) {
    setAmountPaid(calculatedAmountPaid);
  }

  const calculatedAmountDue =
    parseFloat(selfConsumptionOmRevenueApproved || 0) +
    parseFloat(selfConsumptionLandRevenueApproved || 0) +
    parseFloat(selfConsumptionEquipmentRevenueApproved || 0) +
    parseFloat(selfConsumptionUmbrellaRevenueApproved || 0);
  if (calculatedAmountDue !== amountDue) {
    setAmountDue(calculatedAmountDue);
  }

  const save = () => {
    setLoading(true);
    dataProvider
      .create('BrSelfConsumptionBillingCycleUpload', {
        data: {
          brSelfConsumptionOfftakerId: selectedSelfConsumptionOffTakerId,
          billingMonth: moment(
            `${billingReferenceMonth}-${billingReferenceYear}-1`,
            'M-YYYY'
          ).format('YYYY-MM-DD'),
          generationMonth: moment(
            `${generationReferenceMonth}-${generationReferenceYear}-1`,
            'M-YYYY'
          ).format('YYYY-MM-DD'),
          amountPaid: parseFloat(amountPaid),
          amountDue: parseFloat(amountDue),
          selfConsumptionOmRevenue: parseFloat(selfConsumptionOmRevenue || 0),
          selfConsumptionLandRevenue: parseFloat(
            selfConsumptionLandRevenue || 0
          ),
          selfConsumptionEquipmentRevenue: parseFloat(
            selfConsumptionEquipmentRevenue || 0
          ),
          selfConsumptionUmbrellaRevenue: parseFloat(
            selfConsumptionUmbrellaRevenue || 0
          ),
          selfConsumptionOmRevenueApproved: parseFloat(
            selfConsumptionOmRevenueApproved || 0
          ),
          selfConsumptionLandRevenueApproved: parseFloat(
            selfConsumptionLandRevenueApproved || 0
          ),
          selfConsumptionEquipmentRevenueApproved: parseFloat(
            selfConsumptionEquipmentRevenueApproved || 0
          ),
          selfConsumptionUmbrellaRevenueApproved: parseFloat(
            selfConsumptionUmbrellaRevenueApproved || 0
          ),
          brCreditCompensations: data,
        },
      })
      .then(
        (res) => {
          setLoading(false);
          notify('Billing cycle uploaded.', { type: 'success' });
          setData(null);
          setReviewed(false);
          redirect('/BrBillingCycle');
        },
        (e) => {
          setLoading(false);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
          refresh();
        }
      );
  };

  const handleData = (data) => {
    setUploadErrors(false);
    const stringFields = ['installationCode'];

    const lintedData = data.map((row) => {
      const lintedRow = {};
      Object.keys(row).forEach((key) => {
        const entryAttrData = findWithAttr(attrs, 'label', key);
        if (!entryAttrData) {
          console.log('Missing attr detected', key);
        } else {
          let lintedValue = row[String(key)];
          if (stringFields.indexOf(entryAttrData.name) > -1) {
            lintedValue = String(lintedValue);
          }
          lintedRow[entryAttrData.name] = lintedValue;
        }
      });
      return lintedRow;
    });
    setData(lintedData);
  };

  const renderSubmit = () => {
    return (
      <Alert severity={!reviewed ? 'warning' : 'success'}>
        <FormControlLabel
          control={
            <Checkbox
              checked={!!reviewed}
              onChange={() => setReviewed(!reviewed)}
              disabled={
                uploadErrors ||
                !selectedSelfConsumptionOffTakerId ||
                (!amountPaid && amountPaid !== 0) ||
                (!amountDue && amountDue !== 0) ||
                !billingReferenceMonth ||
                !billingReferenceYear ||
                !generationReferenceMonth ||
                !generationReferenceYear
              }
            />
          }
          label="I have checked the list and all values look good"
        />
        <Button
          onClick={save}
          disabled={
            !reviewed ||
            loading ||
            uploadErrors ||
            !selectedSelfConsumptionOffTakerId ||
            (!amountPaid && amountPaid !== 0) ||
            (!amountDue && amountDue !== 0) ||
            !billingReferenceMonth ||
            !billingReferenceYear ||
            !generationReferenceMonth ||
            !generationReferenceYear
          }
          variant="contained"
          size="large"
          color="secondary"
        >
          {loading ? <CircularProgress /> : 'Save'}
        </Button>
      </Alert>
    );
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate ? attr.validate(val, row) : true;
              if (!errors && !validated) {
                errors = true;
                if (!uploadErrors) {
                  setUploadErrors(true);
                }
              }
              const lintedVal = attr.format ? attr.format(val) : val;
              return (
                <TableCell
                  align={attr.align || 'center'}
                  style={{ backgroundColor: validated ? null : 'red' }}
                >
                  {lintedVal}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            {/* <TextInput source="billingMonth" fullWidth />
            <TextInput source="generationMonth" fullWidth />
            <DateInput source="invoicedDt" fullWidth />
            <ReferenceInput
              source="salesforceProject.id"
              reference="SalesforceProject"
              perPage={10_000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput optionText="name" label="Project" fullWidth />
            </ReferenceInput>
            <ReferenceInput
              source="brRateio.id"
              reference="BrRateio"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput optionText="label" label="Rateio" fullWidth />
            </ReferenceInput>
            <Divider style={{ margin: '2rem 0' }} />
             */}
            <Typography variant="h6" gutterBottom>
              Upload Self Consumption Billing Cycle
            </Typography>
            <Grid container item xs={12} direction="column">
              <Grid item>
                <ReferenceInput
                  perPage={10_000}
                  source="brSelfConsumptionOfftaker.id"
                  reference="BrSelfConsumptionOfftaker"
                  sort={{ field: 'id', order: 'ASC' }}
                >
                  <AutocompleteInput
                    label="Self-Consumption Offtaker"
                    onChange={(value) => {
                      setSelectedSelfConsumptionOffTakerId(value);
                    }}
                    optionText="name"
                  />
                </ReferenceInput>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <FormControl margin="normal" required>
                <Typography>Select the billing month and year:</Typography>
                <Grid container spacing={2}>
                  <Grid item>
                    <Select
                      label="month"
                      value={billingReferenceMonth || ''}
                      onChange={(event) =>
                        setBillingReferenceMonth(event.target.value)
                      }
                      style={{ width: '150px' }}
                    >
                      {months.map((month) => (
                        <MenuItem
                          value={month.value}
                          key={`month-${month.value}`}
                        >
                          {month.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </Grid>
                  <Grid item>
                    <Select
                      label="month"
                      value={billingReferenceYear || ''}
                      onChange={(event) =>
                        setBillingReferenceYear(event.target.value)
                      }
                    >
                      {[
                        moment().year() - 3,
                        moment().year() - 2,
                        moment().year() - 1,
                        moment().year(),
                        moment().year() + 1,
                      ].map((year) => (
                        <MenuItem value={year} key={`year-${year}`}>
                          {year}
                        </MenuItem>
                      ))}
                    </Select>
                  </Grid>
                </Grid>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl margin="normal" required>
                <Typography>Select the generation month and year:</Typography>
                <Grid container spacing={2}>
                  <Grid item>
                    <Select
                      label="month"
                      value={generationReferenceMonth || ''}
                      onChange={(event) =>
                        setGenerationReferenceMonth(event.target.value)
                      }
                      style={{ width: '150px' }}
                    >
                      {months.map((month) => (
                        <MenuItem
                          value={month.value}
                          key={`month-${month.value}`}
                        >
                          {month.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </Grid>
                  <Grid item>
                    <Select
                      label="month"
                      value={generationReferenceYear || ''}
                      onChange={(event) =>
                        setGenerationReferenceYear(event.target.value)
                      }
                    >
                      {[
                        moment().year() - 3,
                        moment().year() - 2,
                        moment().year() - 1,
                        moment().year(),
                        moment().year() + 1,
                      ].map((year) => (
                        <MenuItem value={year} key={`year-${year}`}>
                          {year}
                        </MenuItem>
                      ))}
                    </Select>
                  </Grid>
                </Grid>
              </FormControl>
            </Grid>
            <Typography gutterBottom style={{ marginTop: '1rem' }}>
              Issued Revenue:
            </Typography>
            <Grid item xs={12}>
              <MuiTextField
                label="Land Revenue (Issued)"
                value={selfConsumptionLandRevenue}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionLandRevenue(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="O&M Revenue (Issued)"
                value={selfConsumptionOmRevenue}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionOmRevenue(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="Equipment Revenue (Issued)"
                value={selfConsumptionEquipmentRevenue}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionEquipmentRevenue(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="Umbrella Revenue (Issued)"
                value={selfConsumptionUmbrellaRevenue}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionUmbrellaRevenue(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="Amount Paid"
                value={amountPaid}
                type="number"
                disabled
              />
            </Grid>
            <Typography gutterBottom style={{ marginTop: '1rem' }}>
              Approved Revenue:
            </Typography>
            <Grid item xs={12}>
              <MuiTextField
                label="Land Revenue (Approved)"
                value={selfConsumptionLandRevenueApproved}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionLandRevenueApproved(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="O&M Revenue (Approved)"
                value={selfConsumptionOmRevenueApproved}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionOmRevenueApproved(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="Equipment Revenue (Approved)"
                value={selfConsumptionEquipmentRevenueApproved}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionEquipmentRevenueApproved(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="Umbrella Revenue (Approved)"
                value={selfConsumptionUmbrellaRevenueApproved}
                type="number"
                onChange={(event) =>
                  setSelfConsumptionUmbrellaRevenueApproved(event.target.value)
                }
              />
            </Grid>
            <Grid item xs={12}>
              <MuiTextField
                label="Amount Due"
                value={amountDue}
                type="number"
                disabled
              />
            </Grid>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Button
                component="a"
                variant="contained"
                href="/csv-templates/selfConsumptionBillingUpload.xlsx"
                download
              >
                <GetApp />
                Click to download the excel template
              </Button>
            </Grid>
            <ExcelReader handleData={handleData} />
            <Grid item style={{ margin: 'auto' }}>
              {data ? renderSubmit() : null}
            </Grid>
            <Grid item xs={12}>
              {data ? renderData() : null}
            </Grid>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
