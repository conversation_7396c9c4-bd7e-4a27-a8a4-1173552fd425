import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Br Benefit';

export const BrBenefitEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput multiline source="entryEN" fullWidth />
            <TextInput multiline source="entryPT" fullWidth />
            <TextInput source="iconClass" fullWidth />
            <CustomNumberInput source="orderNo" step={1} fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrBenefitList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <NumberField source="orderNo" />
        <TextField source="entryEN" />
        <TextField source="entryPT" />
        <TextField source="iconClass" />
      </Datagrid>
    </List>
  );
};

export const BrBenefitCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput multiline source="entryEN" fullWidth />
          <TextInput multiline source="entryPT" fullWidth />
          <TextInput source="iconClass" fullWidth />
          <CustomNumberInput source="orderNo" step={1} fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
