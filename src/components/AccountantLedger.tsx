import React from 'react';
import moment from 'moment';
import jsonExport from 'jsonexport/dist';

import {
  Datagrid,
  DateInput,
  downloadCSV,
  Filter,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  TextField,
  TextInput,
  usePermissions,
} from 'react-admin';
import { LinkField } from './CustomFields';

const entityName = 'Accountant Ledger';

const exporter = (rows) => {
  const rowsForExport = rows.map((row) => {
    const returnRow = {};
    const dates = ['startDt', 'date', 'sellDt'];
    const attrs = [
      'id',
      'user',
      'portfolio',
      // 'oktaUser',
      'dividends',
      'investments',
      'shareTransfers',
      'firstName',
      'lastName',
      'address1',
      'address2',
      'city',
      'state',
      'postalCode',
      'countryCode',
      'equity',
      'totalInvested',
      'totalNetOwnedShares',
      'totalGrossOwnedShares',
      'totalGrossEarned',
      'totalDividends',
      'totalSoldShares',
      'totalSoldValue',
    ];
    const subAttrs = {
      dividends: ['date', 'value'],
      investments: ['startDt', 'shares', 'value'],
      shareTransfers: ['soldShares', 'value', 'sellDt'],
    };
    Object.keys(row).forEach((attr) => {
      if (attrs.indexOf(attr) === -1) {
        return;
      }
      const data = row[String(attr)];
      if (dates.indexOf(attr) > -1) {
        returnRow[String(attr)] = moment(data).format('MM/DD/YYYY');
        return;
      }
      if (attr === 'user') {
        const { id, __typename, oktaUser, ...allOtherAttrs } = data;
        allOtherAttrs.postalCode = `${allOtherAttrs.postalCode}`;
        allOtherAttrs.email = allOtherAttrs.email;
        returnRow[String(attr)] = allOtherAttrs;
        return;
      }
      if (attr === 'oktaUser') {
        const {
          profile: { email, ssn },
        } = data;
        returnRow[String(attr)] = {
          email,
          ssn,
        };
        return;
      }
      if (attr === 'portfolio') {
        const { id, __typename, ...allOtherAttrs } = data;
        returnRow[String(attr)] = allOtherAttrs;
        return;
      }
      if (Array.isArray(data)) {
        const subData = data.map((subDataRow) => {
          const returnSubRow = {};
          Object.keys(subDataRow).forEach((subDataRowKey) => {
            const subDataAttrData = subDataRow[String(subDataRowKey)];
            if (
              !subAttrs[String(attr)] ||
              subAttrs[String(attr)].indexOf(subDataRowKey) === -1
            ) {
              return;
            }
            if (dates.indexOf(subDataRowKey) > -1) {
              returnSubRow[String(subDataRowKey)] =
                moment(subDataAttrData).format('MM/DD/YYYY');
              return;
            }
            returnSubRow[String(subDataRowKey)] = subDataAttrData;
          });
          return returnSubRow;
        });
        returnRow[String(attr)] = subData;
        return;
      }
      returnRow[String(attr)] = data;
    });
    return returnRow;
  });
  jsonExport(
    rowsForExport,
    {
      headers: ['id'],
    },
    (err, csv) => {
      downloadCSV(csv, entityName); // download as 'posts.csv` file
    }
  );
};

const AccountantLedgerFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <DateInput label="End Date" source="endDt" />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10_000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const AccountantLedgerList = () => {
  return (
    <List
      exporter={exporter}
      title={entityName}
      filters={<AccountantLedgerFilter />}
      pagination={false}
    >
      <Datagrid>
        <TextField source="id" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <NumberField source="equity" sortable={false} />
        <NumberField
          source="totalInvested"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField source="totalNetOwnedShares" sortable={false} />
        <NumberField source="totalGrossOwnedShares" sortable={false} />
        <NumberField
          source="totalGrossEarned"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField
          source="totalDividends"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <NumberField source="totalSoldShares" sortable={false} />
        <NumberField
          source="totalSoldValue"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
      </Datagrid>
    </List>
  );
};
