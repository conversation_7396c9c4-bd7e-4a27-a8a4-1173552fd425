import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  Create,
  Datagrid,
  Edit,
  ImageField,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Event Author';

export const EventAuthorEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput fullWidth source="name" />
            <TextInput fullWidth source="title" />
            <TextInput
              fullWidth
              source="avatarImagePublicId"
              label="Avatar image publicId"
            />
            <TextInput
              fullWidth
              source="emailSignatureImagePublicId"
              label="Email signature image publicId"
            />
          </Grid>
        </Grid>
        <ArrayField fullWidth source="events">
          <Datagrid>
            <TextField source="id" />
            <TextField source="title" />
          </Datagrid>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const EventAuthorList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="title" />
        <ImageField
          label="Email Signature Image"
          source="emailSignatureImageUrl"
          title="email signature image"
          sortable={false}
        />
        <ImageField
          label="Avatar Image"
          source="avatarImageUrl"
          title="avatar image"
          sortable={false}
        />
      </Datagrid>
    </List>
  );
};

export const EventAuthorCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
