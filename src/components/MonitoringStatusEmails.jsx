import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  ArrayInput,
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  ReferenceArrayInput,
  SelectArrayInput,
  SimpleForm,
  SimpleFormIterator,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useRecordContext,
  useResourceDefinition,
  useDataProvider,
  useNotify,
} from 'react-admin';

import { Alert, Button, Chip, Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

import { CustomReferenceField } from './CustomFields';

const entityName = 'Monitoring Status Email';

export const MonitoringStatusEmailEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="name"
              fullWidth
              helperText="Internal name to help organize the email groups."
            />
            <ArrayInput
              source="emailAddresses"
              // helperText=""
            >
              <SimpleFormIterator
                TransitionProps={{ enter: false, exit: false }}
              >
                <TextInput />
              </SimpleFormIterator>
            </ArrayInput>
            <ReferenceArrayInput
              source="projectIds"
              reference="Project"
              helperText="These are the projects to be included in the daily report"
              fullWidth
              sort={{ field: 'name', order: 'ASC' }}
              perPage={10000}
            >
              <SelectArrayInput optionText="name" fullWidth />
            </ReferenceArrayInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomTextField = () => {
  const record = useRecordContext();
  return <Chip style={{ margin: '4px' }} label={record} />;
};

export const MonitoringStatusEmailList = () => {
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  return (
    <List title={entityName} perPage={25}>
      <Alert style={{ borderRadius: 0 }} severity="info">
        This table determines who gets emailed the daily monitoring status
        updates each morning. Reports are sent at 6:00 am EST and the report
        contains the projects listed in the projects column.
      </Alert>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <ArrayField source="projects" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField
              // color={(resource) => (resource.isPublic ? 'primary' : 'default')}
              source="name"
            />
          </SingleFieldList>
        </ArrayField>
        <ArrayField source="emailAddresses" sortable={false}>
          <SingleFieldList>
            <CustomTextField />
          </SingleFieldList>
        </ArrayField>
        <FunctionField
          render={(record) => (
            <Button
              variant="contained"
              color="primary"
              onClick={(event) => {
                event.stopPropagation();
                event.preventDefault();
                // send report
                dataProvider
                  .update('MonitoringStatusEmail', {
                    data: {
                      id: parseInt(record.id, 10),
                      manuallyEmailReport: true,
                    },
                  })
                  .catch((e) => {
                    console.error('ERROR', e);
                    notify('Error sending report', { type: 'error' });
                  })
                  .then(() => {
                    notify('Successfully started the report email process');
                  });
              }}
            >
              Manually Send Report
            </Button>
          )}
        />
      </Datagrid>
    </List>
  );
};

export const MonitoringStatusEmailCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="name"
            helperText="Choose a name that describes the group of projects / recipients for internal use only."
            required
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
