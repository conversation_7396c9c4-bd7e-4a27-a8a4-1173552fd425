import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Rateio Line Item';

export const BrRateioLineItemEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <CustomNumberInput source="allocationPercentage" fullWidth />
            <ReferenceInput
              source="brRateio.id"
              reference="BrRateio"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
              required
            >
              <SelectInput
                optionText="label"
                label="Rateio"
                fullWidth
                required
              />
            </ReferenceInput>
            <ReferenceInput
              perPage={10_000}
              source="brConsumerUnit.id"
              sortable={false}
              reference="BrConsumerUnitLite"
              required
            >
              <AutocompleteInput
                label="Consumer Unit"
                required
                fullWidth
                optionText="name"
                shouldRenderSuggestions={(value) => true}
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrRateioLineItemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrRateio"
          linkSource="brRateio.id"
          labelSource="brRateio.label"
          label="Rateio"
        />
        <LinkField
          reference="BrConsumerUnit"
          linkSource="brConsumerUnit.id"
          labelSource="brConsumerUnit.name"
          label="Consumer Unit"
        />
        <NumberField source="allocationPercentage" />
      </Datagrid>
    </List>
  );
};

export const BrRateioLineItemCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <CustomNumberInput source="allocationPercentage" fullWidth />
          <ReferenceInput
            source="brRateio.id"
            reference="BrRateio"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <SelectInput optionText="label" label="Rateio" fullWidth required />
          </ReferenceInput>
          <ReferenceInput
            perPage={10_000}
            source="brConsumerUnit.id"
            sortable={false}
            reference="BrConsumerUnitLite"
            required
          >
            <AutocompleteInput
              label="Consumer Unit"
              required
              fullWidth
              optionText="name"
              shouldRenderSuggestions={(value) => true}
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
