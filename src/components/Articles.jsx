import React from 'react';
import { useParams } from 'react-router-dom';

import {
  ArrayField,
  ArrayInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SimpleFormIterator,
  SingleFieldList,
  TextField,
  TextInput,
  UrlField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Alert, Button, Grid } from '@mui/material';

import { CustomReferenceField, DetailField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { ArrowForward } from '@mui/icons-material';

const entityName = 'Article';

export const ArticleEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput required source="title" fullWidth />
            <TextInput source="summary" fullWidth />
            <DateInput source="date" label="Posted date" fullWidth />
            <TextInput
              source="readTime"
              helperText="Ex: '5 min read'"
              fullWidth
            />
            <TextInput
              source="slug"
              helperText="Ex: /how-to-invest-in-solar"
              fullWidth
            />
            <BooleanInput
              source="inactiveFlg"
              helperText="Turning this on will hide this article from the articles page but it will still be reachable at its slug"
              fullWidth
            />
            <BooleanInput
              source="pendingDevelopmentFlg"
              helperText="This flag is used to flag articles that still need work from the IT team before going live"
              fullWidth
            />
            <ReferenceInput source="eventAuthor.id" reference="EventAuthor">
              <SelectInput
                label="Author"
                fullWidth
                helperText="This will listed as the author of the article. If you do not see the person you want to select, get Gray (<EMAIL>) to make one."
                optionText="name"
              />
            </ReferenceInput>
            <ArrayInput
              source="articleCategories"
              fullWidth
              label="Article Categories"
            >
              <SimpleFormIterator
                TransitionProps={{ enter: false, exit: false }}
              >
                <BooleanInput
                  source="primaryFlg"
                  label="Primary Category Flag"
                  fullWidth
                  helperText="The primary category will be displayed first"
                />
                <ReferenceInput source="id" reference="ArticleCategory">
                  <SelectInput
                    label="ArticleCategory"
                    required
                    fullWidth
                    optionText="name"
                  />
                </ReferenceInput>
              </SimpleFormIterator>
            </ArrayInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const ArticleFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Article Name"
      source="q"
      alwaysOn
    />
    <BooleanInput
      source="inactiveFlg"
      label="Hide from Articles Page Flg"
      defaultValue={false}
    />
    <BooleanInput
      source="pendingDevelopmentFlg"
      label="Pending IT Development Flg"
      defaultValue={false}
    />
    <ReferenceInput source="articleCategory.id" reference="ArticleCategory">
      <SelectInput label="Category" fullWidth optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const ArticleList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <Grid
        container
        style={{ width: '100%', marginTop: '1rem' }}
        component={Alert}
        severity="info"
      >
        <p>
          To make any changes to live articles, reach out to{' '}
          <a href="mailto:<EMAIL>"><EMAIL></a> so that the
          articles remain SEO optimized.
        </p>
        <p>Visit the link below to view our blog post pipeline:</p>
        <Button
          variant="text"
          endIcon={<ArrowForward />}
          style={{ textTransform: 'none' }}
        >
          <a
            href="https://drive.google.com/drive/u/1/folders/11nVf5Zn3zAwX9BS4NK3NBBnBP-vZj83k"
            target="_blank"
          >
            View Blog Post Drafts
          </a>
        </Button>
      </Grid>
      <List
        title={entityName}
        perPage={25}
        sort={{ field: 'id', order: 'DESC' }}
        filters={<ArticleFilter />}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="title" />
          <DetailField source="summary" />
          <UrlField source="url" />
          <DateField source="date" label="Posted date" />
          <TextField source="readTime" />
          <LinkField
            reference="EventAuthor"
            linkSource="eventAuthor.id"
            labelSource="eventAuthor.name"
            label="Author"
          />
          <BooleanField
            source="inactiveFlg"
            label="Hide from Articles Page Flg"
          />
          <BooleanField
            source="pendingDevelopmentFlg"
            label="Pending IT Development Flg"
          />
          <ArrayField source="articleCategories">
            <SingleFieldList>
              <CustomReferenceField source="name" />
            </SingleFieldList>
          </ArrayField>
        </Datagrid>
      </List>
    </>
  );
};

export const ArticleCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="title" required fullWidth />
          <TextInput source="summary" fullWidth />
          <TextInput
            source="slug"
            helperText="Ex: /how-to-invest-in-solar"
            fullWidth
          />
          <TextInput
            source="readTime"
            helperText="Ex: '5 min read'"
            fullWidth
          />
          <DateInput source="date" label="Posted date" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
