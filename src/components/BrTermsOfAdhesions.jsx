import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  ArrayField,
  AutocompleteInput,
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  Filter,
  FormDataConsumer,
  FunctionField,
  Labeled,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import moment from 'moment';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  TextField as MuiTextField,
  Typography,
} from '@mui/material';
import {
  AddCircleOutline,
  CloudDownload,
  CloudUpload,
  Email,
} from '@mui/icons-material';

import {
  // CustomNumberInput,
  CustomReferenceField,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Terms of Adhesion';

export const BrTermsOfAdhesionEdit = () => {
  const { id } = useParams();
  const [toaUploading, setToaUploading] = useState(false);
  const [
    renegotiateDiscountRateDialogOpen,
    setRenegotiateDiscountRateDialogOpen,
  ] = useState(false);
  const [newDiscountRate, setNewDiscountRate] = useState(0);
  const [loading, setLoading] = useState(false);
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();
  const redirect = useRedirect();

  const uploadTermsOfAdhesionToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `TermsOfAdhesion/${formData.id}/Termo_de_Adesão_${moment().valueOf()}`
      );
      setToaUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          dataProvider
            .update('BrTermsOfAdhesion', {
              data: {
                id: parseInt(id, 10),
                awsObjectKey,
                signatureDt: new Date(),
              },
            })
            .then(
              (res) => {
                notify('Terms of Adhesion uploaded', { type: 'success' });
                setToaUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading terms of adhesion', { type: 'error' });
                setToaUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading terms of adhesion to S3', { type: 'error' });
        }
      );
    }
  };

  const prepareTermsOfAdhesionForm = () => {
    setToaUploading(true);
    dataProvider
      .create('BrTermsOfAdhesionForm', {
        data: {
          brTermsOfAdhesionId: parseInt(id, 10),
        },
      })
      .then(
        () => {
          setToaUploading(false);
          refresh();
          notify(
            'Terms of Adhesion form drafted. Contact will be asked to sign next time they log in.',
            { type: 'success' }
          );
        },
        (e) => {
          setToaUploading(false);
          console.error('Error drafting terms of adhesion form', e);
          let errorMsg = e;
          if (e.body?.graphQLErrors && e.body.graphQLErrors[0]) {
            errorMsg = e.body.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
          refresh();
        }
      );
  };

  const sendNewTermsOfAdhesionEmail = () => {
    setToaUploading(true);
    dataProvider
      .update('BrTermsOfAdhesion', {
        data: {
          id: parseInt(id, 10),
          sendUnsignedToaEmail: true,
        },
      })
      .then(
        (res) => {
          setToaUploading(false);
          refresh();
          notify('Email sent.', { type: 'success' });
        },
        (e) => {
          setToaUploading(false);
          console.error('Error sending email', e);
          let errorMsg = e;
          if (e.body?.graphQLErrors && e.body.graphQLErrors[0]) {
            errorMsg = e.body.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
          refresh();
        }
      );
  };

  const createPromoDiscount = () => {
    dataProvider
      .create('BrPromoDiscount', {
        data: {
          brTermsOfAdhesionId: parseInt(id, 10),
          discountRate: parseFloat(newDiscountRate),
          startDt: new Date(),
          renegotiationFlg: true,
        },
      })
      .then(
        (res) => {
          setLoading(false);
          notify('Renegotiated TOA created', { type: 'success' });
          refresh();
          redirect(`/BrConsumerUnit/${res.brConsumerUnitId}`);
        },
        (e) => {
          console.error(e);
          let errorMsg = e;
          if (e.body?.graphQLErrors && e.body.graphQLErrors[0]) {
            errorMsg = e.body.graphQLErrors[0].message;
          }
          notify(String(errorMsg), { type: 'error' });
          setLoading(false);
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <>
                    <ReferenceInput
                      perPage={10_000}
                      source="brConsumerUnit.id"
                      sortable={false}
                      reference="BrConsumerUnitLite"
                    >
                      <AutocompleteInput
                        label="Consumer Unit"
                        required
                        fullWidth
                        optionText="name"
                        shouldRenderSuggestions={(value) => true}
                        disabled={!!formData.signatureDt}
                      />
                    </ReferenceInput>
                    <ReferenceInput
                      source="brPowerPlan.id"
                      reference="BrPowerPlan"
                      perPage={10_000}
                      sort={{ field: 'discountRate', order: 'ASC' }}
                    >
                      <SelectInput
                        optionText="name"
                        label="Power Plan"
                        fullWidth
                        disabled={!!formData.signatureDt}
                        helperText={
                          formData.signatureDt ? (
                            <Typography variant="caption">
                              If the discount rate was renegotiated with the
                              client,{' '}
                              <a
                                onClick={() => {
                                  setRenegotiateDiscountRateDialogOpen(true);
                                }}
                                style={{ cursor: 'pointer' }}
                              >
                                click here
                              </a>{' '}
                              to change it. This will keep the current
                              commission structure and only change the discount
                              rate.
                            </Typography>
                          ) : null
                        }
                      />
                    </ReferenceInput>
                    <Dialog open={!!renegotiateDiscountRateDialogOpen}>
                      <DialogTitle>
                        Update Terms of Adhesion's Discount Rate
                      </DialogTitle>
                      <DialogContent>
                        <Grid container style={{ width: '100%' }}>
                          <Grid item xs={12}>
                            <MuiTextField
                              id="new-discount-rate-text-input"
                              label="New discount rate (Ex: 0.10)"
                              fullWidth
                              type="number"
                              value={newDiscountRate || ''}
                              onChange={(event) =>
                                setNewDiscountRate(
                                  Math.max(
                                    Math.min(parseFloat(event.target.value), 1),
                                    0
                                  )
                                )
                              }
                              style={{ marginBottom: '1rem' }}
                              InputProps={{
                                inputProps: {
                                  min: 0,
                                  max: 1,
                                },
                              }}
                            />
                          </Grid>
                        </Grid>
                      </DialogContent>
                      <DialogActions>
                        <Button
                          onClick={() =>
                            setRenegotiateDiscountRateDialogOpen(false)
                          }
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={() => createPromoDiscount()}
                          disabled={!newDiscountRate}
                          color="primary"
                          variant="contained"
                        >
                          Save
                        </Button>
                      </DialogActions>
                    </Dialog>
                    <FormDataConsumer>
                      {({ formData, ...rest }) => {
                        if (formData.brPromoDiscounts?.length) {
                          return (
                            <Labeled fullWidth>
                              <ArrayField
                                source="brPromoDiscounts"
                                label="Discount Rate Adjustments"
                              >
                                <SingleFieldList>
                                  <CustomReferenceField source="label" />
                                </SingleFieldList>
                              </ArrayField>
                            </Labeled>
                          );
                        }
                        return null;
                      }}
                    </FormDataConsumer>
                    <DateInput source="startDt" fullWidth />
                    <DateInput source="endDt" fullWidth />
                    <DateInput
                      source="signatureDt"
                      fullWidth
                      // disabled={!!formData.signatureDt}
                    />
                    <DateInput
                      source="signatureRequestedDt"
                      fullWidth
                      helperText="This is the date the contact was last emailed to sign the Terms of Adhesion. This is set automatically when an unsigned Terms of Adhesion is sent from this page or from the partner dashboard. This can also be manually set if you want to."
                    />
                    <DateInput
                      source="contractedTermEndDt"
                      fullWidth
                      helperText="The date that the terms of adhesion contract requires them to be a customer until if applicable."
                    />
                  </>
                );
              }}
            </FormDataConsumer>
            <FunctionField
              label="Terms of Adhesion"
              render={(record) => {
                return (
                  <Grid container spacing={2}>
                    {!record.signatureDt ? (
                      <Grid container item spacing={1}>
                        <Divider style={{ width: '100%', margin: '1rem 0' }} />
                        <Grid item>
                          <Typography variant="h6">
                            Unsigned Terms of Adhesion
                          </Typography>
                        </Grid>
                        <Grid item container>
                          <FormDataConsumer>
                            {({ formData, ...rest }) => {
                              if (formData.signatureDt) return null;
                              if (!formData.brConsumerUnit?.id) return null;
                              if (formData.endDt) return null;
                              return (
                                <Grid item container direction="column">
                                  <Grid item>
                                    <Button
                                      color="primary"
                                      variant="contained"
                                      startIcon={<AddCircleOutline />}
                                      style={{ textTransform: 'none' }}
                                      disabled={toaUploading}
                                      onClick={() => {
                                        prepareTermsOfAdhesionForm();
                                      }}
                                    >
                                      {toaUploading ? (
                                        <CircularProgress
                                          style={{ position: 'absolute' }}
                                        />
                                      ) : null}
                                      Prepare Terms of Adhesion for Signature
                                    </Button>
                                  </Grid>
                                  <Grid item>
                                    <Typography variant="body2">
                                      The next time a user signs in, they will
                                      be presented with this form to sign.
                                      {/* TODO: Question for Credit Mgmt teamL Should this also email the offtaker? */}
                                    </Typography>
                                  </Grid>
                                </Grid>
                              );
                            }}
                          </FormDataConsumer>
                        </Grid>
                        <Grid item container direction="column">
                          <Grid item>
                            <Button
                              disabled={
                                !record.pendingSignatureDownloadUrl ||
                                toaUploading
                              }
                              variant="contained"
                              startIcon={<CloudDownload />}
                              style={{ textTransform: 'none' }}
                              onClick={() =>
                                window.location.assign(
                                  record.pendingSignatureDownloadUrl
                                )
                              }
                            >
                              {toaUploading ? (
                                <CircularProgress
                                  style={{ position: 'absolute' }}
                                />
                              ) : null}
                              Download Unsigned ToA
                            </Button>
                          </Grid>
                          <Grid item>
                            <Typography variant="body2">
                              This is what the contact will be shown to sign.
                            </Typography>
                          </Grid>
                        </Grid>
                        <Grid item container>
                          <FormDataConsumer>
                            {({ formData, ...rest }) => {
                              if (!formData.brConsumerUnit?.id) return null;
                              if (formData.endDt) return null;
                              if (!formData.pendingAwsObjectKey) return null;
                              if (!formData.pendingSignatureDownloadUrl)
                                return null;
                              return (
                                <Grid item container direction="column">
                                  <Grid item>
                                    <Button
                                      color="primary"
                                      variant="contained"
                                      startIcon={<Email />}
                                      style={{ textTransform: 'none' }}
                                      disabled={toaUploading}
                                      onClick={() => {
                                        sendNewTermsOfAdhesionEmail();
                                      }}
                                    >
                                      {toaUploading ? (
                                        <CircularProgress
                                          style={{ position: 'absolute' }}
                                        />
                                      ) : null}
                                      Email Contact to Sign
                                      {formData.signatureRequestedDt
                                        ? ` (Sent: ${moment(
                                            formData.signatureRequestedDt
                                          ).format('MMM D, YYYY HH:mm')})`
                                        : ''}
                                    </Button>
                                  </Grid>
                                  <Grid item>
                                    <Typography variant="body2">
                                      This will send an email to the primary
                                      contact for this customer to log in to
                                      their account where they will sign their
                                      new Terms of Adhesion form.
                                    </Typography>
                                  </Grid>
                                </Grid>
                              );
                            }}
                          </FormDataConsumer>
                        </Grid>
                      </Grid>
                    ) : null}
                    <Grid item container spacing={1}>
                      <Divider style={{ width: '100%', margin: '1rem 0' }} />
                      <Grid item>
                        <Typography variant="h6">
                          Signed Terms of Adhesion
                        </Typography>
                      </Grid>
                      <Grid item container>
                        <FormDataConsumer>
                          {({ formData, ...rest }) => {
                            return (
                              <Grid item>
                                <Button
                                  color="primary"
                                  variant="contained"
                                  component="label" // https://stackoverflow.com/a/54043619
                                  startIcon={<CloudUpload />}
                                  style={{ textTransform: 'none' }}
                                  disabled={toaUploading}
                                >
                                  {toaUploading ? (
                                    <CircularProgress
                                      style={{ position: 'absolute' }}
                                    />
                                  ) : null}
                                  {record.downloadUrl
                                    ? 'Overwrite Signed Terms of Adhesion'
                                    : 'Upload Signed Terms of Adhesion'}
                                  <input
                                    type="file"
                                    hidden
                                    onChange={(event) =>
                                      uploadTermsOfAdhesionToS3(event, formData)
                                    }
                                    accept="application/pdf"
                                  />
                                </Button>
                                <Typography variant="body2">
                                  Upload a signed Terms of Adhesion.
                                </Typography>
                              </Grid>
                            );
                          }}
                        </FormDataConsumer>
                      </Grid>
                      <Grid item container>
                        <Button
                          disabled={!record.downloadUrl}
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={() =>
                            window.location.assign(record.downloadUrl)
                          }
                        >
                          Download Signed ToA
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Consumer Unit Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      <ExportButton maxResults={10_000} />
    </TopToolbar>
  );
};

const styleRow = (record) => {
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (record.signatureDt && !record.startDt) {
    return warningStyle;
  }
  if (
    record.startDt &&
    !record.endDt &&
    [4, 5].includes(record.brConsumerUnit?.brConsumerUnitStage?.id)
  ) {
    return warningStyle;
  }
  return {};
};

export const BrTermsOfAdhesionList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<CustomFilter />}
      actions={<ListActions />}
      sort={{ field: 'id', order: 'DESC' }}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrConsumerUnit"
          linkSource="brConsumerUnit.id"
          labelSource="brConsumerUnit.name"
          label="Consumer Unit"
        />
        <LinkField
          reference="BrConsumerUnitStage"
          linkSource="brConsumerUnit.brConsumerUnitStage.id"
          labelSource="brConsumerUnit.brConsumerUnitStage.name"
          label="Stage"
          sortable={false}
        />
        <LinkField
          reference="BrPowerPlan"
          linkSource="brPowerPlan.id"
          labelSource="brPowerPlan.name"
          label="Power Plan"
        />
        <DateField source="startDt" />
        <DateField source="endDt" />
        <DateField source="signatureDt" />
        <DateField source="signatureRequestedDt" />
        <DateField source="contractedTermEndDt" />
        <FunctionField
          label="Terms of Adhesion"
          render={(record) => {
            if (record.downloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.assign(record.downloadUrl);
                  }}
                >
                  Download
                </Button>
              );
            }
            return null;
          }}
        />
        <ArrayField
          source="brPromoDiscounts"
          label="Discount Rate Adjustments"
          sortable={false}
        >
          <SingleFieldList>
            <CustomReferenceField source="label" />
          </SingleFieldList>
        </ArrayField>
      </Datagrid>
    </List>
  );
};

export const BrTermsOfAdhesionCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={10_000}
            source="brConsumerUnit.id"
            sortable={false}
            reference="BrConsumerUnitLite"
          >
            <AutocompleteInput
              label="Consumer Unit"
              required
              fullWidth
              optionText="name"
              shouldRenderSuggestions={(value) => true}
            />
          </ReferenceInput>
          <ReferenceInput
            source="brPowerPlan.id"
            reference="BrPowerPlan"
            perPage={10_000}
            sort={{ field: 'discountRate', order: 'ASC' }}
          >
            <SelectInput optionText="name" label="Power Plan" fullWidth />
          </ReferenceInput>
          <DateInput source="startDt" fullWidth />
          <DateInput source="endDt" fullWidth />
          <DateInput source="signatureDt" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
