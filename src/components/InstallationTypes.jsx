import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  ArrayField,
  FunctionField,
  SingleFieldList,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Icon, Grid } from '@mui/material';
import { CustomReferenceField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Installation Type';
export const InstallationTypeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="iconClassName" fullWidth />
          </Grid>
        </Grid>
        <ArrayField sortable={false} source="projects">
          <SingleFieldList>
            <CustomReferenceField source="name" />
          </SingleFieldList>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const InstallationTypeList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <FunctionField
          label="Icon"
          sortable={false}
          render={(record) => (
            <Icon
              style={{ width: '2rem', textAlign: 'center', color: '#666' }}
              className={record.iconClassName}
            />
          )}
        />
        <TextField source="name" />
        <TextField source="iconClassName" />
      </Datagrid>
    </List>
  );
};

export const InstallationTypeCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
