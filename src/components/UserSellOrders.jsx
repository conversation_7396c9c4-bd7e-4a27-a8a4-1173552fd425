import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button, useDataProvider, useNotify } from 'react-admin';
import { useParams } from 'react-router-dom';
import {
  Alert,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { Add, Check, CloudDownload, Delete } from '@mui/icons-material';
import numeral from 'numeral';
import moment from 'moment';

export const UserSellOrders = () => {
  const [userSellOrders, setUserSellOrders] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();
  const { id } = useParams();

  const fetchUserSellOrders = () => {
    setLoading(true);
    dataProvider
      .getList('SellOrder', {
        filter: {
          userId: parseInt(id, 10),
        },
        sort: { field: 'id', order: 'desc' },
        pagination: { page: 1, perPage: 10_000 },
      })
      .then(
        (resp) => {
          setUserSellOrders(resp.data);
          setLoading(false);
          setError(null);
        },
        (e) => {
          setLoading(false);
          setError(e);
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  if (!userSellOrders && !loading) {
    fetchUserSellOrders();
  }

  if (loading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Alert severity="error">An error occurred</Alert>;
  }

  if (!userSellOrders || userSellOrders.length === 0) {
    return (
      <Alert severity="info">There are no investments for this user</Alert>
    );
  }
  return (
    <>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <b>Portfolio</b>
            </TableCell>
            <TableCell>
              <b>Sub Account</b>
            </TableCell>
            <TableCell>
              <b>Shares Requested to Sell</b>
            </TableCell>
            <TableCell>
              <b>Shares Sold</b>
            </TableCell>
            <TableCell>
              <b>Sold Value</b>
            </TableCell>
            <TableCell>
              <b>Requested Dt</b>
            </TableCell>
            <TableCell>
              <b>Completed Dt</b>
            </TableCell>
            <TableCell>
              <b>Cancelled Dt</b>
            </TableCell>
            <TableCell>
              <b>Is Early Exit?</b>
            </TableCell>
            <TableCell>
              <b>Edit</b>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {userSellOrders.map((sellOrder) => {
            return (
              <TableRow key={`user-sell-order-list-${sellOrder.id}`}>
                <TableCell>
                  <a href={`/Portfolio/${sellOrder.portfolio?.id}`}>
                    {sellOrder.portfolio?.name}
                  </a>
                </TableCell>
                <TableCell>
                  {sellOrder.subAccount?.id ? (
                    <a href={`/SubAccount/${sellOrder.subAccount?.id}`}>
                      {sellOrder.subAccount?.name}
                    </a>
                  ) : null}
                </TableCell>
                <TableCell>
                  {numeral(sellOrder.shares).format('0,0.0000')}
                </TableCell>
                <TableCell>
                  {numeral(sellOrder.soldShares).format('0,0.0000')}
                </TableCell>
                <TableCell>
                  {numeral(sellOrder.soldValue).format('$0,0.00')}
                </TableCell>
                <TableCell>
                  {moment(sellOrder.requestDt).format('MMM D, YYYY')}
                </TableCell>
                <TableCell>
                  {sellOrder.closedDt
                    ? moment(sellOrder.closedDt).format('MMM D, YYYY')
                    : null}
                </TableCell>
                <TableCell>
                  {sellOrder.cancelledDt
                    ? moment(sellOrder.cancelledDt).format('MMM D, YYYY')
                    : null}
                </TableCell>
                <TableCell>
                  {sellOrder.earlyExitFlg ? <Check /> : null}
                </TableCell>
                <TableCell>
                  <a href={`/SellOrder/${sellOrder.id}`}>
                    <EditIcon />
                  </a>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </>
  );

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Typography variant="h5">Beneficiaries</Typography>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <b>Account holder</b>
              </TableCell>
              <TableCell>
                <b>Beneficiary type</b>
              </TableCell>
              <TableCell>
                <b>Name</b>
              </TableCell>
              <TableCell>
                <b>Entity/Trust name</b>
              </TableCell>
              <TableCell>
                <b>DOB/formation date</b>
              </TableCell>
              <TableCell>
                <b>Email</b>
              </TableCell>
              <TableCell>
                <b>Phone</b>
              </TableCell>
              <TableCell>
                <b>Primary Flg</b>
              </TableCell>
              <TableCell>
                <b>Edit</b>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {userSellOrders?.map((beneficiary) => {
              return (
                <TableRow key={`beneficiary-list-${beneficiary.id}`}>
                  <TableCell>{beneficiary.user.fullName}</TableCell>
                  <TableCell>{beneficiary.type.name}</TableCell>
                  <TableCell>
                    {beneficiary.firstName} {beneficiary.lastName}
                  </TableCell>
                  <TableCell>
                    {beneficiary.entityName || beneficiary.trustName}
                  </TableCell>
                  <TableCell>
                    {beneficiary.dateOfBirth || beneficiary.formationDt}
                  </TableCell>
                  <TableCell>{beneficiary.email}</TableCell>
                  <TableCell>{beneficiary.phone}</TableCell>
                  <TableCell>{beneficiary.primaryFlg ? 'Yes' : 'No'}</TableCell>
                  <TableCell>
                    <a href={`/Beneficiary/${beneficiary.id}`}>
                      <EditIcon />
                    </a>
                  </TableCell>
                </TableRow>
              );
            })}
            <TableRow>
              <TableCell colSpan={9}>
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<Add />}
                  component={Link}
                  to={`/Beneficiary/create?userId=${id}`}
                  style={{ textTransform: 'none' }}
                >
                  Create new beneficiary
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5">Transfer-on-death documents</Typography>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <b>Document name</b>
              </TableCell>
              <TableCell>
                <b>Download</b>
              </TableCell>
              <TableCell>
                <b>Delete</b>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {todDocuments?.map((document) => {
              return (
                <TableRow key={`tod-documents-list-${document.id}`}>
                  <TableCell>{document.name}</TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={<CloudDownload />}
                      style={{ textTransform: 'none' }}
                      onClick={() => {
                        window.location.assign(document.downloadUrl);
                      }}
                    >
                      Download
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      color="secondary"
                      endIcon={<Delete />}
                      onClick={() => deleteTODDocument(document.id)}
                      style={{ textTransform: 'none' }}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
            <TableRow>
              <TableCell colSpan={3}>
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<Add />}
                  component="label" // https://stackoverflow.com/a/54043619
                  style={{ textTransform: 'none' }}
                >
                  Upload new document
                  <input
                    type="file"
                    hidden
                    onChange={(event) => createTodDocument(event)}
                    accept="application/pdf"
                  />
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Grid>
    </Grid>
  );
};
