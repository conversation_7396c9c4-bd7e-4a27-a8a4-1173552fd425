import React, { useState, useEffect, useRef } from 'react';
import { Link, useParams } from 'react-router-dom';
import numeral from 'numeral';
import moment from 'moment';
import {
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
} from 'react-admin';
import {
  Button,
  CircularProgress,
  Grid,
  Table,
  TableContainer,
  TableHead,
  TableCell,
  TableBody,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { Alert } from '@mui/lab';

import {
  CancelOutlined,
  CheckCircleOutlineOutlined,
} from '@mui/icons-material';

const CreateShareTransferBtn = (props) => {
  const refresh = useRefresh();
  const notify = useNotify();
  const { dataProvider, investment } = props;

  return (
    <Button
      variant="contained"
      disabled={
        investment.shareTransfers && investment.shareTransfers.length > 0
      }
      color="primary"
      onClick={(event) => {
        event.preventDefault();
        event.stopPropagation();
        dataProvider
          .update('Investment', {
            data: {
              id: investment.id,
              createShareTransfer: true,
            },
          })
          .then(
            () => {
              refresh();
            },
            (e) => {
              console.error('ERROR', e);
              notify('Error creating share transfer', { type: 'error' });
            }
          );
      }}
    >
      Create Historical Share Transfer
    </Button>
  );
};

const CreatePartialShareTransferBtn = (props) => {
  const refresh = useRefresh();
  const notify = useNotify();
  const { dataProvider, investment } = props;

  return (
    <Button
      variant="contained"
      color="primary"
      onClick={(event) => {
        event.preventDefault();
        dataProvider
          .update('Investment', {
            data: {
              id: investment.id,
              createPartialShareTransfer: true,
            },
          })
          .then(
            () => {
              refresh();
            },
            (e) => {
              console.error('ERROR', e);
              notify('Error creating share transfer', { type: 'error' });
            }
          );
      }}
    >
      Complete Partial Transfer
    </Button>
  );
};

export const PortfolioInvestments = () => {
  const [portfolioInvestments, setPortfolioInvestments] = useState(null);
  const dataProvider = useDataProvider();
  const { id } = useParams();
  const { permissions } = usePermissions();
  const isMountedRef = useRef(true);

  useEffect(() => {
    if (!portfolioInvestments) {
      dataProvider
        .getList('PortfolioInvestment', {
          id: parseInt(id, 10),
        })
        .then(
          (resp) => {
            // Only update state if component is still mounted
            if (isMountedRef.current) {
              setPortfolioInvestments(resp.data);
            }
          },
          (e) => {
            // Only log error if component is still mounted
            if (isMountedRef.current) {
              console.error('HIT AN ERROR', e);
            }
          }
        );
    }

    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
    };
  }, [portfolioInvestments, dataProvider, id]);

  if (!portfolioInvestments)
    return (
      <Grid
        container
        justifyContent="center"
        style={{
          width: '100%',
          textAlign: 'center',
        }}
      >
        <Grid item style={{ textAlign: 'center' }} xs={12}>
          <CircularProgress />
        </Grid>
      </Grid>
    );

  if (portfolioInvestments.length === 0)
    return (
      <Grid style={{ width: '100%' }} container spacing={3}>
        <Grid item xs={12}>
          <Alert severity="info">
            No investments received to date for this Portfolio.
          </Alert>
        </Grid>
      </Grid>
    );

  const hasITRole =
    permissions &&
    permissions.roles?.map((role) => role.name)?.includes('ITWrite');

  const attrs = [
    { label: 'ID', getData: (investment) => investment.id },
    {
      label: 'Investor',
      getData: (investment) => investment.user && investment.user.fullName,
    },
    {
      label: 'Value',
      getData: (investment) => numeral(investment.value).format('$0,0.00'),
    },
    {
      label: 'Shares',
      getData: (investment) => numeral(investment.shares).format('0,0.[000]'),
    },
    {
      label: 'Realized Share Price',
      getData: (investment) =>
        numeral(investment.calculatedSharePrice).format('$0,0.0000'),
    },
    {
      label: 'Share Price At the Time',
      getData: (investment) =>
        numeral(investment.actualSharePrice).format('$0,0.0000'),
    },
    {
      label: 'Date',
      getData: (investment) => moment(investment.startDt).format('M/D/Y'),
    },
    {
      label: 'Cancelled Date',
      getData: (investment) =>
        investment.cancelledDt &&
        moment(investment.cancelledDt).format('M/D/Y'),
    },
    {
      label: 'Is Pending?',
      getData: (investment) =>
        (investment.isPending && (
          <CheckCircleOutlineOutlined style={{ color: 'green' }} />
        )) || <CancelOutlined style={{ color: 'red' }} />,
    },
    {
      label: 'Source',
      getData: (investment) => {
        if (
          !investment.shareTransfers ||
          investment.shareTransfers.length === 0
        ) {
          return 'Unissued Shares';
        }
        let missingTransferBalance = 0;
        let transferredBalance = 0;
        let pendingTransferBalance = 0;
        let totalResold = 0;
        let totalResoldShares = 0;

        investment.shareTransfers.forEach((shareTransfer) => {
          totalResold += shareTransfer.value;
          totalResoldShares += shareTransfer.soldShares;
          if (shareTransfer.historicalFlg) {
            transferredBalance += shareTransfer.value;
          } else if (shareTransfer.status) {
            transferredBalance += shareTransfer.status.transferredBalance;
            pendingTransferBalance +=
              shareTransfer.status.pendingTransferBalance;
            missingTransferBalance +=
              shareTransfer.status.missingTransferBalance;
          }
        });
        const data = [
          {
            label: 'Total Shares',
            data: numeral(investment.shares).format('0,0.[000]'),
          },
          {
            label: 'Resold Shares',
            data: numeral(totalResoldShares).format('0,0.[000]'),
          },
          {
            label: 'Remaining Unissued Shares',
            data: numeral(investment.shares - totalResoldShares).format(
              '0,0.[000]'
            ),
          },
          {
            label: 'Successfully Transferred',
            data: numeral(transferredBalance).format('$0,0.00'),
          },
          {
            label: 'Pending Transfer',
            data: numeral(pendingTransferBalance).format('$0,0.00'),
          },
          {
            label: 'Missing Transfer',
            data: numeral(missingTransferBalance).format('$0,0.00'),
          },
          {
            label: 'Natural Share Balance',
            data: numeral(investment.value - totalResold).format('$0,0.00'),
          },
        ];
        const title = (
          <Grid container>
            {data.map((dataPoint) => (
              <Grid
                container
                justifyContent="space-between"
                key={`title-data-${dataPoint.label}`}
              >
                <Grid item>
                  <Typography>{dataPoint.label} : </Typography>
                </Grid>
                <Grid item>
                  <Typography>{dataPoint.data}</Typography>
                </Grid>
              </Grid>
            ))}
          </Grid>
        );
        return (
          <Tooltip title={title}>
            <span
              style={{
                color:
                  totalResold === transferredBalance
                    ? 'green'
                    : totalResold !== investment.value
                    ? 'red'
                    : 'darkorange',
                fontWeight: 'bold',
                cursor: 'pointer',
              }}
            >
              {totalResold !== investment.value
                ? 'Partially Resold'
                : 'Resold Shares'}
            </span>
          </Tooltip>
        );
      },
    },
  ];

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            {attrs.map((attr, i) => (
              <TableCell key={`portfolio-investments-header-cell-${i}`}>
                {attr.label}
              </TableCell>
            ))}
            {hasITRole ? (
              <>
                <TableCell />
                <TableCell />
              </>
            ) : null}
          </TableRow>
        </TableHead>
        <TableBody>
          {portfolioInvestments.map((investment) => (
            <TableRow
              hover
              style={{ textDecoration: 'none' }}
              role="button"
              component={Link}
              to={{
                pathname: `/investment/${investment.id}`,
                state: { record: { id: investment.id } },
              }}
              key={`investment-table-row-${investment.id}`}
            >
              {attrs.map((attr, i) => (
                <TableCell
                  key={`portfolio-investments-body-${investment.id}=${i}`}
                >
                  {attr.getData(investment)}
                </TableCell>
              ))}
              {hasITRole ? (
                <TableCell>
                  <CreateShareTransferBtn
                    investment={investment}
                    dataProvider={dataProvider}
                  />
                </TableCell>
              ) : null}
              {hasITRole ? (
                <TableCell>
                  <CreatePartialShareTransferBtn
                    investment={investment}
                    dataProvider={dataProvider}
                  />
                </TableCell>
              ) : null}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
