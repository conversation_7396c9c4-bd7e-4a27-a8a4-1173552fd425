import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  downloadCSV,
  Edit,
  Filter,
  FunctionField,
  Labeled,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
  useRecordContext,
} from 'react-admin';
import { Button, Grid } from '@mui/material';
import numeral from 'numeral';
import moment from 'moment';
import jsonExport from 'jsonexport/dist';

import { CustomNumberInput, LinkField } from './CustomFields';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomTopToolbar } from './CustomTopToolbar';

const entityName = 'Share Transfer';

const CreateTransferButton = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const record = useRecordContext();
  const refresh = useRefresh();

  const { id } = useParams();
  const handleClick = () => {
    dataProvider
      .update('ShareTransfer', {
        data: { id: parseInt(id, 10), createTransfer: true },
      })
      .then(() => {
        notify('Successfully created transfer', { type: 'success' });
        refresh();
      })
      .catch((e) => {
        notify(`Error: ${e}`, { type: 'warning' });
      });
  };

  if (record?.transfer) return null;

  return (
    <Button
      color="primary"
      variant="contained"
      disabled={record.transfer}
      onClick={handleClick}
    >
      Create Transfer
    </Button>
  );
};

const CreateAllocationsButton = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const record = useRecordContext();
  const refresh = useRefresh();

  const { id } = useParams();
  const handleClick = () => {
    dataProvider
      .update('ShareTransfer', {
        data: { id: parseInt(id, 10), createAllocation: true },
      })
      .then(() => {
        notify('Successfully created shareTransferAllocation', {
          type: 'success',
        });
        refresh();
      })
      .catch((e) => {
        notify(`Error: ${e}`, { type: 'warning' });
      });
  };

  if (record?.transfer) return null;

  return (
    <Button
      color="primary"
      variant="contained"
      disabled={record.transfer || record.shareTransferAllocations?.length > 0}
      onClick={handleClick}
    >
      Create Allocations
    </Button>
  );
};

export const ShareTransferEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <DateInput source="sellDt" fullWidth />
            <CustomNumberInput source="soldShares" fullWidth />
            <CustomNumberInput source="value" fullWidth />
            <CustomNumberInput source="exitPenaltyAmount" fullWidth />
            <BooleanInput
              source="issuerBuybackFlg"
              helperText="Turn this on if the shares were purchased by the portfolio and no investment is associated with this share transfer."
              fullWidth
            />
            <BooleanInput
              source="historicalFlg"
              helperText="Turn this on if the share transfer is being handled offline, of if this being created retroactively."
              fullWidth
            />
            <Labeled fullWidth>
              <LinkField
                reference="SellOrder"
                linkSource="sellOrder.id"
                labelSource="sellOrder.label"
                label="SellOrder"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                reference="Investment"
                linkSource="investment.id"
                labelSource="investment.label"
                label="Investment"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                reference="Transfer"
                linkSource="transfer.id"
                labelSource="transfer.label"
                label="Transfer"
              />
            </Labeled>
            <ArrayField
              label="Dwolla Transfers"
              fullWidth
              source="transfer.dwollaTransfers"
            >
              <Datagrid>
                <TextField source="id" />
                <TextField source="status" />
                <NumberField
                  label="Amount"
                  source="amount.value"
                  options={{ style: 'currency', currency: 'USD' }}
                />
                <TextField label="Source" source="source.name" />
                <TextField label="Destination" source="destination.name" />
                <DateField source="created" />
              </Datagrid>
            </ArrayField>
            <Grid container spacing={2}>
              <Grid item>
                <CreateTransferButton />
              </Grid>
              <Grid item>
                <CreateAllocationsButton />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const ShareTransferFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '320px' }}
      label="Sold From"
      source="soldFrom"
      alwaysOn
    />
    <TextInput
      style={{ minWidth: '320px' }}
      label="Sold To"
      source="soldTo"
      alwaysOn
    />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Sell Order"
      source="sellOrder.id"
      reference="SellOrderLite"
      perPage={10000}
      sort={{ field: 'id', order: 'desc' }}
    >
      <AutocompleteInput label="Sell Order" optionText="label" />
    </ReferenceInput>
  </Filter>
);
const ShareTransfersPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

const exporter = (rows) => {
  const rowsForExport = rows
    .filter((row) => !row.investment.cancelledDt)
    .map((row) => {
      const returnRow = {};
      const dates = ['startDt', 'createdAt', 'sellDt'];
      Object.keys(row).forEach((attr) => {
        if (attr === '__typename') return;
        const data = row[String(attr)];
        if (data && data.__typename) {
          delete data.__typename;
        }
        if (dates.indexOf(attr) > -1) {
          returnRow[String(attr)] = moment(data).format('MM/DD/YYYY');
        } else {
          returnRow[String(attr)] = data;
        }
      });
      return returnRow;
    });
  jsonExport(
    rowsForExport,
    {
      rowDelimiter: ';',
    },
    (err, csv) => {
      downloadCSV(`sep=;\n${csv}`, entityName); // download as 'posts.csv` file
    }
  );
};

const styleRow = (record, index) => {
  const { dwollaTransfers, dbAmount } = record;
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'orange',
    color: '#fff',
  };
  if (record.transferCountCheck > 1) return errorStyle;
  if (
    record?.investment?.completedDt &&
    !record.transfer &&
    !record?.investment?.isReferral &&
    !record?.investment?.historicalFlg &&
    !record?.investment?.isPromo
  )
    return warningStyle;
  return {};
};
export const ShareTransferList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      actions={<CustomTopToolbar />}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<ShareTransferFilter />}
      pagination={<ShareTransfersPagination />}
    >
      <Datagrid
        size="small"
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <NumberField source="soldShares" />
        <FunctionField
          label="Investment Total Shares"
          textAlign="left"
          render={(record) => (
            <span
              style={{
                color:
                  record.investment &&
                  record.investment.shares < record.soldShares
                    ? 'red'
                    : null,
              }}
            >
              {numeral(record.investment && record.investment.shares).format(
                '0,0.[000]'
              )}
            </span>
          )}
        />
        <FunctionField
          label="Investment Total Value"
          textAlign="left"
          render={(record) => (
            <span
              style={{
                color:
                  record.investment && record.investment.value < record.value
                    ? 'red'
                    : null,
              }}
            >
              {numeral(record.investment && record.investment.value).format(
                '$0,0.00'
              )}
            </span>
          )}
        />
        <NumberField
          label="Pending Balance"
          source="status.pendingTransferBalance"
          sortable={false}
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Awaiting Transfer Balance"
          source="status.missingTransferBalance"
          sortable={false}
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Transferred Balance"
          source="status.transferredBalance"
          sortable={false}
          options={{ style: 'currency', currency: 'USD' }}
        />
        <FunctionField
          label="Total Value Allocated"
          textAlign="left"
          render={(record) => {
            const { shareTransferAllocations, value } = record;
            let totalValueAllocated = 0;
            if (
              shareTransferAllocations &&
              shareTransferAllocations.length > 0
            ) {
              shareTransferAllocations.forEach(
                (shareTransferAllocation) =>
                  (totalValueAllocated += shareTransferAllocation.value)
              );
            }
            return (
              <span
                style={{
                  fontWeight: 'bold',
                  color: totalValueAllocated === value ? 'green' : 'red',
                }}
              >
                {numeral(totalValueAllocated).format('$0,0.00')}
              </span>
            );
          }}
        />
        <NumberField
          source="exitPenaltyAmount"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <DateField
          source="investment.completedDt"
          label="Investment Completed Date"
          style={{ fontWeight: 'bold', color: 'green' }}
        />
        <DateField
          source="investment.cancelledDt"
          label="Investment Cancelled Date"
          style={{ fontWeight: 'bold', color: 'red' }}
        />
        <LinkField
          reference="User"
          linkSource="sellOrder.user.id"
          labelSource="sellOrder.user.fullName"
          label="Sold From"
        />
        <LinkField
          reference="User"
          linkSource="investment.user.id"
          labelSource="investment.user.fullName"
          label="Sold To"
        />
        <LinkField
          reference="Portfolio"
          linkSource="sellOrder.portfolio.id"
          labelSource="sellOrder.portfolio.subtitle"
          label="Portfolio"
        />
        <LinkField
          reference="Transfer"
          linkSource="transfer.id"
          labelSource="transfer.label"
          label="Transfer"
        />
        <LinkField
          reference="SellOrder"
          linkSource="sellOrder.id"
          labelSource="sellOrder.label"
          label="Sell Order"
        />
        <LinkField
          reference="Investment"
          linkSource="investment.id"
          labelSource="investment.label"
          label="Investment"
        />
        <BooleanField source="issuerBuybackFlg" />
        <BooleanField source="historicalFlg" />
        <BooleanField
          label="Completed?"
          source="status.completedFlg"
          sortable={false}
        />
        <DateField label="Sold Date" source="sellDt" showTime={true} />
        <DateField
          label="Transfer Initiated Date"
          source="transferCreatedDt"
          showTime={true}
        />
        <DateField
          label="Transfer Complete Date"
          source="completedDt"
          showTime={true}
        />
      </Datagrid>
    </List>
  );
};

export const ShareTransferCreate = () => (
  <Create
    title={`Create ${entityName}`}
    helperText="This can be edited at any time so no need to be perfect."
  >
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <DateInput source="sellDt" fullWidth />
          <CustomNumberInput source="soldShares" fullWidth />
          <CustomNumberInput source="value" fullWidth />
          <BooleanInput source="historicalFlg" fullWidth />
          <BooleanInput source="issuerBuybackFlg" fullWidth />
          <ReferenceInput
            perPage={10000}
            source="sellOrder.id"
            reference="SellOrderLite"
          >
            <SelectInput label="Sell Order" fullWidth optionText="label" />
          </ReferenceInput>
          <CustomNumberInput
            source="investmentId"
            label="Investment ID"
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
