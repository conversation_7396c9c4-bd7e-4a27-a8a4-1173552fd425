import React from 'react';
import { <PERSON><PERSON>, <PERSON>po<PERSON>, Button, CircularProgress } from '@mui/material';
import { Refresh } from '@mui/icons-material';

/**
 * Error boundary specifically for handling chunk loading errors in React-admin
 * This catches errors that occur when navigating between admin pages after deployments
 */
class ChunkErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null,
      isRecovering: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React-admin Chunk Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Handle chunk loading errors automatically
    this.handleChunkLoadError(error);
  }

  isChunkLoadError = (error) => {
    const errorMessage = error?.message || '';
    const errorName = error?.name || '';
    
    return (
      errorName === 'ChunkLoadError' ||
      errorMessage.includes('Loading chunk') ||
      errorMessage.includes('Failed to fetch dynamically imported module') ||
      errorMessage.includes('Loading CSS chunk') ||
      errorMessage.includes('ChunkLoadError') ||
      errorMessage.includes('Cannot resolve module') ||
      errorMessage.includes('Module not found') ||
      errorMessage.includes('Failed to import')
    );
  };

  handleChunkLoadError = (error) => {
    if (this.isChunkLoadError(error)) {
      console.log('Chunk loading error detected in React-admin');
      
      const maxRetries = 2;
      const currentRetryCount = this.state.retryCount;

      if (currentRetryCount < maxRetries) {
        console.log(`Attempting automatic recovery (attempt ${currentRetryCount + 1}/${maxRetries})`);
        
        this.setState({ 
          isRecovering: true,
          retryCount: currentRetryCount + 1
        });
        
        // Clear caches and reload after a short delay
        this.clearChunkCaches().then(() => {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        });
      } else {
        console.error('Max retries reached for chunk loading error');
        // Reset retry count for future attempts
        this.setState({ retryCount: 0 });
      }
    }
  };

  clearChunkCaches = async () => {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        const chunkCaches = cacheNames.filter(name => 
          name.includes('webpack') || 
          name.includes('chunk') || 
          name.includes('static') ||
          name.includes('js') ||
          name.includes('css')
        );
        
        await Promise.all(
          chunkCaches.map(cacheName => caches.delete(cacheName))
        );
        
        console.log('Cleared chunk caches:', chunkCaches);
      } catch (error) {
        console.error('Failed to clear chunk caches:', error);
      }
    }
  };

  handleManualRetry = () => {
    // Clear the error state and try again
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isRecovering: false
    });
    
    // Clear caches before retry
    this.clearChunkCaches();
  };

  handleReload = () => {
    this.setState({ retryCount: 0 });
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const error = this.state.error;
      const isChunkError = this.isChunkLoadError(error);

      // Show automatic recovery UI for chunk errors
      if (isChunkError && this.state.isRecovering) {
        return (
          <Grid
            container
            style={{ 
              height: '100vh',
              padding: '2rem'
            }}
            alignItems="center"
            justifyContent="center"
            direction="column"
            spacing={3}
          >
            <Grid item>
              <CircularProgress size={60} />
            </Grid>
            <Grid item>
              <Typography variant="h6" align="center">
                Loading updated admin interface...
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body2" color="textSecondary" align="center">
                The admin interface has been updated. Please wait while we load the latest version.
              </Typography>
            </Grid>
          </Grid>
        );
      }

      // Show error UI for chunk errors that couldn't auto-recover
      if (isChunkError) {
        return (
          <Grid
            container
            style={{ 
              height: '100vh',
              padding: '2rem'
            }}
            alignItems="center"
            justifyContent="center"
            direction="column"
            spacing={3}
          >
            <Grid item>
              <Typography variant="h5" align="center" gutterBottom>
                Admin Interface Update Required
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body1" align="center" color="textSecondary">
                The admin interface has been updated with new features. 
                Please refresh to continue using the latest version.
              </Typography>
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<Refresh />}
                onClick={this.handleReload}
                style={{ marginTop: '1rem' }}
              >
                Refresh Admin Interface
              </Button>
            </Grid>
          </Grid>
        );
      }

      // Show generic error UI for non-chunk errors
      return (
        <Grid
          container
          style={{ 
            height: '100vh',
            padding: '2rem'
          }}
          alignItems="center"
          justifyContent="center"
          direction="column"
          spacing={3}
        >
          <Grid item>
            <Typography variant="h5" align="center" gutterBottom>
              Something went wrong
            </Typography>
          </Grid>
          <Grid item>
            <Typography variant="body1" align="center" color="textSecondary">
              An error occurred in the admin interface. Please try refreshing the page.
            </Typography>
          </Grid>
          <Grid item style={{ display: 'flex', gap: '1rem' }}>
            <Button
              variant="outlined"
              onClick={this.handleManualRetry}
            >
              Try Again
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Refresh />}
              onClick={this.handleReload}
            >
              Refresh Page
            </Button>
          </Grid>
          {process.env.NODE_ENV === 'development' && (
            <Grid item style={{ marginTop: '2rem', maxWidth: '600px' }}>
              <details>
                <summary style={{ cursor: 'pointer', marginBottom: '1rem' }}>
                  Error Details (Development)
                </summary>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '1rem', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  overflow: 'auto',
                  whiteSpace: 'pre-wrap'
                }}>
                  {this.state.error && this.state.error.toString()}
                  {this.state.errorInfo && this.state.errorInfo.componentStack}
                </pre>
              </details>
            </Grid>
          )}
        </Grid>
      );
    }

    return this.props.children;
  }
}

export default ChunkErrorBoundary;
