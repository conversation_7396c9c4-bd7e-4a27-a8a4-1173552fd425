import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON><PERSON><PERSON>ield,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
} from 'react-admin';

import { Alert } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Monitoring Project';
const styleRow = (record, index) => {
  const { installationType, hasTrackersFlg, irradianceSensorTracker } = record;
  const requiredFields = [
    'P50ProdCompleteFlg',
    'prCompleteFlg',
    'poaIrradianceCompleteFlg',
    'moduleTempRefCompleteFlg',
    'timezone',
    'revenuePerkWhUSD',
    'latitude',
    'longitude',
  ];
  const errorStyle = {
    backgroundColor: 'rgba(255, 0, 0, 0.3)',
  };

  const missingIrradianceTracker = hasTrackersFlg && !irradianceSensorTracker;

  if (
    ([1, 4].includes(installationType.id) &&
      !requiredFields.every((field) => record[String(field)])) ||
    missingIrradianceTracker
  ) {
    return errorStyle;
  }

  return {};
};

export const MonitoringProjectList = () => (
  <List title={entityName}>
    <Alert severity="info">
      This table is to be used to determine missing fields for projects in the
      monitoring phase. If there are no red columns, then we're all set!
    </Alert>
    <Datagrid rowClick={(id) => `/Project/${id}`} rowStyle={styleRow}>
      <TextField sortable={false} source="id" />
      <TextField sortable={false} source="name" />
      <BooleanField sortable={false} source="P50ProdCompleteFlg" />
      <BooleanField sortable={false} source="prCompleteFlg" />
      <BooleanField sortable={false} source="poaIrradianceCompleteFlg" />
      <BooleanField sortable={false} source="moduleTempRefCompleteFlg" />
      <TextField sortable={false} source="timezone" />
      <NumberField sortable={false} label="NOCT" source="noct" />
      <NumberField sortable={false} source="tempCoeffPmax" />
      <NumberField
        sortable={false}
        label="Degradation Constant"
        source="degradationConstant"
      />
      <NumberField sortable={false} source="transformerLoss" />
      <NumberField
        sortable={false}
        label="Revenue Per kWh (USD)"
        source="revenuePerkWhUSD"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField sortable={false} source="latitude" />
      <NumberField sortable={false} source="longitude" />
      <DateField source="monitoringStartDt" />
      <DateField source="expectedGenerationStartDt" />
      <BooleanField source="horizontalIrradianceSensorFlg" />
      <LinkField
        label="Irradiance Sensor Tracker"
        linkSource="irradianceSensorTracker.id"
        labelSource="irradianceSensorTracker.name"
        reference="Sensor"
      />
    </Datagrid>
  </List>
);
