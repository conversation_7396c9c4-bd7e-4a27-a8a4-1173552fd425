import React, { useState } from 'react';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FormDataConsumer,
  FormTab,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import {
  Alert,
  Button,
  CircularProgress,
  Divider,
  FormHelperText,
  Grid,
  Typography,
} from '@mui/material';

import 'chartjs-adapter-moment';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Sungrow System';

export const SungrowSystemEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [loading, setLoading] = useState(false);
  const { id } = useParams();

  const backfillSiteHistory = (startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ProductionPeriod', {
        input: {
          backfillFromAPI: true,
          sungrowSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Site production history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site production history', { type: 'error' });
      });
  };

  const backfillSiteExpectedGeneration = (formData) => {
    const {
      backfillExpectedStartDt,
      backfillExpectedEndDt,
      project: { id: projectId },
    } = formData;
    if (backfillExpectedStartDt > backfillExpectedEndDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ExpectedProductionPeriod', {
        input: {
          backfillFromAPI: true,
          projectId: parseInt(projectId, 10),
          startDt: backfillExpectedStartDt,
          endDt: backfillExpectedEndDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Project expected generation successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error saving project expected generation', { type: 'error' });
      });
  };

  const backfillSiteSensorHistory = (sensorId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('SensorDataPeriod', {
        input: {
          backfillFromAPI: true,
          sensorId,
          sungrowSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Site sensor history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site sensor history', { type: 'error' });
      });
  };

  const backfillInverterHistory = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('InverterProductionPeriod', {
        input: {
          backfillFromAPI: true,
          inverterId,
          sungrowSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Inverter production history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error back-filling inverter production history', {
          type: 'error',
        });
      });
  };

  const backfillDailyInverterGeneration = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('DailyInverterGeneration', {
        input: {
          backfillFromAPI: true,
          inverterId,
          sungrowSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Daily inverter generation history backfill initiated');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify(
          'Error initiating daily inverter generation history backfill',
          'error'
        );
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm redirect={false}>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <BooleanInput source="isLivePollingEnabled" />
              <TextInput
                source="plantId"
                label="Sungrow system id"
                helperText="This should be the ID of the site in the Sungrow system"
                fullWidth
              />
              <ReferenceInput
                source="project.id"
                reference="Project"
                sort={{ field: 'name', order: 'ASC' }}
                perPage={10000}
              >
                <SelectInput
                  label="Project"
                  required
                  fullWidth
                  helperText="Select the project associated with this Sungrow system."
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput
                source="name"
                label="Sungrow system name"
                fullWidth
                disabled
              />
              <TextInput
                source="timezone"
                label="Sungrow system timezone"
                fullWidth
                disabled
              />
              <CustomNumberInput
                source="noCommsLimitMins"
                fullWidth
                label="No Comms Alert Limit (minutes)"
                helperText="If you want to disable the 'No Comms' alert for this site, set to null."
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Backfill">
          <Grid item xs={12}>
            <Alert severity="warning">
              <Typography>
                Rate Limit Warning: Sungrow API allows 2,000 API requests per
                hour. When backfilling inverter power or sensor data, the API on
                the backend can only fetch 3 hours at a time and makes
                individual fetches for each sensor or inverter. If you are
                attempting to backfill a large block of time, you may run into
                this issue.
              </Typography>
            </Alert>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="h5" gutterBottom>
              Backfill Expected Generation Data
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <TextInput
              label="From"
              source="backfillExpectedStartDt"
              type="datetime-local"
              helperText="Enter date in the timezone of the site"
              style={{ marginRight: '2rem' }}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <TextInput
              label="To"
              source="backfillExpectedEndDt"
              type="datetime-local"
              helperText="Enter date in the timezone of the site"
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <FormDataConsumer>
              {({ formData, ...rest }) => (
                <>
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={
                      loading ||
                      !(
                        formData.backfillExpectedStartDt &&
                        formData.backfillExpectedEndDt
                      )
                    }
                    onClick={() => {
                      setLoading(true);
                      backfillSiteExpectedGeneration(formData);
                    }}
                  >
                    {loading ? (
                      <CircularProgress />
                    ) : (
                      'Backfill Expected Generation Data'
                    )}
                  </Button>
                  <FormHelperText style={{ marginLeft: '14px' }}>
                    Irradiance, inverter power, and module temperature backfills
                    need to be complete before expected generation backfills can
                    be calculated.
                  </FormHelperText>
                </>
              )}
            </FormDataConsumer>
          </Grid>
          <Divider style={{ width: '100%', margin: '2em 0' }} />
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Production Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillStartDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillEndDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={
                      loading ||
                      !(
                        formData.siteBackfillStartDt &&
                        formData.siteBackfillEndDt
                      )
                    }
                    onClick={() => {
                      setLoading(true);
                      backfillSiteHistory(
                        formData.siteBackfillStartDt,
                        formData.siteBackfillEndDt
                      );
                    }}
                  >
                    {loading ? <CircularProgress /> : 'Backfill Site History'}
                  </Button>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Sensor Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillSensorStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillSensorEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="sensor.id"
                reference="Sensor"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ sungrowSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Sensor"
                  helperText="Select the sensor you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.siteBackfillSensorStartDt &&
                          formData.siteBackfillSensorEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteSensorHistory(
                          formData.sensor && formData.sensor.id,
                          formData.siteBackfillSensorStartDt,
                          formData.siteBackfillSensorEndDt
                        );
                      }}
                    >
                      {loading ? <CircularProgress /> : 'Backfill Site History'}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Inverter Power Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="inverterBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="inverterBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ sungrowSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={
                      loading ||
                      !(
                        formData.inverterBackfillStartDt &&
                        formData.inverterBackfillEndDt
                      )
                    }
                    onClick={() => {
                      setLoading(true);
                      backfillInverterHistory(
                        formData.inverter && formData.inverter.id,
                        formData.inverterBackfillStartDt,
                        formData.inverterBackfillEndDt
                      );
                    }}
                  >
                    {loading ? (
                      <CircularProgress />
                    ) : (
                      'Backfill Inverter History'
                    )}
                  </Button>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Daily Inverter Generation Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="dailyInverterGenerationBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="dailyInverterGenerationBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ scadaSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.dailyInverterGenerationBackfillStartDt &&
                          formData.dailyInverterGenerationBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillDailyInverterGeneration(
                          formData.inverter && formData.inverter.id,
                          formData.dailyInverterGenerationBackfillStartDt,
                          formData.dailyInverterGenerationBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Daily Inverter Generation'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Production Upload">
          <Grid container style={{ minWidth: '450px' }}>
            <Typography>
              To upload data from CSV,{' '}
              <a href="/ProductionPeriod/create">click here</a>.
            </Typography>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

export const SungrowSystemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField
          source="plantId"
          label="Sungrow system Id"
          sortable={false}
        />
        <BooleanField source="isLivePollingEnabled" />
        <TextField source="name" />
        <TextField source="timezone" sortable={false} />
        <NumberField source="noCommsLimitMins" fullWidth />
      </Datagrid>
    </List>
  );
};

export const SungrowSystemCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            sort={{ field: 'name', order: 'ASC' }}
            perPage={10000}
          >
            <SelectInput
              label="Project"
              required
              fullWidth
              helperText="Select the project associated with this Sungrow system."
              optionText="name"
            />
          </ReferenceInput>
          <TextInput
            required
            source="plantId"
            label="Sungrow system id"
            helperText="Query fetchAllSitesFromSolarCloud at localhost:5000/graphql and check console logs for asset ids of all projects"
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
