import React, { useState } from 'react';
import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  Pagination,
  ReferenceInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import numeral from 'numeral';
import 'chart.js/auto';

import { Line } from 'react-chartjs-2';
import moment from 'moment';

import {
  Button,
  Card,
  CardContent,
  CircularProgress,
  Grid,
  Typography,
} from '@mui/material';

import theme from '../theme';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Referral';

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

export const ReferralEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const retroactivelyCompleteReferral = () => {
    const updatedRecord = {
      id: parseInt(id, 10),
      retroactivelyCompleteReferral: true,
    };
    dataProvider
      .update('Referral', {
        data: updatedRecord,
      })
      .then(
        (res) => {
          notify('Referral completed', { type: 'success' });
          refresh();
        },
        (err) => {
          notify('Error completing referral', { type: 'error' });
        }
      );
  };

  const cancelReferral = () => {
    const updatedRecord = {
      id: parseInt(id, 10),
      cancelReferral: true,
    };
    dataProvider
      .update('Referral', {
        data: updatedRecord,
      })
      .then(
        (res) => {
          notify('Referral cancelled', { type: 'success' });
          refresh();
        },
        (err) => {
          notify('Error cancelling referral', { type: 'error' });
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              perPage={10000}
              source="referrer.id"
              reference="UserLite"
              helperText="User who referred the referee"
            >
              <AutocompleteInput
                label="Referrer"
                required
                fullWidth
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            <ReferenceInput
              perPage={10000}
              source="referee.id"
              reference="UserLite"
              helperText="User who was referred by the referrer"
            >
              <AutocompleteInput
                label="Referee"
                required
                fullWidth
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            <DateInput source="referralCompletedDt" fullWidth />
            <DateInput source="deactivatedDt" fullWidth />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={
                      !!(formData.referralCompletedDt || formData.deactivatedDt)
                    }
                    style={{
                      backgroundColor:
                        formData.referralCompletedDt || formData.deactivatedDt
                          ? null
                          : theme.palette.error.main,
                    }}
                    onClick={retroactivelyCompleteReferral}
                  >
                    Complete Referral
                  </Button>
                );
              }}
            </FormDataConsumer>
            <Typography variant="body2">
              *Retroactively complete referral and create investments for
              referrer and referee
            </Typography>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={!!formData.deactivatedDt}
                    style={{
                      backgroundColor: formData.deactivatedDt
                        ? null
                        : theme.palette.error.main,
                      marginTop: '1rem',
                    }}
                    onClick={cancelReferral}
                  >
                    Cancel Referral
                  </Button>
                );
              }}
            </FormDataConsumer>
            <Typography variant="body2">
              *Cancelling a referral will{' '}
              <b>set the deactivated date in the database and in HubSpot</b>{' '}
              (removing them from the referral reminder email workflow), and
              will <b>cancel the referrer's and the referee's investments</b> if
              they were created to pull back the shares. An email will be sent
              to both parties indicating that the terms of service have been
              breached and there reward has been voided.
            </Typography>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const ReferralFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Referrer/Referee First Name or Last Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const ReferralList = () => {
  const [programStatus, setProgramStatus] = useState(null);
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  if (!programStatus) {
    dataProvider.getOne('ReferralProgramStatus', {}).then((res) => {
      setProgramStatus(res.data);
    });
  }

  const referralReward = 2 * process.env.REACT_APP_REFERRAL_PROGRAM_REWARD;
  const maxReferrals = programStatus
    ? programStatus.getReferralProgramBalance / referralReward
    : 0;
  const pendingReferrals = programStatus
    ? programStatus.getPendingReferralCount
    : 0;
  const programBalance = programStatus
    ? programStatus.getReferralProgramBalance
    : 0;

  const getStatusColor = (pending, max) => {
    if (pending > max) {
      return 'red';
    }
    if (pending < max - 10) {
      return 'green';
    }
    return 'orange';
  };

  const datasets = [
    {
      label: 'Referrals',
      data: programStatus
        ? programStatus.getReferralChartData.map((pt) => ({
            date: moment(pt.date, 'YYYY-MM-DD'),
            value: pt.createdCount,
          }))
        : [],
      pointRadius: 0,
      borderColor: theme.palette.green.main,
    },
    {
      label: 'Completed Referrals',
      data: programStatus
        ? programStatus.getReferralChartData.map((pt) => ({
            date: moment(pt.date, 'YYYY-MM-DD'),
            value: pt.completedCount,
          }))
        : [],
      pointRadius: 0,
      fill: true,
      backgroundColor: theme.palette.green.main,
      borderColor: theme.palette.green.main,
    },
  ];

  return (
    <>
      <Card style={{ marginTop: '2rem' }}>
        <CardContent>
          <Grid container>
            <Grid item xs={12}>
              <Typography variant="h6">Referral Program Status</Typography>
            </Grid>
            {programStatus ? (
              <>
                <Grid container style={{ padding: '1rem' }}>
                  <Grid item xs={12} md={4}>
                    <Grid container direction="column" alignItems="center">
                      <Grid item>
                        <Typography
                          variant="h3"
                          style={{ color: theme.palette.green.main }}
                        >
                          {numeral(
                            programStatus.getCompletedReferralCount
                          ).format('0,0')}
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography>Completed Referrals</Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Grid container direction="column" alignItems="center">
                      <Grid item>
                        <Typography
                          variant="h3"
                          style={{ color: theme.palette.green.main }}
                        >
                          {numeral(
                            programStatus.getReferralProgramTotalSpend
                          ).format('$0,0[.]00')}
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography>Total Rewarded</Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Grid container direction="column" alignItems="center">
                      <Grid item>
                        <Typography
                          variant="h3"
                          style={{ color: theme.palette.green.main }}
                        >
                          {numeral(
                            programStatus.getTotalInvestedFromReferredUsers -
                              programStatus.getReferralProgramTotalSpend
                          ).format('$0,0[.]00')}
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography>Total Invested after Reward</Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container style={{ padding: '1rem' }} alignItems="center">
                  <Grid item style={{ paddingRight: '1rem' }}>
                    <Grid item xs={12}>
                      <Typography
                        style={{
                          color: getStatusColor(pendingReferrals, maxReferrals),
                        }}
                      >
                        Program Balance:{' '}
                        {numeral(programBalance).format('$0,0[.]00')}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography
                        style={{
                          color: getStatusColor(pendingReferrals, maxReferrals),
                        }}
                      >
                        Pending Referrals:{' '}
                        {numeral(pendingReferrals).format('0,0')}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography>
                        Max Referrals: {numeral(maxReferrals).format('0,0')} (at
                        a {numeral(referralReward).format('$0,0[.]00')} piece)
                      </Typography>
                    </Grid>
                  </Grid>
                  <Grid item xs={12} md={8} style={{ height: '200px' }}>
                    <Line
                      height="100"
                      data={{
                        datasets: datasets,
                      }}
                      options={{
                        maintainAspectRatio: false,
                        parsing: {
                          xAxisKey: 'date',
                          yAxisKey: 'value',
                        },
                        plugins: {
                          legend: { display: false },
                          tooltip: {
                            mode: 'index',
                            intersect: false,
                          },
                        },
                        scales: {
                          x: {
                            type: 'time',
                            time: {
                              tooltipFormat: 'MMM DD, YYYY',
                              unit: 'day',
                            },
                          },
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              </>
            ) : (
              <CircularProgress />
            )}
          </Grid>
        </CardContent>
      </Card>
      <List
        title={entityName}
        perPage={25}
        pagination={<CustomPagination />}
        sort={{ field: 'id', order: 'DESC' }}
        filters={<ReferralFilter />}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <FunctionField
            label="Referrer"
            render={(record) => {
              const isSuspended =
                record.referrer?.dwollaCustomer?.status === 'suspended';
              return (
                <Typography
                  variant="body2"
                  onClick={(event) => {
                    window.location.pathname = `/User/${record.referrer.id}`;
                    event.stopPropagation();
                  }}
                  style={{
                    color: isSuspended ? 'red' : theme.palette.primary.main,
                    fontWeight: isSuspended ? 'bold' : null,
                  }}
                >
                  {record?.referrer?.fullName}{' '}
                  {isSuspended ? '(Suspended)' : ''}
                </Typography>
              );
            }}
          />
          <FunctionField
            label="Referee"
            render={(record) => {
              const isSuspended =
                record.referee?.dwollaCustomer?.status === 'suspended';
              return (
                <Typography
                  variant="body2"
                  onClick={(event) => {
                    window.location.pathname = `/User/${record.referee.id}`;
                    event.stopPropagation();
                  }}
                  style={{
                    color: isSuspended ? 'red' : theme.palette.primary.main,
                    fontWeight: isSuspended ? 'bold' : null,
                  }}
                >
                  {record?.referee?.fullName} {isSuspended ? '(Suspended)' : ''}{' '}
                  {record?.referee?.hubSpotLeadSource &&
                  record?.referee?.hubSpotLeadSource.name !==
                    'Referral Program' ? (
                    <span style={{ color: '#FFB500', fontWeight: 'bold' }}>
                      {' '}
                      (Source : {record?.referee?.hubSpotLeadSource?.name})
                    </span>
                  ) : null}
                </Typography>
              );
            }}
          />
          <FunctionField
            fullWidth
            label="Referee Total Invested"
            render={(record) => {
              if (!record?.referee?.investmentSum) {
                return '';
              }
              return (
                <Typography
                  style={{
                    color: record?.referee?.investmentSum > 50 ? 'green' : null,
                  }}
                  variant="body2"
                >
                  {numeral(record?.referee?.investmentSum).format('$0,0[.]00')}
                </Typography>
              );
            }}
          />
          <DateField source="referralCompletedDt" required fullWidth />
          <DateField source="deactivatedDt" required fullWidth />
          <DateField source="createdAt" required fullWidth />
          <DateField source="updatedAt" required fullWidth />
        </Datagrid>
      </List>
    </>
  );
};

export const ReferralCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={10000}
            source="referrer.id"
            reference="UserLite"
          >
            <AutocompleteInput
              label="Referrer"
              required
              fullWidth
              optionText="fullName"
              helperText="User who referred the referee"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <ReferenceInput
            perPage={10000}
            source="referee.id"
            reference="UserLite"
          >
            <AutocompleteInput
              label="Referee"
              required
              fullWidth
              optionText="fullName"
              helperText="User who was referred by the referrer"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
