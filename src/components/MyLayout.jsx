import React, { useState } from 'react';
import {
  AppBar,
  Layout,
  Sidebar,
  UserMenu,
  MenuItemLink,
  WithPermissions,
  useDataProvider,
  useNotify,
  usePermissions,
  Logout,
} from 'react-admin';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  Hidden,
  TextField,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { makeStyles, withStyles } from '@mui/styles';
import { Settings } from '@mui/icons-material';
import MySearchBar from './MySearchBar';
import MyMenu from './MyMenu';
// import { constants } from '../utils/global';
import theme from '../theme';

const styles = {
  inputRoot: {
    color: 'inherit',
  },
  spacer: {
    flex: 1,
  },
};

const useSidebarStyles = makeStyles({
  fixed: { position: 'absolute' },
});

const MySidebar = (props) => {
  const classes = useSidebarStyles();
  return <Sidebar classes={classes} {...props} />;
};

const MyUserMenu = (props) => (
  <UserMenu {...props}>
    <MenuItemLink to="/profile" primaryText="Profile" leftIcon={<Settings />} />
    <Logout />
  </UserMenu>
);
const MyAppBar = withStyles(styles)(({ classes, ...props }) => {
  const [requestAccessDialogOpen, setRequestAccessDialogOpen] = useState(false);
  const [accessRequestMsg, setAccessRequestMsg] = useState('');
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  const user = permissions;
  if (!user) return null;

  return (
    <AppBar {...props} userMenu={<MyUserMenu />}>
      <Typography variant="h6" color="inherit" id="react-admin-title" />
      <span className={classes.spacer} />
      <WithPermissions
        // authParams={{ key: match.path, params: route.params }}
        // location is not required but it will trigger a new permissions check if specified when it changes
        // location={location}
        render={({ permissions }) => {
          // Get roles from user object (object structure with name property)
          const roleNames = permissions?.roles?.map((role) => role.name) || [];
          const isVictoryHill = roleNames.includes('VictoryHill');
          const isLattice = roleNames.includes('Lattice');
          const isLaerskool = roleNames.includes('Laerskool');
          const isEventide = roleNames.includes('Eventide');
          const isConnaught = roleNames.includes('Connaught');
          const isFresno = roleNames.includes('Fresno');
          const isThirdPartyOMPartner =
            roleNames.includes('OMPartnerI9') ||
            roleNames.includes('OMPartnerRun') ||
            roleNames.includes('OMPartnerSolRen');
          const loading = !permissions;
          if (
            isVictoryHill ||
            isLattice ||
            isThirdPartyOMPartner ||
            isLaerskool ||
            isEventide ||
            isConnaught ||
            isFresno ||
            loading
          )
            return null;
          return (
            <>
              <Hidden only={['xs', 'sm']}>
                <MySearchBar {...props} />
              </Hidden>
              <Button
                variant="text"
                style={{ color: 'white', textTransform: 'none', padding: 0 }}
                onClick={() => {
                  setRequestAccessDialogOpen(true);
                }}
              >
                Need Access?
              </Button>
              <Dialog
                fullScreen={fullScreen}
                open={requestAccessDialogOpen}
                onClose={() => {
                  setRequestAccessDialogOpen(false);
                }}
              >
                <DialogTitle>Request Access</DialogTitle>
                <DialogContent>
                  <Grid container>
                    <Grid item xs={12}>
                      <Typography>
                        Due to recent security enhancements, some pages may no
                        longer be accessible to you. Please describe below what
                        pages you need access to:
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl margin="normal" required fullWidth>
                        <TextField
                          required
                          variant="outlined"
                          minRows="4"
                          label="Message"
                          fullWidth
                          multiline
                          maxRows={12}
                          value={accessRequestMsg}
                          onChange={(event) => {
                            setAccessRequestMsg(event.target.value);
                          }}
                        />
                      </FormControl>
                    </Grid>
                  </Grid>
                </DialogContent>
                <DialogActions>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setAccessRequestMsg('');
                      setRequestAccessDialogOpen(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    disabled={accessRequestMsg.length === 0}
                    onClick={() => {
                      dataProvider
                        .create('LogToSlack', {
                          data: {
                            title: 'CMS Access Request',
                            type: 'it',
                            data: [
                              {
                                label: 'User',
                                value: `${user.firstName} ${user.lastName}`,
                              },
                              {
                                label: 'Request',
                                value: accessRequestMsg,
                              },
                            ],
                          },
                        })
                        .then(
                          () => {
                            setAccessRequestMsg('');
                            setRequestAccessDialogOpen(false);
                            notify(
                              'Access request sent. IT will be in touch soon.',
                              {
                                type: 'success',
                              }
                            );
                          },
                          (err) => {
                            console.error(err);
                            notify(
                              'Failed to send request. If this problem persists, contact <EMAIL>',
                              { type: 'error' }
                            );
                          }
                        );
                    }}
                  >
                    Submit
                  </Button>
                </DialogActions>
              </Dialog>
            </>
          );
        }}
      />
    </AppBar>
  );
});

const Menu = (props) => <MyMenu {...props} />;
const MyLayout = (props) => (
  <Layout {...props} menu={Menu} sidebar={MySidebar} appBar={MyAppBar} />
);

export default MyLayout;
