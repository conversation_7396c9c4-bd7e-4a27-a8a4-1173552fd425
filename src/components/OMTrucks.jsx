import React from 'react';
import { useParams } from 'react-router-dom';

import { Alert } from '@mui/lab';

import {
  <PERSON><PERSON>yField,
  Create,
  Datagrid,
  DateField,
  Edit,
  FunctionField,
  List,
  ReferenceArrayInput,
  ReferenceInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Chip, Grid, Typography } from '@mui/material';

import { CustomReferenceField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'O&M Truck';

export const OMTruckEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth required />
            <TextInput source="color" fullWidth helperText="Ex: #B15728" />
            <ReferenceInput
              perPage={10000}
              source="truckLeader.id"
              reference="EmployeeLite"
              sort={{ field: 'firstName', order: 'ASC' }}
            >
              <SelectInput
                label="Truck Leader"
                required
                fullWidth
                optionText="fullName"
              />
            </ReferenceInput>
            <ReferenceArrayInput
              source="projectIds"
              reference="Project"
              fullWidth
              sort={{ field: 'name', order: 'ASC' }}
              filter={{ omFlg: true }}
              perPage={10000}
            >
              <SelectArrayInput
                optionText="name"
                fullWidth
                helperText="These are the projects assigned to this O&M Truck"
              />
            </ReferenceArrayInput>
            <ReferenceArrayInput
              source="employeeIds"
              reference="EmployeeLite"
              fullWidth
              sort={{ field: 'firstName', order: 'ASC' }}
              filter={{ inactiveFlg: null }}
              perPage={10000}
            >
              <SelectArrayInput
                optionText="fullName"
                fullWidth
                // helperText="These are the field techs assigned to this O&M Truck"
                helperText={
                  <span>
                    These are the field techs assigned to this O&M Truck. Click
                    <a href="/OMTruck/">here</a> to add/remove employees from
                    this project's truck.
                  </span>
                }
              />
            </ReferenceArrayInput>
            {isIT ? (
              <TextInput
                source="slackChannelId"
                fullWidth
                helperText="If using Slack app, this ID can be found if click on the channel's info that was created already and scroll down to Channel ID. If using browser, its in the url. Typically starts with C and is 11 digits long."
              />
            ) : null}
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const OMTruckList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Alert severity="info">
        This table manages O&M Trucks. Please keep the Projects and Field Techs
        up to date.{' '}
        <b>
          You can edit each truck by clicking the truck's row (but not the
          employee chips or Project chips).
        </b>
      </Alert>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <FunctionField
          label="Name"
          render={(record) => (
            <Chip
              label={
                <Typography
                  variant="body2"
                  style={{ color: record.color ? 'white' : null }}
                >
                  {record.name}
                </Typography>
              }
              style={{ backgroundColor: record.color || 'white' }}
            />
          )}
        />
        <LinkField
          reference="Employee"
          linkSource="truckLeader.id"
          labelSource="truckLeader.fullName"
          label="Truck Leader"
        />
        <ArrayField source="employees" label="Field Techs" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="fullName" />
          </SingleFieldList>
        </ArrayField>
        <ArrayField source="projects" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="name" />
          </SingleFieldList>
        </ArrayField>
        <TextField source="slackChannelId" />
        <DateField source="createdAt" />
        <DateField source="updatedAt" />
      </Datagrid>
    </List>
  );
};

export const OMTruckCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="color" fullWidth helperText="Ex: #B15728" />
          <TextInput
            source="slackChannelId"
            fullWidth
            helperText="If using Slack app, this ID can be found if click on the channel's info that was created already and scroll down to Channel ID. If using browser, its in the url. Typically starts with C and is 11 digits long."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
