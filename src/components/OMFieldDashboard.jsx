import React, { useState, Fragment } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardActionArea,
  CardContent,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Alert } from '@mui/lab';
import {
  useDataProvider,
  downloadCSV,
  usePermissions,
  useRefresh,
} from 'react-admin';
import numeral from 'numeral';
import moment from 'moment-timezone';
import 'chartjs-adapter-moment';
import { withStyles } from '@mui/styles';
import {
  AddCircle,
  Cancel,
  Close,
  FileDownload,
  FilterList,
  Refresh,
  Settings,
} from '@mui/icons-material';
import queryString from 'query-string';

import { OMTicketExpansionPanel } from './OMTicketExpansionPanel';
import theme from '../theme';
import { roleMatrix } from '../utils/applyRoleAuth';
import { OMTicketDialog } from './OMTicketDialog';
import MiniImpactedGenerationLineChart from './MiniImpactedGenerationLineChart';
import { downloadSimpleExcelFromRows } from '../utils/excel';

const styles = (theme) => ({
  greenBtn: {
    background: `${theme.palette.green.dark} !important`,
    '&:hover': {
      background: `${theme.palette.green.main} !important`,
    },
  },
});

const updateQueryStringParameter = (key, value) => {
  let url = new URL(window.location.href);
  url.searchParams.set(key, value);
  window.history.pushState({ path: url.href }, '', url.href);
};

const removeQueryStringParameter = (key) => {
  const url = new URL(window.location.href);
  url.searchParams.delete(key);
  window.history.pushState({ path: url.href }, '', url.href);
};

const getLossColor = (val) => {
  if (val < 0.1) return theme.palette.green.dark;
  if (val < 0.2) return theme.palette.orange.main;
  if (val < 0.3) return theme.palette.warning.main;
  return theme.palette.error.main;
};

export default withStyles(styles)(({ classes }) => {
  const queryStringParams = queryString.parse(location.search);

  const fullScreen = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  // data/frontend
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTicketEdit, setSelectedTicketEdit] = useState(null);
  const [omTicketData, setOMTicketData] = useState(null);

  const [headerKPIs, setHeaderKPIs] = useState(null);
  const [headerKPIsLoading, setHeaderKPIsLoading] = useState(false);
  const [headerKPIsError, setHeaderKPIsError] = useState(null);

  const [selectedTruckId, setSelectedTruckId] = useState('all');
  const [trucks, setTrucks] = useState(null);
  const [trucksLoading, setTrucksLoading] = useState(false);
  const [trucksDataError, setTrucksDataError] = useState(null);
  const [updateTicketOpen, setUpdateTicketOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  // filters
  const [projectIds, setProjectIds] = useState(
    queryStringParams.projectIds && queryStringParams.projectIds !== 'all'
      ? queryStringParams.projectIds
          .split(',')
          .map((i) => parseInt(i, 10))
          .filter((el) => !!el)
      : 'all'
  );
  const [projects, setProjects] = useState(null);
  const [omTicketTypeId, setOMTicketTypeId] = useState(
    parseFloat(queryStringParams.ticketTypeId) || 'all'
  );
  const [ticketTypes, setTicketTypes] = useState(null);
  const [severity, setSeverity] = useState(queryStringParams.severity || 'all'); // all, high, medium, low
  const [ticketStatus, setTicketStatus] = useState(
    queryStringParams.status || 'open'
  ); // all, open, closed, pending
  const [startDate, setStartDate] = useState(
    queryStringParams.startDate || null
  );
  const [endDate, setEndDate] = useState(queryStringParams.endDate || null);
  const [ticketId, setTicketId] = useState(
    parseInt(queryStringParams.id) || null
  );
  const [createTicketOpen, setCreateTicketOpen] = useState(false);

  // sorting
  const [order, setOrder] = useState(queryStringParams.order || 'asc');
  const [orderBy, setOrderBy] = useState(
    queryStringParams.orderBy || 'priorityNo'
  );

  // pagination
  const [page, setPage] = useState(0);
  const [perPage, setPerPage] = useState(25);

  // Dialog Filters
  const [ticketFilterDialogOpen, setTicketFilterDialogOpen] = useState(false);

  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();
  const refresh = useRefresh();

  const roleNames = permissions?.roles.map((r) => r.name);
  const isThirdParty =
    roleNames?.includes('OMPartnerI9') ||
    roleNames?.includes('OMPartnerRun') ||
    roleNames.includes('OMPartnerSolRen');

  const lintedFilter = {
    status: ticketStatus,
    severity,
    projectIds,
    omTicketTypeId: omTicketTypeId,
    startDate,
    endDate,
    id: ticketId,
  };

  if (
    lintedFilter.projectIds === 'all' ||
    lintedFilter.projectIds?.length === 0
  ) {
    delete lintedFilter.projectIds;
  }
  if (lintedFilter.omTicketTypeId === 'all') {
    delete lintedFilter.omTicketTypeId;
  }
  if (!startDate) {
    delete lintedFilter.startDate;
  }
  if (!endDate) {
    delete lintedFilter.endDate;
  }
  if (!ticketId) {
    delete lintedFilter.id;
  }
  // if (queryStringParams !== location.search)
  Object.entries(lintedFilter).forEach(([key, value]) => {
    updateQueryStringParameter(key, value);
  });
  updateQueryStringParameter('orderBy', orderBy);
  updateQueryStringParameter('order', order);

  const fetchOMTickets = ({ pagination, filter, sort }, downloadAsReport) => {
    if (downloadAsReport) {
      setExportLoading(true);
    } else {
      setLoading(true);
      setError(null);
    }

    const keys = Object.keys(filter || {});

    const lintedFilter = {
      status: filter?.ticketStatus || ticketStatus,
      severity: filter?.severity || severity,
      projectIds: filter?.projectIds || projectIds,
      omTruckId: filter?.omTruckId || selectedTruckId,
      omTicketTypeId: filter?.omTicketTypeId || omTicketTypeId,
      startDate: keys.indexOf('startDate') > -1 ? filter?.startDate : startDate,
      endDate: keys.indexOf('endDate') > -1 ? filter?.endDate : endDate,
      id:
        keys.indexOf('ticketId') > -1
          ? filter?.ticketId
            ? parseInt(filter?.ticketId)
            : filter?.ticketId
          : parseInt(ticketId),
    };

    if (lintedFilter.omTruckId === 'all') {
      delete lintedFilter.omTruckId;
    }
    if (
      lintedFilter.projectIds === 'all' ||
      lintedFilter.projectIds?.length === 0
    ) {
      delete lintedFilter.projectIds;
    }
    if (lintedFilter.omTicketTypeId === 'all') {
      delete lintedFilter.omTicketTypeId;
    }
    if (!startDate) {
      delete lintedFilter.startDate;
    }
    if (!endDate) {
      delete lintedFilter.endDate;
    }
    if (!ticketId) {
      delete lintedFilter.id;
    }
    const lintedPagination = {
      perPage: pagination?.perPage || perPage,
    };
    if (pagination?.page || pagination?.page === 0) {
      lintedPagination.page = pagination.page + 1;
    } else {
      lintedPagination.page = page + 1;
    }
    if (downloadAsReport) {
      lintedPagination.page = 1;
      lintedPagination.perPage = 100000;
    }
    dataProvider
      .getList('OMFieldTicket', {
        sort: { field: sort?.orderBy || orderBy, order: sort?.order || order },
        pagination: lintedPagination,
        filter: lintedFilter,
      })
      .then(
        (res) => {
          if (downloadAsReport) {
            setExportLoading(false);
            exporter(res);
          } else {
            setOMTicketData(res);
            setLoading(false);
          }
        },
        (e) => {
          if (downloadAsReport) {
            setExportLoading(false);
            console.error('Error exporting ticket list', e);
          } else {
            setLoading(false);
            setError(e);
            console.error('Error retrieving tickets', e);
          }
        }
      );
  };

  const fetchTrucks = () => {
    dataProvider.getOne('OMFieldDashboardTrucks', {}).then(
      (res) => {
        const { truck, projectIds: queryStringProjectIds } = queryString.parse(
          window.location.search
        );
        if (selectedTruckId === 'all' && truck) {
          const queryStringTruck = res.data.allOMTrucks.filter(
            (el) => String(el.id) === truck
          );
          setSelectedTruckId(queryStringTruck?.[0]?.id || 'all');
          if (
            projectIds === 'all' &&
            queryStringProjectIds &&
            queryStringProjectIds !== 'all'
          ) {
            const truckProjectIds =
              (truck.projects && truck.projects.map((p) => p.id)) || [];
            setProjectIds(
              projectIds
                .split(',')
                .map((i) => parseInt(i, 10))
                .filter((i) => truckProjectIds.indexOf(i) > -1)
            );
          }
        }
        if (isThirdParty && res.data.allOMTrucks.length === 1) {
          setSelectedTruckId(res.data.allOMTrucks[0].id);
        }
        setTrucks(res.data.allOMTrucks);
        setTrucksLoading(false);
        setTrucksDataError(null);
      },
      (e) => {
        console.error('Error retrieving trucks', e);
        setTrucksLoading(false);
        setTrucksDataError(e);
      }
    );
  };

  const fetchFilterData = () => {
    dataProvider.getOne('OMFieldDashboardFilterData', {}).then(
      (res) => {
        setProjects(res.data.allOMProjects);
        setTicketTypes(res.data.allOMTicketTypes);
      },
      (err) => {
        console.error('Error fetching OMField Dashboard data', err);
      }
    );
  };

  const fetchHeaderKPIData = () => {
    dataProvider.getOne('OMFieldDashboardHeaderKPIData', {}).then(
      (res) => {
        setHeaderKPIs(res.data);
        setHeaderKPIsLoading(false);
        setHeaderKPIsError(null);
      },
      (err) => {
        console.error('Error fetching header KPIs', err);
        setHeaderKPIsLoading(false);
        setHeaderKPIsError(err);
      }
    );
  };

  if (!trucks && !trucksLoading && !trucksDataError) {
    setTrucksLoading(true);
    fetchTrucks();
  }

  if (!isThirdParty && !headerKPIs && !headerKPIsLoading && !headerKPIsError) {
    setHeaderKPIsLoading(true);
    fetchHeaderKPIData();
  }

  if (!loading && !omTicketData && !error) {
    setLoading(true);
    fetchOMTickets({
      filter: {
        omTruckId: selectedTruckId || null,
      },
    });
    fetchFilterData();
  }

  const handleClearFilters = () => {
    setStartDate(null);
    setEndDate(null);
    setProjectIds('all');
    setSeverity('all');
    setTicketStatus('all');
    setTicketId(null);

    fetchOMTickets({
      filter: {
        startDate: null,
        endDate: null,
        projectIds: 'all',
        severity: 'all',
        ticketStatus: 'all',
        ticketId: null,
      },
    });
  };

  const renderFilterDialog = () => (
    <Dialog
      open={ticketFilterDialogOpen}
      onClose={() => {
        setTicketFilterDialogOpen(false);
        fetchOMTickets({});
      }}
      style={{ margin: '2rem' }}
      fullScreen={fullScreen}
    >
      <DialogTitle>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="h5">Filters</Typography>
          </Grid>
          <Grid item>
            <IconButton
              onClick={() => {
                setTicketFilterDialogOpen(false);
                fetchOMTickets({});
              }}
            >
              <Close />
            </IconButton>
          </Grid>
        </Grid>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Grid item xs={12}>
              <Typography variant="body1">Ticket Impact Date Range:</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="caption" style={{ fontStyle: 'italic' }}>
                The below date selectors will filter the ticket list down to all
                tickets that were open within the range provided. If no end date
                is provided, all open tickets or tickets that were opened after
                the start date, will be included. To see only closed tickets in
                the last week, set the start date to 7 days ago and the ticket
                status to closed.
              </Typography>
            </Grid>
            <TextField
              size="small"
              id="startDt"
              label="Start Date"
              type="date"
              value={startDate || ''}
              onChange={(event) => {
                if (event.target.value) {
                  setStartDate(moment(event.target.value).format('YYYY-MM-DD'));
                } else {
                  setStartDate(null);
                }
              }}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <TextField
              size="small"
              id="endDt"
              label="End Date"
              type="date"
              style={{ marginLeft: fullScreen ? null : '1rem' }}
              value={endDate || ''}
              onChange={(event) => {
                if (event.target.value) {
                  setEndDate(moment(event.target.value).format('YYYY-MM-DD'));
                } else {
                  setEndDate(null);
                }
              }}
              InputLabelProps={{
                shrink: true,
              }}
              error={startDate && endDate && endDate < startDate}
              helperText={
                startDate && endDate && endDate < startDate
                  ? "'Start Date' must precede 'End Date'"
                  : null
              }
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body1">Ticket #:</Typography>
            <TextField
              size="small"
              id="ticketId"
              label="Ticket #"
              type="number"
              value={ticketId || ''}
              onChange={(event) => setTicketId(event.target.value)}
            />
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid container item xs={12} spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="status-select-label">Project</InputLabel>
                <Select
                  labelId="project-select-label"
                  size="small"
                  label="project-filter"
                  value={projectIds === 'all' ? ['all'] : projectIds}
                  multiple
                  onChange={(event) => {
                    if (
                      event.target.value?.length > 0 &&
                      event.target.value[0] !== 'all' &&
                      event.target.value.indexOf('all') > -1
                    ) {
                      setProjectIds('all');
                    } else {
                      setProjectIds(event.target.value);
                    }
                    setPage(0);
                  }}
                >
                  <MenuItem value="all" key="all-projects">
                    All Projects
                  </MenuItem>
                  {projects &&
                    projects
                      ?.filter((p) =>
                        selectedTruckId === 'all'
                          ? true
                          : p.omTruckId === selectedTruckId
                      )
                      ?.map((project) => (
                        <MenuItem
                          value={project.id}
                          key={`project-${project.id}`}
                        >
                          {project.name}
                        </MenuItem>
                      ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="status-select-label">Ticket Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  size="small"
                  label="ticket-status"
                  value={ticketStatus || ''}
                  onChange={(event) => {
                    setTicketStatus(event.target.value);
                    setPage(0);
                  }}
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="open">Open Tickets</MenuItem>
                  <MenuItem value="closed">Closed Tickets</MenuItem>
                  <MenuItem value="pending">Pending Tickets</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="severity-select-label">
                  Ticket Severity
                </InputLabel>
                <Select
                  size="small"
                  labelId="severity-select-label"
                  label="ticket-severity"
                  value={severity || ''}
                  onChange={(event) => {
                    setSeverity(event.target.value);
                    setPage(0);
                  }}
                >
                  <MenuItem value="all">All Severities</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="type-select-label">Ticket Type</InputLabel>
                <Select
                  labelId="type-select-label"
                  size="small"
                  label="type-filter"
                  value={omTicketTypeId || ''}
                  onChange={(event) => {
                    setOMTicketTypeId(event.target.value);
                    setPage(0);
                  }}
                >
                  <MenuItem value="all" key="all-types">
                    All Types
                  </MenuItem>
                  {ticketTypes &&
                    ticketTypes?.map((ticketType) => (
                      <MenuItem
                        value={ticketType.id}
                        key={`type-${ticketType.id}`}
                      >
                        {ticketType.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            fetchOMTickets({});
            setTicketFilterDialogOpen(false);
            // TODO: set filters in url as query strings
            queryString;
          }}
          variant="contained"
          color="primary"
          autoFocus
          disabled={startDate && endDate && endDate < startDate}
        >
          Filter
        </Button>
      </DialogActions>
    </Dialog>
  );

  const renderFilterChips = () => {
    // const projectName = projects?.find((p) => p.id === projectIds?.[0])?.name;
    const truckName = trucks?.find((t) => t.id === selectedTruckId)?.name;
    const filterChipConfig = [
      // {
      //   label: `Project - ${projectName}`,
      //   show:
      //     selectedTruckId === 'all' &&
      //     projectIds !== 'all' &&
      //     projectIds.length === 1,
      //   onDelete: () => {
      //     setProjectIds('all');
      //     removeQueryStringParameter('projectIds');
      //     fetchOMTickets({
      //       filter: { projectIds: 'all' },
      //     });
      //   },
      // },
      {
        label: `Ticket # - ${ticketId}`,
        show: ticketId,
        onDelete: () => {
          setTicketId(null);
          removeQueryStringParameter('id');
          fetchOMTickets({
            filter: { ticketId: null },
          });
        },
      },
      {
        label: `Severity - ${severity === 'all' ? 'All Severities' : severity}`,
        show: severity !== 'all',
        onDelete: () => {
          setSeverity('all');
          removeQueryStringParameter('severity');
          fetchOMTickets({
            filter: { severity: 'all' },
          });
        },
      },
      {
        label: `Status - ${
          ticketStatus === 'all' ? 'All Statuses' : ticketStatus
        }`,
        show: ticketStatus !== 'all',
        onDelete: () => {
          setTicketStatus('all');
          removeQueryStringParameter('status');
          fetchOMTickets({
            filter: { ticketStatus: 'all' },
          });
        },
      },
      {
        label: `Truck - ${
          selectedTruckId === 'all' ? 'All Trucks' : truckName
        }`,
        show: selectedTruckId !== 'all',
        onDelete: () => {
          setSelectedTruckId('all');
          setProjectIds('all');
          removeQueryStringParameter('truck');
          removeQueryStringParameter('projectIds');
          fetchOMTickets({
            filter: { omTruckId: 'all', projectIds: 'all' },
          });
        },
      },
      {
        label: `Date Range - (${
          startDate ? moment(startDate).format('ll') : 'Start'
        } - ${endDate ? moment(endDate).format('ll') : 'Today'})`,
        show: startDate || endDate,
        onDelete: () => {
          setStartDate(null);
          setEndDate(null);
          removeQueryStringParameter('startDate');
          removeQueryStringParameter('endDate');
          fetchOMTickets({
            filter: { startDate: null, endDate: null },
          });
        },
      },
    ];
    const projectFilterChips =
      // selectedTruckId !== 'all' &&
      projects &&
      projects.filter((p) => {
        if (selectedTruckId !== 'all') {
          return p.omTruckId === selectedTruckId;
        }
        if (projectIds !== 'all') {
          return projectIds.indexOf(p.id) > -1;
        }
        return false;
      });
    return (
      <Grid container spacing={1}>
        {filterChipConfig.map((config, i) => {
          if (config.show) {
            return (
              <Grid item key={`filter-chip-${i}}`}>
                <Chip
                  label={config.label}
                  clickable={true}
                  onDelete={config.onDelete}
                  deleteIcon={<Cancel style={{ color: 'white' }} />}
                  onClick={() => {
                    setTicketFilterDialogOpen(true);
                  }}
                  style={{
                    backgroundColor: theme.palette.primary.main,
                    color: 'white',
                  }}
                />
              </Grid>
            );
          }
          return null;
        })}
        {projectFilterChips &&
          projectFilterChips.map((project) => {
            const selected =
              projectIds !== 'all' && projectIds.indexOf(project.id) > -1;
            return (
              <Grid item key={`project-filter-chip-${project?.id}`}>
                <Chip
                  label={project.name}
                  clickable={true}
                  onClick={() => {
                    const updatedProjectsIds =
                      projectIds === 'all'
                        ? projectFilterChips.map((p) => p.id)
                        : [...projectIds];
                    if (selected) {
                      updatedProjectsIds.splice(
                        updatedProjectsIds.indexOf(project.id),
                        1
                      );
                    } else {
                      if (updatedProjectsIds.indexOf(project.id) === -1) {
                        updatedProjectsIds.push(project.id);
                      }
                    }
                    setProjectIds(updatedProjectsIds);
                    fetchOMTickets({
                      filter: { projectIds: updatedProjectsIds },
                    });
                  }}
                  style={{
                    color: selected ? 'white' : null,
                    backgroundColor: selected
                      ? theme.palette.primary.main
                      : null,
                  }}
                />
              </Grid>
            );
          })}
      </Grid>
    );
  };

  const renderDesktopFilters = () => (
    <Grid item>
      <Button
        variant="contained"
        size="small"
        color="secondary"
        onClick={() => setTicketFilterDialogOpen(true)}
        startIcon={<FilterList />}
      >
        Add Filter
      </Button>
    </Grid>
  );

  const renderMobileFilters = () => (
    <IconButton onClick={() => setTicketFilterDialogOpen(true)}>
      <Settings />
    </IconButton>
  );

  const renderTickets = () => {
    if (error) {
      return (
        <Alert severity="error">There was an issue loading tickets.</Alert>
      );
    }

    if (loading || !omTicketData) {
      return (
        <Grid item xs={12}>
          <Grid container style={{ width: '100%' }} justifyContent="center">
            <Grid item>
              <CircularProgress />
            </Grid>
          </Grid>
        </Grid>
      );
    }
    if (omTicketData.data.length === 0) {
      if (
        status === 'open' &&
        startDate === null &&
        endDate === null &&
        severity === 'all' &&
        ticketId === null &&
        projectIds === 'all'
      ) {
        return (
          <Grid item>
            <Alert severity="success" style={{ width: '100%' }}>
              Congratulations! There are currently no open tickets.
            </Alert>
          </Grid>
        );
      }
      return (
        <Grid item>
          <Alert severity="info" style={{ width: '100%' }}>
            No tickets match your filter criteria.
          </Alert>
        </Grid>
      );
    }

    const headerRowColumns = [
      {
        label: 'Ticket #',
        align: 'center',
        sortable: true,
        orderField: 'id',
        renderValue: (ticket) => ticket.id,
      },
      {
        label: 'Status',
        align: 'center',
        renderValue: (ticket) => {
          const getTicketStatusColor = (status) => {
            if (status.toLowerCase() === 'closed')
              return theme.palette.green.dark;
            if (status.toLowerCase() === 'pending')
              return theme.palette.warning.dark;
            if (status.toLowerCase() === 'open')
              return theme.palette.orange.main;
          };
          return (
            <Tooltip
              title={
                ticket.omReportCount
                  ? `${ticket.omReportCount} service reports created`
                  : null
              }
              arrow
            >
              {/* <Badge
          badgeContent={ticket.omReportCount}
          color="primary"
        >
          {ticket.status}
        </Badge> */}
              <Chip
                label={ticket.status}
                clickable={false}
                size="small"
                style={{
                  color: '#fff',
                  fontWeight: 'bold',
                  backgroundColor: getTicketStatusColor(ticket.status),
                }}
              />
            </Tooltip>
          );
        },
      },
      {
        label: 'Priority',
        align: 'center',
        sortable: true,
        orderField: 'priorityNo',
        renderValue: (ticket) => ticket.priorityNo,
      },
      {
        label: 'Ticket Type',
        align: 'center',
        hideOnMobile: true,
        renderValue: (ticket) => ticket.omTicketType?.shortName,
      },
      {
        label: 'Project',
        renderValue: (ticket) => (
          <Grid container direction="column">
            <Grid item>
              <Typography variant="body2">{ticket.project?.name}</Typography>
            </Grid>
            <Grid item>
              <Typography variant="caption">
                ({ticket.project?.omTruck?.name || 'No truck assigned'})
              </Typography>
            </Grid>
          </Grid>
        ),
      },
      { label: 'Title', renderValue: (ticket) => ticket.title },
      {
        label: 'Start Date',
        sortable: true,
        orderField: 'startDt',
        hideOnMobile: true,
        renderValue: (ticket) =>
          ticket.startDt ? moment(ticket.startDt).format('MMM D, YYYY') : null,
      },
      {
        label: 'Days to Acknowledge',
        align: 'center',
        hideOnMobile: true,
        renderValue: (ticket) => {
          const getDaysToAcknowledgeColor = (days) => {
            if (days < 1) return theme.palette.green.dark;
            if (days < 3) return theme.palette.warning.main;
            if (days < 5) return theme.palette.orange.main;
            return theme.palette.error.main;
          };
          const daysToAcknowledge = moment(ticket.createdAt).diff(
            moment(ticket.startDt),
            'days',
            true
          );
          return (
            <Chip
              label={numeral(daysToAcknowledge).format('0,0.0')}
              clickable={false}
              size="small"
              style={{
                color: '#fff',
                fontWeight: 'bold',
                backgroundColor: getDaysToAcknowledgeColor(daysToAcknowledge),
              }}
            />
          );
        },
      },
      {
        label: 'Days Open',
        align: 'center',
        hideOnMobile: true,
        renderValue: (ticket) => {
          const getTicketDaysOpenColor = (days) => {
            if (days < 5) return 'grey';
            if (days < 10) return theme.palette.yellow.main;
            if (days < 15) return theme.palette.orange.main;
            return theme.palette.error.main;
          };
          const ticketDaysOpen =
            ticket.startDt &&
            moment(ticket.endDt || moment()).diff(
              moment(ticket.startDt),
              'days',
              true
            );
          if (ticketDaysOpen) {
            return (
              <Chip
                label={numeral(ticketDaysOpen).format('0,0.0')}
                clickable={false}
                size="small"
                style={{
                  color: '#fff',
                  fontWeight: 'bold',
                  backgroundColor: getTicketDaysOpenColor(ticketDaysOpen),
                }}
              />
            );
          }
          return '-';
        },
      },
      {
        label: 'Severity',
        align: 'center',
        sortable: true,
        orderField: 'estimatedPercentageLoss',
        hideOnMobile: true,
        renderValue: (ticket) => {
          const getTicketSeverityColor = (days) => {
            if (ticket.severity.toLowerCase() === 'high')
              return theme.palette.error.main;
            if (ticket.severity.toLowerCase() === 'medium')
              return theme.palette.orange.main;
            return theme.palette.yellow.main;
          };
          return (
            <Tooltip
              title={
                <Table size="small" style={{ color: '#fff' }}>
                  <TableRow>
                    <TableCell style={{ color: '#fff' }}>
                      Est. Percentage Loss
                    </TableCell>
                    <TableCell style={{ color: '#fff' }}>
                      {`${numeral(ticket.estimatedPercentageLoss || 0).format(
                        '0[.]0'
                      )}
                                      %`}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell style={{ color: '#fff' }}>
                      Est. Generation Loss
                    </TableCell>
                    <TableCell style={{ color: '#fff' }}>
                      {`${numeral(
                        ticket.estimatedImpact?.estimatedGenerationLoss || 0
                      ).format('0,0[.]0')}
                                      kWh`}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell style={{ color: '#fff' }}>
                      Est. Revenue Loss (USD)
                    </TableCell>
                    <TableCell style={{ color: '#fff' }}>
                      {numeral(
                        ticket.estimatedImpact?.estimatedRevenueLossUSD || 0
                      ).format('$0,0')}
                    </TableCell>
                  </TableRow>
                </Table>
              }
              arrow
            >
              <Chip
                label={ticket.severity}
                clickable={false}
                size="small"
                style={{
                  color: '#fff',
                  fontWeight: 'bold',
                  backgroundColor: getTicketSeverityColor(ticket.severity),
                }}
              />
            </Tooltip>
          );
        },
      },
    ];

    return (
      <Grid container style={{ width: '100%' }}>
        <Card style={{ margin: fullScreen ? null : '.5rem', width: '100%' }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  {headerRowColumns.map((headCell, i) => {
                    if (headCell.hideOnMobile && fullScreen) return null;
                    return (
                      <TableCell
                        align={headCell.align}
                        key={`header-cell-2-${i}`}
                      >
                        {headCell.sortable ? (
                          <TableSortLabel
                            active={orderBy === headCell.orderField}
                            direction={
                              orderBy === headCell.orderField ? order : 'asc'
                            }
                            onClick={() => {
                              const isAsc =
                                orderBy === headCell.orderField &&
                                order === 'asc';
                              setOrder(isAsc ? 'desc' : 'asc');
                              setOrderBy(headCell.orderField);
                              fetchOMTickets({
                                sort: {
                                  orderBy: headCell.orderField,
                                  order: isAsc ? 'desc' : 'asc',
                                },
                              });
                            }}
                          >
                            {headCell.label}
                          </TableSortLabel>
                        ) : (
                          headCell.label
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              </TableHead>
              <TableBody>
                {omTicketData.data.map((ticket) => {
                  const rowSelected =
                    selectedTicket && selectedTicket.id === ticket.id;
                  return (
                    <Fragment key={`om-ticket-row-${ticket.id}`}>
                      <TableRow
                        hover
                        onClick={() =>
                          setSelectedTicket(rowSelected ? null : ticket)
                        }
                        style={{ cursor: 'pointer' }}
                        selected={rowSelected}
                      >
                        {headerRowColumns.map((headCell, i) => {
                          if (headCell.hideOnMobile && fullScreen) return null;
                          return (
                            <TableCell
                              align={headCell.align}
                              key={`head-cell-${ticket.id}-${i}`}
                            >
                              {headCell.renderValue(ticket)}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      <OMTicketExpansionPanel
                        open={rowSelected}
                        ticket={ticket}
                        fullScreen={fullScreen}
                        setUpdateTicketOpen={setUpdateTicketOpen}
                        setSelectedTicketEdit={setSelectedTicketEdit}
                      />
                    </Fragment>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[10, 25, 50]}
            component="div"
            count={omTicketData.total}
            rowsPerPage={perPage}
            page={page}
            onPageChange={(event, pageNumber) => {
              setPage(pageNumber);
              fetchOMTickets({
                pagination: { page: pageNumber, perPage },
              });
            }}
            onRowsPerPageChange={(event) => {
              setPage(0);
              setPerPage(event.target.value);
              fetchOMTickets({
                pagination: { page: 0, perPage: event.target.value },
              });
            }}
          />
        </Card>
      </Grid>
    );
  };

  const exporter = (rows) => {
    const columnConfig = [
      { label: 'Ticket #', getValue: (row) => row.id },
      { label: 'Status', getValue: (row) => (row.endDt ? 'Closed' : 'Open') },
      { label: 'Project', getValue: (row) => row.project?.name },
      { label: 'Truck', getValue: (row) => row.project?.omTruck?.name },
      { label: 'Title', getValue: (row) => row.title },
      { label: 'Ticket Type', getValue: (row) => row.omTicketType?.name },
      { label: 'Start Dt', getValue: (row) => row.startDt },
      { label: 'End Dt', getValue: (row) => row.endDt },
      {
        label: 'Acknowledged Dt',
        getValue: (row) => moment(row.createdAt).format('MM/DD/YYYY'),
      },
      { label: 'Severity', getValue: (row) => row.severity },
      { label: 'Priority', getValue: (row) => row.priorityNo },
      {
        label: 'Client Notification Required?',
        getValue: (row) => !!row.clientNotificationRequiredFlg,
      },
      {
        label: 'Client Notified Dt',
        getValue: (row) =>
          row.clientNotifiedDt &&
          moment(row.clientNotifiedDt).format('MM/DD/YYYY'),
      },
      {
        label: 'Client Notification Type',
        getValue: (row) => row.clientNotificationType,
      },
      { label: 'Ticket Author', getValue: (row) => row.author?.fullName },
      { label: 'Ticket Owner', getValue: (row) => row.ticketOwner?.fullName },
      {
        label: 'Estimated Percentage Loss',
        getValue: (row) => row.estimatedPercentageLoss,
      },
      {
        label: 'Est. Generation Loss',
        getValue: (row) => row.estimatedImpact?.estimatedGenerationLoss,
      },
      {
        label: 'Est. Revenue Loss (USD)',
        getValue: (row) => row.estimatedImpact?.estimatedRevenueLossUSD,
      },
      { label: 'Internal Notes', getValue: (row) => row.notes },
      { label: 'Status Notes', getValue: (row) => row.statusNotes },
    ];

    const rowsForExport = rows.data.map((row) => {
      const returnRow = {};
      columnConfig.forEach((column) => {
        returnRow[String(column.label)] = column.getValue(row);
      });
      return returnRow;
    });

    return downloadSimpleExcelFromRows(
      rowsForExport,
      columnConfig.map((c) => c.label),
      `${moment().format('YYYY-MM-DD')}_O&M_Tickets.xlsx`
    );
  };

  const userRoles = permissions?.roles?.map((el) => el.name);
  const hasWriteAccess = roleMatrix['OMFieldDashboard'].writeAccess.some((r) =>
    userRoles.includes(r)
  );

  let truckTilesJsx = null;

  if (trucks) {
    truckTilesJsx = trucks.map((truck) => {
      const selected = truck.id === selectedTruckId;

      const cardContentJsx = (
        <Grid
          container
          justifyContent="center"
          alignItems="center"
          item
          xs={12}
          key={`truck-content-${truck.id}`}
        >
          <Grid item xs={12}>
            <Typography variant="h5" style={{ fontWeight: 'normal' }}>
              {truck.name}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {numeral(truck.projectCount).format('0,0')} project
              {truck.projectCount === 1 ? '' : 's'} (
              {numeral(truck.totalSystemSizeMWDC).format('0,0[.]0')} MW)
            </Typography>
            <Typography variant="body2">
              {numeral(truck.openOMTicketsCount).format('0,0')} open ticket
              {truck.openOMTicketsCount === 1 ? '' : 's'}
            </Typography>
            <Typography variant="body2">
              {numeral(truck.pendingOMTicketsCount).format('0,0')} pending
              ticket
              {truck.pendingOMTicketsCount === 1 ? '' : 's'}
            </Typography>
            <Tooltip arrow title="Service reports created in the last 30 days">
              <Typography variant="body2">
                {numeral(truck.recentServiceReportCount).format('0,0')} recent
                service report
                {truck.recentServiceReportCount === 1 ? '' : 's'}
              </Typography>
            </Tooltip>
            <Tooltip
              arrow
              title="Total generation currently being lost due to open O&M tickets."
            >
              <Typography
                variant="body2"
                style={{
                  color: getLossColor(
                    truck.percentGenerationImpactedByOpenOMTickets
                  ),
                  fontWeight: 'bold',
                }}
              >
                {numeral(truck.percentGenerationImpactedByOpenOMTickets).format(
                  '%0[.]0'
                )}{' '}
                generation affected
              </Typography>
            </Tooltip>
          </Grid>
          <Grid item xs={6}>
            <MiniImpactedGenerationLineChart truck={truck} />
          </Grid>
        </Grid>
      );
      return (
        // NOTE: If you change the breakpoints here, change them in the else clause also for the skeletons
        <Grid key={`truck-container-${truck.id}`} item xl={4} sm={6} xs={12}>
          <Card
            elevation={selected ? 0 : 10}
            style={{
              height: '10.5rem',
              border: selected ? `solid ${theme.palette.primary.main} 3px` : '',
              backgroundColor: selected ? 'rgba(230, 230, 230, 0.3)' : '',
              boxSizing: 'border-box',
              borderRadius: theme.shape.borderRadius,
            }}
          >
            <CardActionArea
              onClick={() => {
                const selectedProjectIds =
                  projects &&
                  truck &&
                  projects
                    .filter((p) => p.omTruckId === truck.id)
                    .map((p) => p.id);
                if (selected) {
                  removeQueryStringParameter('truck');
                  setSelectedTruckId('all');
                  removeQueryStringParameter('projectIds');
                  setProjectIds('all');
                } else {
                  if (history.pushState) {
                    const newUrl =
                      window.location.protocol +
                      '//' +
                      window.location.host +
                      window.location.pathname +
                      `?truck=${truck.id}&projectIds=${
                        selectedProjectIds
                          ? selectedProjectIds.join(',')
                          : 'all'
                      }`;
                    window.history.pushState({ path: newUrl }, '', newUrl);
                  }
                  setSelectedTruckId(truck.id);
                  setProjectIds(selectedProjectIds);
                }
                setTicketId(null);
                fetchOMTickets({
                  filter: {
                    omTruckId: selected ? 'all' : truck.id,
                    projectIds: selected ? 'all' : selectedProjectIds,
                    ticketId: null,
                  },
                });
              }}
              aria-label={`${truck.name} chart`}
              style={{
                cursor: 'pointer',
                height: '100%',
                padding: selected ? null : '3px',
              }}
            >
              <CardContent>{cardContentJsx}</CardContent>
            </CardActionArea>
          </Card>
        </Grid>
      );
    });
  } else {
    truckTilesJsx = [1, 2, 3, 4, 5, 6].map((index) => (
      <Grid
        key={`truck-container-skeleton-${index}`}
        item
        // NOTE: If you change the breakpoints here, change them in the if clause also for the Cards
        xl={4}
        sm={6}
        xs={12}
      >
        <Skeleton
          animation="wave"
          variant="rectangular"
          style={{
            borderRadius: theme.shape.borderRadius,
          }}
          height="10.5rem"
        />
      </Grid>
    ));
  }

  return (
    <>
      <Grid container style={{ margin: fullScreen ? null : '1rem' }}>
        <Grid container style={{ padding: '1rem' }}>
          <Grid
            item
            xs={12}
            style={{ margin: '1rem 0 2rem', textAlign: 'right' }}
          >
            {headerKPIs ? (
              <Grid container justifyContent="space-evenly" spacing={10}>
                <Grid item xs={6} md={4}>
                  <Grid
                    container
                    direction="column"
                    alignItems="center"
                    justifyContent="center"
                    style={{ minWidth: '8rem' }}
                  >
                    <Grid
                      container
                      style={{ minHeight: '5rem' }}
                      item
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Grid
                        container
                        spacing={3}
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Grid item>
                          <Typography
                            style={{
                              fontWeight: 'bold',
                              textAlign: 'center',
                              color: theme.palette.green.main,
                            }}
                            variant="h3"
                          >
                            {numeral(
                              headerKPIs.openOMTicketCountHigh +
                                headerKPIs.openOMTicketCountMedium +
                                headerKPIs.openOMTicketCountLow
                            ).format('0,0')}{' '}
                          </Typography>
                        </Grid>
                        <Grid item>
                          <Grid container direction="column" spacing={0}>
                            {[
                              {
                                severity: 'High',
                                count: headerKPIs.openOMTicketCountHigh,
                              },
                              {
                                severity: 'Medium',
                                count: headerKPIs.openOMTicketCountMedium,
                              },
                              {
                                severity: 'Low',
                                count: headerKPIs.openOMTicketCountLow,
                              },
                            ].map((el, i) => (
                              <Grid item key={`proj-count-summary-key-${i}`}>
                                <Grid
                                  container
                                  justifyContent="space-between"
                                  spacing={2}
                                >
                                  <Grid item>
                                    <Typography variant="body2">
                                      {el.severity}
                                    </Typography>
                                  </Grid>
                                  <Grid item>
                                    <Typography variant="body2">
                                      {numeral(el.count).format('0,0')}{' '}
                                    </Typography>
                                  </Grid>
                                </Grid>
                              </Grid>
                            ))}
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item>
                      <Typography>
                        <b>Open Tickets</b>
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
                {!fullScreen && (
                  <Grid item md={4} xs={6}>
                    <Grid
                      container
                      direction="column"
                      alignItems="center"
                      justifyContent="center"
                      style={{ minWidth: '8rem' }}
                    >
                      <Grid
                        container
                        style={{ minHeight: '5rem' }}
                        item
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Grid
                          container
                          spacing={3}
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Grid item>
                            <Typography
                              style={{
                                fontWeight: 'bold',
                                textAlign: 'center',
                                color: theme.palette.green.main,
                              }}
                              variant="h3"
                            >
                              {numeral(headerKPIs.totalOMProjectCount).format(
                                '0,0'
                              )}{' '}
                            </Typography>
                          </Grid>
                          <Grid item>
                            <Grid container direction="column" spacing={0}>
                              <Grid item key="proj-system-size">
                                <Grid
                                  container
                                  justifyContent="space-between"
                                  spacing={2}
                                >
                                  <Grid item>
                                    <Typography variant="body2">
                                      {numeral(
                                        headerKPIs.totalOMProjectSystemSizeSumDC
                                      ).format('0,0[.]0')}{' '}
                                      MW
                                    </Typography>
                                  </Grid>
                                </Grid>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                      <Grid item>
                        <Typography>
                          <b>Number of Projects Under O&M Mgmt</b>
                        </Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                )}
                <Grid item md={4} xs={6}>
                  <Grid
                    container
                    direction="column"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Grid
                      container
                      style={{ minHeight: '5rem' }}
                      item
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Typography
                        style={{
                          fontWeight: 'bold',
                          textAlign: 'center',
                          color: theme.palette.green.main,
                        }}
                        variant="h3"
                      >
                        {numeral(
                          headerKPIs.percentGenerationImpactedByOpenOMTickets
                        ).format('%0[.]0')}
                      </Typography>
                    </Grid>
                    <Grid item>
                      <Typography>
                        <b>Generation Affected by Open Tickets</b>
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            ) : isThirdParty ? null : (
              <Grid container style={{ width: '100%' }} justifyContent="center">
                <CircularProgress />
              </Grid>
            )}
          </Grid>
          <Grid container spacing={3}>
            {truckTilesJsx}
          </Grid>
          <Divider
            style={{
              width: '100%',
              marginTop: '2em',
              marginBottom: '2em',
            }}
          />
        </Grid>

        <Grid
          container
          item
          xs={12}
          justifyContent="space-between"
          alignItems="center"
          style={{ paddingBottom: '.5rem' }}
        >
          <Grid item>
            <Grid container alignItems="center">
              <Grid item>
                <Typography variant="h5">Tickets</Typography>
              </Grid>
              <Grid item>{fullScreen ? renderMobileFilters() : null}</Grid>
            </Grid>
          </Grid>
          <Grid item>
            <Grid
              container
              justifyContent="flex-end"
              alignItems="center"
              spacing={1}
            >
              {fullScreen ? null : renderDesktopFilters()}
              {renderFilterDialog()}
              <Grid item>
                {hasWriteAccess ? (
                  <Button
                    size="small"
                    startIcon={<AddCircle />}
                    color="primary"
                    variant="contained"
                    className={classes.greenBtn}
                    // style={{
                    //   background: theme.palette.green.main,
                    //   '&:hover': {
                    //     background: theme.palette.secondary.main,
                    //   },
                    // }}
                    onClick={() => {
                      setCreateTicketOpen(true);
                    }}
                  >
                    Create Ticket
                  </Button>
                ) : null}
              </Grid>
              <Grid item>
                <Button
                  startIcon={<FileDownload />}
                  size="small"
                  variant="outlined"
                  onClick={() => fetchOMTickets({}, true)}
                  disabled={exportLoading}
                >
                  Export
                </Button>
              </Grid>
              <Grid item style={{ marginRight: '1rem' }}>
                <IconButton onClick={() => handleClearFilters()}>
                  <Refresh />
                </IconButton>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        {/* {fullScreen ? (
          <Grid container style={{ padding: '.5rem' }}>
            {renderFilterChips()}
          </Grid>
        ) : (
          )} */}
        {renderFilterChips()}
        {renderTickets()}
      </Grid>
      {createTicketOpen ? (
        <OMTicketDialog
          action="create"
          onClose={() => {
            setCreateTicketOpen(false);
            refresh();
          }}
        />
      ) : null}
      {updateTicketOpen ? (
        <OMTicketDialog
          action="update"
          projectId={selectedTicketEdit.project.id}
          projectInstallationTypeId={
            selectedTicketEdit.project.installationTypeId
          }
          omTicket={selectedTicketEdit}
          onClose={() => {
            setUpdateTicketOpen(false);
            setSelectedTicketEdit(null);
            fetchOMTickets({});
          }}
        />
      ) : null}
    </>
  );
});
