import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Gift Card';

export const GiftCardEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <CustomNumberInput source="amount" fullWidth />
            <DateInput source="redeemedDt" fullWidth />
            <TextInput source="redemptionCode" fullWidth />
            <TextInput source="cardStatus" fullWidth />
            <TextInput source="buyerEmail" fullWidth />
            <TextInput source="recipientAddress1" fullWidth />
            <TextInput source="recipientAddress2" fullWidth />
            <TextInput source="recipientCity" fullWidth />
            <TextInput source="recipientPostalCode" fullWidth />
            <TextInput source="recipientState" fullWidth />
            <TextInput source="deliveryType" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const GiftCardList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" fullWidth />
        <NumberField source="amount" fullWidth />
        <DateField source="redeemedDt" fullWidth />
        <TextField source="redemptionCode" fullWidth />
        <TextField source="cardStatus" fullWidth />
        <TextField source="buyerEmail" fullWidth />
        <TextField source="recipientAddress1" fullWidth />
        <TextField source="recipientAddress2" fullWidth />
        <TextField source="recipientCity" fullWidth />
        <TextField source="recipientPostalCode" fullWidth />
        <TextField source="recipientState" fullWidth />
        <TextField source="deliveryType" fullWidth />
      </Datagrid>
    </List>
  );
};

export const GiftCardCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="redemptionCode" required fullWidth />
          <CustomNumberInput source="amount" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
