import React from 'react';
import { use<PERSON>arams } from 'react-router-dom';
import moment from 'moment';
import {
  ArrayField,
  AutocompleteInput,
  BooleanInput,
  Create,
  Datagrid,
  DateTimeInput,
  DateField,
  Edit,
  FunctionField,
  Link,
  List,
  ListButton,
  NumberField,
  NumberInput,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  TopToolbar,
  usePermissions,
  useResourceDefinition,
  FormDataConsumer,
  required,
} from 'react-admin';
import { ChevronLeft } from '@mui/icons-material';
import { Grid } from '@mui/material';

import {
  CustomBooleanField,
  CustomNumberInput,
  CustomReferenceField,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'O&M Ticket';

const minStartDt = moment('2020-01-01', 'YYYY-MM-DD');
const validateMaxStartDate = (value) => {
  if (!value) return null;
  const dt = moment(value);
  const maxDt = moment().add(30, 'years');
  return dt.isAfter(maxDt)
    ? 'Start date must be within the next 30 years'
    : null;
};
const validateMinStartDate = (value) => {
  const dt = moment(value);
  return dt.isBefore(minStartDt)
    ? `Start date must be after ${moment(minStartDt).format('YYYY-MM-DD')}`
    : null;
};
const validateMaxEndDate = (value) => {
  if (!value) return null;
  const dt = moment(value);
  const maxDt = moment().add(30, 'years');
  return dt.isAfter(maxDt) ? 'End date must be within the next 30 years' : null;
};

const CustomEditActions = ({ basePath }) => (
  <TopToolbar>
    <ListButton
      // basePath={basePath}
      component={Link}
      to="/OMFieldDashboard"
      label="Back to O&M Field Dashboard"
      icon={<ChevronLeft />}
    />
  </TopToolbar>
);

export const OMTicketEdit = () => {
  const { id } = useParams();
  return (
    <Edit
      title={`${entityName} #${id}`}
      undoable={false}
      redirect={false}
      actions={<CustomEditActions />}
    >
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="title" required fullWidth />
            <TextInput
              disabled
              source="author.fullName"
              label="Author"
              fullWidth
              helperText="Auto-generated when ticket is created"
            />
            {/* <ReferenceInput
              perPage={10000}
              source="author.id"
              reference="UserLite"
            >
              <AutocompleteInput
                allowEmpty={true}
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
                label="Ticket Owner"
                required
                fullWidth
              />
            </ReferenceInput> */}
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Project"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="ticketOwner.id"
              reference="EmployeeLite"
              perPage={10000}
              sort={{ field: 'firstName', order: 'ASC' }}
            >
              <SelectInput
                optionText="fullName"
                label="Ticket Owner"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="omTicketType.id"
              reference="OMTicketType"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Ticket Type"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <FunctionField
              label="Equipment Item"
              helperText={
                <span>
                  Select the equipment item that this ticket is associated with
                  if applicable. If you don't see the equipment type you need,
                  create it <a href="/EquipmentItem/create">here</a>.
                </span>
              }
              render={(record) => (
                <ReferenceInput
                  source="equipmentItem.id"
                  reference="EquipmentItem"
                  perPage={10000}
                  filter={{
                    project: { id: record?.project?.id },
                  }}
                >
                  <SelectInput
                    optionText="label"
                    label="Equipment Item"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
              )}
            />
            <TextInput source="deviceName" fullWidth />
            <DateTimeInput
              source="startDt"
              required
              fullWidth
              helperText="Timestamp of the beginning of the issue"
              parse={(v) => {
                if (!v) return null;
                return moment(v).format('YYYY-MM-DD HH:mm:ss');
              }}
              format={(v) => {
                if (!v) return null;
                return moment(v).format('YYYY-MM-DD HH:mm:ss');
              }}
              validate={[validateMinStartDate, validateMaxStartDate]}
            />
            {/* TODO: Finish this...not working for some reason */}
            {/* <DateTimeInput
              source="createdAt"
              label="Acknowledged Date"
              required
              fullWidth
              helperText="Ex: 2023-05-24 13:00:00. Only edit the acknowledged date if this ticket was backfilled at a later date. This field is not supposed to be automatically set when a ticket is created."
            /> */}
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                const validateMinEndDate = (value) => {
                  if (!value) return null;
                  const endDt = moment(value);
                  const startDt = formData.startDt
                    ? moment(formData.startDt)
                    : moment(minStartDt);
                  return endDt.isBefore(startDt)
                    ? `End date must not precede start date or ${moment(
                        minStartDt
                      ).format('YYYY-MM-DD')}`
                    : null;
                };
                return (
                  <DateTimeInput
                    source="endDt"
                    fullWidth
                    helperText="Timestamp of the issue resolution"
                    parse={(v) => {
                      if (!v) return null;
                      return moment(v).format('YYYY-MM-DD HH:mm:ss');
                    }}
                    format={(v) => {
                      if (!v) return null;
                      return moment(v).format('YYYY-MM-DD HH:mm:ss');
                    }}
                    validate={[validateMinEndDate, validateMaxEndDate]}
                  />
                );
              }}
            </FormDataConsumer>
            <NumberInput
              source="priorityNo"
              fullWidth
              helperText="Priority of the ticket. 1 is the highest priority."
            />
            <TextInput
              multiline
              source="notes"
              label="Notes"
              fullWidth
              helperText="Notes for internal use only describing the ticket in more detail."
            />
            <TextInput
              multiline
              source="statusNotes"
              label="Status Notes"
              fullWidth
              helperText="Notes for any updates on ticket progress."
            />
            <CustomNumberInput
              source="estimatedPercentageLoss"
              fullWidth
              min={0}
              max={100}
              helperText="Percentage of site generation that the issue is affecting (Ex: 25)"
            />
            <BooleanInput
              source="internalOnlyFlg"
              fullWidth
              label="Hide from Monthly OM Reports"
              helperText="Turn on to prevent from being included on the Monthly OM Reports"
            />
            <BooleanInput
              source="clientNotificationRequiredFlg"
              fullWidth
              label="Client Notification Required"
              helperText="Flags a ticket as requiring a notification be sent to the client"
            />
            <DateTimeInput source="clientNotifiedDt" fullWidth />
            <SelectInput
              labelId="client-notification-type"
              placeholder="Client Notification Type"
              source="clientNotificationType"
              // labelWidth={200}
              // value={clientNotificationType || ''}
              // onChange={(event) => {
              //   setClientNotificationType(event.target.value);
              // }}
              style={{ width: '100%' }}
              choices={[
                { id: 'Email', name: 'Email' },
                { id: 'Phone', name: 'Phone' },
                { id: 'Text', name: 'Text' },
              ]}
            />
            <ReferenceInput
              source="omPmpChecklistItem.id"
              reference="OMPmpChecklistItem"
              perPage={10000}
              sort={{ field: 'id', order: 'DESC' }}
            >
              <AutocompleteInput
                optionText="label"
                label="PMP Checklist Item"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const OMTicketList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <LinkField
          label="Ticket Type"
          linkSource="omTicketType.id"
          labelSource="omTicketType.name"
          reference="OMTicketType"
        />
        <LinkField
          reference="User"
          linkSource="author.id"
          labelSource="author.fullName"
          label="Ticket Author"
        />
        <LinkField
          label="Ticket Owner"
          linkSource="ticketOwner.id"
          labelSource="ticketOwner.fullName"
          reference="Employee"
        />
        <LinkField
          label="Equipment Item"
          linkSource="equipmentItem.id"
          labelSource="equipmentItem.label"
          reference="EquipmentItem"
        />
        <TextField source="deviceName" />
        <TextField source="title" />
        <TextField source="notes" label="Internal Notes" />
        <DateField source="startDt" showTime />
        <DateField source="endDt" showTime />
        <NumberField source="estimatedPercentageLoss" />
        <NumberField
          source="estimatedImpact.estimatedGenerationLoss"
          label="Est. Generation Loss (kWh)"
        />
        <NumberField
          source="estimatedImpact.estimatedRevenueLoss"
          label="Est. Revenue Loss (Currency of project)"
        />
        <NumberField
          source="estimatedImpact.estimatedRevenueLossUSD"
          label="Est. Revenue Loss (USD)"
        />
        <DateField source="acknowledgedDt" showTime />
        <CustomBooleanField source="internalOnlyFlg" />
        <CustomBooleanField source="clientNotificationRequiredFlg" />
        <ArrayField source="omReports" label="Reports" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField
              // color={(resource) => 'primary'}
              source="label"
            />
          </SingleFieldList>
        </ArrayField>
      </Datagrid>
    </List>
  );
};

export const OMTicketCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="title" required fullWidth />
          <TextInput
            multiline
            source="notes"
            label="Internal Notes"
            required
            fullWidth
          />
          <ReferenceInput
            source="project.id"
            reference="Project"
            perPage={10000}
            required
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput optionText="name" label="Project" fullWidth required />
          </ReferenceInput>
          <ReferenceInput
            source="ticketOwner.id"
            reference="EmployeeLite"
            perPage={10000}
          >
            <SelectInput optionText="fullName" label="Ticket Owner" fullWidth />
          </ReferenceInput>
          <ReferenceInput
            source="omTicketType.id"
            reference="OMTicketType"
            perPage={10000}
            required
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput optionText="name" label="Ticket Type" fullWidth />
          </ReferenceInput>
          <ReferenceInput
            source="equipmentItem.id"
            reference="EquipmentItem"
            perPage={10000}
            required
          >
            <SelectInput optionText="label" label="Equipment Item" fullWidth />
          </ReferenceInput>
          <TextInput source="deviceName" fullWidth />
          <DateTimeInput
            source="startDt"
            required
            fullWidth
            helperText="Timestamp of the beginning of the issue"
            parse={(v) => {
              if (!v) return null;
              return moment(v).format('YYYY-MM-DD HH:mm:ss');
            }}
            format={(v) => {
              if (!v) return null;
              return moment(v).format('YYYY-MM-DD HH:mm:ss');
            }}
            validate={[validateMinStartDate, validateMaxStartDate]}
          />
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              const validateMinEndDate = (value) => {
                if (!value) return null;
                const endDt = moment(value);
                const startDt = formData.startDt
                  ? moment(formData.startDt)
                  : moment(minStartDt);
                return endDt.isBefore(startDt)
                  ? `End date must not precede start date or ${moment(
                      minStartDt
                    ).format('YYYY-MM-DD')}`
                  : null;
              };
              return (
                <DateTimeInput
                  source="endDt"
                  fullWidth
                  helperText="Timestamp of the issue resolution"
                  parse={(v) => {
                    if (!v) return null;
                    return moment(v).format('YYYY-MM-DD HH:mm:ss');
                  }}
                  format={(v) => {
                    if (!v) return null;
                    return moment(v).format('YYYY-MM-DD HH:mm:ss');
                  }}
                  validate={[validateMinEndDate, validateMaxEndDate]}
                />
              );
            }}
          </FormDataConsumer>
          <CustomNumberInput source="estimatedPercentageLoss" fullWidth />
          <BooleanInput
            source="internalOnlyFlg"
            fullWidth
            label="Hide from Monthly OM Reports"
            helperText="Turn on to prevent from being included on the Monthly OM Reports"
          />
          <BooleanInput
            source="clientNotificationRequiredFlg"
            fullWidth
            label="Client Notification Required"
            helperText="Flags a ticket as requiring a notification be sent to the client"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
