import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FunctionField,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid } from '@mui/material';

import { DetailField, <PERSON>Field } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CloudDownload } from '@mui/icons-material';

const entityName = 'Job Application';

export const JobApplicationEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="jobPosting.id"
              reference="JobPosting"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput optionText="title" label="Job Posting" fullWidth />
            </ReferenceInput>
            <ReferenceInput
              source="jobApplicationStatus.id"
              reference="JobApplicationStatus"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="title"
                label="Job Application Status"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <DateInput source="createdAt" label="Applied Dt" fullWidth />
            <TextInput source="applicantFirstName" required fullWidth />
            <TextInput source="applicantLastName" required fullWidth />
            <TextInput source="applicantEmail" required fullWidth />
            <TextInput source="applicantPhoneNumber" required fullWidth />
            <TextInput
              source="internalNotes"
              helperText="This is an internal only facing notes field for anything that would be helpful including who has been in contact with them, where they are in the application process, etc."
              multiline
            />
            <FunctionField
              label="Resume"
              render={(record) => {
                return (
                  <>
                    <Button
                      variant="contained"
                      startIcon={<CloudDownload />}
                      style={{ textTransform: 'none', margin: '.5rem' }}
                      disabled={!record.resumeDownloadUrl}
                      onClick={(event) => {
                        window.location.assign(record.resumeDownloadUrl);
                        event.stopPropagation();
                      }}
                    >
                      Download Resume
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={<CloudDownload />}
                      style={{ textTransform: 'none', margin: '.5rem' }}
                      disabled={!record.coverLetterDownloadUrl}
                      onClick={(event) => {
                        window.location.assign(record.coverLetterDownloadUrl);
                        event.stopPropagation();
                      }}
                    >
                      Download Cover Letter
                    </Button>
                  </>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const JobApplicationFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Job Posting"
      source="jobPosting.id"
      reference="JobPosting"
      perPage={10000}
      sort={{ field: 'id', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Job Posting" optionText="title" />
    </ReferenceInput>
    <ReferenceInput
      label="Application Status"
      source="jobApplicationStatus.id"
      reference="JobApplicationStatus"
      perPage={10000}
      sort={{ field: 'title', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Application Status" optionText="title" />
    </ReferenceInput>
  </Filter>
);
export const JobApplicationList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<JobApplicationFilter />}
      sort={{ field: 'id', order: 'desc' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          label="Job Posting"
          linkSource="jobPosting.id"
          labelSource="jobPosting.title"
          reference="JobPosting"
        />
        <FunctionField
          label="Manager"
          render={(record) =>
            record.jobPosting?.manager ? (
              // <Typography component="a" variant="body2">
              <a
                href={`mailto:${record.jobPosting.manager.email}?subject=New Job Applicant for ${record.jobPosting.title}&body=Hey ${record.jobPosting.manager.firstName}, %0D%0A%0D%0AThere was a new applicant posted to the '${record.title}' posting. You can view the posting here: https://energea-cms.herokuapp.com/JobPosting/${record.id}`}
                target="_blank"
              >
                {record.jobPosting.manager.fullName}
              </a>
            ) : (
              // </Typography>
              <span>-</span>
            )
          }
        />
        <LinkField
          label="Job Application Status"
          linkSource="jobApplicationStatus.id"
          labelSource="jobApplicationStatus.title"
          reference="JobApplicationStatus"
        />
        <DateField source="createdAt" label="Applied Dt" />
        <TextField source="applicantFirstName" />
        <TextField source="applicantLastName" />
        <TextField source="applicantEmail" />
        <TextField source="applicantPhoneNumber" />
        <DetailField source="internalNotes" />
      </Datagrid>
    </List>
  );
};

export const JobApplicationCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="jobPosting.id"
            reference="JobPosting"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="title"
              label="Job Posting"
              fullWidth
              required
            />
          </ReferenceInput>
          <ReferenceInput
            source="jobApplicationStatus.id"
            reference="JobApplicationStatus"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="title"
              label="Job Application Status"
              fullWidth
              required
            />
          </ReferenceInput>
          <TextInput source="applicantFirstName" required fullWidth />
          <TextInput source="applicantLastName" required fullWidth />
          <TextInput source="applicantEmail" required fullWidth />
          <TextInput source="applicantPhoneNumber" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
