import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  Edit,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Onboarding Document';

export const BrOnboardingDocumentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              perPage={10_000}
              source="brCustomer.id"
              sortable={false}
              reference="BrCustomer"
              required
            >
              <AutocompleteInput
                label="Customer"
                required
                fullWidth
                optionText="name"
                shouldRenderSuggestions={(value) => true}
              />
            </ReferenceInput>
            <SelectInput
              source="type"
              choices={[{ id: 'other', name: 'Other' }]}
              fullWidth
            />
            <TextInput source="awsObjectKey" fullWidth required />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrOnboardingDocumentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrCustomer"
          linkSource="brCustomer.id"
          labelSource="brCustomer.name"
          label="Customer"
        />
        <TextField source="type" />
        <TextField source="awsObjectKey" />
      </Datagrid>
    </List>
  );
};

export const BrOnboardingDocumentCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={10_000}
            source="brCustomer.id"
            sortable={false}
            reference="BrCustomer"
            required
          >
            <AutocompleteInput
              label="Customer"
              required
              fullWidth
              optionText="name"
              shouldRenderSuggestions={(value) => true}
            />
          </ReferenceInput>
          <SelectInput
            source="type"
            choices={[{ id: 'other', name: 'Other' }]}
            fullWidth
          />
          <TextInput source="awsObjectKey" fullWidth required />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
