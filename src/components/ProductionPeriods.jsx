import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON>oleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  Edit,
  Filter,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Alert } from '@mui/lab';

import { Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import { ProductionPeriodsUpload } from './ProductionPeriodsUpload';
import moment from 'moment';

const entityName = 'Meter Generation';

export const ProductionPeriodEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput source="solarEdgeSite.id" reference="SolarEdgeSite">
              <SelectInput
                label="SolarEdge Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="greenAntMeter.id" reference="GreenAntMeter">
              <SelectInput
                label="GreenAnt Meter"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="sunExchangeSite.id"
              reference="SunExchangeSite"
            >
              <SelectInput
                label="AMMP Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="powerFactorSystem.id"
              reference="PowerFactorSystem"
            >
              <SelectInput
                label="PowerFactor System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="scadaSystem.id" reference="ScadaSystemLite">
              <SelectInput
                label="SCADA System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="sgdSystem.id" reference="SgdSystem">
              <SelectInput
                label="SGD System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="smaSite.id" reference="SmaSite">
              <SelectInput
                label="SMA Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="flexOmSite.id" reference="FlexOmSite">
              <SelectInput
                label="FlexOM Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="sungrowSystem.id" reference="SungrowSystem">
              <SelectInput
                label="Sungrow System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="solisSystem.id" reference="SolisSystem">
              <SelectInput
                label="Solis System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <TextInput
              source="periodStartDt"
              required
              fullWidth
              helperText="Ex: 2021-11-08 18:14:00"
            />
            <CustomNumberInput
              source="production"
              required
              fullWidth
              helperText="This value is stored in Wh"
            />
            <CustomNumberInput
              source="rawProduction"
              required
              fullWidth
              helperText="This value is stored in Wh"
            />
            <BooleanInput
              source="freezeDataFlg"
              fullWidth
              helperText="This prevents data from being overwritten"
            />
            <TextInput source="unit" defaultValue="Wh" required fullWidth />
            <TextInput
              source="timeUnit"
              defaultValue="HOUR"
              required
              fullWidth
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const ProductionPeriodFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      source="solarEdgeSite.id"
      label="SolarEdge Site"
      reference="SolarEdgeSite"
    >
      <SelectInput label="SolarEdge Site" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="sunExchangeSite.id"
      label="AMMP Site"
      reference="SunExchangeSite"
    >
      <SelectInput label="AMMP Site" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="powerFactorSystem.id"
      label="PowerFactor System"
      reference="PowerFactorSystem"
    >
      <SelectInput label="PowerFactor System" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="scadaSystem.id"
      label="SCADA System"
      reference="ScadaSystemLite"
    >
      <SelectInput label="SCADA System" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="sgdSystem.id"
      label="SGD System"
      reference="SgdSystem"
    >
      <SelectInput label="SGD System" optionText="name" />
    </ReferenceInput>
    <ReferenceInput source="smaSite.id" label="SMA Site" reference="SmaSite">
      <SelectInput label="SMA Site" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="flexOmSite.id"
      label="FlexOM Site"
      reference="FlexOmSite"
    >
      <SelectInput label="FlexOM Site" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="sungrowSystem.id"
      label="Sungrow System"
      reference="SungrowSystem"
    >
      <SelectInput label="Sungrow System" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="solisSystem.id"
      label="Solis System"
      reference="SolisSystem"
    >
      <SelectInput label="Solis System" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="greenAntMeter.id"
      label="GreenAnt Meter"
      reference="GreenAntMeter"
    >
      <SelectInput label="GreenAnt Meter" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const ProductionPeriodList = () => {
  const { permissions } = usePermissions();
  return (
    <Grid container>
      <Grid item xs={12}>
        <Alert severity="info" style={{ marginTop: '1rem' }}>
          Note: Some historical data points are a summation of inverter
          generation rather than readings from the meter.
        </Alert>
      </Grid>
      <Grid item xs={12}>
        <List
          title={entityName}
          perPage={25}
          sort={{ field: 'id', order: 'DESC' }}
          filters={<ProductionPeriodFilter />}
        >
          <Datagrid
            rowClick={
              getEditable(useResourceDefinition().name, permissions)
                ? 'edit'
                : 'show'
            }
          >
            <TextField source="id" />
            <LinkField
              label="SolarEdge Site"
              linkSource="solarEdgeSite.id"
              labelSource="solarEdgeSite.name"
              reference="SolarEdgeSite"
            />
            <LinkField
              label="SCADA System"
              linkSource="scadaSystem.id"
              labelSource="scadaSystem.name"
              reference="ScadaSystem"
            />
            <LinkField
              label="SGD System"
              linkSource="sgdSystem.id"
              labelSource="sgdSystem.name"
              reference="SgdSystem"
            />
            <LinkField
              label="SMA Site"
              linkSource="smaSite.id"
              labelSource="smaSite.name"
              reference="SmaSite"
            />
            <LinkField
              label="FlexOM Site"
              linkSource="flexOmSite.id"
              labelSource="flexOmSite.name"
              reference="FlexOmSite"
            />
            <LinkField
              label="Sungrow System"
              linkSource="sungrowSystem.id"
              labelSource="sungrowSystem.name"
              reference="SungrowSystem"
            />
            <LinkField
              label="Solis System"
              linkSource="solisSystem.id"
              labelSource="solisSystem.name"
              reference="SolisSystem"
            />
            <LinkField
              label="AMMP Site"
              linkSource="sunExchangeSite.id"
              labelSource="sunExchangeSite.name"
              reference="SunExchangeSite"
            />
            <LinkField
              label="PowerFactor System"
              linkSource="powerFactorSystem.id"
              labelSource="powerFactorSystem.name"
              reference="PowerFactorSystem"
            />
            <LinkField
              label="GreenAnt Meter"
              linkSource="greenAntMeter.id"
              labelSource="greenAntMeter.name"
              reference="GreenAntMeter"
            />
            <TextField source="periodStartDt" />
            <NumberField source="production" />
            <NumberField source="rawProduction" />
            <TextField source="unit" />
            <TextField source="timeUnit" />
            <BooleanField source="freezeDataFlg" />
            <DateField source="updatedAt" showTime={true} />
            <DateField source="createdAt" showTime={true} />
          </Datagrid>
        </List>
      </Grid>
    </Grid>
  );
};

const attrs = [
  {
    name: 'periodStartDt',
    label: 'Period Start Dt',
    align: 'center',
    dataFormat: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    name: 'production',
    label:
      'Production (transformer loss will be applied automatically if applicable)',
    align: 'center',
  },
  // {
  //   name: 'rawProduction',
  //   label: 'Raw Production',
  //   align: 'center',
  // },
  {
    name: 'timeUnit',
    label: "Time Unit ('DAY')",
    align: 'center',
  },
  {
    name: 'unit',
    label: "Unit ('Wh')",
    align: 'center',
  },
  {
    name: 'sgdSystemId',
    label: 'SGDSystemId',
    align: 'center',
  },
  {
    name: 'smaSiteId',
    label: 'SMASiteId',
    align: 'center',
  },
  {
    name: 'flexOmSiteId',
    label: 'FlexOMSiteId',
    align: 'center',
  },
  {
    name: 'sungrowSystemId',
    label: 'SungrowSystemId',
    align: 'center',
  },
  {
    name: 'solisSystemId',
    label: 'SolisSystemId',
    align: 'center',
  },
  {
    name: 'greenAntMeterId',
    label: 'GreenAntMeterId',
    align: 'center',
  },
  {
    name: 'sunExchangeSiteId',
    label: 'SunExchangeSiteId',
    align: 'center',
  },
  {
    name: 'powerFactorSystemId',
    label: 'PowerFactorSystemId',
    align: 'center',
  },
  {
    name: 'scadaSystemId',
    label: 'ScadaSystemId',
    align: 'center',
  },
  {
    name: 'solarEdgeSiteId',
    label: 'SolarEdgeSiteId',
    align: 'center',
  },
];

export const ProductionPeriodCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <Grid container>
      <Grid item>
        <Typography>Upload from CSV:</Typography>
        <ProductionPeriodsUpload attrs={attrs} />
      </Grid>
      <Grid item>
        <Typography>Create Manually:</Typography>
        <SimpleForm>
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <ReferenceInput
                source="solarEdgeSite.id"
                reference="SolarEdgeSite"
              >
                <SelectInput
                  label="SolarEdge Site"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="greenAntMeter.id"
                reference="GreenAntMeter"
              >
                <SelectInput
                  label="GreenAnt Meter"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="sunExchangeSite.id"
                reference="SunExchangeSite"
              >
                <SelectInput
                  label="AMMP Site"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="powerFactorSystem.id"
                reference="PowerFactorSystem"
              >
                <SelectInput
                  label="PowerFactor System"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="scadaSystem.id"
                reference="ScadaSystemLite"
              >
                <SelectInput
                  label="SCADA System"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput source="sgdSystem.id" reference="SgdSystem">
                <SelectInput
                  label="SGD System"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput source="smaSite.id" reference="SmaSite">
                <SelectInput
                  label="SMA Site"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput source="flexOmSite.id" reference="FlexOmSite">
                <SelectInput
                  label="FlexOM Site"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="sungrowSystem.id"
                reference="SungrowSystem"
              >
                <SelectInput
                  label="Sungrow System"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput source="solisSystem.id" reference="SolisSystem">
                <SelectInput
                  label="Solis System"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput
                source="periodStartDt"
                required
                fullWidth
                helperText="Ex: 2021-11-08 18:14:00"
              />
              <CustomNumberInput
                source="production"
                required
                fullWidth
                helperText="This value is stored in Wh"
              />
              <CustomNumberInput
                source="rawProduction"
                required
                fullWidth
                helperText="This value is stored in Wh"
              />
              <TextInput source="unit" defaultValue="Wh" required fullWidth />
              <TextInput
                source="timeUnit"
                defaultValue="DAY"
                required
                fullWidth
              />
              <BooleanInput
                source="freezeDataFlg"
                fullWidth
                helperText="This prevents data from being overwritten"
              />
            </Grid>
          </Grid>
        </SimpleForm>
      </Grid>
    </Grid>
  </Create>
);
