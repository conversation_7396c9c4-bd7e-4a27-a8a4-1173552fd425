import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>y<PERSON>ield,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Alert,
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

import {
  CustomNumberInput,
  CustomReferenceField,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CheckCircle, Error, GetApp } from '@mui/icons-material';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';

const entityName = 'Br Rateio';

export const BrRateioEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const refresh = useRefresh();
  const notify = useNotify();
  const [loading, setLoading] = useState(false);

  const handleRateioAccepted = async () => {
    setLoading(true);
    dataProvider
      .update('BrRateio', {
        data: {
          id: parseInt(id, 10),
          rateioAcceptedFlg: true,
        },
      })
      .then(
        () => {
          setLoading(false);
          refresh();
          notify('Rateio accepted', { type: 'success' });
        },
        (err) => {
          setLoading(false);
          console.error(err);
          notify('Error accepting rateio', { type: 'error' });
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput optionText="name" label="Project" fullWidth />
            </ReferenceInput>
            <DateInput source="submittedDt" fullWidth />
            <DateInput source="rejectedDt" fullWidth />
            <FunctionField
              label="Accept Rateio"
              render={(record) => {
                if (!record.acceptedDt && !record.rejectedDt) {
                  return (
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        handleRateioAccepted();
                      }}
                      disabled={loading}
                    >
                      Accept Rateio
                    </Button>
                  );
                }
                return <DateInput source="acceptedDt" fullWidth disabled />;
              }}
            />
            <ReferenceInput
              source="fallbackRateioLineItem.id"
              reference="BrRateioLineItem"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="label"
                label="Rateio Line Item"
                fullWidth
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const styleRow = (record) => {
  const cancelledStyle = {
    backgroundColor: '#ddd',
    fontStyle: 'italic',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (record.rejectedDt) {
    return cancelledStyle;
  }
  if (!record.acceptedDt) {
    return warningStyle;
  }
  return {};
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="project.id"
      reference="ProjectLite"
      perPage={10_000}
      sort={{ field: 'name', order: 'ASC' }}
      filter={{ consortiumFlg: true }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const BrRateioList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<CustomFilter />}
      sort={{ field: 'submittedDt', order: 'DESC' }}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <LinkField
          reference="BrRateioLineItem"
          linkSource="fallbackRateioLineItem.id"
          labelSource="fallbackRateioLineItem.label"
          label="Fallback Rateio Line Item"
        />
        <DateField source="submittedDt" />
        <DateField source="acceptedDt" />
        <DateField source="rejectedDt" />
        <ArrayField source="brRateioLineItems" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="label" />
          </SingleFieldList>
        </ArrayField>
      </Datagrid>
    </List>
  );
};

const attrs = [
  {
    name: 'installationCode',
    format: (val) => val || '',
    label: 'Installation code',
    align: 'left',
  },
  {
    name: 'allocationPercentage',
    format: (val) => val || '',
    label: 'Allocation percentage',
    align: 'right',
  },
];

export const BrRateioCreate = () => {
  const [data, setData] = useState(null);
  const [reviewed, setReviewed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState(null);
  const [projectsLoading, setProjectsLoading] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState(null);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  if (!projects && !projectsLoading) {
    setProjectsLoading(true);
    dataProvider
      .getList('ProjectLite', {
        pagination: { page: 1, perPage: 10_000 },
        filter: { consortiumFlg: true },
        sort: { field: 'name', order: 'ASC' },
      })
      .then((res) => {
        setProjects(res.data);
        setProjectsLoading(false);
      });
  }

  const save = () => {
    setLoading(true);
    dataProvider
      .create('BrRateioUpload', {
        data: {
          data,
          projectId: selectedProjectId,
        },
      })
      .then(
        (res) => {
          notify('Data saved successfully', { type: 'success' });
          window.location.href = `/BrRateio/${res.data.id}`;
          setLoading(false);
        },
        (e) => {
          console.log('ERROR', e, e?.errors, e.message, e[0]);
          setLoading(false);
          notify(e?.message, {
            type: 'error',
          });
        }
      );
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell align={attr.align || 'center'}>
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  const renderSubmit = () => {
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!reviewed}
                onChange={() => setReviewed(!reviewed)}
              />
            }
            label="I have checked and the list looks good (this will only create new records and will not effect existing records.)"
          />
          <Button
            onClick={save}
            disabled={!reviewed || loading || !selectedProjectId}
            variant="contained"
            size="large"
            color="secondary"
          >
            {loading ? <CircularProgress /> : 'Save'}
          </Button>
        </Alert>
      </>
    );
  };

  const lintData = (data, attrs) => {
    return data
      .map((entry) => {
        const returnObj = {};
        Object.keys(entry).forEach((entryAttr) => {
          const entryAttrData = findWithAttr(attrs, 'name', entryAttr);
          if (!entryAttrData) {
            console.log('Missing attr detected', entryAttr);
          } else if (entryAttrData.dataFormat) {
            returnObj[String(entryAttr)] = entryAttrData.dataFormat(
              entry[String(entryAttr)]
            );
          } else {
            returnObj[String(entryAttr)] = entry[String(entryAttr)] || null;
          }
        });
        return returnObj;
      })
      .filter((el) => !!el);
  };

  const handleData = (data) => {
    const lintedData = lintData(data, attrs);
    setData(lintedData);
  };

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <Grid container>
        <Grid item xs={12} md={6} style={{ padding: '1em' }}>
          <FormControl fullWidth>
            <InputLabel id="project-select-input">Project</InputLabel>
            <Select
              id="project-select-input"
              labelId="project-select-input"
              variant="outlined"
              value={selectedProjectId || ''}
              onChange={(event) => {
                setSelectedProjectId(event.target.value);
              }}
            >
              {projects ? (
                projects.map((project) => (
                  <MenuItem
                    value={project.id}
                    key={`menu-item-project-${project.id}`}
                  >
                    {project.name}
                  </MenuItem>
                ))
              ) : (
                <CircularProgress />
              )}
            </Select>
          </FormControl>
        </Grid>
        <Grid xs={12} item style={{ padding: '1em' }}>
          <Button
            component="a"
            variant="contained"
            href="/csv-templates/rateioUpload.xlsx"
            download
          >
            <GetApp />
            Click to download the excel template
          </Button>
        </Grid>
        <ExcelReader handleData={handleData} />
        <Grid item style={{ margin: 'auto' }}>
          {data ? renderSubmit() : null}
        </Grid>
        <Grid item xs={12}>
          {data ? renderData() : null}
        </Grid>
      </Grid>
    </Create>
  );
};
