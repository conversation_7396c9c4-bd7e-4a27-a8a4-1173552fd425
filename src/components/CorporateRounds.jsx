import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Corporate Round';

export const CorporateRoundEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth required />
            <TextInput source="description" fullWidth />
            <ReferenceInput
              source="energeaGlobalSharePrice.id"
              reference="EnergeaGlobalSharePrice"
              perPage={10_000}
              sort={{ field: 'date', order: 'DESC' }}
            >
              <SelectInput
                optionText="label"
                label="Energea Global Share Price"
                fullWidth
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const CorporateRoundList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <LinkField
          reference="EnergeaGlobalSharePrice"
          linkSource="energeaGlobalSharePrice.id"
          labelSource="energeaGlobalSharePrice.label"
          label="Energea Global Share Price"
        />
      </Datagrid>
    </List>
  );
};

export const CorporateRoundCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="description" fullWidth />
          <ReferenceInput
            source="energeaGlobalSharePrice.id"
            reference="EnergeaGlobalSharePrice"
            perPage={10_000}
            sort={{ field: 'date', order: 'DESC' }}
          >
            <SelectInput
              optionText="label"
              label="Energea Global Share Price"
              fullWidth
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
