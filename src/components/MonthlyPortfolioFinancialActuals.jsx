import React, { useState } from 'react';
import moment from 'moment';
import numeral from 'numeral';
import { Alert } from '@mui/lab';
import { useParams } from 'react-router-dom';
import {
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  TabbedForm,
  TextField,
  TextInput,
  useRefresh,
  useNotify,
  useDataProvider,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { RichTextInput } from 'ra-input-rich-text';

import {
  Button,
  CircularProgress,
  Collapse,
  Dialog,
  DialogContent,
  DialogTitle,
  Fab,
  Grid,
  IconButton,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import MuiTable from '@mui/material/Table';

import {
  Add,
  CheckCircle,
  CloudDownload,
  Info,
  Warning,
} from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import {
  CustomNumberInput,
  DetailField,
  LinkField,
  PercentageInput,
} from './CustomFields';
import { MonthlyPortfolioFinancialsUpload } from './MonthlyPortfolioFinancialsUpload';
import { MonthlyPortfolioFinancialActualDividends } from './MonthlyPortfolioFinancialActualDividends';

const entityName = 'Monthly Financial Actual';

const UpdateSharePriceButton = ({ id }) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [pending, setPending] = useState(false);

  const updateSharePrice = (event) => {
    setPending(true);
    event.preventDefault();
    dataProvider
      .update('MonthlyPortfolioFinancialActual', {
        data: { id, recalculateSharePrice: true },
      })
      .then(
        () => {
          notify('Share price successfully updated', { type: 'success' });
          refresh();
          setPending(false);
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
          setPending(false);
        }
      );
  };

  return (
    <Tooltip arrow title="Click to recalculate share price">
      <Fab
        color="primary"
        variant="extended"
        onClick={updateSharePrice}
        edge="end"
        disabled={pending}
        aria-label="recalculate share price"
      >
        <>
          {pending ? (
            <CircularProgress style={{ marginRight: '4px' }} />
          ) : (
            <Add style={{ marginRight: '4px' }} />
          )}
          {pending ? 'Share Price Recalculating...' : 'Recalculate Share Price'}
        </>
      </Fab>
    </Tooltip>
  );
};

const RegenerateDividendResolutionButton = ({ id }) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [pending, setPending] = useState(false);

  const regenerateDividendResolutionLetter = (event) => {
    setPending(true);
    event.preventDefault();
    dataProvider
      .update('MonthlyPortfolioFinancialActual', {
        data: { id, regenerateDividendResolutionLetter: true },
      })
      .then(
        () => {
          notify('Dividend Resolution Letter successfully updated', {
            type: 'success',
          });
          refresh();
          setPending(false);
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
          setPending(false);
        }
      );
  };

  return (
    <Tooltip arrow title="Click to regenerate Dividend Resolution letter">
      <Fab
        color="primary"
        variant="extended"
        onClick={regenerateDividendResolutionLetter}
        edge="end"
        aria-label="regenerate Dividend Resolution letter"
      >
        <Add style={{ marginRight: '4px' }} />{' '}
        {pending ? (
          <CircularProgress />
        ) : (
          `Regenerate Dividend Resolution Letter`
        )}
      </Fab>
    </Tooltip>
  );
};

const ConfirmActualButton = ({ id }) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [pending, setPending] = useState(false);

  const confirmActual = (event) => {
    setPending(true);
    event.preventDefault();
    dataProvider
      .update('MonthlyPortfolioFinancialActual', {
        data: {
          id,
          recalculateSharePrice: true,
          confirmActual: true,
        },
      })
      .then(
        () => {
          notify('Actual successfully confirmed', { type: 'success' });
          refresh();
          setPending(false);
          dataProvider
            .update('MonthlyPortfolioFinancialActual', {
              data: {
                id,
                regenerateDividendResolutionLetter: true,
              },
            })
            .catch((err) => {
              console.error('Error creating dividend resolution letter', err);
            });
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
          setPending(false);
        }
      );
  };

  return (
    <Tooltip arrow title="Click to confirm actual">
      <Fab
        color="primary"
        variant="extended"
        onClick={confirmActual}
        edge="end"
        disabled={pending}
        aria-label="confirm actual"
      >
        {pending ? (
          <CircularProgress style={{ marginRight: '4px' }} />
        ) : (
          <Add style={{ marginRight: '4px' }} />
        )}
        {pending ? 'Confirming Actual...' : 'Confirm Actual'}
      </Fab>
    </Tooltip>
  );
};
const CreateMonthlyInvestorNotesButton = ({ id }) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [pending, setPending] = useState(false);

  const createMonthlyInvestorNotes = (event) => {
    setPending(true);
    event.preventDefault();
    dataProvider
      .update('MonthlyPortfolioFinancialActual', {
        data: { id, createMonthlyInvestorNotes: true },
      })
      .then(
        () => {
          notify('Report successfully generated', {
            type: 'success',
          });
          refresh();
          setPending(false);
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
          setPending(false);
        }
      );
  };

  return (
    <Tooltip arrow title="Click to draft investor notes">
      <Fab
        color="primary"
        variant="extended"
        onClick={createMonthlyInvestorNotes}
        edge="end"
        aria-label="Draft investor notes"
      >
        <Add style={{ marginRight: '4px' }} />{' '}
        {pending ? <CircularProgress /> : `Print Monthly Report`}
      </Fab>
    </Tooltip>
  );
};
const UpdateProjectedSharePricesButton = ({ id }) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [pending, setPending] = useState(false);

  const updateProjectedSharePrice = (event) => {
    setPending(true);
    event.preventDefault();
    dataProvider
      .update('MonthlyPortfolioFinancialActual', {
        data: { id, recalculateProjectedSharePrices: true },
      })
      .then(
        (record) => {
          notify('Projected share prices successfully updated', {
            type: 'success',
          });
          refresh();
          setPending(false);
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
          setPending(false);
        }
      );
  };

  return (
    <Tooltip arrow title="Click to recalculate projected share price">
      <Fab
        color="primary"
        variant="extended"
        disabled={pending}
        onClick={updateProjectedSharePrice}
        edge="end"
        aria-label="update projected share prices"
      >
        <>
          {pending ? (
            <CircularProgress style={{ marginRight: '4px' }} />
          ) : (
            <Add style={{ marginRight: '4px' }} />
          )}
          {pending
            ? 'Projected Share Prices Recalculating'
            : 'Recalculate Projected Share Prices'}
        </>
      </Fab>
    </Tooltip>
  );
};

const MonthlyPortfolioFinancialActualEditClass = (props) => {
  const { id } = props.params;
  return (
    <Edit title={`MonthlyPortfolioFinancialActual #${id}`} undoable={false}>
      <TabbedForm margin="none">
        <FormTab margin="none" label="details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (formData.dividendsPendingFlg) {
                    return (
                      <Alert severity="error">
                        <Typography style={{ fontWeight: 'bold' }}>
                          Dividends are currently being processed for this
                          actual!
                        </Typography>
                        <Typography>
                          Please wait until this process completes. Feel free to
                          refresh this page for a status update at any time. A
                          Slack message will be sent to platform-events upon
                          completion.
                        </Typography>
                      </Alert>
                    );
                  }
                }}
              </FormDataConsumer>
              <BooleanInput
                source="accountantReviewedFlg"
                label="Internal Review Flag"
                helperText="I certify that the Controller (Marta Coelho) has reviewed and confirmed the dividend distribution number, that the Head of Investor Relations (Tyler Hurlburt) has been briefed on a summary of dividends, and that a Partner has signed off on the distribution amount. Dividends will not be issued until this is checked."
                fullWidth
              />
              <FunctionField
                fullWidth
                style={{ marginBottom: '.5rem' }}
                label="Review"
                render={(record) => {
                  if (!record.accountantReviewedFlg) {
                    return null;
                  }
                  return (
                    <Typography
                      gutterBottom
                      style={{ color: 'green', fontWeight: 'bold' }}
                      variant="body2"
                    >
                      Checked on {moment(record.effectiveDt).format('lll')} by{' '}
                      {'Gray Reinhard'}
                    </Typography>
                  );
                }}
              />
              <DateTimeInput source="effectiveDt" fullWidth />
              <CustomNumberInput disabled source="lintedMaxShares" fullWidth />
              {/* <CustomNumberInput
                disabled
                source="previousIRR"
                label="Previous IRR"
                fullWidth
              /> */}
              {/* <CustomNumberInput
                disabled
                source="newIRR"
                label="New IRR"
                fullWidth
              /> */}
              <CustomNumberInput source="fees" fullWidth />
              <PercentageInput source="feePercent" fullWidth />
              <CustomNumberInput source="carry" fullWidth />
              <PercentageInput source="carryPercent" fullWidth />
              <PercentageInput source="preferredReturn" fullWidth />
              <PercentageInput source="portfolioIrr" fullWidth />
              <CustomNumberInput source="lfdiFull" fullWidth />
              <CustomNumberInput source="projectEquity" fullWidth />
              <CustomNumberInput source="cash" fullWidth />
              <CustomNumberInput source="loans" fullWidth />
              <CustomNumberInput source="shortTermInvestments" fullWidth />
              <CustomNumberInput
                source="lifetimeValueOfDollarInvested"
                fullWidth
              />
              <CustomNumberInput source="totalEquity" fullWidth />
              <CustomNumberInput source="grossCafd" fullWidth />
              <CustomNumberInput source="production" fullWidth />
              <CustomNumberInput
                source="sharePrice"
                helperText="This is the share price that will determine share counts for investments made from the effective date above until the next actuals effective date"
                fullWidth
              />
              <FunctionField
                fullWidth
                style={{ marginBottom: '.5rem' }}
                label="Review"
                render={(record) => {
                  return (
                    <Grid container spacing={2}>
                      <Grid item lg={6} xs={12}>
                        <UpdateSharePriceButton id={record.id} />
                      </Grid>
                      <Grid item>
                        <UpdateProjectedSharePricesButton id={record.id} />
                      </Grid>
                      <Grid item>
                        <Collapse in={!record.effectiveDt}>
                          <ConfirmActualButton id={record.id} />
                        </Collapse>
                        <Collapse in={record.effectiveDt}>
                          <RegenerateDividendResolutionButton id={record.id} />
                        </Collapse>
                      </Grid>
                      <Grid item>
                        <Collapse in={!record.effectiveDt}>
                          <CreateMonthlyInvestorNotesButton id={record.id} />
                        </Collapse>
                      </Grid>
                    </Grid>
                  );
                }}
              />
              <ReferenceInput source="portfolio.id" reference="PortfolioLite">
                <SelectInput label="Portfolio" fullWidth optionText="name" />
              </ReferenceInput>
              <RichTextInput
                multiline
                fullWidth
                source="investorNotes"
                label="Manager's Comments"
                helperText="This field should be in paragraph form and will be included at the bottom of each investor's dividend email. Leave this blank if you do not want any description of a dividend to be included in the email."
              />
              <RichTextInput multiline fullWidth source="internalNotes" />
              <TextInput
                source="investorNotesPrompt.prompt"
                fullWidth
                multiline
                label="GPT Prompt"
                disabled
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab margin="none" label="dividends">
          <MonthlyPortfolioFinancialActualDividends id={id} />
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const withParams = (WrappedComponent) => {
  return (props) => <WrappedComponent {...props} params={useParams()} />;
};

export const MonthlyPortfolioFinancialActualEdit = withParams((props) => (
  <MonthlyPortfolioFinancialActualEditClass {...props} />
));

const ActualsPagination = () => <Pagination rowsPerPageOptions={[10, 25]} />;

const ActualsFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
  </Filter>
);

if (!document.getElementById('portfolio-pulse-animation')) {
  const style = document.createElement('style');
  style.id = 'portfolio-pulse-animation';
  style.textContent = `
    @keyframes pulse {
      0% { background-color: rgba(255,255,0,.1); }
      50% { background-color: rgba(255,255,0,.3); }
      100% { background-color: rgba(255,255,0,.1); }
    }
  `;
  document.head.appendChild(style);
}

export const MonthlyPortfolioFinancialActualList = () => {
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();
  const [recentProjections, setRecentProjections] = useState(null);
  const [recentProjectedCafdDialogOpen, setRecentProjectedCafdDialogOpen] =
    useState(false);
  const [selectedActual, setSelectedActual] = useState(null);
  const { permissions } = usePermissions();

  const getRecentProjections = (actualId) => {
    setRecentProjectedCafdDialogOpen(true);
    dataProvider
      .getList('MonthlyPortfolioFinancialActualProjections', {
        filter: { monthlyPortfolioFinancialActualId: actualId },
        pagination: { page: 1, perPage: 5 },
        sort: { field: 'createdAt', order: 'DESC' },
      })
      .then(
        (res) => {
          setRecentProjections(res.data);
        },
        (err) => {
          console.error(err);
          notify('Error getting recent CAFD projections', { type: 'error' });
          setRecentProjectedCafdDialogOpen(false);
          setRecentProjections(null);
        }
      );
  };

  const regenerateDividendResolutionLetter = (id) => {
    dataProvider
      .update('MonthlyPortfolioFinancialActual', {
        data: { id, regenerateDividendResolutionLetter: true },
      })
      .then(
        (record) => {
          notify('Dividend Resolution Letter successfully updated', {
            type: 'success',
          });
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
        }
      );
  };

  const styleRow = (record) => {
    const { dividendResolutionLetterDownloadUrl, effectiveDt, grossCafd } =
      record;
    const errorStyle = {
      backgroundColor: 'rgba(255,0,0,.2)',
      color: '#fff',
    };
    const warningStyle = {
      animationName: 'pulse',
      animationDuration: '1.8s',
      animationIterationCount: 'infinite',
      backgroundColor: 'rgba(255,255,0,.2)',
      color: '#000',
    };
    if (!dividendResolutionLetterDownloadUrl && effectiveDt && grossCafd > 0) {
      return errorStyle;
    }
    if (record.dividendsPendingFlg) {
      return warningStyle;
    }
    return {};
  };

  return (
    <>
      <List
        title="Monthly Financial Actuals"
        perPage={8}
        pagination={<ActualsPagination />}
        filters={<ActualsFilter />}
        sort={{ field: 'id', order: 'DESC' }}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
          rowStyle={styleRow}
        >
          <TextField source="id" />
          <DateField source="effectiveDt" />
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />
          <FunctionField
            fullWidth
            label="Expected Prod (MWh)"
            render={(record) => {
              if (!record.latestMonthlyPerformance) {
                return '';
              }
              if (record.latestMonthlyPerformance.productionDelta === null)
                return <span>-</span>;
              const difference =
                record.latestMonthlyPerformance.productionDelta;
              const val = record.latestMonthlyPerformance.expectedProduction;
              let color;
              if (difference === null) {
                color = 'grey';
              } else if (difference >= 0) {
                color = 'green';
              } else {
                color = 'red';
              }
              return (
                <span style={{ color }}>{numeral(val).format('0,0.[00]')}</span>
              );
            }}
          />
          <FunctionField
            fullWidth
            label="Actual Prod (MWh)"
            render={(record) => {
              if (!record.latestMonthlyPerformance) {
                return '';
              }
              if (record.latestMonthlyPerformance.actualProduction === null)
                return <span>-</span>;
              const difference =
                record.latestMonthlyPerformance.productionDelta;
              const val = record.latestMonthlyPerformance.actualProduction;
              let color;
              if (difference === null) {
                color = 'grey';
              } else if (difference >= 0) {
                color = 'green';
              } else {
                color = 'red';
              }
              return (
                <span style={{ color }}>{numeral(val).format('0,0.[00]')}</span>
              );
            }}
          />
          <FunctionField
            fullWidth
            label="Prod Delta"
            render={(record) => {
              if (!record.latestMonthlyPerformance) {
                return '';
              }
              if (
                record.latestMonthlyPerformance.productionDeltaPercentage ===
                null
              )
                return <span>-</span>;
              const difference =
                record.latestMonthlyPerformance.productionDelta;
              const val =
                record.latestMonthlyPerformance.productionDeltaPercentage;
              let color;
              if (difference === null) {
                color = 'grey';
              } else if (difference >= 0) {
                color = 'green';
              } else {
                color = 'red';
              }
              return (
                <span style={{ color }}>{numeral(val).format('0,0.[0]')}%</span>
              );
            }}
          />
          <FunctionField
            fullWidth
            label="Expected Net CAFD"
            render={(record) => {
              if (!record.latestMonthlyPerformance) {
                return '';
              }
              if (record.latestMonthlyPerformance.cafdDelta === null)
                return <span>-</span>;
              const difference = record.latestMonthlyPerformance.cafdDelta;
              const val = record.latestMonthlyPerformance.expectedCafd;
              let color;
              if (difference === null) {
                color = 'grey';
              } else if (difference >= 0) {
                color = 'green';
              } else {
                color = 'red';
              }
              return (
                <span style={{ color }}>
                  {numeral(val).format('$0,0.[00]')}
                </span>
              );
            }}
          />
          <FunctionField
            fullWidth
            label="Actual Net CAFD"
            render={(record) => {
              let textJsx = '';
              if (!record.latestMonthlyPerformance) {
                textJsx = '';
              } else {
                if (record.latestMonthlyPerformance.actualCafd === null) {
                  textJsx = <span>-</span>;
                } else {
                  const difference = record.latestMonthlyPerformance.cafdDelta;
                  const val = record.latestMonthlyPerformance.actualCafd;
                  let color;
                  if (difference === null) {
                    color = 'grey';
                  } else if (difference >= 0) {
                    color = 'green';
                  } else {
                    color = 'red';
                  }
                  textJsx = (
                    <span style={{ color }}>
                      {numeral(val).format('$0,0.[00]')}
                    </span>
                  );
                }
              }
              return (
                <Grid
                  container
                  alignItems="center"
                  justifyContent="space-between"
                  style={{ minWidth: '130px' }}
                >
                  <Grid item>{textJsx}</Grid>
                  <Grid item>
                    <IconButton
                      onClick={(event) => {
                        event.stopPropagation();
                        getRecentProjections(record.id);
                        setSelectedActual(record);
                      }}
                      size="large"
                    >
                      <Info />
                    </IconButton>
                  </Grid>
                </Grid>
              );
            }}
          />
          <FunctionField
            fullWidth
            label="CAFD Delta"
            render={(record) => {
              if (!record.latestMonthlyPerformance) {
                return '';
              }
              if (record.latestMonthlyPerformance.cafdDeltaPercentage === null)
                return <span>-</span>;
              const difference = record.latestMonthlyPerformance.cafdDelta;
              const val = record.latestMonthlyPerformance.cafdDeltaPercentage;
              let color;
              if (difference === null) {
                color = 'grey';
              } else if (difference >= 0) {
                color = 'green';
              } else {
                color = 'red';
              }
              return (
                <span style={{ color }}>{numeral(val).format('0,0.[0]')}%</span>
              );
            }}
          />
          <NumberField
            source="payoutData.annualizedCOCYield"
            label="Annualized COC Yield"
            options={{ style: 'percent', maximumFractionDigits: 1 }}
          />
          <FunctionField
            fullWidth
            label="COC Yield"
            render={(record) => {
              if (!record.payoutData.annualizedCOCYield) {
                return '0%';
              }
              return numeral(record.payoutData.annualizedCOCYield / 12).format(
                '0,0.00%'
              );
            }}
          />
          <FunctionField
            fullWidth
            align="center"
            label="Dividends Issued"
            render={(record) => {
              if (record.hasUnallocatedDividends) {
                return <Warning style={{ color: 'gold' }} />;
              }
              return <CheckCircle style={{ color: 'green' }} />;
            }}
          />
          <NumberField
            source="currentDividendPayoutSum"
            label="Actual Dividends Paid Out"
            options={{ style: 'currency', currency: 'USD' }}
          />

          <FunctionField
            fullWidth
            label="Payout delta"
            render={(record) => {
              const difference =
                record.currentDividendPayoutSum - record.grossCafd;
              let color;
              if (Math.abs(difference) < 0.1) {
                color = 'green';
              } else if (Math.abs(difference) < 5) {
                color = 'darkorange';
              } else {
                color = 'red';
              }
              return (
                <span style={{ color }}>
                  {numeral(difference).format('$0,0.00')}
                </span>
              );
            }}
          />
          <NumberField
            source="totalDividendsDistributed"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalDividendsReinvested"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalDividendsReinvestedSoldToEnergea"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalDividendsReinvestedSoldToCrowd"
            options={{ style: 'currency', currency: 'USD' }}
          />
          {/* <NumberField
            label="Percentage of Equity Contributing to Dividends"
            source="payoutData.percentageOfEquityContributingToDividends"
            options={{ style: 'percent', maximumFractionDigits: 1 }}
          />
          <NumberField
            source="payoutData.adjustedCOCYield"
            label="Adjusted COC Yield"
            options={{ style: 'percent', maximumFractionDigits: 1 }}
          /> */}

          <NumberField
            source="fees"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="feePercent"
            options={{
              style: 'percent',
              maximumFractionDigits: 4,
            }}
          />
          <NumberField
            source="carry"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="carryPercent"
            options={{ style: 'percent', maximumFractionDigits: 4 }}
          />
          <NumberField
            source="preferredReturn"
            options={{ style: 'percent', maximumFractionDigits: 4 }}
          />
          <NumberField
            source="portfolioIrr"
            options={{ style: 'percent', maximumFractionDigits: 4 }}
          />
          <NumberField
            source="lfdiFull"
            label="LFDI Full"
            options={{
              style: 'currency',
              currency: 'USD',
              maximumFractionDigits: 4,
            }}
          />
          <NumberField
            source="lifetimeValueOfDollarInvested"
            label="LFDI Actual"
            options={{
              style: 'currency',
              currency: 'USD',
              maximumFractionDigits: 4,
            }}
          />
          <NumberField
            source="totalEquity"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="sharePrice"
            options={{
              style: 'currency',
              currency: 'USD',
              maximumFractionDigits: 8,
            }}
          />
          <NumberField source="lintedMaxShares" />

          <DetailField
            source="investorNotes"
            label="Manager's Comments"
            sortable={false}
            richText={true}
          />
          <DetailField
            source="internalNotes"
            sortable={false}
            richText={true}
          />
          <FunctionField
            label="Dividend Resolution Letter"
            render={(record) => {
              if (record.grossCafd === 0) return null;
              if (record.dividendResolutionLetterDownloadUrl) {
                return (
                  <Button
                    variant="contained"
                    startIcon={<CloudDownload />}
                    style={{ textTransform: 'none' }}
                    onClick={(event) => {
                      event.stopPropagation();
                      event.preventDefault();
                      window.location.assign(
                        record.dividendResolutionLetterDownloadUrl
                      );
                    }}
                  >
                    Download Dividend Resolution Letter
                  </Button>
                );
              } else {
                return (
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    color="error"
                    style={{ textTransform: 'none' }}
                    disabled={!record.effectiveDt}
                    onClick={(event) => {
                      event.preventDefault();
                      event.stopPropagation();
                      regenerateDividendResolutionLetter(record.id);
                    }}
                  >
                    Create Dividend Resolution Letter
                  </Button>
                );
              }
            }}
          />
          <DateField source="createdAt" />
        </Datagrid>
      </List>
      {selectedActual ? (
        <Dialog
          open={recentProjectedCafdDialogOpen}
          onClose={() => {
            setRecentProjectedCafdDialogOpen(false);
            setRecentProjections(null);
            setSelectedActual(null);
          }}
        >
          <DialogTitle>
            <Typography variant="h5">Previous Projections</Typography>
            <Typography>Financial Actual: {selectedActual.id}</Typography>
          </DialogTitle>
          <DialogContent>
            <Grid container>
              {recentProjections ? (
                <MuiTable>
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <b>Projection</b>
                      </TableCell>
                      <TableCell>
                        <b>CAFD</b>
                      </TableCell>
                      <TableCell>
                        <b>CAFD Delta</b>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow key="actual-cafd-table-row">
                      <TableCell>
                        {moment(selectedActual.effectiveDt).format(
                          'MMM D, YYYY'
                        )}{' '}
                        (actual)
                      </TableCell>
                      <TableCell>
                        {numeral(
                          selectedActual?.latestMonthlyPerformance
                            ?.actualCafd || 0
                        ).format('$0,0.00')}
                      </TableCell>
                      <TableCell />
                    </TableRow>
                    {recentProjections.map((projection) => (
                      <TableRow
                        key={`projection-${projection.id}-cafd-table-row`}
                      >
                        <TableCell>
                          {moment(projection.createdAt).format('MMM D, YYYY')}
                        </TableCell>
                        <TableCell>
                          {numeral(projection.grossCafd).format('$0,0.00')}
                        </TableCell>
                        <TableCell>
                          {selectedActual?.latestMonthlyPerformance
                            ?.actualCafd ? (
                            <Typography
                              variant="body2"
                              style={{
                                color:
                                  projection.grossCafd >=
                                  selectedActual.latestMonthlyPerformance
                                    .actualCafd
                                    ? 'red'
                                    : 'green',
                              }}
                            >
                              {numeral(
                                (selectedActual.latestMonthlyPerformance
                                  .actualCafd -
                                  projection.grossCafd) /
                                  projection.grossCafd
                              ).format('0,0[.]00%')}
                            </Typography>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </MuiTable>
              ) : (
                <CircularProgress />
              )}
            </Grid>
          </DialogContent>
        </Dialog>
      ) : null}
    </>
  );
};

export const MonthlyPortfolioFinancialActualCreate = () => {
  const dataProvider = useDataProvider();

  const attrs = [
    { name: 'portfolioId', label: 'Portfolio ID', align: 'center' },
    {
      name: 'totalEquity',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Total Equity',
      align: 'center',
    },
    {
      name: 'netDistribution',
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Net Distribution',
      align: 'center',
    },
    {
      name: 'fees',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Fees',
      align: 'center',
    },
    {
      name: 'feePercent',
      format: (val) => numeral(val).format('%0,0.0000'),
      label: 'Fee Percent',
      align: 'center',
    },
    {
      name: 'carry',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Carry',
      align: 'center',
    },
    {
      name: 'carryPercent',
      format: (val) => numeral(val).format('%0,0.0000'),
      label: 'Carry Percent',
      align: 'center',
    },
    {
      name: 'lifetimeValueOfDollarInvested',
      validate: () => true, // value not required
      format: (val) => numeral(val).format('$0,0.000000'),
      label: 'LVDI',
      align: 'center',
    },
    {
      name: 'lfdiFull',
      validate: () => true, // value not required
      format: (val) => numeral(val).format('$0,0.000000'),
      label: 'LFDI Full',
      align: 'center',
    },
    {
      name: 'production',
      dataFormat: (val) => Math.round(val * 10) / 10,
      format: (val) => numeral(val).format('0,0.0'),
      label: 'Production (MWh)',
      align: 'center',
    },
    {
      name: 'sharePrice',
      dataFormat: (val) => Math.round(val * 1000000) / 1000000,
      format: (val) => numeral(val).format('$0,0.0000'),
      label: 'Share Price',
      align: 'center',
    },
    {
      name: 'portfolioIrr',
      // dataFormat: (val) => Math.round(val * 10000) / 10000,
      format: (val) => numeral(val).format('%0,0.0000'),
      label: 'Portfolio IRR',
      align: 'center',
    },
    {
      name: 'preferredReturn',
      // dataFormat: (val) => Math.round(val * 10000) / 10000,
      format: (val) => (val === 'NA' ? 'NA' : numeral(val).format('%0,0.0000')),
      label: 'Preferred Return',
      align: 'center',
    },
    {
      name: 'cash',
      dataFormat: (val) => Math.round(val * 10000) / 10000,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Cash',
      align: 'center',
    },
    {
      name: 'loans',
      dataFormat: (val) => Math.round(val * 10000) / 10000,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Loans',
      align: 'center',
    },
    {
      name: 'shortTermInvestments',
      dataFormat: (val) => Math.round(val * 10000) / 10000,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Short Term Investments',
      align: 'center',
    },
    {
      name: 'projectEquity',
      dataFormat: (val) => Math.round(val * 10000) / 10000,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Project Equity',
      align: 'center',
    },
  ];
  return (
    <Create title={`Create ${entityName}`}>
      <MonthlyPortfolioFinancialsUpload
        dataProvider={dataProvider}
        attrs={attrs}
        type="Actual"
      />
    </Create>
  );
};
