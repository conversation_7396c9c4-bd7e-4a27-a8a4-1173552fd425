import React from 'react';
import { useParams } from 'react-router-dom';
import copy from 'copy-to-clipboard';
import {
  <PERSON>reate,
  <PERSON>grid,
  DateField,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useNotify,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { RichTextInput } from 'ra-input-rich-text';

import { Button, Grid } from '@mui/material';
import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import { FileCopy } from '@mui/icons-material';

const entityName = 'Faq	 Entry';

export const FaqEntryEdit = () => {
  const { id } = useParams();
  const faqLink = `https://www.energea.com/faq?qid=${id}`;
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={8}>
            <TextInput multiline source="question" fullWidth />
            <RichTextInput multiline source="answer" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth step={1} />
            <ReferenceInput source="category.id" reference="FaqCategory">
              <SelectInput label="Category" fullWidth optionText="name" />
            </ReferenceInput>
            Sharable link to more easily direct users to FAQ entries:
            <br />
            <a href={faqLink}>{faqLink}</a>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by question"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const FaqEntryList = () => {
  const notify = useNotify();
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<CustomFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <NumberField source="orderNo" />
        <TextField source="question" sortable={false} />
        <FunctionField
          label="Answer"
          style={{ width: '100%' }}
          render={(record) => {
            return <div dangerouslySetInnerHTML={{ __html: record.answer }} />;
          }}
        />
        <FunctionField
          label="Link"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <Button
                onClick={(event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  copy(`https://www.energea.com/faq?qid=${record.id}`);
                  notify('FAQ Link copied to clipboard', { type: 'success' });
                }}
                startIcon={<FileCopy />}
                style={{ textTransform: 'none' }}
              >
                Copy Link
              </Button>
            );
          }}
        />
        <LinkField
          label="Category"
          linkSource="category.id"
          labelSource="category.name"
          reference="FaqCategory"
        />
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const FaqEntryCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={8}>
          <TextInput
            source="question"
            helperText="This can be edited at any time so no need to be perfect."
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
