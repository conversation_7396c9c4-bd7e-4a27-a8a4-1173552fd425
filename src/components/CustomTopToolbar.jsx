import React from 'react';
import {
  CreateButton,
  ExportButton,
  TopToolbar,
  useListContext,
} from 'react-admin';
import { EmailExportButton } from './EmailExportButton';

export const CustomTopToolbar = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();

  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      {props?.hideExportButton ? null : <ExportButton maxResults={50000} />}
      <EmailExportButton
        {...props}
        resource={resource}
        filterValues={filterValues}
      />
    </TopToolbar>
  );
};
