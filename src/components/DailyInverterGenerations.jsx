import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  Filter,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  TopToolbar,
  useListContext,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Daily Inverter Generation';

export const DailyInverterGenerationEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="date" fullWidth helperText="YYYY-MM-DD" />
            <CustomNumberInput
              source="production"
              fullWidth
              label="Production (Wh)"
            />
            <ReferenceInput
              source="inverter.id"
              reference="Inverter"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                label="Inverter"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => {
  const { filterValues } = useListContext();
  const inverterFilter = {};
  if (filterValues.project?.id) {
    inverterFilter.project = { id: filterValues.project?.id };
  }

  return (
    <Filter {...props}>
      <ReferenceInput
        label="Project"
        source="project.id"
        reference="Project"
        perPage={10000}
        sort={{ field: 'name', order: 'ASC' }}
      >
        <SelectInput label="Project" optionText="name" />
      </ReferenceInput>
      <ReferenceInput
        label="Inverter"
        source="inverter.id"
        reference="Inverter"
        perPage={10000}
        sort={{ field: 'id', order: 'ASC' }}
        filter={inverterFilter}
      >
        <SelectInput label="Inverter" optionText="name" />
      </ReferenceInput>
      <DateInput source="dateLowerBound" label="Date (Lower Bound)" />
      <DateInput source="dateUpperBound" label="Date (Upper Bound)" />
    </Filter>
  );
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      <ExportButton maxResults={10000} />
    </TopToolbar>
  );
};

export const DailyInverterGenerationList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<CustomFilter />}
      actions={<ListActions />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Inverter"
          linkSource="inverter.id"
          labelSource="inverter.name"
          label="Inverter"
        />
        <TextField source="date" />
        <NumberField source="production" label="Production (Wh)" />
        <DateField showTime source="updatedAt" />
        <DateField showTime source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const DailyInverterGenerationCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="date" required fullWidth helperText="YYYY-MM-DD" />
          <CustomNumberInput
            source="production"
            fullWidth
            label="Production (Wh)"
          />
          <ReferenceInput
            source="inverter.id"
            reference="Inverter"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              label="Inverter"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
