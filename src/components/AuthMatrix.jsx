import React, { useState } from 'react';
import {
  useResourceDefinitions,
  Title,
  useDataProvider,
  useNotify,
} from 'react-admin';

import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardContent,
  Collapse,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItem,
  ListItemText,
  MenuItem,
  Select,
  Table,
  TableCell,
  TableHead,
  TableRow,
  TableBody,
  Tab,
  Tabs,
  Typography,
  Alert,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Apps,
  Delete,
  Group,
  KeyboardArrowDown,
  KeyboardArrowUp,
  Person,
} from '@mui/icons-material';
import MuiList from '@mui/material/List';
import MuiTextField from '@mui/material/TextField';

import { roleMatrix } from '../utils/applyRoleAuth';

const entityName = 'Auth Matrix';

const rowShadeColor = '#f9f9f9';

function a11yProps(index) {
  return {
    id: `vertical-tab-${index}`,
    'aria-controls': `vertical-tabpanel-${index}`,
  };
}

function ResourceRow(props) {
  const { resource, usersWithReadAccess, usersWithWriteAccess } = props;
  const [open, setOpen] = useState(false);
  const [userSearchString, setUserSearchString] = useState(null);
  const [groupSearchString, setGroupSearchString] = useState(null);

  return (
    <>
      <TableRow sx={{ '& > *': { borderBottom: 'unset' } }}>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {resource.name}
        </TableCell>
        <TableCell align="right">
          {(resource.options && resource.options.category) || 'other'}
        </TableCell>
        <TableCell align="right">{`[${
          (resource.readAccess &&
            resource.readAccess.length &&
            resource.readAccess.join(', ')) ||
          ''
        }]`}</TableCell>
        <TableCell align="right">{`[${
          (resource.writeAccess &&
            resource.writeAccess.length &&
            resource.writeAccess.join(', ')) ||
          ''
        }]`}</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Grid container style={{ margin: '1rem' }} direction="column">
              <Grid
                item
                xs={12}
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography variant="h6" gutterBottom component="div">
                    Groups With Access
                  </Typography>
                </Grid>
                <Grid item>
                  <MuiTextField
                    label="Search"
                    variant="filled"
                    value={groupSearchString}
                    onChange={(event) =>
                      setGroupSearchString(event.target.value)
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} container style={{ paddingLeft: '1rem' }}>
                <Grid item xs={12}>
                  <Typography>Read Access</Typography>
                </Grid>
                <Grid item xs={12}>
                  <MuiList dense>
                    {resource.readAccess && resource.readAccess.length > 0 ? (
                      resource.readAccess
                        .filter((role) => {
                          if (groupSearchString) {
                            return role
                              .toLowerCase()
                              .includes(groupSearchString.toLowerCase());
                          }
                          return true;
                        })
                        .map((role) => (
                          <ListItem
                            key={`auth-matrix-resource-row-${resource}-read-${role}`}
                          >
                            <ListItemText primary={role} />
                          </ListItem>
                        ))
                    ) : (
                      <ListItem>
                        <ListItemText primary="No roles" />
                      </ListItem>
                    )}
                  </MuiList>
                </Grid>
                <Grid item xs={12}>
                  <Typography>Write Access</Typography>
                </Grid>
                <Grid item xs={12}>
                  <MuiList dense>
                    {resource.readAccess && resource.readAccess.length > 0 ? (
                      resource.readAccess
                        .filter((role) => {
                          if (groupSearchString) {
                            return role
                              .toLowerCase()
                              .includes(groupSearchString.toLowerCase());
                          }
                          return true;
                        })
                        .map((role) => (
                          <ListItem
                            key={`auth-matrix-resource-row-${resource}-write-${role}`}
                          >
                            <ListItemText primary={role} />
                          </ListItem>
                        ))
                    ) : (
                      <ListItem>
                        <ListItemText primary="No roles" />
                      </ListItem>
                    )}
                  </MuiList>
                </Grid>
              </Grid>
              <Grid
                item
                xs={12}
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography variant="h6" gutterBottom component="div">
                    Users With Access
                  </Typography>
                </Grid>
                <Grid item>
                  <MuiTextField
                    label="Search"
                    variant="filled"
                    value={userSearchString}
                    onChange={(event) =>
                      setUserSearchString(event.target.value)
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} container style={{ paddingLeft: '1rem' }}>
                <Grid item xs={12}>
                  <Typography>Read Access</Typography>
                </Grid>
                <Grid item xs={12}>
                  <MuiList dense>
                    {usersWithReadAccess
                      .filter((user) => {
                        if (userSearchString) {
                          return (
                            user.profile.email
                              .toLowerCase()
                              .includes(userSearchString.toLowerCase()) ||
                            `${user.profile.firstName} ${user.profile.lastName}`
                              .toLowerCase()
                              .includes(userSearchString.toLowerCase())
                          );
                        }
                        return true;
                      })
                      .map((user) => (
                        <ListItem
                          key={`auth-matrix-resource-row-${resource.name}-read-${user.id}`}
                        >
                          <ListItemText
                            primary={`${user.profile.firstName} ${user.profile.lastName}`}
                            secondary={user.profile.email}
                          />
                        </ListItem>
                      ))}
                  </MuiList>
                </Grid>
                <Grid item xs={12}>
                  <Typography>Write Access</Typography>
                </Grid>
                <Grid item xs={12}>
                  <MuiList dense>
                    {usersWithWriteAccess
                      .filter((user) => {
                        if (userSearchString) {
                          return (
                            user.profile.email
                              .toLowerCase()
                              .includes(userSearchString.toLowerCase()) ||
                            `${user.profile.firstName} ${user.profile.lastName}`
                              .toLowerCase()
                              .includes(userSearchString.toLowerCase())
                          );
                        }
                        return true;
                      })
                      .map((user) => (
                        <ListItem
                          key={`auth-matrix-resource-row-${resource.name}-write-${user.id}`}
                        >
                          <ListItemText
                            primary={`${user.profile.firstName} ${user.profile.lastName}`}
                            secondary={user.profile.email}
                          />
                        </ListItem>
                      ))}
                  </MuiList>
                </Grid>
              </Grid>
            </Grid>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

function GroupRow(props) {
  const { oktaGroup, accessibleResources } = props;
  const [open, setOpen] = useState(false);
  const [userSearchString, setUserSearchString] = useState(null);
  const [resourceSearchString, setResourceSearchString] = useState(null);

  return (
    <>
      <TableRow sx={{ '& > *': { borderBottom: 'unset' } }}>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {oktaGroup.name}
        </TableCell>
        <TableCell align="right">{oktaGroup.description}</TableCell>
        <TableCell align="right">{oktaGroup.users.length}</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Grid container style={{ margin: '1rem' }}>
              <Grid item xs={12}>
                <Grid
                  container
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Grid item>
                    <Typography variant="h6" gutterBottom component="div">
                      User Membership
                    </Typography>
                  </Grid>
                  <Grid item>
                    <MuiTextField
                      label="Search"
                      variant="filled"
                      value={userSearchString}
                      onChange={(event) =>
                        setUserSearchString(event.target.value)
                      }
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <MuiList dense>
                  {oktaGroup.users
                    .filter((user) => {
                      if (userSearchString) {
                        return (
                          user.profile.email
                            .toLowerCase()
                            .includes(userSearchString.toLowerCase()) ||
                          `${user.profile.firstName} ${user.profile.lastName}`
                            .toLowerCase()
                            .includes(userSearchString.toLowerCase())
                        );
                      }
                      return true;
                    })
                    .map((user, index) => (
                      <ListItem
                        key={`auth-matrix-group-row-${oktaGroup.name}-${user.id}`}
                        style={{
                          backgroundColor:
                            index % 2 === 1 ? rowShadeColor : null,
                        }}
                      >
                        <ListItemText
                          primary={`${user.profile.firstName} ${user.profile.lastName}`}
                          secondary={user.profile.email}
                        />
                      </ListItem>
                    ))}
                </MuiList>
              </Grid>
              <Grid item xs={12}>
                <Grid
                  container
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Grid item>
                    <Typography variant="h6" gutterBottom component="div">
                      Resource Access
                    </Typography>
                  </Grid>
                  <Grid item>
                    <MuiTextField
                      label="Search"
                      variant="filled"
                      value={resourceSearchString}
                      onChange={(event) =>
                        setResourceSearchString(event.target.value)
                      }
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} container style={{ paddingLeft: '1rem' }}>
                <Grid item xs={12}>
                  <Typography>Read Access</Typography>
                </Grid>
                <Grid item xs={12}>
                  <MuiList dense>
                    {accessibleResources ? (
                      accessibleResources.readAccess
                        ?.filter((resource) => {
                          if (resourceSearchString) {
                            return resource
                              .toLowerCase()
                              .includes(resourceSearchString.toLowerCase());
                          }
                          return true;
                        })
                        .map((resource, index) => (
                          <ListItem
                            key={`auth-matrix-group-row-${oktaGroup.name}-${resource}`}
                            style={{
                              backgroundColor:
                                index % 2 === 1 ? rowShadeColor : null,
                            }}
                          >
                            <ListItemText primary={resource} />
                          </ListItem>
                        ))
                    ) : (
                      <ListItem>
                        <ListItemText primary="No Resources" />
                      </ListItem>
                    )}
                  </MuiList>
                </Grid>
                <Grid item xs={12}>
                  <Typography>Write Access</Typography>
                </Grid>
                <Grid item xs={12}>
                  <MuiList dense>
                    {accessibleResources ? (
                      accessibleResources.writeAccess
                        ?.filter((resource) => {
                          if (resourceSearchString) {
                            return resource
                              .toLowerCase()
                              .includes(resourceSearchString.toLowerCase());
                          }
                          return true;
                        })
                        .map((resource, index) => (
                          <ListItem
                            key={`auth-matrix-group-row-${oktaGroup.name}-${resource}`}
                            style={{
                              backgroundColor:
                                index % 2 === 1 ? rowShadeColor : null,
                            }}
                          >
                            <ListItemText primary={resource} />
                          </ListItem>
                        ))
                    ) : (
                      <ListItem>
                        <ListItemText primary="No Resources" />
                      </ListItem>
                    )}
                  </MuiList>
                </Grid>
              </Grid>
            </Grid>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

function UserRow(props) {
  const { user, accessibleResources, handleRemoveUserFromGroup } = props;
  const [open, setOpen] = useState(false);
  const [resourceSearchString, setResourceSearchString] = useState(null);
  const [groupSearchString, setGroupSearchString] = useState(null);

  const getResourceListJsx = () => {
    const readJsx = [];
    const writeJsx = [];
    accessibleResources &&
      Object.keys(accessibleResources).forEach((resource) => {
        if (
          resourceSearchString &&
          !resource.toLowerCase().includes(resourceSearchString.toLowerCase())
        ) {
          return;
        }
        const resourceWithRoles = accessibleResources[String(resource)];
        if (resourceWithRoles.readAccess?.length > 0) {
          readJsx.push(
            <ListItem
              key={`auth-matrix-user-row-${user.id}-read-${resource}`}
              style={{
                backgroundColor:
                  readJsx.length % 2 === 1 ? rowShadeColor : null,
              }}
            >
              <ListItemText
                primary={resource}
                secondary={resourceWithRoles?.readAccess?.join(', ')}
              />
            </ListItem>
          );
        }
        if (resourceWithRoles.writeAccess?.length > 0) {
          writeJsx.push(
            <ListItem
              key={`auth-matrix-user-row-${user.id}-write-${resource}`}
              style={{
                backgroundColor:
                  writeJsx.length % 2 === 1 ? rowShadeColor : null,
              }}
            >
              <ListItemText
                primary={resource}
                secondary={
                  (resourceWithRoles.writeAccess &&
                    resourceWithRoles.writeAccess.join(', ')) ||
                  null
                }
              />
            </ListItem>
          );
        }
      });

    return (
      <>
        <Grid item xs={12}>
          <Typography>Read Access</Typography>
        </Grid>
        <Grid item xs={12}>
          <MuiList dense>
            {readJsx.length > 0 ? (
              readJsx
            ) : (
              <ListItem>
                <ListItemText primary="No Resources" />
              </ListItem>
            )}
          </MuiList>
        </Grid>
        <Grid item xs={12}>
          <Typography>Write Access</Typography>
        </Grid>
        <Grid item xs={12}>
          <MuiList dense>
            {writeJsx.length > 0 ? (
              writeJsx
            ) : (
              <ListItem>
                <ListItemText primary="No Resources" />
              </ListItem>
            )}
          </MuiList>
        </Grid>
      </>
    );
  };

  return (
    <>
      <TableRow sx={{ '& > *': { borderBottom: 'unset' } }}>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {`${user.profile.firstName} ${user.profile.lastName}`}
        </TableCell>
        <TableCell align="right">{user.profile.email}</TableCell>
        <TableCell align="right">
          {user.roles.map((r) => r.name).join(', ')}
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Grid container style={{ margin: '1rem' }}>
              <Grid
                item
                xs={12}
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography variant="h6" gutterBottom component="div">
                    Group Membership
                  </Typography>
                </Grid>
                <Grid item>
                  <MuiTextField
                    label="Search"
                    variant="filled"
                    value={groupSearchString}
                    onChange={(event) =>
                      setGroupSearchString(event.target.value)
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <MuiList dense>
                  {user.roles
                    .filter((role) => {
                      if (groupSearchString) {
                        return (
                          role.name
                            .toLowerCase()
                            .includes(groupSearchString.toLowerCase()) ||
                          role.name
                            .toLowerCase()
                            .includes(groupSearchString.toLowerCase())
                        );
                      }
                      return true;
                    })
                    .map((role, index) => (
                      <ListItem
                        key={`auth-matrix-user-row-${user.id}-${role.id}`}
                        style={{
                          backgroundColor:
                            index % 2 === 1 ? rowShadeColor : null,
                        }}
                      >
                        <ListItemText
                          primary={role.name}
                          secondary={role.description}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            onClick={() => {
                              if (
                                window.confirm(
                                  `Clicking 'OK' will remove '${user.profile.email}' from '${role.name}' which may remove some of their access. Are you sure you wish to continue?`
                                )
                              ) {
                                handleRemoveUserFromGroup(user.id, role.id);
                              }
                            }}
                          >
                            <Delete />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                </MuiList>
              </Grid>
              <Grid
                item
                xs={12}
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography variant="h6" gutterBottom component="div">
                    Resource Access
                  </Typography>
                </Grid>
                <Grid item>
                  <MuiTextField
                    label="Search"
                    variant="filled"
                    value={resourceSearchString}
                    onChange={(event) =>
                      setResourceSearchString(event.target.value)
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} container style={{ paddingLeft: '1rem' }}>
                {getResourceListJsx()}
              </Grid>
            </Grid>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

export const AuthMatrixView = () => {
  // Component temporarily disabled during Auth0 migration
  return (
    <div style={{ padding: '2rem' }}>
      <Title title={entityName} />
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Auth Matrix - Temporarily Unavailable
        </Typography>
        <Typography variant="body1">
          This component is being updated to work with Auth0 instead of Okta.
          User and role management is now handled through the Auth0 Dashboard.
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Please use the Auth0 Dashboard to manage user roles and permissions
          during the migration period.
        </Typography>
      </Alert>
    </div>
  );
};

// Legacy component code (commented out during migration)
const AuthMatrixViewLegacy = () => {
  const [data, setData] = useState();
  const [selectedTab, setSelectedTab] = useState(0);

  const [resourceFilter, setResourceFilter] = useState(null);
  const [groupFilter, setGroupFilter] = useState(null);
  const [userFilter, setUserFilter] = useState(null);

  const [selectedAction, setSelectedAction] = useState('add');
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();

  const resourcesDefinitions = useResourceDefinitions();
  const resources = Object.keys(resourcesDefinitions).map(
    (name) => resourcesDefinitions[String(name)]
  );

  const fetchData = (input = {}) => {
    dataProvider.getOne('AuthMatrix', {}).then(
      (resp) => {
        setData(resp.data);
      },
      (e) => {
        console.error('Error fetching data', e);
      }
    );
  };

  const handleAddUserToGroup = (oktaId, oktaGroupId) => {
    dataProvider
      .update('OktaUser', {
        input: { oktaId, oktaGroupId, addUserToGroup: true },
      })
      .then(
        (resp) => {
          fetchData();
          notify(`Successfully added user to group`, { type: 'success' });
        },
        (e) => {
          notify(`Error adding user to group`, { type: 'error' });
          console.error('Error adding okta user to group', e);
        }
      );
  };

  const handleRemoveUserFromGroup = (oktaId, oktaGroupId) => {
    dataProvider
      .update('OktaUser', {
        input: { oktaId, oktaGroupId, removeUserFromGroup: true },
      })
      .then(
        (resp) => {
          fetchData();
          notify(`Successfully removed user from group`, { type: 'success' });
        },
        (e) => {
          notify(`Error removing user from group`, { type: 'error' });
          console.error('Error remove okta user from group', e);
        }
      );
  };

  const handleDeactivateUser = (oktaId) => {
    dataProvider
      .update('OktaUser', {
        input: { oktaId, deactivateUser: true },
      })
      .then(
        (resp) => {
          fetchData();
          notify(`Successfully deactivated user`, { type: 'success' });
        },
        (e) => {
          notify(`Error deactivating user`, { type: 'error' });
          console.error('Error deactivating okta user', e);
        }
      );
  };

  const getUsersWithGroups = (groupsWithUsers) => {
    // In the AuthMatrixData query, we could change the query to get the groups for each user
    // but that would make an okta call for each user for each group and we want to avoid the rate limit
    const userGroupMap = {};
    const oktaIdUserMap = {};
    groupsWithUsers.forEach((group) => {
      group.users.forEach((user) => {
        if (userGroupMap[user.id]) {
          userGroupMap[user.id].push({
            id: group.id,
            name: group.name,
            description: group.description,
          });
        } else {
          userGroupMap[user.id] = [
            {
              id: group.id,
              name: group.name,
              description: group.description,
            },
          ];
          oktaIdUserMap[user.id] = user;
        }
      });
    });
    const res = [];
    Object.keys(userGroupMap).forEach((oktaId) => {
      const oUser = oktaIdUserMap[String(oktaId)];
      oUser.roles = userGroupMap[String(oktaId)];
      res.push(oUser);
    });
    return res;
  };

  const getGroupsWithResources = (resourcesWithGroups) => {
    const groupsWithResources = {};
    resourcesWithGroups.forEach((resource) => {
      const { readAccess, writeAccess } = resource;
      readAccess.forEach((group) => {
        if (groupsWithResources[String(group)]) {
          if (groupsWithResources[String(group)].readAccess) {
            groupsWithResources[String(group)].readAccess.push(resource.name);
          } else {
            groupsWithResources[String(group)].readAccess = [resource.name];
          }
        } else {
          groupsWithResources[String(group)] = {
            readAccess: [resource.name],
          };
        }
      });
      writeAccess.forEach((group) => {
        if (groupsWithResources[String(group)]) {
          if (groupsWithResources[String(group)].writeAccess) {
            groupsWithResources[String(group)].writeAccess.push(resource.name);
          } else {
            groupsWithResources[String(group)].writeAccess = [resource.name];
          }
        } else {
          groupsWithResources[String(group)] = {
            writeAccess: [resource.name],
          };
        }
      });
    });
    return groupsWithResources;
  };

  const getUsersWithResources = (
    usersWGroups,
    groupsWResources,
    resourcesWAccess
  ) => {
    const usersWithResources = {};
    usersWGroups.forEach((userWGroup) => {
      const oktaId = userWGroup.id;
      usersWithResources[String(oktaId)] = {};
      userWGroup.roles.forEach((role) => {
        if (groupsWResources[String(role.name)]) {
          const { readAccess, writeAccess } =
            groupsWResources[String(role.name)];
          readAccess?.forEach((resource) => {
            if (usersWithResources[String(oktaId)][String(resource)]) {
              if (
                usersWithResources[String(oktaId)][String(resource)].readAccess
              ) {
                usersWithResources[String(oktaId)][
                  String(resource)
                ].readAccess.push(role.name);
              } else {
                usersWithResources[String(oktaId)][
                  String(resource)
                ].readAccess = [role.name];
              }
            } else {
              usersWithResources[String(oktaId)][String(resource)] = {
                readAccess: [role.name],
              };
            }
          });
          writeAccess?.forEach((resource) => {
            if (usersWithResources[String(oktaId)][String(resource)]) {
              if (
                usersWithResources[String(oktaId)][String(resource)].writeAccess
              ) {
                usersWithResources[String(oktaId)][
                  String(resource)
                ].writeAccess.push(role.name);
              } else {
                usersWithResources[String(oktaId)][
                  String(resource)
                ].writeAccess = [role.name];
              }
            } else {
              usersWithResources[String(oktaId)][String(resource)] = {
                writeAccess: [role.name],
              };
            }
          });
        }
      });
    });
    return usersWithResources;
  };

  const getResourcesWithUsers = (resourcesWAccess, groupWUsersMap) => {
    const resourcesWUsers = {};
    resourcesWAccess.forEach((resource) => {
      const userOktaIdsAddedRead = [];
      resourcesWUsers[resource.name] = { readAccess: [] };
      resource.readAccess.forEach((role) => {
        const usersInRole = groupWUsersMap[String(role)]?.users || [];
        usersInRole.forEach((user) => {
          if (userOktaIdsAddedRead.indexOf(user.id) === -1) {
            resourcesWUsers[resource.name].readAccess.push(user);
            userOktaIdsAddedRead.push(user.id);
          }
        });
      });
      const userOktaIdsAddedWrite = [];
      resourcesWUsers[resource.name].writeAccess = [];
      resource.writeAccess.forEach((role) => {
        const usersInRole = groupWUsersMap[String(role)]?.users || [];
        usersInRole.forEach((user) => {
          if (userOktaIdsAddedWrite.indexOf(user.id) === -1) {
            resourcesWUsers[resource.name].writeAccess.push(user);
            userOktaIdsAddedWrite.push(user.id);
          }
        });
      });
    });
    return resourcesWUsers;
  };

  if (!data) {
    fetchData();
    return null;
  }

  const resourcesWithAccess = resources.map((resource) => {
    const resourceWithAccess = resource;
    resourceWithAccess.readAccess = roleMatrix[resource.name].readAccess;
    resourceWithAccess.writeAccess = roleMatrix[resource.name].writeAccess;
    return resourceWithAccess;
  });

  const groupedResources = {};
  resourcesWithAccess.forEach((resource) => {
    const groupName =
      resource.options && resource.options.category
        ? resource.options.category
        : 'other';
    if (groupedResources[String(groupName)]) {
      groupedResources[String(groupName)].push(resource);
    } else {
      groupedResources[String(groupName)] = [resource];
    }
  });

  const groupsWUsers = data.allOktaGroups;
  const usersWGroups = getUsersWithGroups(groupsWUsers);
  const groupsWithResources = getGroupsWithResources(resourcesWithAccess);
  const usersWithResources = getUsersWithResources(
    usersWGroups,
    groupsWithResources,
    resourcesWithAccess
  );
  const groupWithUsersMap = {};
  groupsWUsers.forEach((group) => {
    groupWithUsersMap[String(group.name)] = group;
  });
  const resourcesWithUsers = getResourcesWithUsers(
    resourcesWithAccess,
    groupWithUsersMap
  );

  return (
    <Card>
      <Title title={entityName} />
      <CardContent>
        <Grid container>
          <Grid item xs={12}>
            <Typography variant="h5">{entityName}</Typography>
          </Grid>
          <Divider style={{ width: '100%', margin: '1rem 0' }} />
          <Grid item xs={12} container alignItems="center">
            <Grid item>
              <FormControl>
                <InputLabel id="auth-user-action-select-label">
                  Action
                </InputLabel>
                <Select
                  labelId="auth-user-action-select-label"
                  id="auth-user-action-select"
                  style={{ width: 150 }}
                  variant="outlined"
                  value={selectedAction || ''}
                  onChange={(event) => {
                    setSelectedAction(event.target.value);
                  }}
                >
                  <MenuItem value={'add'}>Add</MenuItem>
                  <MenuItem value={'remove'}>Remove</MenuItem>
                  <MenuItem value={'deactivate'}>Deactivate</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item style={{ paddingLeft: '1rem' }}>
              <Autocomplete
                id="auto-complete-users"
                options={usersWGroups}
                getOptionLabel={(option) =>
                  `${option.profile.firstName} ${option.profile.lastName}`
                }
                renderOption={(props, option, state) => (
                  <ListItem {...props}>
                    <ListItemText
                      style={{ padding: 0, margin: 0 }}
                      primary={`${option.profile.firstName} ${option.profile.lastName}`}
                      secondary={option.profile.email}
                    />
                  </ListItem>
                )}
                style={{ width: 250 }}
                renderInput={(params) => (
                  <MuiTextField {...params} label="User" variant="outlined" />
                )}
                value={selectedUser}
                onChange={(event, newValue) => {
                  setSelectedUser(newValue);
                }}
              />
            </Grid>
            {selectedAction !== 'deactivate' ? (
              <>
                <Grid item style={{ paddingLeft: '1rem' }}>
                  <Typography>
                    {selectedAction === 'add' ? 'to' : 'from'}
                  </Typography>
                </Grid>
                <Grid item style={{ paddingLeft: '1rem' }}>
                  <Autocomplete
                    id="auto-complete-groups"
                    options={groupsWUsers.filter((group) => {
                      if (!selectedUser) return false;
                      for (
                        let index = 0;
                        index < group.users.length;
                        index += 1
                      ) {
                        const user = group.users[parseInt(index, 10)];
                        if (user.id === selectedUser.id) {
                          return selectedAction === 'remove';
                        }
                      }
                      return selectedAction === 'add';
                    })}
                    getOptionLabel={(option) => option.name}
                    style={{ width: 200 }}
                    renderInput={(params) => (
                      <MuiTextField
                        {...params}
                        label="Group"
                        variant="outlined"
                      />
                    )}
                    value={selectedGroup}
                    disabled={!selectedUser}
                    onChange={(event, newValue) => {
                      setSelectedGroup(newValue);
                    }}
                  />
                </Grid>
              </>
            ) : null}
            <Grid item style={{ paddingLeft: '1rem' }}>
              <Button
                variant="contained"
                color="primary"
                disabled={
                  !selectedUser ||
                  (!selectedGroup &&
                    (selectedAction === 'add' || selectedAction === 'remove'))
                }
                onClick={() => {
                  if (selectedAction === 'add') {
                    handleAddUserToGroup(selectedUser.id, selectedGroup.id);
                    setSelectedGroup(null);
                  } else if (selectedAction === 'remove') {
                    handleRemoveUserFromGroup(
                      selectedUser.id,
                      selectedGroup.id
                    );
                    setSelectedGroup(null);
                  } else if (selectedAction === 'deactivate') {
                    handleDeactivateUser(selectedUser.id);
                    setSelectedUser(null);
                    setSelectedGroup(null);
                  }
                }}
              >
                {selectedAction.toUpperCase()}
              </Button>
            </Grid>
          </Grid>
          {selectedAction === 'deactivate' ? (
            <Grid item xs={12} style={{ paddingTop: '1rem' }}>
              <Alert severity="warning">
                Deactivating a user will prevent this user from logging in but
                will not delete the Okta user.
              </Alert>
            </Grid>
          ) : null}
          <Grid item xs={12} style={{ paddingTop: '1rem' }}>
            <Alert severity="info">
              Adding or removing a Group from a resource requires a code change.
            </Alert>
          </Grid>
          <Divider style={{ width: '100%', margin: '1rem 0' }} />
          {/* <AppBar position="static"> */}
          <Tabs
            value={selectedTab}
            onChange={(event, newValue) => setSelectedTab(newValue)}
            centered
            style={{ width: '100%' }}
          >
            <Tab label="Users" icon={<Person />} {...a11yProps(0)} />
            <Tab label="Groups" icon={<Group />} {...a11yProps(1)} />
            <Tab label="Resources" icon={<Apps />} {...a11yProps(2)} />
          </Tabs>
          {/* </AppBar> */}
          <TabPanel value={selectedTab} index={0} style={{ width: '100%' }}>
            <Grid container>
              <Grid item xs={12} container justifyContent="center">
                <MuiTextField
                  label="Search"
                  variant="filled"
                  style={{ width: 600 }}
                  value={userFilter}
                  onChange={(event) => setUserFilter(event.target.value)}
                />
              </Grid>
              <Table aria-label="collapsible table">
                <TableHead>
                  <TableRow>
                    <TableCell />
                    <TableCell>
                      <b>User</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Email</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Group Membership</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {usersWGroups
                    .filter((user) => {
                      if (userFilter) {
                        return (
                          user.profile.email
                            .toLowerCase()
                            .includes(userFilter.toLowerCase()) ||
                          `${user.profile.firstName} ${user.profile.lastName}`
                            .toLowerCase()
                            .includes(userFilter.toLowerCase())
                        );
                      }
                      return true;
                    })
                    .map((user) => (
                      <UserRow
                        key={user.id}
                        user={user}
                        accessibleResources={
                          usersWithResources[String(user.id)]
                        }
                        handleRemoveUserFromGroup={handleRemoveUserFromGroup}
                      />
                    ))}
                </TableBody>
              </Table>
            </Grid>
          </TabPanel>
          <TabPanel value={selectedTab} index={1} style={{ width: '100%' }}>
            <Grid container>
              <Grid item xs={12} container justifyContent="center">
                <MuiTextField
                  label="Search"
                  variant="filled"
                  style={{ width: 600 }}
                  value={groupFilter}
                  onChange={(event) => setGroupFilter(event.target.value)}
                />
              </Grid>
              <Table aria-label="collapsible table">
                <TableHead>
                  <TableRow>
                    <TableCell />
                    <TableCell>
                      <b>Group</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Description</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Membership</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {groupsWUsers
                    .filter((oktaGroup) => {
                      if (groupFilter) {
                        return oktaGroup.name
                          .toLowerCase()
                          .includes(groupFilter.toLowerCase());
                      }
                      return true;
                    })
                    .map((oktaGroup) => (
                      <GroupRow
                        key={oktaGroup.name}
                        oktaGroup={oktaGroup}
                        accessibleResources={
                          groupsWithResources[String(oktaGroup.name)]
                        }
                      />
                    ))}
                </TableBody>
              </Table>
            </Grid>
          </TabPanel>
          <TabPanel value={selectedTab} index={2} style={{ width: '100%' }}>
            <Grid container>
              <Grid item xs={12} container justifyContent="center">
                <MuiTextField
                  label="Search"
                  variant="filled"
                  style={{ width: 600 }}
                  value={resourceFilter}
                  onChange={(event) => setResourceFilter(event.target.value)}
                />
              </Grid>
              <Table aria-label="collapsible table">
                <TableHead>
                  <TableRow>
                    <TableCell />
                    <TableCell>
                      <b>Resource</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Category</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Read Access</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Write Access</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {resourcesWithAccess
                    .filter((resource) => {
                      if (resourceFilter) {
                        return resource.name
                          .toLowerCase()
                          .includes(resourceFilter.toLowerCase());
                      }
                      return true;
                    })
                    .map((resource) => (
                      <ResourceRow
                        key={resource.name}
                        resource={resource}
                        usersWithReadAccess={
                          resourcesWithUsers[String(resource.name)].readAccess
                        }
                        usersWithWriteAccess={
                          resourcesWithUsers[String(resource.name)].readAccess
                        }
                      />
                    ))}
                </TableBody>
              </Table>
            </Grid>
          </TabPanel>
        </Grid>
      </CardContent>
    </Card>
  );
};
