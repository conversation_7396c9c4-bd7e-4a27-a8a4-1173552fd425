import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Voltage Phase';

export const BrVoltagePhaseEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <CustomNumberInput
              source="utilityRequiredConsumptionKWh"
              fullWidth
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrVoltagePhaseList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <NumberField
          source="utilityRequiredConsumptionKWh"
          label="Utility required consumption (kWh)"
        />
      </Datagrid>
    </List>
  );
};

export const BrVoltagePhaseCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <CustomNumberInput
            source="utilityRequiredConsumptionKWh"
            required
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
