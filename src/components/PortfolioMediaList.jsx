import React, { useState } from 'react';
import {
  Datagrid,
  FunctionField,
  List,
  TextField,
  usePermissions,
  useRedirect,
} from 'react-admin';
import {
  Avatar,
  Badge,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Typography,
} from '@mui/material';
import { Close, Edit, Image as ImageIcon } from '@mui/icons-material';
import { Image, Transformation } from 'cloudinary-react';
import Config from '../config/config';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Portfolio Media';

// Image zoom dialog for avatar images
const ImageZoomDialog = ({ open, onClose, imageUrl, title }) => {
  const getPublicId = (url) => {
    if (!url) return null;
    if (!url.includes('cloudinary.com')) return url;
    const match = url.match(/\/v\d+\/(.+?)(\.[^.]+)?$/);
    return match ? match[1] : url;
  };

  const publicId = getPublicId(imageUrl);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md">
      <DialogContent sx={{ position: 'relative', padding: 0 }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 1)',
            },
            zIndex: 1,
          }}
        >
          <Close />
        </IconButton>
        {publicId ? (
          <Image
            cloud_name={Config.cloud_name}
            publicId={publicId}
            style={{ width: '100%', height: 'auto', display: 'block' }}
          >
            <Transformation
              width="248"
              height="248"
              crop="fill"
              gravity="center"
            />
          </Image>
        ) : (
          <Box
            sx={{
              width: 248,
              height: 248,
              bgcolor: '#e0e0e0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            No Image
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

// Dialog component for displaying imageEvents
const ImageEventsDialog = ({
  open,
  onClose,
  imageEvents,
  portfolioName,
  portfolioId,
}) => {
  const redirect = useRedirect();

  const handleEditClick = () => {
    const filterParams = encodeURIComponent(
      JSON.stringify({ portfolio: { id: portfolioId } })
    );
    const displayedFilters = encodeURIComponent(
      JSON.stringify({ 'portfolio.id': true })
    );
    redirect(
      `/Event?displayedFilters=${displayedFilters}&filter=${filterParams}`
    );
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="h6">
              {portfolioName} - Image Events ({imageEvents?.length || 0})
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Edit />}
                onClick={handleEditClick}
                size="small"
              >
                Edit Image Events
              </Button>
              <IconButton onClick={onClose}>
                <Close />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          {imageEvents && imageEvents.length > 0 ? (
            imageEvents.map((event) => {
              // Extract Cloudinary public ID from URL
              const getPublicId = (url) => {
                if (!url) return null;
                if (!url.includes('cloudinary.com')) return url;
                const match = url.match(/\/v\d+\/(.+?)(\.[^.]+)?$/);
                return match ? match[1] : url;
              };

              const publicId = getPublicId(event.bannerImageCardUrl);

              return (
                <Grid item xs={12} sm={6} md={4} key={event.id}>
                  <Box
                    sx={{
                      position: 'relative',
                      width: '100%',
                      maxWidth: 319,
                      height: 440,
                      borderRadius: '25px',
                      overflow: 'hidden',
                      margin: '0 auto',
                    }}
                  >
                    {publicId ? (
                      <Image
                        cloud_name={Config.cloud_name}
                        publicId={publicId}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                      >
                        <Transformation
                          width="319"
                          height="440"
                          crop="fill"
                          gravity="center"
                        />
                      </Image>
                    ) : (
                      <Box
                        sx={{
                          width: '100%',
                          height: '100%',
                          bgcolor: '#e0e0e0',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h6" color="textSecondary">
                          No Image
                        </Typography>
                      </Box>
                    )}
                    {/* Title overlay in lower left */}
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        padding: '16px',
                        background:
                          'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
                      }}
                    >
                      <Typography
                        sx={{
                          color: 'white',
                          fontSize: '32px',
                          fontWeight: 'bold',
                          lineHeight: '1.2em',
                          letterSpacing: '0.01em',
                          textShadow: '2px 2px 4px rgba(0,0,0,0.4)',
                        }}
                      >
                        {event.title}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              );
            })
          ) : (
            <Grid item xs={12}>
              <Typography variant="body1" align="center">
                No image events found
              </Typography>
            </Grid>
          )}
        </Grid>
      </DialogContent>
    </Dialog>
  );
};

export const PortfolioMediaList = () => {
  const { permissions } = usePermissions();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPortfolio, setSelectedPortfolio] = useState(null);
  const [zoomDialogOpen, setZoomDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const handleImageEventsClick = (event, record) => {
    event.stopPropagation(); // Prevent row click
    setSelectedPortfolio(record);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedPortfolio(null);
  };

  const handleImageClick = (event, imageUrl, portfolioName) => {
    event.stopPropagation(); // Prevent row click navigation
    setSelectedImage({ url: imageUrl, title: portfolioName });
    setZoomDialogOpen(true);
  };

  const handleCloseZoom = () => {
    setZoomDialogOpen(false);
    setSelectedImage(null);
  };

  return (
    <>
      <List title={entityName} perPage={25} resource="PortfolioMedia">
        <Datagrid
          rowClick={
            getEditable('Portfolio', permissions)
              ? (id) => `/Portfolio/${id}/2`
              : (id) => `/Portfolio/${id}/show`
          }
        >
          <TextField source="id" />
          <TextField source="name" label="Portfolio Name" />

          <FunctionField
            label="Mobile Banner Image"
            sortable={false}
            render={(record) => {
              if (!record.mobileBannerImageUrl) {
                return (
                  <Avatar
                    sx={{
                      width: 62,
                      height: 62,
                      bgcolor: '#e0e0e0',
                      color: '#999',
                    }}
                  >
                    ?
                  </Avatar>
                );
              }

              // Extract Cloudinary public ID from URL if it's a full URL
              const getPublicId = (url) => {
                if (!url) return null;
                // If it's already a public ID, return it
                if (!url.includes('cloudinary.com')) return url;
                // Extract public ID from Cloudinary URL
                const match = url.match(/\/v\d+\/(.+?)(\.[^.]+)?$/);
                return match ? match[1] : url;
              };

              const publicId = getPublicId(record.mobileBannerImageUrl);

              return (
                <Box
                  onClick={(event) =>
                    handleImageClick(
                      event,
                      record.mobileBannerImageUrl,
                      record.name
                    )
                  }
                  sx={{
                    width: 62,
                    height: 62,
                    borderRadius: '50%',
                    overflow: 'hidden',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    '&:hover': {
                      opacity: 0.8,
                    },
                  }}
                >
                  <Image
                    cloud_name={Config.cloud_name}
                    publicId={publicId}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  >
                    <Transformation
                      width="124"
                      height="124"
                      crop="fill"
                      gravity="center"
                    />
                  </Image>
                </Box>
              );
            }}
          />

          <FunctionField
            label="Small Avatar Image"
            sortable={false}
            render={(record) => {
              if (!record.smallAvatarImageUrl) {
                return (
                  <Avatar
                    sx={{
                      width: 62,
                      height: 62,
                      bgcolor: '#e0e0e0',
                      color: '#999',
                    }}
                  >
                    ?
                  </Avatar>
                );
              }

              // Extract Cloudinary public ID from URL if it's a full URL
              const getPublicId = (url) => {
                if (!url) return null;
                // If it's already a public ID, return it
                if (!url.includes('cloudinary.com')) return url;
                // Extract public ID from Cloudinary URL
                const match = url.match(/\/v\d+\/(.+?)(\.[^.]+)?$/);
                return match ? match[1] : url;
              };

              const publicId = getPublicId(record.smallAvatarImageUrl);

              return (
                <Box
                  onClick={(event) =>
                    handleImageClick(
                      event,
                      record.smallAvatarImageUrl,
                      record.name
                    )
                  }
                  sx={{
                    width: 62,
                    height: 62,
                    borderRadius: '50%',
                    overflow: 'hidden',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    '&:hover': {
                      opacity: 0.8,
                    },
                  }}
                >
                  <Image
                    cloud_name={Config.cloud_name}
                    publicId={publicId}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  >
                    <Transformation
                      width="124"
                      height="124"
                      crop="fill"
                      gravity="center"
                    />
                  </Image>
                </Box>
              );
            }}
          />

          <FunctionField
            label="Image Events"
            sortable={false}
            render={(record) => {
              const count = record.imageEvents?.length || 0;
              return (
                <IconButton
                  onClick={(event) => handleImageEventsClick(event, record)}
                  disabled={count === 0}
                >
                  <Badge badgeContent={count} color="primary">
                    <ImageIcon />
                  </Badge>
                </IconButton>
              );
            }}
          />
        </Datagrid>
      </List>

      <ImageEventsDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        imageEvents={selectedPortfolio?.imageEvents}
        portfolioName={selectedPortfolio?.name}
        portfolioId={selectedPortfolio?.id}
      />

      <ImageZoomDialog
        open={zoomDialogOpen}
        onClose={handleCloseZoom}
        imageUrl={selectedImage?.url}
        title={selectedImage?.title}
      />
    </>
  );
};
