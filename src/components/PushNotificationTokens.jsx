import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Push Notification Token';

export const PushNotificationTokenEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="token" fullWidth required />
            <BooleanInput
              source="staleFlg"
              fullWidth
              helperText="If this flag is turned on, push notifications will not be sent using this token."
            />
            <TextInput
              source="device"
              fullWidth
              disabled
              helperText="Changing this will not change which device the notification is sent to. The only way to change that is by changing the token."
            />
            <TextInput
              source="application"
              fullWidth
              required
              disabled
              helperText="Changing this will not change which application the notification is sent to. The only way to change that is by changing the token."
            />
            <ReferenceInput
              perPage={10000}
              source="user.id"
              reference="UserLite"
              required
            >
              <AutocompleteInput
                optionText="label"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
                label="User"
                required
                fullWidth
                disabled
                helperText="Changing this will not change which device or person the notification is sent to. The only way to change that is by changing the token."
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const PushNotificationTokenList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="token" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="device" />
        <TextField source="application" />
        <BooleanField source="staleFlg" />
      </Datagrid>
    </List>
  );
};

export const PushNotificationTokenCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="token"
            required
            fullWidth
            helperText="Ex: 'ExponentPushToken[...]'"
          />
          <TextInput
            source="device"
            required
            fullWidth
            helperText="A description of the device. Ex: Apple iPhone 14"
          />
          <TextInput
            source="application"
            required
            fullWidth
            helperText="The name of the application this token will send to"
          />
          <ReferenceInput
            perPage={10000}
            source="user.id"
            reference="UserLite"
            required
          >
            <AutocompleteInput
              allowEmpty={true}
              optionText="label"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
              label="User"
              required
              fullWidth
              helperText="The user this token will send notifications to"
            />
          </ReferenceInput>
          <BooleanInput
            source="staleFlg"
            fullWidth
            helperText="If enabled, push notifications won't be sent to this token."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
