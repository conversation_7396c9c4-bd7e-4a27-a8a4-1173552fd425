import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';
import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'SubAccount Type';

export const SubAccountTypeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth step={1} />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const SubAccountTypeList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <NumberField source="orderNo" />
      </Datagrid>
    </List>
  );
};

export const SubAccountTypeCreate = () => (
  <Create
    title={`Create ${entityName}`}
    helperText="This can be edited at any time so no need to be perfect."
  >
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
