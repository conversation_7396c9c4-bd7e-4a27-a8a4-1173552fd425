import React, { Component } from 'react';
import { withStyles } from '@mui/styles';
import { useTheme, useMediaQuery } from '@mui/material';
import mapboxgl from '!mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

mapboxgl.accessToken = process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;

const styles = (theme) => ({
  mapContainer: {
    minHeight: '600px',
    width: '100%',
    position: 'relative',
  },
});

const getHeight = (height) => {
  if (!height) return 400;
  if (typeof height === 'function') {
    return height();
  }
  return height;
};

const getFocusPoint = (markers, fullScreen) => {
  let latMax;
  let latMin;
  let longMax;
  let longMin;
  markers.forEach((marker) => {
    const { latitude, longitude } = marker;
    if (latitude === null || longitude === null) return;
    if (latitude >= latMax || typeof latMax === 'undefined') latMax = latitude;
    if (latitude <= latMin || typeof latMin === 'undefined') latMin = latitude;
    if (longitude >= longMax || typeof longMin === 'undefined')
      longMax = longitude;
    if (longitude <= longMin || typeof longMin === 'undefined')
      longMin = longitude;
  });
  const longDiff = Math.abs(longMax - longMin);
  const latDiff = Math.abs(longMax - longMin);
  const maxDiff = longDiff > latDiff ? longDiff : latDiff;
  let zoom;
  if (maxDiff > 100) {
    zoom = 1.25;
  } else if (maxDiff > 50) {
    zoom = 1.5;
  } else if (maxDiff > 24) {
    zoom = 2.5;
  } else if (maxDiff > 6) {
    zoom = 3;
  } else if (maxDiff > 2) {
    zoom = 4;
  } else if (maxDiff > 0.75) {
    zoom = 5;
  } else if (maxDiff > 0.2) {
    zoom = 6;
  } else if (maxDiff > 0.15) {
    zoom = 6;
  } else if (maxDiff > 0) {
    zoom = 8;
  } else {
    zoom = 10;
  }
  if (fullScreen) {
    zoom *= 0.6;
  }
  zoom = Math.max(zoom, 1);
  return {
    latAvg: (latMax + latMin) / 2,
    longAvg: (longMax + longMin) / 2,
    zoom,
  };
};

const mapboxStyles = {
  energea: 'mapbox://styles/greinhard/clddcqhzd000f01laziilntp9',
  street: 'mapbox://styles/mapbox/streets-v11',
  outdoors: 'mapbox://styles/mapbox/outdoors-v11',
  light: 'mapbox://styles/mapbox/light-v10',
  dark: 'mapbox://styles/mapbox/dark-v10',
  satellite: 'mapbox://styles/mapbox/satellite-v9',
  satelliteStreets: 'mapbox://styles/mapbox/satellite-streets-v11',
  navigationDay: 'mapbox://styles/mapbox/navigation-day-v1',
  navigationNight: 'mapbox://styles/mapbox/navigation-night-v1',
};

const mapboxProjections = {
  mercator: 'mercator',
  equalEarth: 'equalEarth',
  globe: 'globe',
};

class Map extends Component {
  constructor(props) {
    super(props);
    const focusPoint = getFocusPoint(props.markers, props.fullScreen);
    this.state = {
      longitude: props.longitude || focusPoint.longAvg || 0,
      latitude: props.latitude || focusPoint.latAvg || 0,
      zoom: props.zoom || focusPoint.zoom,
      width: props.width || 400,
      height: getHeight(props.height) || 400,
      portfolioId: props.portfolioId,
    };

    this.map = null;
    this.mapContainer = React.createRef();
    this.currentMarkers = [];

    this.onViewportChange = this.onViewportChange.bind(this);
    this.resize = this.resize.bind(this);
  }

  addMarkersToMap(markers) {
    markers.forEach((marker, index) => {
      // To add custom icons to pins see https://docs.mapbox.com/mapbox-gl-js/example/custom-marker-icons/
      // One way is to change 'div' to 'svg' and className to the fontawesome icon class.
      // Might be helpful to create our own if we want something like a lightnight bolt on the head of the pin
      const div = document.createElement('span');
      div.key = `marker-${index}`;
      div.innerHTML = '<span>&#x2022;</span>'; //'<i class="fas fa-circle"/>';
      div.style.color = marker.color;
      // div.style.filter = `saturate(${marker.saturation})`;
      div.style.cursor = 'pointer';
      div.style.fontSize = marker.sizePx;

      const newMarker = new mapboxgl.Marker({
        element: div,
      })
        .setLngLat([marker.longitude, marker.latitude])
        .addTo(this.map);
      // NOTE: If we add this instead of pin, remove when switch portfolio
      // const popUp = new mapboxgl.Popup({
      // 	// closeButton: false,
      // 	// closeOnClick: false
      // })
      // 	.setLngLat([marker.longitude, marker.latitude])
      // 	.setHTML(
      // 		'<strong>Salinas</strong>',
      // 	)
      // 	.addTo(this.map);
      const el = newMarker.getElement();
      el.addEventListener('click', (event) => {
        marker.onMarkerClick(event);
      });
      // el.addEventListener('mouseenter', (event) => {
      //   marker.onMarkerClick(event);
      // });
      // el.addEventListener('mouseleave', (event) => {
      //   marker.onMarkerClose();
      // });
      this.currentMarkers.push(newMarker);
    });
  }

  componentDidMount() {
    const { markers } = this.props;
    const { longitude, latitude, zoom } = this.state;

    this.map = new mapboxgl.Map({
      container: this.mapContainer.current,
      style: mapboxStyles.light,
      center: [longitude, latitude],
      zoom: zoom,
    });

    // Add navigation control (zoom buttons)
    this.map.addControl(new mapboxgl.NavigationControl(), 'top-right');

    this.addMarkersToMap(markers);

    this.map.on('move', () => {
      this.setState({
        longitude: this.map.getCenter().lng.toFixed(4),
        latitude: this.map.getCenter().lat.toFixed(4),
        zoom: this.map.getZoom().toFixed(2),
      });
    });

    // Trigger resize after a slight delay to ensure container dimensions are set
    setTimeout(() => {
      if (this.map) {
        this.map.resize();
      }
    }, 0);

    // Handle browser resize events
    window.addEventListener('resize', this._resize);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.resize);
  }

  resize() {
    if (this.map) {
      this.map.resize();
    }
  }

  onViewportChange(viewport) {
    if (viewport.zoom < 1 || viewport.zoom > 20) return;
    this.setState(viewport);
  }

  render() {
    const { classes } = this.props;
    return (
      <div>
        <div ref={this.mapContainer} className={classes.mapContainer} />
      </div>
    );
  }
}

function withMobileDialog(WrappedComponent) {
  return (props) => (
    <WrappedComponent
      {...props}
      fullScreen={useMediaQuery(useTheme().breakpoints.down('sm'))}
    />
  );
}

export default withStyles(styles)(withMobileDialog(Map));
