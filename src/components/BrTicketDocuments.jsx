import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON>reate,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Ticket Document';

export const BrTicketDocumentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="brTicket.id"
              reference="BrTicket"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput optionText="title" label="Ticket" fullWidth />
            </ReferenceInput>
            <TextInput source="awsObjectKey" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrTicketDocumentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrTicket"
          linkSource="brTicket.id"
          labelSource="brTicket.title"
          label="Ticket"
        />
        <TextField source="awsObjectKey" />
      </Datagrid>
    </List>
  );
};

export const BrTicketDocumentCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="brTicket.id"
            reference="BrTicket"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="title"
              label="Ticket"
              fullWidth
              allowEmpty
            />
          </ReferenceInput>
          <TextInput source="awsObjectKey" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
