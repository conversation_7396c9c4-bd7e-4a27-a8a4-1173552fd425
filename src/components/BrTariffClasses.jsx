import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Alert, Grid, Typography } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Tariff Class';

export const BrTariffClassEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <CustomNumberInput source="utilityQuota" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrTariffClassList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <Grid container style={{ marginTop: '1rem' }}>
        <Alert severity="info">
          <Typography variant="body2">
            Note: B1/B3 tariff class is a group that was used in Salesforce
            however we want to properly reassign the consumer units in this
            tariff class to what they actually are in case B1 and B3 ever aren't
            handled the same as each other.
          </Typography>
        </Alert>
      </Grid>
      <List title={entityName} perPage={25}>
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="name" />
          <NumberField source="utilityQuota" />
        </Datagrid>
      </List>
    </>
  );
};

export const BrTariffClassCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <CustomNumberInput source="utilityQuota" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
