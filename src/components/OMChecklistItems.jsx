import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON>reate,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Alert, Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Checklist Item';

export const OMChecklistItemEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container item xs={12} md={6}>
          <Alert severity="info">
            A checklist item is meant to be assigned to as many PMP's as are
            applicable. Editing anything on this checklist item will update it
            for all PMP's that this checklist item is assigned to but it will
            not update any tickets that have already been created.
            <br />
            <br />
            Um item da checklist deve ser atribuído a todos os PMPs aos quais
            for aplicável. Editar qualquer parte desse item da checklist irá
            atualizá-lo para todos os PMPs aos quais ele estiver atribuído, mas
            não atualizará os tickets que já tiverem sido criados.
          </Alert>
        </Grid>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth required />
            <TextInput source="code" fullWidth required />
            <TextInput
              source="defaultTicketDescription"
              fullWidth
              multiline
              helperText="This will be the initial description for automatically created tickets related to this checklist item"
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const OMChecklistItemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="code" />
        <TextField source="name" />
        <TextField source="defaultTicketDescription" />
      </Datagrid>
    </List>
  );
};

export const OMChecklistItemCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth required />
          <TextInput source="code" fullWidth required />
          <TextInput
            source="defaultTicketDescription"
            fullWidth
            multiline
            helperText="This will be the initial description for automatically created tickets related to this checklist item"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
