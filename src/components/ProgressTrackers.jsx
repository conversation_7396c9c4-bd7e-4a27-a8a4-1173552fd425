import React from 'react';
import { withStyles } from '@mui/styles';
import { Typography, Box } from '@mui/material';

const styles = (theme) => ({
  progressBarContainer: {
    width: '100%',
    backgroundColor: '#e0e0e0',
    borderRadius: '20px',
    overflow: 'hidden',
    position: 'relative',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    height: '40px',
  },
  progressFill: {
    backgroundColor: theme.palette.green.main,
    height: '100%',
    borderRadius: '20px 0 0 20px',
    transition: 'width 2s ease-out',
  },
  progressText: {
    position: 'absolute',
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%)',
    color: theme.palette.primary.contrastText,
    fontWeight: 'bold',
  },
});

const ProgressTracker = ({ classes, destination, current }) => {
  const progress = Math.min((current / destination) * 100, 100);

  return (
    <Box sx={{ width: '100%', padding: '20px' }}>
      <Box className={classes.progressBarContainer}>
        <Box
          className={classes.progressFill}
          style={{ width: `${progress}%` }}
        />
        <Typography className={classes.progressText} variant="body1">
          <b>
            ${current.toLocaleString()} / ${destination.toLocaleString()}
          </b>
        </Typography>
      </Box>
    </Box>
  );
};

export default withStyles(styles)(ProgressTracker);
