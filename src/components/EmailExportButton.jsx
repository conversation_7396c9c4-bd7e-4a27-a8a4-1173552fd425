import React, { useState } from 'react';
import { Button, useDataProvider, useNotify } from 'react-admin';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import { GetApp } from '@mui/icons-material';
import MuiTextField from '@mui/material/TextField';
import MuiButton from '@mui/material/Button';

export const EmailExportButton = (props) => {
  const [exportEmail, setExportEmail] = useState(null);
  const [emailExportOpen, setEmailExportOpen] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  const handleEmailExport = () => {
    const { resource, filterValues } = props;
    dataProvider
      .getList(resource, {
        sort: { field: 'id', order: 'DESC' },
        filter: {
          ...filterValues,
        },
        exportConfig: {
          emailResults: true,
          email: exportEmail,
        },
      })
      .then(
        (res) => {
          setEmailExportOpen(false);
          notify('You will receive an email shortly', { type: 'success' });
        },
        (err) => {
          console.error(err);
          notify('There was an error exporting data', { type: 'error' });
        }
      );
  };

  return (
    <>
      <Button
        label="Email Export"
        onClick={() => {
          setEmailExportOpen(true);
        }}
      >
        <GetApp />
      </Button>
      <Dialog open={emailExportOpen} fullWidth>
        <DialogTitle>Email Export</DialogTitle>
        <DialogContent>
          <MuiTextField
            label="Email"
            value={exportEmail || ''}
            onChange={(event) => {
              setExportEmail(event.target.value);
            }}
            fullWidth
            required
          />
        </DialogContent>
        <DialogActions>
          <MuiButton onClick={() => setEmailExportOpen(false)} color="primary">
            Cancel
          </MuiButton>
          <MuiButton
            onClick={handleEmailExport}
            color="primary"
            variant="contained"
            disabled={!exportEmail}
          >
            Export
          </MuiButton>
        </DialogActions>
      </Dialog>
    </>
  );
};
