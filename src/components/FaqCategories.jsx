import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  ArrayField,
  NumberField,
  NumberInput,
  SingleFieldList,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import {
  CustomNumberInput,
  CustomReferenceField,
  DetailField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Faq Category';
export const FaqCategoryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth />
            <TextInput source="description" multiline fullWidth />
          </Grid>
        </Grid>
        <ArrayField sortable={false} source="entries">
          <SingleFieldList>
            <CustomReferenceField source="question" />
          </SingleFieldList>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const FaqCategoryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <NumberField source="orderNo" />
        <ArrayField source="entries" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="question" />
          </SingleFieldList>
        </ArrayField>
        <DetailField source="description" sortable={false} />
      </Datagrid>
    </List>
  );
};

export const FaqCategoryCreate = () => (
  <Create
    title={`Create ${entityName}`}
    helperText="This can be edited at any time so no need to be perfect."
  >
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
