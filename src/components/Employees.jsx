import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Link } from 'react-router-dom';
import {
  BooleanInput,
  BooleanField,
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  ImageField,
  NumberField,
  Pagination,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useListContext,
  useNotify,
  useRefresh,
  usePermissions,
  Filter,
  ArrayField,
  SingleFieldList,
  ReferenceInput,
  SelectInput,
  ReferenceArrayInput,
  SelectArrayInput,
  AutocompleteInput,
  TopToolbar,
  CreateButton,
  ExportButton,
  useResourceDefinition,
  DateInput,
  DateField,
  TabbedForm,
  FormTab,
} from 'react-admin';

import {
  Button,
  Avatar,
  FormHelperText,
  Grid,
  CardContent,
  Card,
  Typography,
  List as MuiList,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Alert,
  Dialog,
} from '@mui/material';
import {
  AddCircle,
  CloudDownload,
  Edit as EditIcon,
} from '@mui/icons-material';
import moment from 'moment';

import Config from '../config/config';
import { getEditable } from '../utils/applyRoleAuth';
import { openUploadWidget } from '../utils/CloudinaryService';
import { CustomReferenceField, LinkField } from './CustomFields';
import { EmploymentAgreementCreate } from './EmploymentAgreements';
import { EmployeeShareAwardCreate } from './EmployeeShareAwards';

const entityName = 'Employee';

const styleRow = (record, index) => {
  const { investment, transfer, date } = record;
  const errorStyle = {
    backgroundColor: 'rgba(255,0,0,.3)',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (record.terminationDt) {
    if (record.hasActiveZoomFlg) {
      return errorStyle;
    } else if (record.user?.hasCMSAccess) {
      return errorStyle;
    } else if (record.hasActiveSlackFlg) {
      return errorStyle;
    } else if (record.whatsAppFullTeamAccessFlg) {
      return errorStyle;
    } else if (record.hasDropboxAccessFlg) {
      return errorStyle;
    }
  } else {
    if (!record.user) {
      return errorStyle;
    } else if (!record.user?.hasCMSAccess) {
      return errorStyle;
    } else if (!record.hasActiveZoomFlg) {
      return errorStyle;
    } else if (!record.hasDropboxAccessFlg) {
      return warningStyle;
    } else if (!record.hasActiveSlackFlg) {
      return warningStyle;
    } else if (!record.whatsAppFullTeamAccessFlg) {
      return warningStyle;
    } else if (!record.user?.hasAuth0Account) {
      return warningStyle;
    }
  }
  return {};
};

export const EmployeeEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();
  const { permissions } = usePermissions();
  const [employmentAgreementDialogOpen, setEmploymentAgreementDialogOpen] =
    useState(false);
  const [employeeShareAwardDialogOpen, setEmployeeShareAwardDialogOpen] =
    useState(false);

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  const onPhotosUploaded = (aPhotos) => {
    const photoUrl = aPhotos[0].url;
    dataProvider
      .update('Employee', {
        data: { id: parseInt(id, 10), imgUrl: photoUrl },
      })
      .then(
        (record) => {
          notify('Image successfully uploaded');
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error uploading image.', { type: 'error' });
        }
      );
  };

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['projects'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.employee_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };
  return (
    <>
      <Edit title={`${entityName} #${id}`} undoable={false}>
        <TabbedForm>
          <FormTab label="Profile">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <TextInput
                  source="firstName"
                  fullWidth
                  helperText="The first name of the employee that will be displayed on the About Us page"
                />
                <TextInput
                  source="lastName"
                  fullWidth
                  helperText="The last name of the employee that will be displayed on the About Us page"
                />
                <TextInput source="email" fullWidth />
                <TextInput source="jobTitle" fullWidth />
                <DateInput
                  source="startDt"
                  fullWidth
                  label="Employment start dt"
                />
                <DateInput
                  source="terminationDt"
                  fullWidth
                  helperText="Only applicable for employees who no longer work for Energea"
                />
                <ReferenceArrayInput
                  source="employeeTypeIds"
                  reference="EmployeeType"
                  fullWidth
                >
                  <SelectArrayInput
                    optionText="name"
                    fullWidth
                    helperText={
                      <Typography variant="caption">
                        To add a new employee type,{' '}
                        <a href="/EmployeeType/create">click here.</a> Currently
                        this is used to filter employee lists in certain
                        scenarios
                      </Typography>
                    }
                  />
                </ReferenceArrayInput>
                <ReferenceInput
                  source="omTruck.id"
                  reference="OMTruck"
                  perPage={10000}
                  sort={{ field: 'id', order: 'ASC' }}
                >
                  <SelectInput
                    optionText="name"
                    label="O&M Truck"
                    fullWidth
                    allowEmpty
                    helperText="This is used to link the employee to an O&M truck and only applies to O&M employees."
                  />
                </ReferenceInput>
                {isIT ? (
                  <ReferenceInput
                    perPage={10000}
                    source="user.id"
                    reference="UserLite"
                  >
                    <AutocompleteInput
                      allowEmpty={true}
                      optionText="label"
                      shouldRenderSuggestions={(value) =>
                        value.trim().length > 0
                      }
                      label="User"
                      required
                      fullWidth
                      helperText="This is used to link the employee to their Okta user."
                    />
                  </ReferenceInput>
                ) : null}
                <Card style={{ backgroundColor: '#fafafa' }}>
                  <CardContent>
                    <Typography variant="h6">Employee Head-shot</Typography>
                    <ImageField
                      label="Main Image"
                      source="imgUrl"
                      title="title"
                      sortable={false}
                    />
                    {isIT ? (
                      <TextInput
                        source="imgUrl"
                        label="Cloudinary img url"
                        fullWidth
                        helperText="For IT use only. Please don't edit."
                      />
                    ) : null}
                    <div className="actions">
                      <Button
                        variant="outlined"
                        onClick={uploadImageWithCloudinary}
                      >
                        Add / replace photo
                      </Button>
                      <FormHelperText>
                        Images should be square, black and white, and black
                        background. Resolution should be around 720px X 720px.
                      </FormHelperText>
                    </div>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Access">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <BooleanInput
                  source="hasDropboxAccessFlg"
                  label="Has Dropbox Access"
                  defaultValue={false}
                  helperText="When on, the employee has been granted access to Dropbox"
                />
                <BooleanInput
                  source="whatsAppFullTeamAccessFlg"
                  label="Has WhatsApp Full Team Access"
                  defaultValue={false}
                  helperText="When on, the employee has been added to the Full Team WhatsApp group"
                />
                {isIT ? (
                  <TextInput
                    source="slackId"
                    label="Slack ID"
                    helperText="This is the unique identifier of the slack user. Within Slack, click a user's profile picture within a chat to open their profile on the right, click the three dots, and then click 'Copy Member ID'. Ex: U01B0RQR8TT"
                    fullWidth
                  />
                ) : null}
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Documents">
            <Grid container style={{ width: '100%' }} spacing={2}>
              <Grid item xs={12}>
                <Grid
                  container
                  justifyContent="space-between"
                  alignItems="center"
                  style={{ width: '100%' }}
                >
                  <Grid item>
                    <Typography variant="h6">
                      Employment Agreement(s)
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddCircle />}
                      onClick={() => {
                        setEmploymentAgreementDialogOpen(true);
                      }}
                      style={{ textTransform: 'none' }}
                    >
                      Upload
                    </Button>
                  </Grid>
                </Grid>
                <FunctionField
                  render={(record) => {
                    if (record.employmentAgreements?.length > 0) {
                      return (
                        <MuiList dense>
                          {record.employmentAgreements.map(
                            (agreement, index) => (
                              <ListItem
                                key={agreement.id}
                                divider={
                                  index < record.employmentAgreements.length - 1
                                }
                              >
                                <ListItemText
                                  primary={`Employment Agreement (${moment(
                                    agreement.startDt
                                  ).format('MMM D, YYYY')} - ${
                                    agreement.endDt
                                      ? moment(agreement.endDt).format(
                                          'MMM D, YYYY'
                                        )
                                      : 'Current'
                                  })`}
                                />
                                <ListItemSecondaryAction>
                                  <IconButton
                                    onClick={() => {
                                      window.location.assign(
                                        agreement.agreementDownloadUrl
                                      );
                                    }}
                                    disabled={!agreement.agreementDownloadUrl}
                                  >
                                    <CloudDownload />
                                  </IconButton>
                                  <IconButton
                                    component={Link}
                                    to={`/EmploymentAgreement/${agreement.id}`}
                                  >
                                    <EditIcon />
                                  </IconButton>
                                </ListItemSecondaryAction>
                              </ListItem>
                            )
                          )}
                        </MuiList>
                      );
                    }
                    return (
                      <Alert severity="info">
                        No employment agreements have been uploaded yet.
                      </Alert>
                    );
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <Grid
                  container
                  justifyContent="space-between"
                  alignItems="center"
                  style={{ width: '100%' }}
                >
                  <Grid item>
                    <Typography variant="h6">
                      Employee Share Award(s)
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddCircle />}
                      onClick={() => {
                        setEmployeeShareAwardDialogOpen(true);
                      }}
                      style={{ textTransform: 'none' }}
                    >
                      Upload
                    </Button>
                  </Grid>
                </Grid>
                <FunctionField
                  render={(record) => {
                    if (record.employeeShareAwards?.length > 0) {
                      return (
                        <MuiList dense>
                          {record.employeeShareAwards.map((award, index) => (
                            <ListItem
                              key={award.id}
                              divider={
                                index < record.employeeShareAwards.length - 1
                              }
                            >
                              <ListItemText
                                primary={`Employee Share Award (${moment(
                                  award.awardedDt
                                ).format('MMM D, YYYY')}) (${
                                  award.shares
                                } shares)`}
                              />
                              <ListItemSecondaryAction>
                                <IconButton
                                  onClick={() => {
                                    window.location.assign(
                                      award.awardDownloadUrl
                                    );
                                  }}
                                  disabled={!award.awardDownloadUrl}
                                >
                                  <CloudDownload />
                                </IconButton>
                                <IconButton
                                  component={Link}
                                  to={`/EmployeeShareAward/${award.id}`}
                                >
                                  <EditIcon />
                                </IconButton>
                              </ListItemSecondaryAction>
                            </ListItem>
                          ))}
                        </MuiList>
                      );
                    }
                    return (
                      <Alert severity="info">
                        No employee share awards have been uploaded yet.
                      </Alert>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
        </TabbedForm>
      </Edit>
      <Dialog
        open={employmentAgreementDialogOpen}
        onClose={() => setEmploymentAgreementDialogOpen(false)}
      >
        <EmploymentAgreementCreate
          withinDialog={true}
          employeeId={id}
          onSuccess={() => {
            setEmploymentAgreementDialogOpen(false);
            refresh();
          }}
        />
      </Dialog>
      <Dialog
        open={employeeShareAwardDialogOpen}
        onClose={() => setEmployeeShareAwardDialogOpen(false)}
      >
        <EmployeeShareAwardCreate
          withinDialog={true}
          employeeId={id}
          onSuccess={() => {
            setEmployeeShareAwardDialogOpen(false);
            refresh();
          }}
        />
      </Dialog>
    </>
  );
};
const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);
const EmployeeFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <BooleanInput
      label="Inactive Employees"
      source="inactiveFlg"
      defaultValue={false}
      alwaysOn
    />
    <BooleanInput
      label="Has Dropbox"
      source="hasDropboxAccessFlg"
      defaultValue={null}
    />
    <BooleanInput
      label="Has Slack"
      source="hasActiveSlackFlg"
      defaultValue={null}
    />
    <BooleanInput
      label="Has Zoom"
      source="hasActiveZoomFlg"
      defaultValue={null}
    />
    {/* <BooleanInput label="Has CMS Access" source="hasCMSAccess" /> TODO: not set up yet on energea-app */}
    <BooleanInput
      label="Has Whatsapp"
      source="whatsAppFullTeamAccessFlg"
      defaultValue={null}
    />
  </Filter>
);

export const EmployeeList = () => {
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();
  const refresh = useRefresh();

  const isIT = permissions?.roles?.map((el) => el.name)?.indexOf('ITRead') > -1;
  const isHR =
    permissions?.roles?.map((el) => el.name)?.indexOf('HumanResourcesRead') >
    -1;

  const handlePhotoReminderEmail = (record) => {
    return (event) => {
      event.preventDefault();
      event.stopPropagation();
      dataProvider
        .update('Employee', {
          data: {
            id: record.id,
            sendPhotoReminderEmail: true,
          },
        })
        .then(
          () => {
            notify('Employee has been emailed a reminder.');
          },
          () => notify(`Error sending employee reminder`, { type: 'error' })
        );
    };
  };

  const handleSyncZoomAccountIds = () => {
    return (event) => {
      event.preventDefault();
      event.stopPropagation();
      dataProvider.update('SyncZoomAccountIds', {}).then(
        (res) => {
          notify('Zoom account ids have been synced.');
          refresh();
        },
        (error) => {
          notify(
            `Error syncing Zoom account ids. If this error persists, contact <EMAIL>`,
            { type: 'error' }
          );
          console.error(`Error syncing Zoom account ids, ${error}`);
        }
      );
    };
  };

  const handleSyncSlackAccountIds = () => {
    return (event) => {
      event.preventDefault();
      event.stopPropagation();
      dataProvider.update('SyncSlackAccountIds', {}).then(
        (res) => {
          notify('Slack account ids have been synced.');
          refresh();
        },
        (error) => {
          notify(
            `Error syncing Slack account ids. If this error persists, contact <EMAIL>`,
            { type: 'error' }
          );
          console.error(`Error syncing Slack account ids, ${error}`);
        }
      );
    };
  };

  const ListActions = (props) => {
    const { resource, displayedFilters, filterValues, showFilter } =
      useListContext();
    return (
      <TopToolbar>
        {props.filters &&
          React.cloneElement(props.filters, {
            resource,
            showFilter,
            displayedFilters,
            filterValues,
            context: 'button',
          })}
        <CreateButton />
        <ExportButton maxResults={100000} />
        <Button
          color="secondary"
          variant="contained"
          onClick={handleSyncZoomAccountIds()}
        >
          Sync Zoom
        </Button>
        <Button
          color="secondary"
          variant="contained"
          onClick={handleSyncSlackAccountIds()}
        >
          Sync Slack
        </Button>
      </TopToolbar>
    );
  };

  return (
    <List
      actions={<ListActions />}
      title={entityName}
      perPage={50}
      pagination={<CustomPagination />}
      sort={{ field: 'orderNo', order: 'ASC' }}
      filters={<EmployeeFilter />}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
        bulkActionButtons={false}
      >
        <FunctionField
          label="Main Image"
          sortable={false}
          render={(record) => {
            if (!record.imgUrl) {
              return (
                <Button
                  color="secondary"
                  variant="contained"
                  onClick={handlePhotoReminderEmail(record)}
                  disabled={record.imgUrl}
                >
                  Send HeadShot Reminder Email
                </Button>
              );
            }
            return (
              <Avatar
                style={{ width: '4em', height: '4em' }}
                alt={`${record.firstName} ${record.lastName}`}
                src={record.imgUrl}
              />
            );
          }}
        />
        <TextField source="firstName" />
        <TextField source="lastName" />
        <TextField source="email" />
        <TextField source="jobTitle" />
        {isHR ? <NumberField source="currentSalary" sortable={false} /> : null}
        {isHR ? (
          <NumberField source="awardedEnergeaGlobalShares" sortable={false} />
        ) : null}
        {isHR ? (
          <NumberField
            source="estimatedSharesEarnedSinceLastShareAward"
            sortable={false}
          />
        ) : null}
        <DateField source="startDt" label="Employment start dt" />
        <DateField source="terminationDt" />
        <BooleanField
          sortable={false}
          source="user.hasCMSAccess"
          label="Has CMS Access"
        />
        <BooleanField
          sortable={false}
          source="user.hasAuth0Account"
          label="Has Auth0 Account"
        />
        <BooleanField
          sortable={false}
          source="hasDropboxAccessFlg"
          label="Has Dropbox Access"
        />
        <BooleanField
          sortable={false}
          source="hasActiveSlackFlg"
          label="Has Active Slack Account"
        />
        <BooleanField
          sortable={false}
          source="hasActiveZoomFlg"
          label="Has Active Zoom Account"
        />
        <BooleanField
          sortable={false}
          source="whatsAppFullTeamAccessFlg"
          label="Has WhatsApp Full Team Access"
        />
        {isIT ? (
          <LinkField
            reference="User"
            linkSource="user.id"
            labelSource="user.fullName"
            label="User"
          />
        ) : null}
        <ArrayField source="employeeTypes">
          <SingleFieldList>
            <CustomReferenceField source="name" />
          </SingleFieldList>
        </ArrayField>
        <NumberField source="orderNo" />
      </Datagrid>
    </List>
  );
};

export const EmployeeCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <Alert severity="info">
      If you need to create a new Employee with access to the CMS, create them
      on the{' '}
      <Link to={`/User/create`}>
        <u>User Create Page</u>
      </Link>
      .
    </Alert>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="firstName"
            fullWidth
            helperText="The first name of the employee that will be displayed on the About Us page"
          />
          <TextInput
            source="lastName"
            fullWidth
            helperText="The last name of the employee that will be displayed on the About Us page"
          />
          <TextInput source="email" fullWidth />
          <TextInput source="jobTitle" required fullWidth />
          <DateInput source="startDt" fullWidth label="Employment start dt" />
          <TextInput
            source="imgUrl"
            label="Cloudinary img url"
            fullWidth
            helperText="For IT use only. Leave blank."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
