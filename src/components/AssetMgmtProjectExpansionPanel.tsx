import React, { useState, useEffect } from 'react';
import moment from 'moment-timezone';
import { Link } from 'react-router-dom';
// import 'chart.js/auto';

import { Bar, Line } from 'react-chartjs-2';
import 'chartjs-adapter-moment';
import xl from 'excel4node';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Avatar,
  Badge,
  Button,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  Icon,
  IconButton,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  Skeleton,
  Switch,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tabs,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from '@mui/material';

import {
  // Block,
  Check,
  Edit,
  GetApp,
  TrendingFlat,
  OpenInNew,
  // <PERSON>Arrow,
  PictureAsPdf,
  Settings,
  VisibilityOffOutlined,
  VisibilityOutlined,
  AddCircle,
  Delete,
  Refresh,
  ExpandMore,
} from '@mui/icons-material';
import { Alert } from '@mui/lab';
import { useDataProvider, useNotify, usePermissions } from 'react-admin';
import numeral from 'numeral';
import annotationPlugin from 'chartjs-plugin-annotation';
import { Chart } from 'chart.js';
// import { Image, Transformation } from 'cloudinary-react';
import { withStyles } from '@mui/styles';

import { interpolateColors, getGradient } from '../utils/global';
import LegendItem from './LegendItem';
import Daylight from './Daylight';
// import Config from '../config/config';
import theme from '../theme';
import { OMTicketDialog } from './OMTicketDialog';
import { roleMatrix } from '../utils/applyRoleAuth';
import { downloadWorkbook, writeToCell } from '../utils/excel';

const styles = () => ({
  // animation: `${pulseAnimation} 2s infinite`,
});

const CloudSummary = (props: any) => {
  const { cloudCoverage } = props;
  return (
    <Grid item>
      <Grid
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
      >
        <Grid item>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
            style={{ height: '108px' }}
          >
            <Grid item>
              <i
                style={{ height: '3em', width: '3em' }}
                className="fa-duotone fa-cloud"
              ></i>
            </Grid>
          </Grid>
        </Grid>
        <Grid item>
          <Typography variant="h5" style={{ color: '#666' }}>
            {cloudCoverage}%
          </Typography>
        </Grid>
        <Grid item>
          <Typography variant="body2">cloud coverage</Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};

const RainSummary = (props: any) => {
  const { lastHour, measurementSystem } = props;
  const getLastHour = () => {
    if (measurementSystem === 'metric') {
      return <span>{Math.round(lastHour * 254) / 100} cm.</span>;
    }
    return <span>{lastHour} in.</span>;
  };
  return (
    <Grid item>
      <Grid
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
      >
        <Grid item>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
            style={{ height: '108px' }}
          >
            <Grid item>
              <i
                style={{ height: '3em', width: '3em' }}
                className="fa-duotone fa-raindrops"
              ></i>
            </Grid>
          </Grid>
        </Grid>
        <Grid item>
          <Typography variant="h5" style={{ color: '#666' }}>
            {getLastHour()}
          </Typography>
        </Grid>
        <Grid item>
          <Typography variant="body2">rain in last hour</Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};
const WeatherSummary = (props: any) => {
  const { temp, description, icon, measurementSystem } = props;
  const getTemp = (temp: number) => {
    if (measurementSystem === 'metric') {
      return <span>{Math.round((temp - 32) * (5 / 9))}&deg;C</span>;
    }
    return <span>{Math.round(temp)}&deg;F</span>;
  };
  return (
    <Grid item>
      <Grid
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
      >
        <Grid item>
          <img
            alt="description"
            src={`http://openweathermap.org/img/wn/${icon}@2x.png`}
          />
        </Grid>
        <Grid item>
          <Typography style={{ color: '#666' }} variant="h5">
            {getTemp(temp)}
          </Typography>
        </Grid>
        <Grid item>
          <Typography variant="body2">{description}</Typography>
        </Grid>
        <Grid item></Grid>
      </Grid>
    </Grid>
  );
};

const PasswordTableCell = (props: any) => {
  const { password } = props;
  const [passwordHidden, setPasswordHidden] = useState(true);
  const notify = useNotify();
  return (
    <TableCell>
      <Grid container alignItems="center">
        <a
          onClick={() => {
            navigator.clipboard.writeText(password);
            notify('Password copied to clipboard', { type: 'success' });
          }}
          style={{ cursor: 'pointer' }}
          role="button"
        >
          {password && passwordHidden ? (
            '*******'
          ) : (
            <span translate="no">{password}</span>
          )}
        </a>
        {password && (
          <IconButton
            onClick={() => {
              setPasswordHidden(!passwordHidden);
            }}
            size="large"
          >
            {passwordHidden ? (
              <VisibilityOffOutlined />
            ) : (
              <VisibilityOutlined />
            )}
          </IconButton>
        )}
      </Grid>
    </TableCell>
  );
};

const WindSummary = (props: any) => {
  const { speed, gust, deg, measurementSystem } = props;
  const getSpeed = (speed: number) => {
    if (measurementSystem === 'metric') {
      return <span>{Math.round(speed * 1.60934)} km/h</span>;
    }
    return <span>{speed} mph</span>;
  };
  return (
    <Grid item>
      <Grid
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
      >
        <Grid item>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
            style={{ height: '108px' }}
          >
            <Grid item>
              <Typography variant="body2">
                <b>N</b>
              </Typography>
            </Grid>
            <Grid item>
              <TrendingFlat
                style={{
                  transform: `rotate(${deg - 90}deg)`,
                  height: '1.7em',
                  width: '1.7em',
                }}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid item>
          <Typography variant="h5" style={{ color: '#666' }}>
            {getSpeed(speed)}
          </Typography>
        </Grid>
        <Grid item>
          <Typography variant="body2">{getSpeed(gust)} (gust)</Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};

export const AssetMgmtProjectExpansionPanel = withStyles(styles)((props) => {
  const { open, project, contextUser, refetchPortfolioData } = props;
  const [dataFetchError, setDataFetchError] = useState(null);
  const [selectedTab, setSelectedTab] = useState('generation');
  const [productionChartData, setProductionChartData] = useState(null);
  const [productionTotals, setProductionTotals] = useState(null);
  const [tickets, setTickets] = useState(null);
  const [projectDetails, setProjectDetails] = useState(project);
  const [projectEquipmentData, setProjectEquipmentData] = useState(null);
  const [omPlatformCredentialsData, setOmPlatformCredentialsData] =
    useState(null);
  const [projectContactData, setProjectContactData] = useState(null);
  // const [projectIpCameraData, setProjectIpCameraData] = useState(null);
  const [projectFinanceData, setProjectFinanceData] = useState(null);

  const [measurementSystem, setMeasurementSystem] = useState('imperial');
  // const [portfolioOffTakerDetails, setPortfolioOffTakerDetails] =
  //   useState(null);

  // Power chart customize state vbls
  const [presetTimeFrame, setPresetTimeFrame] = useState('Last 72 hours');
  const [openCustomizePowerChartDialog, setOpenCustomizePowerChartDialog] =
    useState(false);
  const [fromDt, setFromDt] = useState(null);
  const [toDt, setToDt] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedInverterIds, setSelectedInverterIds] = useState([]);
  const [selectedSensorIds, setSelectedSensorIds] = useState([]);
  const [selectedMeterIds, setSelectedMeterIds] = useState([]);
  const [selectedTrackerIds, setSelectedTrackerIds] = useState([]);
  const [equipmentLoading, setEquipmentLoading] = useState(false);
  const [omPlatformCredentialsLoading, setOmPlatformCredentialsLoading] =
    useState(false);
  const [contactLoading, setContactLoading] = useState(false);
  // const [ipCameraLoading, setIpCameraLoading] = useState(false);
  const [projectFinanceDataLoading, setProjectFinanceDataLoading] =
    useState(false);
  const [snapshotDataLoading, setSnapshotDataLoading] = useState(false);
  const [generationDataLoading, setGenerationDataLoading] = useState(false);
  const [
    highlightMissingGenerationValues,
    setHighlightMissingGenerationValues,
  ] = useState(false);
  const [overlayGeneration, setOverlayGeneration] = useState(false);

  // Energy chart customize state vbls
  const [projectGenerationReportLoading, setProjectGenerationReportLoading] =
    useState(false);
  const [openCustomizeEnergyChartDialog, setOpenCustomizeEnergyChartDialog] =
    useState(false);
  const [energyTotalsBucketSize, setEnergyTotalsBucketSize] = useState({
    current: 'day',
    next: 'day',
  }); // 'month', 'week', 'day'
  const [energyTotalsTimeFrame, setEnergyTotalsTimeFrame] = useState({
    current: 'Last 30 days',
    next: 'Last 30 days',
  }); // 'Last 30 days', 'All time', 'Last week', 'Last 365 days'
  const [generationToDt, setGenerationToDt] = useState(null);
  const [generationFromDt, setGenerationFromDt] = useState(null);

  // const [cameraFeedIFrameUrl, setCameraFeedIFrameUrl] = useState(null);

  const [alarms, setAlarms] = useState(null);
  const [alarmStatus, setAlarmStatus] = useState('open');
  const [alarmsPerPage, setAlarmsPerPage] = useState(10);
  const [alarmListCount, setAlarmListCount] = useState(0);
  const [alarmListPage, setAlarmListPage] = useState(0);

  const [createTicketOpen, setCreateTicketOpen] = useState(false);
  const [updateTicketOpen, setUpdateTicketOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  const { permissions } = usePermissions();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  const irradianceSensorIds = [2, 3, 4];
  const trackerSensorIds = [7];
  const meterSensorIds = [8];

  useEffect(() => {
    Chart.register(annotationPlugin);
  });

  const userRoles = permissions?.roles?.map(
    ({ name }: { name: string }) => name
  );
  const hasWriteAccess = roleMatrix['MonitoringDashboard'].writeAccess.some(
    (r) => userRoles.includes(r)
  );
  const hideInverterHealth =
    userRoles.includes('Laerskool') ||
    userRoles.includes('Connaught') ||
    userRoles.includes('Eventide');

  const handleChangePage = (event: React.ChangeEvent<any>, page: number) => {
    setAlarmListPage(page);
    fetchAlarms({
      pageNum: page,
      perPage: alarmsPerPage,
      alarmType: alarmStatus,
    });
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<any>) => {
    setAlarmListPage(0);
    setAlarmsPerPage(event.target.value);
    fetchAlarms({
      pageNum: 0,
      perPage: event.target.value,
      alarmType: alarmStatus,
    });
  };

  const fetchAlarms = ({
    pageNum,
    perPage,
    alarmType,
  }: {
    pageNum: number;
    perPage: number;
    alarmType: string;
  }) => {
    dataProvider
      .getList('MonitoringAlarm', {
        filter: {
          alarmType,
          projectId: project.id,
        },
        sort: { field: 'openedDt', order: 'desc' },
        pagination: { page: pageNum + 1, perPage },
      })
      .then(
        ({ data, total }) => {
          setAlarms(data);
          setAlarmListCount(total || 0);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  // const fetchIpCameraData = () => {
  //   setIpCameraLoading(true);
  //   dataProvider
  //     .getOne('ProjectIpCameraList', {
  //       id: project.id,
  //     })
  //     .then(
  //       (resp) => {
  //         setIpCameraLoading(false);
  //         setProjectIpCameraData(resp.data);
  //       },
  //       (e) => {
  //         console.error('HIT AN ERROR', e);
  //         return new Error(e);
  //       }
  //     );
  // };

  const fetchProjectFinanceData = () => {
    return;
    setProjectFinanceData({});
    setProjectFinanceDataLoading(true);
    dataProvider
      .getOne('ProjectFinanceData', {
        // TODO: create this.
        id: project.id,
      })
      .then(
        (resp) => {
          setProjectFinanceDataLoading(false);
          setProjectFinanceData(resp.data);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  const fetchEquipmentData = () => {
    setEquipmentLoading(true);
    dataProvider
      .getOne('ProjectEquipmentList', {
        id: project.id,
      })
      .then(
        (resp) => {
          setEquipmentLoading(false);
          setProjectEquipmentData(resp.data);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  const fetchOmPlatformCredentialsData = () => {
    setOmPlatformCredentialsLoading(true);
    dataProvider
      .getOne('ProjectOmPlatformCredentialsList', {
        id: project.id,
      })
      .then(
        (resp) => {
          setOmPlatformCredentialsLoading(false);
          setOmPlatformCredentialsData(resp.data);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  const fetchContactData = () => {
    setContactLoading(true);
    dataProvider
      .getOne('ProjectContactList', {
        id: project.id,
      })
      .then(
        (resp) => {
          setContactLoading(false);
          setProjectContactData(resp.data);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  if (open && !alarms) {
    fetchAlarms({
      pageNum: alarmListPage,
      perPage: alarmsPerPage,
      alarmType: alarmStatus,
    });
  }

  if (
    open &&
    !projectContactData &&
    !contactLoading &&
    selectedTab === 'contacts'
  ) {
    fetchContactData();
  }

  if (
    open &&
    !projectEquipmentData &&
    !equipmentLoading &&
    selectedTab === 'equipment'
  ) {
    fetchEquipmentData();
  }
  if (
    open &&
    !omPlatformCredentialsData &&
    !omPlatformCredentialsLoading &&
    selectedTab === 'credentials'
  ) {
    fetchOmPlatformCredentialsData();
  }

  if (
    open &&
    !projectFinanceData &&
    !projectFinanceDataLoading &&
    selectedTab === 'finance'
  ) {
    fetchProjectFinanceData();
    // fetchIpCameraData();
  }

  const fetchPowerChartData = ({
    projectId,
    inverterIds,
    sensorIds,
    fetchSiteGeneration,
  }: {
    projectId: number;
    inverterIds: any;
    sensorIds: any;
    fetchSiteGeneration: any;
  }) => {
    setSnapshotDataLoading(true);
    dataProvider
      .getOne('ProductionMonitoringAnalytics', {
        input: {
          projectId: parseInt(projectId, 10),
          startDt: getStartDtFromTimeFrame(presetTimeFrame, fromDt),
          endDt: getEndDtFromTimeFrame(presetTimeFrame, toDt),
          inverterIds,
          sensorIds,
          fetchSiteGeneration,
          includeNullsForMissingSiteProductionData:
            fetchSiteGeneration && highlightMissingGenerationValues,
        },
      })
      .then(
        (res) => {
          setProductionChartData(res.data);
          setSnapshotDataLoading(false);
        },
        (e) => {
          console.error('Error retrieving project production data', e);
          setSnapshotDataLoading(false);
        }
      );
  };

  const fetchGenerationChartData = (projectId: number) => {
    setGenerationDataLoading(true);
    dataProvider
      .getOne('ProductionTotals', {
        input: {
          projectId: parseInt(projectId, 10),
          groupedBy: energyTotalsBucketSize.next,
          startDt: getStartDtFromTimeFrame(
            energyTotalsTimeFrame.next,
            generationFromDt
          ),
          endDt: getEndDtFromTimeFrame(
            energyTotalsTimeFrame.next,
            generationToDt
          ),
        },
      })
      .then(
        (res) => {
          setEnergyTotalsBucketSize({
            current: energyTotalsBucketSize.next,
            next: energyTotalsBucketSize.next,
          });
          setEnergyTotalsTimeFrame({
            current: energyTotalsTimeFrame.next,
            next: energyTotalsTimeFrame.next,
          });
          setProductionTotals(res.data.productionTotals);
          setTickets(res.data.allOMTickets);
          setGenerationDataLoading(false);
        },
        (e) => {
          setGenerationDataLoading(false);
          console.error('Error retrieving project production data', e);
        }
      );
  };

  const fetchProjectPanelDetails = () => {
    setLoading(true);
    dataProvider
      .getOne('CMSMonitoringProject', {
        projectId: project.id,
      })
      .then(
        (res) => {
          const {
            inverters,
            sensors,
            tusdInvoices,
            // ipCameras,
            // omPlatformCredentials,
            // projectContacts,
            // projectEquipmentItems,
          } = res.data;
          const updatedProject = { ...project };
          updatedProject.inverters = inverters;
          updatedProject.sensors = sensors.filter(
            (sensor) =>
              sensor.sensorType &&
              sensor.sensorType.id !== 7 &&
              sensor.sensorType.id !== 8
          );
          updatedProject.meters = sensors.filter(
            (sensor) => sensor.sensorType && sensor.sensorType.id === 8
          );
          updatedProject.tusdInvoices = tusdInvoices;
          updatedProject.trackers = sensors
            .filter((sensor) => sensor.sensorType && sensor.sensorType.id === 7)
            .sort((a, b) => (a.name < b.name ? -1 : 1));
          const pvPowerSensors = sensors.filter((sensor) =>
            sensor.name.toLowerCase().includes('pv power')
          );
          // updatedProject.omPlatformCredentials = omPlatformCredentials;
          // updatedProject.projectContacts = projectContacts;
          // updatedProject.ipCameras = ipCameras;
          setProjectDetails(updatedProject);

          const inverterIds =
            inverters && inverters.length > 0
              ? [...inverters.map((inv: any) => inv.id)]
              : [];
          const meterIds = [];
          const sensorIds = [];
          const trackerIds = [];
          let addGenerationLine = false;
          if (inverterIds.length === 0) {
            if (pvPowerSensors.length > 0) {
              pvPowerSensors.forEach((sensor: any) => {
                sensorIds.push(sensor.id);
              });
            } else if (updatedProject.meters.length > 0) {
              updatedProject.meters.forEach((meter: any) => {
                meterIds.push(meter.id);
              });
            } else {
              addGenerationLine = true;
            }
          }
          setSelectedInverterIds(inverterIds);
          setSelectedMeterIds(meterIds);
          setSelectedSensorIds(sensorIds);
          setOverlayGeneration(addGenerationLine);
          fetchPowerChartData({
            projectId: project.id,
            inverterIds,
            sensorIds: [...sensorIds, ...trackerIds, ...meterIds],
            fetchSiteGeneration: addGenerationLine,
          });
          fetchGenerationChartData(project.id);
          setDataFetchError(null);
          setLoading(false);
        },
        (e) => {
          console.error('Error retrieving project data', e);
          setDataFetchError(e);
          setLoading(false);
        }
      );
  };

  const createOMTicket = (input) => {
    dataProvider
      .create('OMTicket', {
        data: input,
      })
      .then(
        (res) => {
          notify(`Ticket ${res.data.id} created`, { type: 'success' });
          fetchGenerationChartData(project.id);
        },
        (err) => {
          notify('Error creating ticket', { type: 'error' });
        }
      );
  };

  const deleteOMTicket = (id) => {
    dataProvider
      .delete('OMTicket', {
        id: parseInt(id, 10),
      })
      .then(
        (res) => {
          notify('Ticket deleted', { type: 'success' });
          fetchGenerationChartData(project.id);
        },
        (err) => {
          notify('Error deleting ticket', { type: 'error' });
        }
      );
  };

  if (
    open &&
    !dataFetchError &&
    !loading &&
    (!projectDetails.inverters || !projectDetails.sensors)
  ) {
    fetchProjectPanelDetails();
  }

  // const fetchPortfolioOffTakerDetails = () => {
  //   dataProvider.getList('CMSPortfolioOffTakerDetails', {}).then(
  //     (resp) => {
  //       setPortfolioOffTakerDetails(resp.data);
  //     },
  //     (e) => {
  //       console.error('Error retrieving portfolios offtaker data', e);
  //     }
  //   );
  // };

  // if (!portfolioOffTakerDetails) {
  //   fetchPortfolioOffTakerDetails();
  // }

  const handleAcknowledgeAlarm = (alarm) => {
    const updateObj = {
      id: alarm.id,
      acknowledgedDt: new Date(),
      acknowledgedByEmployeeId: contextUser.employee?.id,
    };
    dataProvider
      .update('MonitoringAlarm', {
        data: updateObj,
      })
      .then(
        () => {
          fetchAlarms({
            pageNum: alarmListPage,
            perPage: alarmsPerPage,
            alarmType: alarmStatus,
          });
          refetchPortfolioData();
        },
        (err) => {
          let eMsg;
          if (
            err.message.indexOf('You do not have access to this endpoint') > -1
          ) {
            eMsg = 'You do not have access to this function.';
          } else {
            eMsg = 'Error syncing alarms.';
          }
          notify(eMsg, { type: 'error' });
          fetchAlarms({
            pageNum: alarmListPage,
            perPage: alarmsPerPage,
            alarmType: alarmStatus,
          });
          refetchPortfolioData();
        }
      );
    const updatedAlarms = [...alarms];
    const stateAlarm = updatedAlarms.filter((a) => a.id === alarm.id)[0];
    stateAlarm.acknowledgedDt = updateObj.acknowledgedDt;
    stateAlarm.acknowledgedByEmployee = {
      id: contextUser.id,
      fullName: contextUser.fullName,
    };
    setAlarms(updatedAlarms);
  };

  const handleCloseAlarm = (alarm) => {
    const updateObj = {
      id: alarm.id,
      closedDt: new Date(),
      closedByEmployeeId: contextUser.employee?.id,
    };
    if (!alarm.acknowledgedDt) {
      updateObj.acknowledgedDt = new Date();
      updateObj.acknowledgedByEmployeeId = contextUser.employee?.id;
    }
    dataProvider
      .update('MonitoringAlarm', {
        data: updateObj,
      })
      .then(
        () => {
          project.openMonitoringAlarmsCount -= 1;
          fetchAlarms({
            pageNum: alarmListPage,
            perPage: alarmsPerPage,
            alarmType: alarmStatus,
          });
          refetchPortfolioData();
        },
        (err) => {
          let eMsg;
          if (
            err.message.indexOf('You do not have access to this endpoint') > -1
          ) {
            eMsg = 'You do not have access to this function.';
          } else {
            eMsg = 'Error closing alarm.';
          }
          notify(eMsg, { type: 'error' });
          fetchAlarms({
            pageNum: alarmListPage,
            perPage: alarmsPerPage,
            alarmType: alarmStatus,
          });
          refetchPortfolioData();
        }
      );
    const updatedAlarms = [...alarms];
    const indexOfClosedAlarm = updatedAlarms.map((a) => a.id).indexOf(alarm.id);
    if (indexOfClosedAlarm > -1) {
      updatedAlarms.splice(indexOfClosedAlarm, 1);
      setAlarms(updatedAlarms);
    }
  };

  const handleSyncAlarms = () => {
    const updateObj = {
      id: -1, // UpdateMonitoringAlarmInput type requires an id.
      projectId: project.id,
      syncOpenAlarmStatuses: true,
    };
    dataProvider
      .update('MonitoringAlarm', {
        data: updateObj,
      })
      .then(
        () => {
          fetchAlarms({
            pageNum: alarmListPage,
            perPage: alarmsPerPage,
            alarmType: alarmStatus,
          });
          refetchPortfolioData();
        },
        (err) => {
          let eMsg;
          if (
            err.message.indexOf('You do not have access to this endpoint') > -1
          ) {
            eMsg = 'You do not have access to this function.';
          } else {
            eMsg = 'Error syncing alarms.';
          }
          notify(eMsg, { type: 'error' });
          fetchAlarms({
            pageNum: alarmListPage,
            perPage: alarmsPerPage,
            alarmType: alarmStatus,
          });
          refetchPortfolioData();
        }
      );
  };

  const getStartDtFromTimeFrame = (timeFrame, dt) => {
    switch (timeFrame) {
      case 'Last 24 hours':
        return moment().add(-1, 'days').format();
      case 'Last 72 hours':
        return moment().add(-3, 'days').format();
      case 'Last week':
        return moment().add(-7, 'days').format();
      case 'Last 30 days':
        return moment().add(-30, 'days').format();
      case 'Last 365 days':
        return moment().add(-365, 'days').format();
      case 'All time':
        return null;
      case 'Custom':
        return moment(dt).startOf('day').format();
      default:
        console.error('Unknown time frame');
        return moment().add(-7, 'days').format();
    }
  };

  const getEndDtFromTimeFrame = (timeFrame, dt) => {
    switch (timeFrame) {
      case 'Last 24 hours':
      case 'Last 72 hours':
      case 'Last week':
      case 'Last 30 days':
      case 'Last 365 days':
      case 'All time':
        return moment().format();
      case 'Custom':
        return moment(dt).endOf('day').format();
      default:
        console.error('Unknown time frame');
        return moment().format();
    }
  };

  const renderPowerChartCustomizeDialog = () => {
    return (
      <Dialog open={openCustomizePowerChartDialog} style={{ margin: '2rem' }}>
        <DialogTitle>
          <Typography variant="h5">Chart Customization</Typography>
        </DialogTitle>
        <DialogContent>
          <Grid container>
            <Grid
              container
              item
              alignItems="center"
              justifyContent="space-between"
              style={{ marginBottom: '1rem' }}
            >
              <Typography variant="h6">Date Range Selector:</Typography>
              <Grid item>
                <Select
                  label="filter"
                  value={presetTimeFrame}
                  onChange={(event) => setPresetTimeFrame(event.target.value)}
                  style={{ width: '200px' }}
                >
                  <MenuItem value="Last 24 hours">Last 24 hrs</MenuItem>
                  <MenuItem value="Last 72 hours">Last 72 hrs</MenuItem>
                  <MenuItem value="Last week">Last week</MenuItem>
                  <MenuItem value="Last 30 days">Last 30 days</MenuItem>
                  <Divider />
                  <MenuItem value="Custom">Custom</MenuItem>
                </Select>
              </Grid>
            </Grid>
            <Grid container justifyContent="flex-end">
              <Collapse in={presetTimeFrame === 'Custom'}>
                <Grid container spacing={2}>
                  <Grid item>
                    <TextField
                      id="fromDate"
                      label="From"
                      type="date"
                      value={fromDt || ''}
                      onChange={(event) => {
                        setFromDt(
                          moment(event.target.value).format('yyyy-MM-DD')
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        inputProps: {
                          min: moment(
                            projectDetails.actualCOD,
                            'YYYY-MM-DD'
                          ).format('yyyy-MM-DD'),
                          max: moment().format('yyyy-MM-DD'),
                        },
                      }}
                      error={toDt && fromDt && toDt <= fromDt}
                      helperText={
                        toDt && fromDt && toDt <= fromDt
                          ? "'From' date must precede 'To' date"
                          : null
                      }
                    />
                  </Grid>
                  <Grid item>
                    <TextField
                      id="toDate"
                      label="To"
                      type="date"
                      value={toDt || ''}
                      onChange={(event) =>
                        setToDt(moment(event.target.value).format('yyyy-MM-DD'))
                      }
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        inputProps: {
                          min: moment(
                            projectDetails.actualCOD,
                            'YYYY-MM-DD'
                          ).format('yyyy-MM-DD'),
                          max: moment().format('yyyy-MM-DD'),
                        },
                      }}
                      error={toDt && fromDt && toDt <= fromDt}
                      helperText={
                        toDt && fromDt && toDt <= fromDt
                          ? "'From' date must precede 'To' date"
                          : null
                      }
                    />
                  </Grid>
                </Grid>
              </Collapse>
            </Grid>
            <Grid
              container
              item
              justifyContent="space-between"
              style={{ marginBottom: '1rem' }}
              direction="column"
            >
              <Grid item>
                <Typography variant="h6">Inverters:</Typography>
              </Grid>
              {loading ? (
                <CircularProgress />
              ) : projectDetails.inverters &&
                projectDetails.inverters.length > 0 ? (
                <Grid
                  container
                  direction="column"
                  style={{ paddingLeft: '2rem' }}
                >
                  <Grid item key={`chart-settings-inverter-check-all`}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="check-all-inverters"
                          checked={
                            selectedInverterIds.length ===
                            projectDetails.inverters.length
                          }
                          onChange={() => {
                            if (
                              selectedInverterIds.length ===
                              projectDetails.inverters.length
                            ) {
                              setSelectedInverterIds([]);
                            } else {
                              setSelectedInverterIds([
                                ...projectDetails.inverters.map((i) => i.id),
                              ]);
                            }
                          }}
                        />
                      }
                      label={
                        selectedInverterIds.length ===
                        projectDetails.inverters.length
                          ? 'Unselect All'
                          : 'Select All'
                      }
                    />
                  </Grid>
                  {projectDetails.inverters.map((inverter) => (
                    <Grid
                      container
                      alignItems="center"
                      key={`chart-settings-inverter-${inverter.id}`}
                    >
                      <Grid item>
                        <FormControlLabel
                          control={
                            <Checkbox
                              name={inverter.name}
                              checked={
                                selectedInverterIds.indexOf(inverter.id) !== -1
                              }
                              onChange={() => {
                                const inverterIds = selectedInverterIds;
                                const index = inverterIds.indexOf(inverter.id);
                                if (index === -1) {
                                  inverterIds.push(inverter.id);
                                } else {
                                  inverterIds.splice(index, 1);
                                }
                                setSelectedInverterIds([...inverterIds]);
                              }}
                            />
                          }
                          label={inverter.name}
                        />
                      </Grid>
                      <InverterIntegrityChecks inverterId={inverter.id} />
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">No inverters available</Alert>
              )}
            </Grid>
            <Grid
              container
              item
              justifyContent="space-between"
              style={{ marginBottom: '1rem' }}
              direction="column"
            >
              <Grid item>
                <Typography variant="h6">Sensors:</Typography>
              </Grid>
              {loading ? (
                <CircularProgress />
              ) : projectDetails.sensors &&
                projectDetails.sensors.length > 0 ? (
                <Grid
                  container
                  direction="column"
                  style={{ paddingLeft: '2rem' }}
                >
                  <Grid item key={`chart-settings-sensor-check-all`}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="check-all-sensors"
                          checked={
                            selectedSensorIds.length ===
                            projectDetails.sensors.length
                          }
                          onChange={() => {
                            if (
                              selectedSensorIds.length ===
                              projectDetails.sensors.length
                            ) {
                              setSelectedSensorIds([]);
                            } else {
                              setSelectedSensorIds([
                                ...projectDetails.sensors.map((i) => i.id),
                              ]);
                            }
                          }}
                        />
                      }
                      label={
                        selectedSensorIds.length ===
                        projectDetails.sensors.length
                          ? 'Unselect All'
                          : 'Select All'
                      }
                    />
                  </Grid>
                  {projectDetails.sensors.map((sensor) => (
                    <Grid
                      container
                      alignItems="center"
                      key={`chart-settings-sensor-${sensor.id}`}
                    >
                      <Grid item>
                        <FormControlLabel
                          control={
                            <Checkbox
                              name={sensor.name}
                              checked={
                                selectedSensorIds.indexOf(sensor.id) !== -1
                              }
                              onChange={() => {
                                const sensorIds = selectedSensorIds;
                                const index = sensorIds.indexOf(sensor.id);
                                if (index === -1) {
                                  sensorIds.push(sensor.id);
                                } else {
                                  sensorIds.splice(index, 1);
                                }
                                setSelectedSensorIds([...sensorIds]);
                              }}
                            />
                          }
                          label={sensor.name}
                        />
                      </Grid>
                      <SensorIntegrityChecks sensorId={sensor.id} />
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">No sensors available</Alert>
              )}
            </Grid>
            {loading ||
            (projectDetails.meters &&
              projectDetails.meters.length === 0) ? null : (
              <Grid
                container
                item
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
                direction="column"
              >
                <Grid item>
                  <Typography variant="h6">Meters:</Typography>
                </Grid>
                {loading ? (
                  <CircularProgress />
                ) : projectDetails.meters &&
                  projectDetails.meters.length > 0 ? (
                  <Grid
                    container
                    direction="column"
                    style={{ paddingLeft: '2rem' }}
                  >
                    <Grid item key={`chart-settings-meter-check-all`}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            name="check-all-meters"
                            checked={
                              selectedMeterIds.length ===
                              projectDetails.meters.length
                            }
                            onChange={() => {
                              if (
                                selectedMeterIds.length ===
                                projectDetails.meters.length
                              ) {
                                setSelectedMeterIds([]);
                              } else {
                                setSelectedMeterIds([
                                  ...projectDetails.meters.map((i) => i.id),
                                ]);
                              }
                            }}
                          />
                        }
                        label={
                          selectedMeterIds.length ===
                          projectDetails.meters.length
                            ? 'Unselect All'
                            : 'Select All'
                        }
                      />
                    </Grid>
                    {projectDetails.meters.map((meter) => (
                      <Grid
                        container
                        alignItems="center"
                        key={`chart-settings-meter-${meter.id}`}
                      >
                        <Grid item>
                          <FormControlLabel
                            control={
                              <Checkbox
                                name={meter.name}
                                checked={
                                  selectedMeterIds.indexOf(meter.id) !== -1
                                }
                                onChange={() => {
                                  const meterIds = selectedMeterIds;
                                  const index = meterIds.indexOf(meter.id);
                                  if (index === -1) {
                                    meterIds.push(meter.id);
                                  } else {
                                    meterIds.splice(index, 1);
                                  }
                                  setSelectedMeterIds([...meterIds]);
                                }}
                              />
                            }
                            label={meter.name}
                          />
                        </Grid>
                        <SensorIntegrityChecks sensorId={meter.id} />
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Alert severity="info">No meters available</Alert>
                )}
              </Grid>
            )}
            <Grid
              container
              item
              justifyContent="space-between"
              style={{ marginBottom: '1rem' }}
              direction="column"
            >
              <Grid item>
                <Typography variant="h6">Trackers:</Typography>
              </Grid>
              {loading ? (
                <CircularProgress />
              ) : projectDetails.trackers &&
                projectDetails.trackers.length > 0 ? (
                <Grid
                  container
                  direction="column"
                  style={{ paddingLeft: '2rem' }}
                >
                  <Grid item key={`chart-settings-tracker-check-all`}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="check-all-trackers"
                          checked={
                            selectedTrackerIds.length ===
                            projectDetails.trackers.length
                          }
                          onChange={() => {
                            if (
                              selectedTrackerIds.length ===
                              projectDetails.trackers.length
                            ) {
                              setSelectedTrackerIds([]);
                            } else {
                              setSelectedTrackerIds([
                                ...projectDetails.trackers.map((i) => i.id),
                              ]);
                            }
                          }}
                        />
                      }
                      label={
                        selectedTrackerIds.length ===
                        projectDetails.trackers.length
                          ? 'Unselect All'
                          : 'Select All'
                      }
                    />
                  </Grid>
                  {projectDetails.trackers.map((tracker) => (
                    <Grid
                      container
                      alignItems="center"
                      key={`chart-settings-tracker-${tracker.id}`}
                    >
                      <Grid item>
                        <FormControlLabel
                          control={
                            <Checkbox
                              name={tracker.name}
                              checked={
                                selectedTrackerIds.indexOf(tracker.id) !== -1
                              }
                              onChange={() => {
                                const trackerIds = selectedTrackerIds;
                                const index = trackerIds.indexOf(tracker.id);
                                if (index === -1) {
                                  trackerIds.push(tracker.id);
                                } else {
                                  trackerIds.splice(index, 1);
                                }
                                setSelectedTrackerIds([...trackerIds]);
                              }}
                            />
                          }
                          label={tracker.name}
                        />
                      </Grid>
                      <SensorIntegrityChecks
                        sensorId={tracker.id}
                        isTracker={true}
                      />
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">No trackers available</Alert>
              )}
            </Grid>
            <Grid container style={{ marginBottom: '1rem' }}>
              <Grid
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography variant="h6">Overlay Generation Line:</Typography>
                </Grid>
                <Grid item>
                  <Switch
                    checked={!!overlayGeneration}
                    onChange={() => {
                      setOverlayGeneration(!overlayGeneration);
                    }}
                  />
                </Grid>
              </Grid>
              <Grid container style={{ width: '100%' }}>
                <Collapse in={overlayGeneration} style={{ width: '100%' }}>
                  <Grid
                    container
                    justifyContent="space-between"
                    alignItems="center"
                    style={{ paddingLeft: '1rem' }}
                  >
                    <Grid item>
                      <Typography>
                        Highlight missing generation data:
                      </Typography>
                    </Grid>
                    <Grid item>
                      <Switch
                        checked={!!highlightMissingGenerationValues}
                        onChange={() => {
                          setHighlightMissingGenerationValues(
                            !highlightMissingGenerationValues
                          );
                        }}
                      />
                    </Grid>
                  </Grid>
                </Collapse>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenCustomizePowerChartDialog(false)}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              fetchPowerChartData({
                projectId: projectDetails.id,
                inverterIds: selectedInverterIds,
                sensorIds: [
                  ...selectedSensorIds,
                  ...selectedTrackerIds,
                  ...selectedMeterIds,
                ],
                fetchSiteGeneration: overlayGeneration,
              });
              setOpenCustomizePowerChartDialog(false);
            }}
            variant="contained"
            color="primary"
            disabled={
              presetTimeFrame === 'Custom' &&
              (!toDt || !fromDt || fromDt >= toDt)
            }
          >
            Go
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const exportProjectGenerationData = () => {
    setProjectGenerationReportLoading(true);
    dataProvider
      .create('ProjectGenerationReport', {
        input: { projectId: projectDetails.id },
      })
      .then(
        (res) => {
          setProjectGenerationReportLoading(false);
          const link = document.createElement('a');
          link.href = res.data.downloadUrl;
          link.download = true;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (e) => {
          setProjectGenerationReportLoading(false);
          console.error('Error downloading project generation report', e);
          notify('Error downloading project generation report', {
            type: 'error',
          });
        }
      );
  };

  const exportProjectSnapshotData = () => {
    const wb = new xl.Workbook();
    const ws = wb.addWorksheet();

    const filename = `${projectDetails.name} Snapshot Report.xlsx`;
    const title = `${projectDetails.name} Snapshot Report`;
    writeToCell({
      ws,
      rowIndex: 1,
      columnIndex: 1,
      val: title,
      style: { bold: true, fontSize: 20 },
    });
    writeToCell({
      ws,
      rowIndex: 3,
      columnIndex: 1,
      val: 'Timestamp',
      style: { bold: true },
    });

    const dataMatrix = {};
    const totalColumnCount =
      productionChartData.inverters.length + productionChartData.sensors.length;
    productionChartData.inverters.forEach((inverter, index) => {
      const filteredInverters =
        projectDetails.inverters.filter((i) => i.id === inverter.inverterId) ||
        [];
      const inverterName =
        filteredInverters && filteredInverters.length > 0
          ? filteredInverters[0].name
          : 'Unknown inverter';
      writeToCell({
        ws,
        rowIndex: 3,
        columnIndex: index + 2,
        val: inverterName,
        style: { bold: true },
      });
      inverter.productionPeriods.forEach((data) => {
        if (!dataMatrix[String(data.date)]) {
          dataMatrix[String(data.date)] = new Array(totalColumnCount).fill(
            null
          );
        }
        dataMatrix[String(data.date)][parseInt(index, 10)] = data.value;
      });
    });

    productionChartData.sensors.forEach((sensor, index) => {
      const nonTrackerFilteredSensors = projectDetails.sensors.filter(
        (s) => s.id === sensor.sensorId
      );
      const trackerFilteredSensors = projectDetails.trackers.filter(
        (t) => t.id === sensor.sensorId
      );
      const meterFilteredSensors = projectDetails.meters.filter(
        (m) => m.id === sensor.sensorId
      );
      const isTracker = trackerSensorIds.indexOf(sensor.sensorTypeId) > -1;
      const isMeter = meterSensorIds.indexOf(sensor.sensorTypeId) > -1;
      let filteredSensors = [];
      if (isTracker) {
        filteredSensors = trackerFilteredSensors;
      } else if (isMeter) {
        filteredSensors = meterFilteredSensors;
      } else {
        filteredSensors = nonTrackerFilteredSensors;
      }
      const sensorName =
        filteredSensors && filteredSensors.length > 0
          ? filteredSensors[0].name
          : 'Unknown sensor';
      writeToCell({
        ws,
        rowIndex: 3,
        columnIndex: index + productionChartData.inverters.length + 2,
        val: sensorName,
        style: { bold: true },
      });
      sensor.sensorDataPeriods.forEach((data) => {
        if (!dataMatrix[String(data.date)]) {
          dataMatrix[String(data.date)] = new Array(totalColumnCount).fill(
            null
          );
        }
        dataMatrix[String(data.date)][
          index + productionChartData.inverters.length
        ] = data.value;
      });
    });

    const sortedMatrix = [];
    const timestamps = Object.keys(dataMatrix);
    timestamps.sort();
    timestamps.forEach((date, dateIndex) => {
      const row = [date, ...dataMatrix[String(date)]];
      row.forEach((val, index) => {
        writeToCell({
          ws,
          rowIndex: sortedMatrix.length + 4 + dateIndex,
          columnIndex: index + 1,
          val,
        });
      });
    });

    downloadWorkbook(wb, filename);
  };

  const renderEnergyChartCustomizeDialog = () => {
    return (
      <Dialog open={openCustomizeEnergyChartDialog} style={{ margin: '2rem' }}>
        <DialogTitle>
          <Typography variant="h5">Chart Customization</Typography>
        </DialogTitle>
        <DialogContent>
          <Grid
            container
            item
            justifyContent="space-between"
            style={{ marginBottom: '1rem' }}
          >
            <Grid item>
              <Typography variant="h6">Group By:</Typography>
            </Grid>
            <Grid item>
              <Select
                label="filter"
                value={
                  energyTotalsBucketSize.next || energyTotalsBucketSize.current
                }
                onChange={(event) => {
                  setEnergyTotalsBucketSize({
                    current: energyTotalsBucketSize.current,
                    next: event.target.value,
                  });
                  if (
                    event.target.value === 'month' ||
                    event.target.value === 'year'
                  ) {
                    setEnergyTotalsTimeFrame({
                      current: energyTotalsTimeFrame.current,
                      next: 'All time',
                    });
                  }
                }}
                style={{ width: '150px' }}
              >
                <MenuItem value="day">Day</MenuItem>
                <MenuItem value="week">Week</MenuItem>
                <MenuItem value="month">Month</MenuItem>
                <MenuItem value="year">Year</MenuItem>
              </Select>
            </Grid>
          </Grid>
          <Grid container>
            <Grid
              container
              item
              alignItems="center"
              justifyContent="space-between"
            >
              <Typography variant="h6">Date Range Selector:</Typography>
              <Grid item>
                <Select
                  label="filter"
                  value={
                    energyTotalsTimeFrame.next || energyTotalsTimeFrame.current
                  }
                  onChange={(event) =>
                    setEnergyTotalsTimeFrame({
                      current: energyTotalsTimeFrame.current,
                      next: event.target.value,
                    })
                  }
                  style={{ width: '200px' }}
                >
                  <MenuItem
                    value="Last week"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Last week
                  </MenuItem>
                  <MenuItem
                    value="Last 30 days"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Last 30 days
                  </MenuItem>
                  <MenuItem
                    value="Last 365 days"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Last 365 days
                  </MenuItem>
                  <MenuItem value="All time">All time</MenuItem>
                  <Divider />
                  <MenuItem
                    value="Custom"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Custom
                  </MenuItem>
                </Select>
              </Grid>
            </Grid>
            <Grid
              container
              justifyContent="flex-end"
              style={{ marginBottom: '1rem' }}
            >
              <Collapse in={energyTotalsTimeFrame.next === 'Custom'}>
                <Grid container spacing={2}>
                  <Grid item>
                    <TextField
                      id="fromDate"
                      label="From"
                      type="date"
                      value={generationFromDt || ''}
                      onChange={(event) => {
                        setGenerationFromDt(
                          moment(event.target.value).format('yyyy-MM-DD')
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        inputProps: {
                          min: moment(
                            projectDetails.actualCOD,
                            'YYYY-MM-DD'
                          ).format('yyyy-MM-DD'),
                          max: moment().format('yyyy-MM-DD'),
                        },
                      }}
                      error={
                        !!(
                          generationToDt &&
                          generationFromDt &&
                          generationToDt <= generationFromDt
                        )
                      }
                      helperText={
                        generationToDt &&
                        generationFromDt &&
                        generationToDt <= generationFromDt
                          ? "'From' date must precede 'To' date"
                          : null
                      }
                    />
                  </Grid>
                  <Grid item>
                    <TextField
                      id="toDate"
                      label="To"
                      type="date"
                      value={generationToDt || ''}
                      onChange={(event) =>
                        setGenerationToDt(
                          moment(event.target.value).format('yyyy-MM-DD')
                        )
                      }
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        inputProps: {
                          min: moment(
                            projectDetails.actualCOD,
                            'YYYY-MM-DD'
                          ).format('yyyy-MM-DD'),
                          max: moment().format('yyyy-MM-DD'),
                        },
                      }}
                      error={
                        !!(
                          generationToDt &&
                          generationFromDt &&
                          generationToDt <= generationFromDt
                        )
                      }
                      helperText={
                        generationToDt &&
                        generationFromDt &&
                        generationToDt <= generationFromDt
                          ? "'From' date must precede 'To' date"
                          : null
                      }
                    />
                  </Grid>
                </Grid>
              </Collapse>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setEnergyTotalsBucketSize({
                current: energyTotalsBucketSize.current,
                next: energyTotalsBucketSize.current,
              });
              setEnergyTotalsTimeFrame({
                current: energyTotalsTimeFrame.current,
                next: energyTotalsTimeFrame.current,
              });
              setOpenCustomizeEnergyChartDialog(false);
            }}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              fetchGenerationChartData(projectDetails.id);
              setOpenCustomizeEnergyChartDialog(false);
            }}
            variant="contained"
            color="primary"
          >
            Go
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const renderProjectProductionData = () => {
    let powerChartJsx = null;
    const ticketAnnotations = {};
    tickets?.forEach((ticket: any) => {
      if (!ticket.startDt || !ticket.estimatedPercentageLoss) {
        return;
      }
      const xMin = moment
        .max(
          [
            moment(ticket.startDt),
            getStartDtFromTimeFrame(
              energyTotalsTimeFrame.next,
              generationFromDt
            ) &&
              moment(
                getStartDtFromTimeFrame(
                  energyTotalsTimeFrame.next,
                  generationFromDt
                )
              ),
          ].filter((el) => !!el)
        )
        .subtract(12, 'hour'); // NOTE: This offset is to match the bar chart which sets the xAxis daily tics at noon
      const xMax = ticket.endDt
        ? moment
            .min([
              moment(
                getEndDtFromTimeFrame(
                  energyTotalsTimeFrame.next,
                  generationToDt
                ) || null
              ),
              moment(ticket.endDt),
              moment(),
            ])
            .subtract(12, 'hour') // NOTE: This offset is to match the bar chart which sets the xAxis daily tics at noon
        : undefined;
      if (ticket.startDt && ticket.endDt && ticket.endDt < ticket.startDt) {
        console.log(
          `Ticket #${ticket.id} has an end date that is before the start date. Please correct this in the ticket details.`
        );
      } else {
        ticketAnnotations[`ticket${ticket.id}`] = {
          type: 'box',
          xMin,
          xMax,
          backgroundColor: 'rgba(255,0,0,.06)',
          borderWidth: 0,
          drawTime: 'beforeDatasetsDraw', // NOTE: This is to put the highlighting behind the datasets
        };
      }
    });
    if (loading || snapshotDataLoading) {
      powerChartJsx = (
        <Grid item style={{ padding: '1rem' }}>
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="16rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </Grid>
      );
    } else {
      if (productionChartData) {
        const scales = {
          x: {
            type: 'time',
            time: {
              tooltipFormat: 'MM-DD-YYYY HH:mm',
              unit: 'day',
            },
          },
          y: {
            beginAtZero: true,
            ticks: {
              callback: (value) =>
                `${numeral(value).format('0,0.[0]')} ${
                  overlayGeneration && selectedInverterIds.length === 0
                    ? 'kWh'
                    : 'kW'
                }`,
            },
          },
        };

        const datasets = [];
        let dataExists = false;
        const generationRedBoxes = {};

        if (overlayGeneration) {
          if (productionChartData.siteProductionPeriods.length > 0) {
            dataExists = true;
          }

          const isDailyData =
            productionChartData.siteProductionPeriods.filter(
              (p) => p.timeUnit.toLowerCase() === 'day'
            ).length > 0;

          datasets.push({
            label: `${projectDetails.name} (Generation)`,
            data: productionChartData.siteProductionPeriods.map(
              (point, index: number) => {
                // If its a daily generation value, move it to noon so the point is at mid day
                const xVal = isDailyData
                  ? moment(point.periodStartDt, 'YYYY-MM-DD HH:mm:ss').set({
                      hour: 12,
                    })
                  : moment(point.periodStartDt, 'YYYY-MM-DD HH:mm:ss');
                return {
                  x: xVal,
                  y: point.production !== null ? point.production / 1000 : NaN, // NOTE: spanGaps: false will cause a break in the line if the value is NaN (not the same for null),
                  timeUnit: point.timeUnit,
                };
              }
            ),
            unit: 'kWh',
            pointRadius: isDailyData ? 2 : 0,
            borderWidth: 4,
            borderColor: theme.palette.green.main,
            fill: !isDailyData,
            yAxisID: 'y',
            spanGaps: false,
          });

          if (highlightMissingGenerationValues) {
            const redRanges = [];
            productionChartData.siteProductionPeriods.map(
              (point, index: number) => {
                if (point.production === null) {
                  if (
                    redRanges.length === 0 ||
                    redRanges[redRanges.length - 1].endIndex < index - 1
                  ) {
                    const startDt =
                      index > 0
                        ? moment(
                            productionChartData.siteProductionPeriods[index - 1]
                              .periodStartDt,
                            'YYYY-MM-DD HH:mm:ss'
                          )
                        : moment(point.periodStartDt, 'YYYY-MM-DD HH:mm:ss');
                    const endDt =
                      index <
                      productionChartData.siteProductionPeriods.length - 1
                        ? moment(
                            productionChartData.siteProductionPeriods[index + 1]
                              .periodStartDt,
                            'YYYY-MM-DD HH:mm:ss'
                          )
                        : moment(point.periodStartDt, 'YYYY-MM-DD HH:mm:ss');
                    redRanges.push({
                      start: startDt,
                      startIndex: index,
                      end: endDt,
                      endIndex: index,
                    });
                  } else {
                    const recentRange = redRanges[redRanges.length - 1];
                    const endDt =
                      index <
                      productionChartData.siteProductionPeriods.length - 1
                        ? moment(
                            productionChartData.siteProductionPeriods[index + 1]
                              .periodStartDt,
                            'YYYY-MM-DD HH:mm:ss'
                          )
                        : moment(point.periodStartDt, 'YYYY-MM-DD HH:mm:ss');
                    recentRange.end = endDt;
                    recentRange.endIndex = index;
                  }
                }
              }
            );
            redRanges.forEach((redRange, index) => {
              generationRedBoxes[`line${index}`] = {
                type: 'line',
                xMin: redRange.start,
                xMax: redRange.start,
                borderColor: theme.palette.error.dark,
                borderWidth: 2,
              };
              generationRedBoxes[`box${index}`] = {
                type: 'box',
                xMin: redRange.start,
                xMax: redRange.end,
                borderWidth: 0,
                backgroundColor: 'rgba(255, 99, 132, 0.25)',
              };
            });
          }
        }

        const inverterBorderColorsArray = interpolateColors(
          productionChartData.inverters.length,
          'rgba(0, 217, 227, 1)',
          'rgba(180, 0, 255, 1)',
          1
        );
        productionChartData.inverters.forEach((inverter) => {
          if (inverter.productionPeriods.length > 0) {
            dataExists = true;
          }
          const filteredInverters =
            projectDetails.inverters.filter(
              (i) => i.id === inverter.inverterId
            ) || [];
          const inverterName =
            filteredInverters && filteredInverters.length > 0
              ? filteredInverters[0].name
              : 'Unknown inverter';
          const color = inverterBorderColorsArray.shift();
          datasets.push({
            label: inverterName,
            data: inverter.productionPeriods.map((point) => ({
              x: moment(point.date, 'YYYY-MM-DD HH:mm:ss'),
              y: point.value !== null ? point.value / 1000 : NaN, // NOTE: spanGaps: false will cause a break in the line if the value is NaN (not the same for null),
            })),
            tension: 0.2,
            unit: 'kW',
            pointRadius: 0,
            borderWidth: 2,
            borderColor: color,
            fill: true,
            backgroundColor: getGradient(color, 0.1),
            yAxisID: 'y',
            spanGaps: false,
          });
        });

        const sensorBorderColorsArray = interpolateColors(
          productionChartData.sensors.filter(
            (s) => selectedSensorIds.indexOf(s.sensorId) > -1
          ).length,
          'rgb(128,0,0)',
          'rgb(255,165,0)',
          1
        );
        const meterBorderColorsArray = interpolateColors(
          productionChartData.sensors.filter(
            (s) => selectedMeterIds.indexOf(s.sensorId) > -1
          ).length,
          'rgb(0,0,128)',
          'rgb(0,165,255)',
          1
        );
        const trackerBorderColorsArray = interpolateColors(
          productionChartData.sensors.filter(
            (s) => selectedTrackerIds.indexOf(s.sensorId) > -1
          ).length,
          'rgb(0,128,0)',
          'rgb(0,255,0)',
          1
        );

        productionChartData.sensors.forEach((sensor) => {
          if (sensor.sensorDataPeriods.length > 0) {
            dataExists = true;
          }
          const nonTrackerFilteredSensors = projectDetails.sensors.filter(
            (s) => s.id === sensor.sensorId
          );
          const trackerFilteredSensors = projectDetails.trackers.filter(
            (t) => t.id === sensor.sensorId
          );
          const meterFilteredSensors = projectDetails.meters.filter(
            (m) => m.id === sensor.sensorId
          );
          const isTracker = trackerSensorIds.indexOf(sensor.sensorTypeId) > -1;
          const isMeter = meterSensorIds.indexOf(sensor.sensorTypeId) > -1;
          const isIrradianceSensor =
            irradianceSensorIds.indexOf(sensor.sensorTypeId) > -1;
          let filteredSensors = [];
          if (isTracker) {
            filteredSensors = trackerFilteredSensors;
          } else if (isMeter) {
            filteredSensors = meterFilteredSensors;
          } else {
            filteredSensors = nonTrackerFilteredSensors;
          }

          const sensorName =
            filteredSensors && filteredSensors.length > 0
              ? filteredSensors[0].name
              : 'Unknown sensor';

          let yAxisID;
          let yAxisName;
          if (isTracker) {
            yAxisID = 'yTracker';
            yAxisName = 'Tracker angle';
          } else if (isIrradianceSensor) {
            yAxisID = 'yIrradiance';
            yAxisName = 'Irradiance';
          } else {
            yAxisID = `ySensor-${sensor.sensorId}`;
            yAxisName = sensorName;
          }
          const color = isTracker
            ? trackerBorderColorsArray.shift()
            : isMeter
            ? meterBorderColorsArray.shift()
            : sensorBorderColorsArray.shift();
          datasets.push({
            label: sensorName,
            data: sensor.sensorDataPeriods.map((point) => ({
              x: moment(point.date, 'YYYY-MM-DD HH:mm:ss'),
              y: point.value !== null ? point.value : NaN, // NOTE: spanGaps: false will cause a break in the line if the value is NaN (not the same for null),
            })),
            unit: '', // TODO: This data is in the sensors table
            pointRadius: 0,
            borderWidth: 2,
            tension: 0.2,
            borderColor: color,
            backgroundColor: isTracker ? undefined : getGradient(color, 0.2),
            // backgroundColor: 'rgba(255,255,255,.15)',
            fill: !isTracker,
            yAxisID,
            spanGaps: false,
          });
          scales[String(yAxisID)] = {
            title: {
              display: true,
              text: yAxisName,
            },
            grid: {
              display: false,
            },
            position: 'right',
          };
        });

        if (!dataExists || loading) {
          powerChartJsx = (
            <Alert severity="warning" style={{ marginTop: '1rem' }}>
              No project production data for this time period.
            </Alert>
          );
        } else {
          powerChartJsx = (
            <>
              <Typography variant="caption">
                <em>
                  *Gaps in the data mean there were null data points in the data
                  logger(s). Zero values get plotted as zero and null or missing
                  data creates a gap. Once a site comes back online after an
                  outage data is backfilled if it exists.
                </em>
              </Typography>
              <Grid item xs={12} style={{ maxHeight: '500px' }}>
                <Line
                  height={450}
                  data={{
                    datasets,
                  }}
                  options={{
                    maintainAspectRatio: false,
                    plugins: {
                      tooltip: {
                        mode: 'x',
                        intersect: false,
                        callbacks: {
                          label: (tooltipItem) => {
                            if (
                              (tooltipItem.chart.tooltip?.title.length &&
                                tooltipItem.chart.tooltip?.title[0] ===
                                  tooltipItem.label) ||
                              tooltipItem.datasetIndex === 0
                            ) {
                              return `${tooltipItem.dataset.label}: ${numeral(
                                tooltipItem.formattedValue
                              ).format('0,0')} ${tooltipItem.dataset?.unit}`;
                            }
                            return null;
                          },
                          title: (tooltipItem) => {
                            // If its a daily generation point, remove time from the timestamp label
                            if (tooltipItem.length) {
                              if (
                                tooltipItem[0].datasetIndex === 0 &&
                                tooltipItem[0].raw.timeUnit?.toLowerCase() ===
                                  'day'
                              ) {
                                return moment(tooltipItem[0].label).format(
                                  'YYYY-MM-DD'
                                );
                              } else {
                                return tooltipItem[0].label;
                              }
                            }
                          },
                        },
                        filter: (tooltipItem, index, tooltipItems, data) => {
                          if (tooltipItems.length > data.datasets.length) {
                            for (let i = 0; i < tooltipItems.length; i += 1) {
                              if (
                                tooltipItem.datasetIndex ===
                                tooltipItems[parseInt(i, 10)].datasetIndex
                              ) {
                                return i === index;
                              }
                            }
                          }
                          return true;
                        },
                      },
                      // NOTE: removing because the ticket data call date range is based on generation
                      // TODO: separate ticket data call so that you can use the min and max of the 2 charts' date ranges
                      // annotation: {
                      //   annotations: {
                      //     ...generationRedBoxes,
                      //     ...ticketAnnotations,
                      //   },
                      // },
                    },
                    scales,
                  }}
                />
              </Grid>
            </>
          );
        }
      }
    }

    let generationChartJsx = null;
    if (loading || generationDataLoading) {
      generationChartJsx = (
        <Grid item style={{ padding: '1rem' }}>
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="16rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </Grid>
      );
    } else {
      if (productionTotals) {
        let actualDataset = null;
        let inverterDataset = null;
        let expectedDataset = null;
        let p50Dataset = null;
        let moduleTempRefDataset = null;
        let globEffIrradianceDataset = null;
        let actualIrradianceDataset = null;
        let irradianceDataCoverageDataset = null;
        let inverterAvailabilityDataset = null;
        let actualPRDataset = null;
        let avgModuleTemperatureDataset = null;
        let tusdDataset = null;
        let xScale = null;
        // const hasExpectedProduction =
        //   productionTotals.filter((pt) => !!pt?.expectedProduction).length > 0;
        const actualDatasetColor = theme.palette.green.main;
        const inverterDatasetColor = theme.palette.primary.main;
        const expectedDatasetColor = '#76D299';
        const p50DatasetColor = theme.palette.green.dark;
        const tusdDatasetColor = 'rgba(42, 201, 186, .4)';
        const globEffIrradianceDatasetColor = 'orange';
        const actualIrradianceDatasetColor = 'rgba(220, 70, 20)';
        const irradianceDataCoverageDatasetColor = '#fcd97e';
        const inverterAvailabilityDatasetColor = 'rgba(75,0,130, .3)';
        const actualPRDatasetColor = theme.palette.appSecondary.main;
        const avgModuleTemperatureDatasetColor = '#F0F000';
        const moduleTempRefDatasetColor = '#bfba00';
        const hideTUSD = projectDetails.portfolioId !== 5;

        if (energyTotalsBucketSize.current === 'year') {
          actualDataset = {
            label: 'Actual Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.actualProduction,
            })),
            pointRadius: 0,
            backgroundColor: actualDatasetColor,
            fill: true,
          };
          inverterDataset = {
            label: 'Inverter Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.inverterProduction,
            })),
            pointRadius: 0,
            backgroundColor: inverterDatasetColor,
            fill: true,
            hidden: true,
          };
          expectedDataset = {
            label: 'Expected Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.expectedProduction,
            })),
            pointRadius: 0,
            backgroundColor: expectedDatasetColor,
            fill: true,
            hidden: true,
            // hidden: !hasExpectedProduction,
          };
          p50Dataset = {
            label: 'P-50 Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.p50Production,
            })),
            pointRadius: 0,
            backgroundColor: p50DatasetColor,
          };
          tusdDataset = {
            label: 'TUSD Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.tusdProduction || null,
            })),
            pointRadius: 0,
            backgroundColor: tusdDatasetColor,
            fill: true,
            hidden: true,
            // hidden: !hasExpectedProduction,
          };
          globEffIrradianceDataset = {
            label: 'Projected Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.globEffIrradiance,
            })),
            // backgroundColor: 'rgba(255, 181, 0, .3)',
            backgroundColor: globEffIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
          };
          actualIrradianceDataset = {
            label: 'Actual Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.actualIrradiance,
            })),
            backgroundColor: actualIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
          };
          irradianceDataCoverageDataset = {
            label: 'Irradiance Data Coverage',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.irradianceDataCoverage,
            })),
            backgroundColor: irradianceDataCoverageDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          inverterAvailabilityDataset = {
            label: 'Availability',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.inverterAvailability,
            })),
            backgroundColor: inverterAvailabilityDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          actualPRDataset = {
            label: 'Actual Performance Ratio',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.actualPR,
            })),
            backgroundColor: actualPRDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          avgModuleTemperatureDataset = {
            label: 'Actual Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.avgModuleTemperature,
            })),
            backgroundColor: avgModuleTemperatureDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
          };
          moduleTempRefDataset = {
            label: 'Projected Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'YYYY'),
              y: point.moduleTempRef,
            })),
            backgroundColor: moduleTempRefDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
          };
          xScale = {
            type: 'time',
            time: {
              tooltipFormat: 'YYYY',
              unit: 'year',
            },
          };
        } else if (energyTotalsBucketSize.current === 'month') {
          actualDataset = {
            label: 'Actual Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.actualProduction,
            })),
            pointRadius: 0,
            backgroundColor: actualDatasetColor,
            fill: true,
          };
          inverterDataset = {
            label: 'Inverter Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.inverterProduction,
            })),
            pointRadius: 0,
            backgroundColor: inverterDatasetColor,
            fill: true,
            hidden: true,
          };
          expectedDataset = {
            label: 'Expected Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.expectedProduction,
            })),
            pointRadius: 0,
            backgroundColor: expectedDatasetColor,
            fill: true,
            hidden: true,
            // hidden: !hasExpectedProduction,
          };
          p50Dataset = {
            label: 'P-50 Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.p50Production,
            })),
            pointRadius: 0,
            backgroundColor: p50DatasetColor,
          };
          tusdDataset = {
            label: 'TUSD Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.tusdProduction || null,
            })),
            pointRadius: 0,
            backgroundColor: tusdDatasetColor,
            fill: true,
            hidden: true,
            // hidden: !hasExpectedProduction,
          };
          globEffIrradianceDataset = {
            label: 'Projected Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.globEffIrradiance,
            })),
            // backgroundColor: 'rgba(255, 181, 0, .3)',
            backgroundColor: globEffIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
          };
          actualIrradianceDataset = {
            label: 'Actual Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.actualIrradiance,
            })),
            backgroundColor: actualIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
          };
          irradianceDataCoverageDataset = {
            label: 'Irradiance Data Coverage',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.irradianceDataCoverage,
            })),
            backgroundColor: irradianceDataCoverageDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          inverterAvailabilityDataset = {
            label: 'Availability',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.inverterAvailability,
            })),
            backgroundColor: inverterAvailabilityDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          actualPRDataset = {
            label: 'Actual Performance Ratio',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.actualPR,
            })),
            backgroundColor: actualPRDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          avgModuleTemperatureDataset = {
            label: 'Actual Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.avgModuleTemperature,
            })),
            backgroundColor: avgModuleTemperatureDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
          };
          moduleTempRefDataset = {
            label: 'Projected Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM YYYY'),
              y: point.moduleTempRef,
            })),
            backgroundColor: moduleTempRefDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
          };
          xScale = {
            type: 'time',
            time: {
              tooltipFormat: 'MMM YYYY',
              unit: 'month',
            },
          };
        } else if (energyTotalsBucketSize.current === 'week') {
          actualDataset = {
            label: 'Actual Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.actualProduction,
            })),
            pointRadius: 0,
            backgroundColor: actualDatasetColor,
            fill: true,
          };
          inverterDataset = {
            label: 'Inverter Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.inverterProduction,
            })),
            pointRadius: 0,
            backgroundColor: inverterDatasetColor,
            fill: true,
            hidden: true,
          };
          expectedDataset = {
            label: 'Expected Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.expectedProduction,
            })),
            pointRadius: 0,
            backgroundColor: expectedDatasetColor,
            fill: true,
            hidden: true,
            // hidden: !hasExpectedProduction,
          };
          p50Dataset = {
            label: 'P-50 Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.p50Production,
            })),
            pointRadius: 0,
            backgroundColor: p50DatasetColor,
            hidden: true,
          };
          tusdDataset = {
            label: 'TUSD Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.tusdProduction || null,
            })),
            pointRadius: 0,
            backgroundColor: tusdDatasetColor,
            fill: true,
            hidden: true,
            // hidden: !hasExpectedProduction,
          };
          globEffIrradianceDataset = {
            label: 'Projected Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.globEffIrradiance,
            })),
            // backgroundColor: 'rgba(255, 181, 0, .3)',
            backgroundColor: globEffIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
          };
          actualIrradianceDataset = {
            label: 'Actual Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.actualIrradiance,
            })),
            backgroundColor: actualIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
          };
          irradianceDataCoverageDataset = {
            label: 'Irradiance Data Coverage',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.irradianceDataCoverage,
            })),
            backgroundColor: irradianceDataCoverageDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          inverterAvailabilityDataset = {
            label: 'Availability',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.inverterAvailability,
            })),
            backgroundColor: inverterAvailabilityDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          actualPRDataset = {
            label: 'Actual Performance Ratio',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.actualPR,
            })),
            backgroundColor: actualPRDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
          };
          avgModuleTemperatureDataset = {
            label: 'Actual Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.avgModuleTemperature,
            })),
            backgroundColor: avgModuleTemperatureDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
          };
          moduleTempRefDataset = {
            label: 'Projected Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.moduleTempRef,
            })),
            backgroundColor: moduleTempRefDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
          };
          xScale = {
            type: 'time',
            time: {
              tooltipFormat: 'ddd, MMM D, YYYY',
              unit: 'week',
            },
            title: {
              display: true,
              text: 'Weeks (Sunday to Saturday)',
            },
          };
        } else {
          actualDataset = {
            label: 'Actual',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.actualProduction,
            })),
            pointRadius: 0,
            backgroundColor: actualDatasetColor,
            fill: true,
            order: 1000,
          };
          inverterDataset = {
            label: 'Inverter Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.inverterProduction,
            })),
            pointRadius: 0,
            backgroundColor: inverterDatasetColor,
            fill: true,
            hidden: true,
            order: 1000,
          };
          expectedDataset = {
            label: 'Expected',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.expectedProduction,
            })),
            pointRadius: 0,
            // backgroundColor: 'rgba(21, 48, 76, .4)',
            // backgroundColor: theme.palette.green.light,
            backgroundColor: expectedDatasetColor,
            // borderColor: theme.palette.green.main,
            fill: true,
            hidden: true,
            order: 1000,
            // hidden: !hasExpectedProduction,
          };
          p50Dataset = {
            label: 'P50 Projected',
            type: 'line',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.p50Production,
            })),
            pointRadius: 0,
            borderColor: p50DatasetColor,
            backgroundColor: 'rgba(0,0,0,0)',
            borderDash: [10, 5],
            order: 1,
          };
          tusdDataset = {
            label: 'TUSD Production',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.tusdInvoiceProduction,
            })),
            pointRadius: 0,
            backgroundColor: tusdDatasetColor,
            fill: true,
            hidden: true,
            order: 1000,
            // hidden: !hasExpectedProduction,
          };
          globEffIrradianceDataset = {
            label: 'Projected Irradiance',
            type: 'line',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.globEffIrradiance,
            })),
            pointRadius: 0,
            backgroundColor: 'rgba(0,0,0,0)',
            borderColor: globEffIrradianceDatasetColor,
            borderDash: [10, 5],
            yAxisID: 'yIrradiance',
            hidden: true,
            order: 2,
          };
          actualIrradianceDataset = {
            label: 'Actual Irradiance',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.actualIrradiance,
            })),
            backgroundColor: actualIrradianceDatasetColor,
            pointRadius: 0,
            yAxisID: 'yIrradiance',
            hidden: true,
            order: 1000,
          };
          irradianceDataCoverageDataset = {
            label: 'Irradiance Data Coverage',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.irradianceDataCoverage,
            })),
            backgroundColor: irradianceDataCoverageDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
            order: 1000,
          };
          inverterAvailabilityDataset = {
            label: 'Availability',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.inverterAvailability,
            })),
            backgroundColor: inverterAvailabilityDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
            order: 1000,
          };
          actualPRDataset = {
            label: 'Actual PR',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.actualPR,
            })),
            backgroundColor: actualPRDatasetColor,
            pointRadius: 0,
            yAxisID: 'yPercentage',
            hidden: true,
            order: 1000,
          };
          avgModuleTemperatureDataset = {
            label: 'Actual Module Temp',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.avgModuleTemperature,
            })),
            backgroundColor: avgModuleTemperatureDatasetColor,
            pointRadius: 0,
            yAxisID: 'yTemperature',
            hidden: true,
            order: 1000,
          };
          moduleTempRefDataset = {
            label: 'Projected Module Temp',
            type: 'line',
            data: productionTotals.map((point) => ({
              x: moment(point.date, 'MMM D, YYYY'),
              y: point.moduleTempRef,
            })),
            pointRadius: 0,
            borderColor: moduleTempRefDatasetColor,
            backgroundColor: 'rgba(0,0,0,0)',
            borderDash: [10, 5],
            order: 3,
            hidden: true,
            yAxisID: 'yTemperature',
          };
          xScale = {
            type: 'time',
            time: {
              tooltipFormat: 'MMM D, YYYY',
              unit: 'day',
            },
          };
        }

        if (productionTotals.length === 0) {
          generationChartJsx = (
            <Alert severity="warning" style={{ marginTop: '1rem' }}>
              No project production data for this time period.
            </Alert>
          );
        } else {
          generationChartJsx = (
            <Grid item xs={12} style={{ maxHeight: '700px' }}>
              <Bar
                height={500}
                data={{
                  datasets: [
                    actualDataset,
                    expectedDataset,
                    p50Dataset,
                    inverterDataset,
                    tusdDataset,
                    actualIrradianceDataset,
                    globEffIrradianceDataset,
                    irradianceDataCoverageDataset,
                    inverterAvailabilityDataset,
                    actualPRDataset,
                    avgModuleTemperatureDataset,
                    moduleTempRefDataset,
                  ],
                }}
                options={{
                  pointRadius: 100,
                  grouped: true,
                  maintainAspectRatio: false,
                  interaction: {
                    mode: 'index',
                  },
                  borderRadius: 4,
                  plugins: {
                    annotation: {
                      annotations: {
                        ...ticketAnnotations,
                      },
                    },
                    tooltip: {
                      callbacks: {
                        label: function (context) {
                          let label = context.dataset.label || '';
                          if (label) {
                            label += ': ';
                          }
                          if (
                            context.parsed.y !== null &&
                            [0, 1, 2, 3, 4].indexOf(context.datasetIndex) > -1
                          ) {
                            label += `${numeral(context.parsed.y).format(
                              '0,0'
                            )} kWh`;
                          } else if (
                            [5, 6].indexOf(context.datasetIndex) > -1
                          ) {
                            label += `${numeral(context.parsed.y).format(
                              '0,0.[00]'
                            )} kWh/m2`;
                          } else if (
                            [7, 8, 9].indexOf(context.datasetIndex) > -1
                          ) {
                            label += `${numeral(context.parsed.y).format(
                              '0,0.[00]'
                            )}%`;
                          } else if (
                            [10, 11].indexOf(context.datasetIndex) > -1
                          ) {
                            label += `${numeral(context.parsed.y).format(
                              '0,0.[0]'
                            )}°C`;
                          } else {
                            label += `${context.parsed.y}`;
                          }
                          if ([1, 2, 3].indexOf(context.datasetIndex) > -1) {
                            const actual =
                              actualDataset.data[context.dataIndex].y;
                            label += ` (${
                              !context.parsed.y
                                ? '∞'
                                : numeral(
                                    (actual - context.parsed.y) /
                                      context.parsed.y
                                  ).format('0,0.0%')
                            })`;
                          } else if (context.datasetIndex === 6) {
                            const actual =
                              actualIrradianceDataset.data[context.dataIndex].y;
                            label += ` (${
                              !context.parsed.y
                                ? '∞'
                                : numeral(
                                    (actual - context.parsed.y) /
                                      context.parsed.y
                                  ).format('0,0.0%')
                            })`;
                          }
                          return label;
                        },
                      },
                    },
                    legend: {
                      labels: {
                        filter: (legendItem, data) =>
                          !hideTUSD || legendItem.datasetIndex !== 4,
                      },
                    },
                  },
                  scales: {
                    x: xScale,
                    y: {
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0,0.[0]')} kWh`,
                      },
                    },
                    yIrradiance: {
                      title: {
                        display: true,
                        text: 'Irradiance (kWh/m2)',
                      },
                      grid: {
                        display: false,
                      },
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0,0.[0]')} kWh/m2`,
                      },
                      position: 'right',
                    },
                    yPercentage: {
                      title: {
                        display: true,
                        text: 'Data Coverage / Availability / PR (%)',
                      },
                      grid: {
                        display: false,
                      },
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0,0.[0]')}%`,
                      },
                      position: 'right',
                    },
                    yTemperature: {
                      title: {
                        display: true,
                        text: 'Temperature (°C)',
                      },
                      grid: {
                        display: false,
                      },
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0,0.[0]')} °C`,
                      },
                      position: 'right',
                    },
                  },
                }}
              />
            </Grid>
          );
        }
      }
    }

    const renderInverterHealthTable = () => {
      let bodyJsx = null;
      if (loading || !projectDetails?.inverters) {
        bodyJsx = (
          <TableBody>
            <TableRow>
              <TableCell
                colSpan={6}
                align="center"
                style={{ borderBottom: 'none' }}
              >
                <CircularProgress />
              </TableCell>
            </TableRow>
          </TableBody>
        );
      } else if (projectDetails.inverters.length === 0) {
        bodyJsx = (
          <TableBody>
            <TableRow>
              <TableCell colSpan={6} style={{ borderBottom: 'none' }}>
                <Alert severity="info">No inverters</Alert>
              </TableCell>
            </TableRow>
          </TableBody>
        );
      } else {
        const allInvertersHaveCapacity =
          projectDetails.inverters.filter((i) => i.capacity !== null).length ===
          projectDetails.inverters.length;
        let maxCapacityScaledInverterGeneration = 0;
        projectDetails.inverters.forEach((inverter) => {
          // Scale the max generation by inverter capacity
          const prod =
            inverter.mostRecentDailyInverterGeneration?.production || 0;
          const inverterCapacity = allInvertersHaveCapacity
            ? inverter.capacity
            : 1;
          maxCapacityScaledInverterGeneration = Math.max(
            maxCapacityScaledInverterGeneration,
            prod / inverterCapacity
          );
        });

        bodyJsx = (
          <TableBody>
            {projectDetails.inverters.map((inverter, index) => {
              const lastRow = index === projectDetails.inverters.length - 1;

              const inverterProd =
                inverter.mostRecentDailyInverterGeneration?.production || 0;
              const capacityScaledInverterProd =
                allInvertersHaveCapacity && inverterProd
                  ? inverterProd / inverter.capacity
                  : inverterProd;
              let performanceStyleColor = undefined;
              if (inverter.mostRecentDailyInverterGeneration?.production) {
                const performanceFactor =
                  (maxCapacityScaledInverterGeneration -
                    capacityScaledInverterProd) /
                  maxCapacityScaledInverterGeneration;
                if (performanceFactor === 0) {
                  performanceStyleColor = 'green';
                } else if (performanceFactor > 0.1) {
                  performanceStyleColor = 'red';
                } else if (performanceFactor > 0.05) {
                  performanceStyleColor = 'orange';
                }
              }
              let percentBelowTopPerformer = 0;
              if (maxCapacityScaledInverterGeneration !== 0) {
                percentBelowTopPerformer =
                  (maxCapacityScaledInverterGeneration -
                    capacityScaledInverterProd) /
                  maxCapacityScaledInverterGeneration;
              }
              return (
                <TableRow
                  key={`inverter-health-tbl-${projectDetails.id}-${inverter.id}`}
                >
                  <TableCell
                    style={{ borderBottom: lastRow ? 'none' : undefined }}
                  >
                    {inverter.name}
                  </TableCell>
                  <TableCell
                    align="center"
                    style={{
                      borderBottom: lastRow ? 'none' : undefined,
                      textAlign: 'center',
                    }}
                  >
                    <Grid container justifyContent="center">
                      <InverterIntegrityChecks inverterId={inverter.id} />
                    </Grid>
                  </TableCell>
                  <TableCell
                    align="center"
                    style={{ borderBottom: lastRow ? 'none' : undefined }}
                  >
                    {inverter.capacity
                      ? numeral(inverter.capacity).format('0,0')
                      : null}
                  </TableCell>
                  <TableCell
                    style={{ borderBottom: lastRow ? 'none' : undefined }}
                  >
                    <Grid
                      container
                      direction="column"
                      justifyContent="center"
                      alignItems="center"
                    >
                      <Grid item>
                        {inverter.mostRecentDailyInverterGeneration?.production
                          ? numeral(
                              inverter.mostRecentDailyInverterGeneration
                                .production / 1_000_000
                            ).format('0,0[.]00')
                          : null}
                      </Grid>
                      <Grid item>
                        <Typography variant="caption">
                          {`(${inverter.mostRecentDailyInverterGeneration?.date})`}
                        </Typography>
                      </Grid>
                    </Grid>
                  </TableCell>
                  <TableCell
                    align="right"
                    style={{
                      borderBottom: lastRow ? 'none' : undefined,
                      color: performanceStyleColor,
                      fontWeight: 'bold',
                    }}
                  >
                    {maxCapacityScaledInverterGeneration !== 0
                      ? numeral(percentBelowTopPerformer).format('%0,0[.]0')
                      : null}
                  </TableCell>
                  <TableCell
                    align="right"
                    style={{
                      borderBottom: lastRow ? 'none' : undefined,
                    }}
                  >
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddCircle />}
                      onClick={() =>
                        createOMTicket({
                          omTicketTypeId: 1,
                          startDt: moment(
                            inverter.mostRecentDailyInverterGeneration?.date ||
                              new Date()
                          ).format('YYYY-MM-DD HH:mm:ss'),
                          projectId: projectDetails.id,
                          title: `${inverter.name} underperforming`,
                          notes: `${inverter.name} is underperforming relative to this project's other inverters.`,
                          userId: permissions?.id || null,
                          deviceName: inverter.name,
                          estimatedPercentageLoss:
                            percentBelowTopPerformer *
                            (1 / projectDetails.inverters.length),
                          internalOnlyFlg: false,
                          clientNotificationRequiredFlg: false,
                        })
                      }
                    >
                      Create Ticket
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        );
      }
      return (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>
                  <b>Inverter</b>
                </TableCell>
                <TableCell align="center">
                  <b>Comms / Integrity</b>
                </TableCell>
                <TableCell>
                  <b>DC Power Capacity (Wp)</b>
                </TableCell>
                <TableCell align="center">
                  <b>Last Day's Generation (MWh)</b>
                </TableCell>
                <TableCell align="right">
                  <b>% Below Top Performer</b>
                </TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            {bodyJsx}
          </Table>
        </TableContainer>
      );
    };

    const temp = projectDetails?.currentWeatherData?.main?.temp;
    // const tempMin = projectDetails?.currentWeatherData?.main?.temp_min;
    // const tempMax = projectDetails?.currentWeatherData?.main?.temp_max;
    const description =
      projectDetails?.currentWeatherData?.weather?.[0]?.description;
    const icon = projectDetails?.currentWeatherData?.weather?.[0]?.icon;
    const showWeatherSummary = temp && description && icon;
    const speed = projectDetails?.currentWeatherData?.wind?.speed;
    const gust = projectDetails?.currentWeatherData?.wind?.gust;
    const deg = projectDetails?.currentWeatherData?.wind?.deg;
    const lastHour = projectDetails?.currentWeatherData?.rain?.lastHour;
    const cloudCoverage = projectDetails?.currentWeatherData?.clouds?.all;
    return (
      <Grid container style={{ width: '100%' }}>
        <Grid item container xs={12} justifyContent="flex-end">
          {renderPowerChartCustomizeDialog()}
          {renderEnergyChartCustomizeDialog()}
        </Grid>
        <Grid container justifyContent="flex-end" style={{ margin: '1rem 0' }}>
          <Card
            style={{
              width: '100%',
              borderRadius: theme.shape.borderRadius,
            }}
          >
            <CardContent style={{ padding: '1rem 3rem' }}>
              <Grid container alignContent="center" style={{ width: '100%' }}>
                <Grid item xs={12} style={{ textAlign: 'right' }}>
                  <ToggleButtonGroup
                    value={measurementSystem}
                    exclusive
                    size="small"
                    onChange={(event) => {
                      const { value } = event.currentTarget;
                      setMeasurementSystem(value);
                    }}
                    aria-label="Measurement system"
                  >
                    <ToggleButton
                      value="imperial"
                      aria-label="Use Imperial measurement system"
                    >
                      Imperial
                    </ToggleButton>
                    <ToggleButton
                      value="metric"
                      aria-label="Use Metric measurement system"
                    >
                      Metric
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Grid>
              </Grid>
              <Grid
                container
                style={{ width: '100%' }}
                spacing={2}
                alignItems="center"
                justifyContent="space-evenly"
              >
                {showWeatherSummary && (
                  <>
                    <Grid item>
                      <WeatherSummary
                        temp={temp}
                        description={description}
                        icon={icon}
                        measurementSystem={measurementSystem}
                      />
                    </Grid>
                    <Grid item>
                      <Divider
                        orientation="vertical"
                        style={{ height: '160px' }}
                      />
                    </Grid>
                  </>
                )}
                {showWeatherSummary && (
                  <>
                    <Grid item>
                      <WindSummary
                        speed={speed}
                        gust={gust}
                        deg={deg}
                        measurementSystem={measurementSystem}
                      />
                    </Grid>
                    <Grid item>
                      <Divider
                        orientation="vertical"
                        style={{ height: '160px' }}
                      />
                    </Grid>
                  </>
                )}
                {showWeatherSummary && (
                  <>
                    <Grid item>
                      <RainSummary
                        lastHour={lastHour || 0}
                        measurementSystem={measurementSystem}
                      />
                    </Grid>
                    <Grid item>
                      <Divider
                        orientation="vertical"
                        style={{ height: '160px' }}
                      />
                    </Grid>
                  </>
                )}
                {showWeatherSummary && (
                  <>
                    <Grid item>
                      <CloudSummary cloudCoverage={cloudCoverage || 0} />
                    </Grid>
                    <Grid item>
                      <Divider
                        orientation="vertical"
                        style={{ height: '160px' }}
                      />
                    </Grid>
                  </>
                )}
                <Grid item>
                  <Daylight
                    timezone={projectDetails.timezone}
                    sunrise={projectDetails.sunrise}
                    sunset={projectDetails.sunset}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12}>
          <Grid container>
            <Grid item xs={12} style={{ marginBottom: '2rem' }}>
              <Grid
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography gutterBottom variant="h6">
                    Snapshot Data :{' '}
                    {snapshotDataLoading ? (
                      <CircularProgress
                        style={{
                          position: 'absolute',
                          marginLeft: '1rem',
                          height: '2rem',
                          width: '2rem',
                        }}
                      />
                    ) : null}
                  </Typography>
                </Grid>
                <Grid item>
                  <Grid container alignItems="center">
                    <Grid item style={{ paddingRight: '1rem' }}>
                      <Typography>
                        {presetTimeFrame === 'Custom' && fromDt && toDt
                          ? `${moment(fromDt, 'yyyy-MM-DD').format(
                              'MMM DD, YYYY'
                            )} - ${moment(toDt, 'yyyy-MM-DD').format(
                              'MMM DD, YYYY'
                            )}`
                          : presetTimeFrame}
                      </Typography>
                    </Grid>
                    <Grid item>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<GetApp />}
                        style={{ textTransform: 'none' }}
                        onClick={() => exportProjectSnapshotData()}
                        disabled={!productionChartData || !projectDetails}
                      >
                        Export
                      </Button>
                    </Grid>
                    <Grid item>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<Settings />}
                        style={{ textTransform: 'none', marginLeft: '1rem' }}
                        onClick={() => setOpenCustomizePowerChartDialog(true)}
                      >
                        Customize
                      </Button>
                    </Grid>
                    <IconButton
                      style={{ marginLeft: '1rem' }}
                      variant="contained" // Add the variant prop with value "contained"
                      color="primary"
                      onClick={() =>
                        fetchPowerChartData({
                          projectId: project.id,
                          inverterIds: selectedInverterIds,
                          sensorIds: [
                            ...selectedSensorIds,
                            ...selectedTrackerIds,
                            ...selectedMeterIds,
                          ],
                          fetchSiteGeneration: overlayGeneration,
                        })
                      }
                    >
                      <Refresh />
                    </IconButton>
                  </Grid>
                </Grid>
              </Grid>
              {powerChartJsx}
            </Grid>
            {!hideInverterHealth && (
              <Grid item xs={12} style={{ padding: '2rem 0' }}>
                <Grid container>
                  <Grid item xs={12}>
                    <Accordion
                      style={{
                        borderRadius: theme.shape.borderRadius,
                      }}
                    >
                      <AccordionSummary expandIcon={<ExpandMore />}>
                        <Typography variant="h6">Inverter Health :</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        {renderInverterHealthTable()}
                      </AccordionDetails>
                    </Accordion>
                  </Grid>
                </Grid>
              </Grid>
            )}
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
                <Typography gutterBottom variant="h6">
                  Generation :{' '}
                  {generationDataLoading ? (
                    <CircularProgress
                      style={{
                        position: 'absolute',
                        marginLeft: '1rem',
                        height: '2rem',
                        width: '2rem',
                      }}
                    />
                  ) : null}
                </Typography>
              </Grid>
              <Grid item>
                <Grid container>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<GetApp />}
                      style={{ textTransform: 'none' }}
                      onClick={() => exportProjectGenerationData()}
                      disabled={projectGenerationReportLoading}
                    >
                      {projectGenerationReportLoading ? (
                        <CircularProgress
                          style={{ position: 'absolute', color: 'white' }}
                        />
                      ) : null}
                      Export
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<Settings />}
                      style={{ textTransform: 'none', marginLeft: '1rem' }}
                      disabled={loading}
                      onClick={() => setOpenCustomizeEnergyChartDialog(true)}
                    >
                      Customize
                    </Button>
                  </Grid>
                  <Grid item>
                    <IconButton
                      style={{ marginLeft: '1rem' }}
                      color="primary"
                      onClick={() => fetchGenerationChartData(project.id)}
                    >
                      <Refresh />
                    </IconButton>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              {generationChartJsx}
            </Grid>
            <Grid item xs={12} style={{ marginBottom: '1rem' }}>
              <Grid
                container
                justifyContent="space-between"
                alignItems="center"
                style={{ margin: '1rem 0' }}
              >
                <Grid item>
                  <LegendItem label="Open Ticket" color="lightyellow" />
                </Grid>
                <Grid item>
                  {hasWriteAccess ? (
                    <Button
                      startIcon={<AddCircle />}
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        setCreateTicketOpen(true);
                      }}
                    >
                      Create Ticket
                    </Button>
                  ) : null}
                </Grid>
              </Grid>
              {tickets && tickets.length > 0 ? (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell style={{ minWidth: '82px' }}>
                          Ticket #
                        </TableCell>
                        <TableCell>Ticket Type</TableCell>
                        <TableCell>Equipment Item</TableCell>
                        <TableCell>Device Name</TableCell>
                        <TableCell>Title</TableCell>
                        <TableCell>Issue Start</TableCell>
                        <TableCell>Issue Closed</TableCell>
                        {/* <TableCell>Acknowledged Date</TableCell> */}
                        <TableCell align="right">
                          Effect on Generation (%)
                        </TableCell>
                        <TableCell align="right">
                          Est. Generation Loss (kWh)
                        </TableCell>
                        <TableCell align="right">Est. Revenue Loss</TableCell>
                        {hasWriteAccess ? <TableCell /> : null}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {tickets &&
                        tickets.map((ticket) => (
                          <TableRow
                            key={`ticket-${ticket.id}`}
                            style={{
                              backgroundColor:
                                ticket.endDt &&
                                moment(ticket.endDt).isBefore(
                                  moment().endOf('day')
                                )
                                  ? null
                                  : 'lightyellow',
                            }}
                          >
                            <TableCell>{ticket?.id}</TableCell>
                            <TableCell>{ticket?.omTicketType?.name}</TableCell>
                            <TableCell>
                              {ticket?.equipmentItem?.label}
                            </TableCell>
                            <TableCell>{ticket.deviceName}</TableCell>
                            <TableCell>{ticket.title}</TableCell>
                            <TableCell
                              style={
                                ticket.startDt &&
                                ticket.endDt &&
                                ticket.endDt < ticket.startDt
                                  ? {
                                      color: theme.palette.error.main,
                                      fontWeight: 'bold',
                                    }
                                  : {}
                              }
                            >
                              {moment(ticket.startDt).format('MMM D, h:mma')}
                            </TableCell>
                            <TableCell
                              style={
                                ticket.startDt &&
                                ticket.endDt &&
                                ticket.endDt < ticket.startDt
                                  ? {
                                      color: theme.palette.error.main,
                                      fontWeight: 'bold',
                                    }
                                  : {}
                              }
                            >
                              {ticket.endDt &&
                                moment(ticket.endDt).format('MMM D, h:mma')}
                            </TableCell>
                            <TableCell align="right">
                              {`${numeral(
                                ticket.estimatedPercentageLoss || 0
                              ).format('0[.]0')}%`}
                            </TableCell>
                            <TableCell align="right">
                              {`${numeral(
                                ticket.estimatedImpact
                                  ?.estimatedGenerationLoss || 0
                              ).format('0,0')}`}
                            </TableCell>
                            <TableCell align="right">
                              {`${numeral(
                                ticket.estimatedImpact
                                  ?.estimatedRevenueLossUSD || 0
                              ).format('$0,0')}`}
                            </TableCell>
                            {hasWriteAccess ? (
                              <TableCell
                                align="right"
                                style={{ minWidth: '114px' }}
                              >
                                <Tooltip arrow title="Edit Ticket">
                                  <IconButton
                                    onClick={() => {
                                      setSelectedTicket(ticket);
                                      setUpdateTicketOpen(true);
                                    }}
                                  >
                                    <Edit />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip arrow title="Delete Ticket">
                                  <IconButton
                                    onClick={() => {
                                      deleteOMTicket(ticket.id);
                                    }}
                                  >
                                    <Delete
                                      style={{
                                        color: theme.palette.error.main,
                                      }}
                                    />
                                  </IconButton>
                                </Tooltip>
                              </TableCell>
                            ) : null}
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>{' '}
                </TableContainer>
              ) : null}
              {updateTicketOpen ? (
                <OMTicketDialog
                  action="update"
                  projectId={project.id}
                  projectInstallationTypeId={project.installationTypeId}
                  omTicket={selectedTicket}
                  onClose={() => {
                    setUpdateTicketOpen(false);
                    setSelectedTicket(null);
                    fetchGenerationChartData(project.id);
                  }}
                />
              ) : null}
              {createTicketOpen ? (
                <OMTicketDialog
                  action="create"
                  projectId={project.id}
                  projectInstallationTypeId={project.installationTypeId}
                  onClose={() => {
                    setCreateTicketOpen(false);
                    fetchGenerationChartData(project.id);
                  }}
                />
              ) : null}
            </Grid>
            {projectDetails.tusdInvoices &&
            projectDetails.tusdInvoices.length > 0 ? (
              <Grid container>
                <Grid item xs={12}>
                  <Accordion
                    style={{
                      borderRadius: theme.shape.borderRadius,
                    }}
                  >
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="h6">TUSD Invoices :</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List dense>
                        {projectDetails.tusdInvoices
                          .sort((a, b) => {
                            if (a.accountYear < b.accountYear) return 1;
                            else if (a.accountYear > b.accountYear) return -1;
                            else
                              return a.accountMonth > b.accountMonth ? -1 : 1;
                          })
                          .map((invoice) => (
                            <Tooltip
                              arrow
                              title={
                                hasWriteAccess
                                  ? 'Click to edit TUSD Invoice'
                                  : null
                              }
                              key={`invoice-li-${invoice.id}`}
                            >
                              <ListItem
                                component={
                                  invoice.downloadUrl || hasWriteAccess
                                    ? Button
                                    : null
                                }
                                href={
                                  hasWriteAccess
                                    ? `/TUSDInvoice/${invoice.id}`
                                    : invoice.downloadUrl
                                }
                                target="_blank"
                                style={{ textTransform: 'none' }}
                              >
                                <ListItemText
                                  primary={`${moment()
                                    .month(invoice.accountMonth - 1)
                                    .format('MMMM')} ${
                                    invoice.accountYear
                                  } Invoice`}
                                  secondary={`Month Energy Injected: ${numeral(
                                    invoice.totalGeneratedEnergy
                                  ).format('0,0[.]0')} kWh`}
                                />
                                {invoice.downloadUrl && (
                                  <ListItemSecondaryAction>
                                    <IconButton
                                      disabled={!invoice.downloadUrl}
                                      href={invoice.downloadUrl}
                                      size="large"
                                    >
                                      <PictureAsPdf />
                                    </IconButton>
                                  </ListItemSecondaryAction>
                                )}
                              </ListItem>
                            </Tooltip>
                          ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              </Grid>
            ) : null}
          </Grid>
        </Grid>
      </Grid>
    );
  };

  const renderEquipmentInfo = (loading) => {
    return (
      <Grid container style={{ width: '100%' }} direction="column">
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Equipment:
          </Typography>
          <Button
            size="small"
            color="primary"
            variant="contained"
            component={Link}
            to={`/Project/${projectDetails.id}/Inventory`}
            startIcon={<Edit />}
          >
            Edit Equipment Inventory
          </Button>
        </Grid>
        {projectEquipmentData?.projectEquipmentItems &&
        projectEquipmentData.projectEquipmentItems.length > 0 ? (
          <Grid item>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Qty</TableCell>
                    <TableCell>Spare Parts?</TableCell>
                    <TableCell>Model</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {projectEquipmentData.projectEquipmentItems.map(
                    (projectEquipmentItem) => (
                      <TableRow
                        key={`table-row-project-equipment_item-${projectEquipmentItem.id}`}
                      >
                        <TableCell>
                          <Tooltip
                            arrow
                            title={
                              projectEquipmentItem.equipmentItem?.equipmentType
                                ?.name
                            }
                          >
                            <Avatar
                              alt={
                                projectEquipmentItem.equipmentItem
                                  ?.equipmentType?.name
                              }
                            >
                              <Icon
                                style={{
                                  width: '1.5rem',
                                  textAlign: 'center',
                                  color: '#fff',
                                }}
                                className={
                                  projectEquipmentItem.equipmentItem
                                    .equipmentType?.iconClass
                                }
                              />
                            </Avatar>
                          </Tooltip>
                        </TableCell>
                        <TableCell>{projectEquipmentItem.quantity}</TableCell>
                        <TableCell>
                          {projectEquipmentItem.sparePartFlg ? <Check /> : null}
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {projectEquipmentItem?.equipmentItem.model}
                          </Typography>
                          <Typography variant="body2">
                            {projectEquipmentItem?.equipmentItem.manufacturer}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        ) : equipmentLoading ? (
          <Grid
            container
            spacing={2}
            justifyContent="center"
            style={{ width: '100%' }}
          >
            <Grid item>
              <CircularProgress />
            </Grid>
          </Grid>
        ) : (
          <Alert severity="info">
            No equipment has been added yet.{' '}
            <a href={`/Project/${projectDetails.id}/Inventory`}>Click here</a>{' '}
            to add equipment to this project.
          </Alert>
        )}
      </Grid>
    );
  };

  const renderProjectCredentials = () => {
    return (
      <Grid container style={{ width: '100%' }} direction="column">
        <Grid
          container
          item
          style={{ paddingTop: '2rem' }}
          justifyContent="space-between"
          alignItems="center"
        >
          <Grid item>
            <Typography variant="h6" gutterBottom>
              Credentials:
            </Typography>
          </Grid>
          <Grid item>
            <Button
              color="primary"
              variant="contained"
              component={Link}
              to={`/Project/${projectDetails.id}/O&M`}
            >
              Add Credential
            </Button>
          </Grid>
        </Grid>
        {omPlatformCredentialsData?.omPlatformCredentials &&
        omPlatformCredentialsData.omPlatformCredentials.length > 0 ? (
          <Grid item>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <b>Platform</b>
                  </TableCell>
                  <TableCell>
                    <b>URL</b>
                  </TableCell>
                  <TableCell>
                    <b>Username</b>
                  </TableCell>
                  <TableCell>
                    <b>Password</b>
                  </TableCell>
                  <TableCell>
                    <b>Notes</b>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {omPlatformCredentialsData.omPlatformCredentials.map(
                  (omPlatformCredential) => (
                    <TableRow
                      key={`table-row-om-platform-creds-${omPlatformCredential.id}`}
                    >
                      <TableCell>{omPlatformCredential.name}</TableCell>
                      <TableCell>
                        <a href={projectDetails.omPlatformUrl} target="_blank">
                          {omPlatformCredential.url}
                        </a>
                      </TableCell>
                      <TableCell>{omPlatformCredential.username}</TableCell>
                      {/* TODO: make this its own component */}
                      <PasswordTableCell
                        password={omPlatformCredential.password}
                      />
                      <TableCell>
                        <div
                          dangerouslySetInnerHTML={{
                            __html: omPlatformCredential.notes,
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
          </Grid>
        ) : loading ? (
          <CircularProgress />
        ) : (
          <Alert severity="info">
            No platform login information has been provided.{' '}
            <a href={`/Project/${projectDetails.id}/O&M`}>Click here</a> to
            upload login credentials.
          </Alert>
        )}
      </Grid>
    );
  };

  const renderProjectContactInfo = () => {
    return (
      <Grid container style={{ width: '100%' }} direction="column">
        <Grid container item justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="h6" gutterBottom>
              Contacts:
            </Typography>
          </Grid>
          <Grid item>
            <Button
              color="primary"
              variant="contained"
              component={Link}
              to={`/Project/${projectDetails.id}/O&M`}
            >
              Add Contact
            </Button>
          </Grid>
        </Grid>
        {projectContactData?.projectContacts &&
        projectContactData.projectContacts.length > 0 ? (
          <Grid item>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <b>Name</b>
                  </TableCell>
                  <TableCell>
                    <b>Email</b>
                  </TableCell>
                  <TableCell>
                    <b>Phone</b>
                  </TableCell>
                  <TableCell>
                    <b>Title</b>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {projectContactData.projectContacts.map((contact) => (
                  <TableRow key={`table-row-project-contact-${contact.id}`}>
                    <TableCell>{contact.primaryContactName}</TableCell>
                    <TableCell>{contact.email}</TableCell>
                    <TableCell>{contact.phone}</TableCell>
                    <TableCell>{contact.name}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Grid>
        ) : loading ? (
          <CircularProgress />
        ) : (
          <Alert severity="info">
            No project contact information has been provided.{' '}
            <a href={`/Project/${projectDetails.id}/O&M`}>Click here</a> to add
            project contacts.
          </Alert>
        )}
      </Grid>
    );
  };

  const renderProjectAlarms = () => {
    const getDurationString = (dt1, dt2) => {
      const duration = moment.duration(moment(dt2).diff(moment(dt1)));
      const days = Math.floor(duration.asDays());
      const hours = Math.floor(duration.asHours()) % 24;
      const minutes = Math.floor(duration.asMinutes()) % 60;
      return `${days} days, ${hours} hrs, ${minutes} mins`;
    };
    if (!alarms) {
      return (
        <CircularProgress
          style={{
            position: 'absolute',
            // marginLeft: '1rem',
            height: '2rem',
            width: '2rem',
          }}
        />
      );
    }
    return (
      <Grid container direction="column">
        <Grid container item justifyContent="space-between">
          <Grid item>
            <Typography variant="h6" gutterBottom>
              {`${alarmStatus === 'open' ? 'Open' : 'Closed'} Alarms:`}
            </Typography>
          </Grid>
          <Grid item>
            <Select
              label="alarm filter"
              value={alarmStatus}
              onChange={(event) => {
                setAlarmListPage(0);
                setAlarmStatus(event.target.value);
                fetchAlarms({
                  pageNum: 0,
                  perPage: alarmsPerPage,
                  alarmType: event.target.value,
                });
              }}
              style={{ width: '100px' }}
            >
              <MenuItem value="open">Open</MenuItem>
              <MenuItem value="closed">Closed</MenuItem>
            </Select>
          </Grid>
        </Grid>
        {alarms.length > 0 ? (
          <>
            {project.scadaSystem ? (
              <Grid container item justifyContent="flex-end">
                <Button
                  onClick={handleSyncAlarms}
                  color="primary"
                  variant="contained"
                >
                  Refresh Alarm Status
                </Button>
              </Grid>
            ) : null}
            <Grid item>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Title</b>
                    </TableCell>
                    <TableCell>
                      <b>Description</b>
                    </TableCell>
                    <TableCell>
                      <b>Opened Date</b>
                    </TableCell>
                    <TableCell>
                      <b>Acknowledged Date</b>
                    </TableCell>
                    <TableCell>
                      <b>Closed Date</b>
                    </TableCell>
                    {alarmStatus === 'closed' ? (
                      <TableCell>
                        <b>Duration</b>
                      </TableCell>
                    ) : null}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {alarms.map((alarm) => (
                    <TableRow
                      key={`table-row-project-${alarmStatus}-alert-${alarm.id}`}
                    >
                      <TableCell>{alarm.alertType.name}</TableCell>
                      <TableCell>{alarm.lintedDescription}</TableCell>
                      <TableCell>
                        {moment(alarm.openedDt).format(
                          'MMM DD, YYYY hh:mm:ss a Z'
                        )}
                      </TableCell>
                      <TableCell style={{ padding: '6px 16px' }}>
                        {alarm.acknowledgedDt ? (
                          <ListItemText
                            primary={
                              <Typography variant="body2">
                                {moment(alarm.acknowledgedDt).format(
                                  'MMM DD, YYYY hh:mm:ss a Z'
                                )}
                              </Typography>
                            }
                            secondary={`by: ${
                              alarm.acknowledgedByEmployee
                                ? alarm.acknowledgedByEmployee.fullName
                                : 'System'
                            }`}
                          />
                        ) : (
                          <Button
                            color="primary"
                            variant="contained"
                            onClick={() => handleAcknowledgeAlarm(alarm)}
                          >
                            Acknowledge
                          </Button>
                        )}
                      </TableCell>
                      <TableCell>
                        {alarm.closedDt ? (
                          <ListItemText
                            primary={
                              <Typography variant="body2">
                                {moment(alarm.closedDt).format(
                                  'MMM DD, YYYY hh:mm:ss a Z'
                                )}
                              </Typography>
                            }
                            secondary={`by: ${
                              alarm.closedByEmployee
                                ? alarm.closedByEmployee.fullName
                                : 'System'
                            }`}
                          />
                        ) : (
                          <Button
                            color="primary"
                            variant="contained"
                            onClick={() => handleCloseAlarm(alarm)}
                          >
                            Close
                          </Button>
                        )}
                      </TableCell>
                      {alarmStatus === 'closed' ? (
                        <TableCell>
                          {alarm.openedDt && alarm.closedDt
                            ? getDurationString(alarm.openedDt, alarm.closedDt)
                            : null}
                        </TableCell>
                      ) : null}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <TablePagination
                rowsPerPageOptions={[10, 25, 50]}
                rowsPerPage={alarmsPerPage}
                component="div"
                count={alarmListCount}
                page={alarmListPage}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </Grid>
          </>
        ) : (
          <Alert severity="info">
            {`There are no ${alarmStatus} alarms for this project.`}
          </Alert>
        )}
      </Grid>
    );
  };

  const renderProjectFinance = (loading) => {
    const height = window.innerHeight;
    const width = height * 1.3;
    const loadingComp = (
      <Grid container>
        <Grid item xs={12}>
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="31.25rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </Grid>
      </Grid>
    );

    return (
      <Grid container direction="column">
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Finance:{' '}
            {projectFinanceDataLoading ? (
              <CircularProgress
                style={{
                  position: 'absolute',
                  marginLeft: '1rem',
                  height: '2rem',
                  width: '2rem',
                }}
              />
            ) : null}
          </Typography>
        </Grid>
        {projectFinanceDataLoading ? (
          loadingComp
        ) : (
          <>
            <Alert severity="info">Under Development</Alert>
          </>
        )}
      </Grid>
    );
    {
      /*
    return (
      <Grid container direction="column">
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Cameras:{' '}
            {ipCameraLoading ? (
              <CircularProgress
                style={{
                  position: 'absolute',
                  marginLeft: '1rem',
                  height: '2rem',
                  width: '2rem',
                }}
              />
            ) : null}
          </Typography>
        </Grid>
        {ipCameraLoading ? (
          loadingComp
        ) : (
          <>
            <div>
              <Dialog
                open={!!cameraFeedIFrameUrl}
                onClose={() => setCameraFeedIFrameUrl(null)}
                maxWidth={false}
              >
                {cameraFeedIFrameUrl ? (
                  <iframe
                    width={String(width)}
                    height={String(height)}
                    src={cameraFeedIFrameUrl}
                    frameborder="0"
                    allowfullscreen
                  ></iframe>
                ) : null}
              </Dialog>
            </div>
            {projectIpCameraData?.ipCameras?.length > 0 ? (
              <Grid container spacing={1}>
                {projectIpCameraData.ipCameras.map((camera) => {
                  return (
                    <Grid
                      item
                      xs={12}
                      md={3}
                      xl={2}
                      key={`site-ip-camera-${camera.id}`}
                    >
                      <Card
                        style={{ cursor: camera.iframeUrl ? 'pointer' : null }}
                        onClick={() => {
                          setCameraFeedIFrameUrl(camera.iframeUrl);
                        }}
                      >
                        <CardMedia
                          style={{ height: '200px' }}
                          title={`${camera.name} thumbnail image`}
                        >
                          <Image
                            style={{
                              objectFit: 'cover',
                              width: '100%',
                              height: '100%',
                              // maxHeight: '285px'
                            }}
                            title={`camera thumbnail ${camera.name} banner`}
                            crop="scale"
                            cloud_name={Config.cloud_name}
                            publicId={
                              camera.thumbnailPublicId || 'site/missing_image'
                            }
                          >
                            <Transformation width="200" crop="scale" />
                          </Image>
                          {camera.iframeUrl ? (
                            <PlayArrow
                              style={{
                                zIndex: 7,
                                position: 'relative',
                                // marginTop: '28px',
                                // marginLeft: '-128px',
                                left: '30%',
                                top: '-84%',
                                transition: 'color .5s',
                                color: 'rgba(255,255,255,.7)',
                                fontSize: '8rem',
                              }}
                            />
                          ) : (
                            <Block
                              style={{
                                zIndex: 7,
                                position: 'relative',
                                // marginTop: '28px',
                                // marginLeft: '-128px',
                                left: '30%',
                                top: '-84%',
                                transition: 'color .5s',
                                color: 'rgba(255,255,255,.7)',
                                fontSize: '8rem',
                              }}
                            />
                          )}
                        </CardMedia>
                        <CardContent>
                          <Grid container justifyContent="space-between">
                            <Grid item>
                              <Typography style={{ fontWeight: 'bold' }}>
                                {camera.name}
                              </Typography>
                            </Grid>
                            <Grid item>
                              <IconButton
                                component={Link}
                                to={`/IPCamera/${camera.id}`}
                                style={{ padding: '0px' }}
                                size="large"
                              >
                                <Edit />
                              </IconButton>
                            </Grid>
                          </Grid>
                          <Typography>{camera.description}</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            ) : (
              <Alert severity="info">
                There are currently no cameras for this project.
              </Alert>
            )}
          </>
        )}
      </Grid>
    ); */
    }
  };
  const renderProjectImpact = (loading) => {
    const height = window.innerHeight;
    const width = height * 1.3;
    const loadingComp = (
      <Grid container>
        <Grid item xs={12}>
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="31.25rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </Grid>
      </Grid>
    );

    return (
      <Grid container direction="column">
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Environmental Impact:{' '}
            {projectFinanceDataLoading ? (
              <CircularProgress
                style={{
                  position: 'absolute',
                  marginLeft: '1rem',
                  height: '2rem',
                  width: '2rem',
                }}
              />
            ) : null}
          </Typography>
        </Grid>
        {projectFinanceDataLoading ? (
          loadingComp
        ) : (
          <>
            <Alert severity="info">Under Development</Alert>
          </>
        )}
      </Grid>
    );
    {
      /*
    return (
      <Grid container direction="column">
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Cameras:{' '}
            {ipCameraLoading ? (
              <CircularProgress
                style={{
                  position: 'absolute',
                  marginLeft: '1rem',
                  height: '2rem',
                  width: '2rem',
                }}
              />
            ) : null}
          </Typography>
        </Grid>
        {ipCameraLoading ? (
          loadingComp
        ) : (
          <>
            <div>
              <Dialog
                open={!!cameraFeedIFrameUrl}
                onClose={() => setCameraFeedIFrameUrl(null)}
                maxWidth={false}
              >
                {cameraFeedIFrameUrl ? (
                  <iframe
                    width={String(width)}
                    height={String(height)}
                    src={cameraFeedIFrameUrl}
                    frameborder="0"
                    allowfullscreen
                  ></iframe>
                ) : null}
              </Dialog>
            </div>
            {projectIpCameraData?.ipCameras?.length > 0 ? (
              <Grid container spacing={1}>
                {projectIpCameraData.ipCameras.map((camera) => {
                  return (
                    <Grid
                      item
                      xs={12}
                      md={3}
                      xl={2}
                      key={`site-ip-camera-${camera.id}`}
                    >
                      <Card
                        style={{ cursor: camera.iframeUrl ? 'pointer' : null }}
                        onClick={() => {
                          setCameraFeedIFrameUrl(camera.iframeUrl);
                        }}
                      >
                        <CardMedia
                          style={{ height: '200px' }}
                          title={`${camera.name} thumbnail image`}
                        >
                          <Image
                            style={{
                              objectFit: 'cover',
                              width: '100%',
                              height: '100%',
                              // maxHeight: '285px'
                            }}
                            title={`camera thumbnail ${camera.name} banner`}
                            crop="scale"
                            cloud_name={Config.cloud_name}
                            publicId={
                              camera.thumbnailPublicId || 'site/missing_image'
                            }
                          >
                            <Transformation width="200" crop="scale" />
                          </Image>
                          {camera.iframeUrl ? (
                            <PlayArrow
                              style={{
                                zIndex: 7,
                                position: 'relative',
                                // marginTop: '28px',
                                // marginLeft: '-128px',
                                left: '30%',
                                top: '-84%',
                                transition: 'color .5s',
                                color: 'rgba(255,255,255,.7)',
                                fontSize: '8rem',
                              }}
                            />
                          ) : (
                            <Block
                              style={{
                                zIndex: 7,
                                position: 'relative',
                                // marginTop: '28px',
                                // marginLeft: '-128px',
                                left: '30%',
                                top: '-84%',
                                transition: 'color .5s',
                                color: 'rgba(255,255,255,.7)',
                                fontSize: '8rem',
                              }}
                            />
                          )}
                        </CardMedia>
                        <CardContent>
                          <Grid container justifyContent="space-between">
                            <Grid item>
                              <Typography style={{ fontWeight: 'bold' }}>
                                {camera.name}
                              </Typography>
                            </Grid>
                            <Grid item>
                              <IconButton
                                component={Link}
                                to={`/IPCamera/${camera.id}`}
                                style={{ padding: '0px' }}
                                size="large"
                              >
                                <Edit />
                              </IconButton>
                            </Grid>
                          </Grid>
                          <Typography>{camera.description}</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            ) : (
              <Alert severity="info">
                There are currently no cameras for this project.
              </Alert>
            )}
          </>
        )}
      </Grid>
    ); */
    }
  };

  return (
    <TableRow>
      <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={16}>
        <Collapse in={open} timeout="auto" unmountOnExit>
          <Grid style={{ padding: '2rem' }}>
            <Tabs
              value={selectedTab}
              onChange={(event, newValue) => setSelectedTab(newValue)}
              centered
            >
              <Tab
                label={
                  <Tooltip
                    arrow
                    open={!!project.openOMTicketsCount ? undefined : false}
                    title={`${project.openOMTicketsCount} open tickets (see yellow-highlighted tickets below).`}
                  >
                    <Badge
                      badgeContent={project.openOMTicketsCount}
                      color="error"
                    >
                      Generation
                    </Badge>
                  </Tooltip>
                }
                value="generation"
              />
              <Tab label="Contacts" value="contacts" />
              {hasWriteAccess && (
                <Tab label="Credentials" value="credentials" />
              )}
              <Tab label="Equipment" value="equipment" />
              {/* <Tab
                label={
                  <Badge
                    badgeContent={project.openMonitoringAlarmsCount}
                    color="error"
                  >
                    Alarms
                  </Badge>
                }
                value="alarms"
              /> */}
              {/* <Tab label={'Env. Impact'} value="impact" /> */}
              {hasWriteAccess && <Tab label="Finance" value="finance" />}
              {project.cmsMonitoringPath &&
              contextUser?.roles &&
              contextUser.roles.filter((el) => el === 'ITRead').length > 0 ? (
                <Button
                  style={{ borderRadius: '50px' }}
                  size="small"
                  component={Link}
                  to={project.cmsMonitoringPath}
                  variant="contained"
                  endIcon={<OpenInNew />}
                >
                  Go to SCADA page
                </Button>
              ) : null}
            </Tabs>
            {selectedTab === 'generation'
              ? renderProjectProductionData()
              : null}
            {selectedTab === 'contacts'
              ? renderProjectContactInfo(loading)
              : null}
            {selectedTab === 'credentials'
              ? renderProjectCredentials(loading)
              : null}
            {selectedTab === 'equipment' ? renderEquipmentInfo(loading) : null}
            {selectedTab === 'alarms' ? renderProjectAlarms() : null}
            {selectedTab === 'finance' ? renderProjectFinance(loading) : null}
            {/* {selectedTab === 'impact' ? renderProjectImpact(loading) : null} */}
          </Grid>
        </Collapse>
      </TableCell>
    </TableRow>
  );
});

export const SensorIntegrityChecks = (props: any) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();

  // const fetchData = () => {
  //   setLoading(true);
  //   dataProvider.getOne('SensorIntegrityCheck', { id: props.sensorId }).then(
  //     (res) => {
  //       setLoading(false);
  //       setData(res.data);
  //     },
  //     (err) => {
  //       console.error(err);
  //     }
  //   );
  // };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await dataProvider.getOne('SensorIntegrityCheck', {
          id: props.sensorId,
        });
        if (isMounted) {
          setData(res.data);
          setLoading(false);
        }
      } catch (err) {
        console.error(err);
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [dataProvider, props.sensorId]);

  // if (!data && !loading) {
  //   fetchData();
  // }

  if (!data || loading) {
    return (
      <>
        <Skeleton
          animation="wave"
          variant="circular"
          height="1.5rem"
          width="1.5rem"
          style={{ marginRight: '.5rem' }}
        />
        <Skeleton
          animation="wave"
          variant="circular"
          height="1.5rem"
          width="1.5rem"
        />
      </>
    );
  }

  return (
    <>
      <Grid item style={{ marginRight: '.5rem' }}>
        <Tooltip
          arrow
          title={`Previous 24hr data coverage: ${numeral(
            data.commsCoverageLast24Hrs
          ).format('%0')}`}
        >
          <span>
            <Icon
              style={{
                color:
                  data.commsCoverageLast24Hrs > 0.8
                    ? theme.palette.green.main
                    : data.commsCoverageLast24Hrs > 0.2
                    ? theme.palette.warning.main
                    : theme.palette.error.main,
              }}
              className={
                data.commsCoverageLast24Hrs > 0.8
                  ? 'fa-solid fa-circle'
                  : data.commsCoverageLast24Hrs > 0.6
                  ? 'fa-solid fa-circle-three-quarters'
                  : data.commsCoverageLast24Hrs > 0.4
                  ? 'fa-solid fa-circle-half'
                  : data.commsCoverageLast24Hrs > 0.2
                  ? 'fa-solid fa-circle-quarter'
                  : 'fa fa-circle'
              }
            />
          </span>
        </Tooltip>
      </Grid>
      <Grid item>
        <Tooltip
          arrow
          title={`${!!props.isTracker ? 'Tracker' : 'Sensor'} ${
            data.dataIntegrityLast24Hrs ? 'passes' : 'fails'
          } integrity check. Either no data exists, or the data values seem incorrect.`}
        >
          <span>
            <Icon
              style={{
                color: data.dataIntegrityLast24Hrs
                  ? theme.palette.green.main
                  : theme.palette.error.main,
              }}
              className={
                data.dataIntegrityLast24Hrs
                  ? 'fas fa-circle-check'
                  : 'fas fa-circle-xmark'
              }
            />
          </span>
        </Tooltip>
      </Grid>
    </>
  );
};

export const InverterIntegrityChecks = (props: { inverterId: number }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await dataProvider.getOne('InverterIntegrityCheck', {
          id: props.inverterId,
        });
        if (isMounted) {
          setData(res.data);
          setLoading(false);
        }
      } catch (err) {
        console.error(err);
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [dataProvider, props.inverterId]);

  // if (!data && !loading) {
  //   fetchData();
  // }

  if (!data || loading) {
    return (
      <>
        <Skeleton
          animation="wave"
          variant="circular"
          height="1.5rem"
          width="1.5rem"
          style={{ marginRight: '.5rem' }}
        />
        <Skeleton
          animation="wave"
          variant="circular"
          height="1.5rem"
          width="1.5rem"
        />
      </>
    );
  }

  return (
    <>
      <Grid item style={{ marginRight: '.5rem' }}>
        <Tooltip
          arrow
          title={`Previous 24hr data coverage: ${numeral(
            data.commsCoverageLast24Hrs
          ).format('%0')}`}
        >
          <span>
            <Icon
              style={{
                color:
                  data.commsCoverageLast24Hrs > 0.8
                    ? theme.palette.green.main
                    : data.commsCoverageLast24Hrs > 0.2
                    ? theme.palette.warning.main
                    : theme.palette.error.main,
              }}
              className={
                data.commsCoverageLast24Hrs > 0.8
                  ? 'fa-solid fa-circle'
                  : data.commsCoverageLast24Hrs > 0.6
                  ? 'fa-solid fa-circle-three-quarters'
                  : data.commsCoverageLast24Hrs > 0.4
                  ? 'fa-solid fa-circle-half'
                  : data.commsCoverageLast24Hrs > 0.2
                  ? 'fa-solid fa-circle-quarter'
                  : 'fa fa-circle'
              }
            />
          </span>
        </Tooltip>
      </Grid>
      <Grid item>
        <Tooltip
          arrow
          title={`Inverter ${
            data.dataIntegrityLast24Hrs ? 'passes' : 'fails'
          } integrity check. Either no data exists, or the data values seem incorrect.`}
        >
          <span>
            <Icon
              style={{
                color: data.dataIntegrityLast24Hrs
                  ? theme.palette.green.main
                  : theme.palette.error.main,
              }}
              className={
                data.dataIntegrityLast24Hrs
                  ? 'fas fa-circle-check'
                  : 'fas fa-circle-xmark'
              }
            />
          </span>
        </Tooltip>
      </Grid>
    </>
  );
};
