// in src/Menu.js
import React, { useState } from 'react';
import {
  <PERSON>lapse,
  Divider,
  Icon,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
  useMediaQuery,
} from '@mui/material';
import {
  MenuItemLink,
  useResourceDefinitions,
  WithPermissions,
  useSidebarState,
} from 'react-admin';
import DefaultIcon from '@mui/icons-material/ViewList';
import {
  AccountBalance,
  Computer,
  Dashboard,
  ExpandLess,
  ExpandMore,
  Info,
  Language,
  Movie,
  MonetizationOn,
  People,
  // PersonAdd,
  Public,
  List,
  WbSunny,
  Security,
  SupervisorAccount,
  Business,
  // SupportAgent,
  // Terminal,
} from '@mui/icons-material';
import moment from 'moment';

import { constants } from '../utils/global';

const getCategoryLabel = (label) => {
  switch (label) {
    case 'content':
      return 'Content';
    case 'assetMgmt':
      return 'Asset Mgmt';
    case 'financials':
      return 'Financials';
    case 'hr':
      return 'Human Resources';
    case 'itUtilities':
      return 'IT Utilities';
    case 'ira':
      return 'IRA';
    case 'brazil':
      return 'Brazil';
    case 'assets':
      return 'Assets';
    case 'users':
      return 'Users';
    case 'accounting':
      return 'Accounting';
    case 'monitoring':
      return 'Monitoring';
    case 'marketing':
      return 'Marketing';
    case 'security':
      return 'Security';
    case 'creditMgmt':
      return 'Credit Mgmt';
    default:
      return label;
  }
};

export const getCategoryIcon = (label) => {
  switch (label) {
    case 'content':
      return <Movie />;
    case 'assetMgmt':
      return (
        <span>
          <Icon
            className="fas fa-solar-panel"
            fontSize="small"
            style={{ paddingLeft: '4px' }}
          />
        </span>
      );
    case 'financials':
      return <MonetizationOn />;
    case 'creditMgmt':
      return <Business />;
    case 'hr':
      return <SupervisorAccount />;
    case 'itUtilities':
      return <Computer />;
    case 'ira':
      return <AccountBalance />;
    case 'brazil':
      return <Public />;
    case 'portfolios':
      return <DefaultIcon />;
    case 'security':
      return <Security />;
    case 'users':
      return <People />;
    case 'accounting':
      // This post https://stackoverflow.com/a/******** and its comments explain why we need to wrap the Icon in a span. There are other suggestions in this post also but this is simplest sol'n.
      return (
        <span>
          <Icon
            className="fas fa-calculator"
            fontSize="small"
            style={{ paddingLeft: '4px' }}
          />
        </span>
      );
    case 'monitoring':
      return <WbSunny />;
    case 'marketing':
      return <Language />;
    default:
      return <List />;
  }
};

const MyMenu = (props) => {
  const { onMenuClick, logout } = props;
  const [categoryExpanded, setCategoryExpanded] = useState('');
  const isXSmall = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [open] = useSidebarState();
  const resourcesDefinitions = useResourceDefinitions();
  const resources = Object.keys(resourcesDefinitions).map(
    (name) => resourcesDefinitions[String(name)]
  );
  const filteredResources = resources.filter(
    (resource) => resource.hasList && resource.options.category
  );
  const filteredDashboards = resources.filter(
    (resource) => resource?.options?.dashboard
  );

  const resourceCategories = {};
  filteredResources.forEach((resource) => {
    const resourceList = resourceCategories[resource.options.category];
    if (resourceList) {
      resourceList.push(resource);
      resourceCategories[resource.options.category] = resourceList;
    } else {
      resourceCategories[resource.options.category] = [resource];
    }
  });

  return (
    <WithPermissions
      // authParams={{ key: match.path, params: route.params }}
      // location is not required but it will trigger a new permissions check if specified when it changes
      // location={location}
      render={({ permissions }) => {
        // Get roles from user object (object structure with name property)
        const roleNames = permissions?.roles?.map((role) => role.name) || [];

        const isVictoryHill = roleNames.includes('VictoryHill');
        const isLattice = roleNames.includes('Lattice');
        const isEmployee = roleNames.includes('Admin');
        const isLaerskool = roleNames.includes('Laerskool');
        const isEventide = roleNames.includes('Eventide');
        const isConnaught = roleNames.includes('Connaught');
        const isFresno = roleNames.includes('Fresno');

        const getDashboardName = (resource) => {
          if (isVictoryHill && resource?.options?.vhLabel)
            return resource.options.vhLabel;
          if (isLattice && resource?.options?.latticeLabel)
            return resource.options.latticeLabel;
          if (isLaerskool && resource?.options?.projectCustomerLabel)
            return resource.options.laerskoolLabel;
          if (isEventide && resource?.options?.projectCustomerLabel)
            return resource.options.eventideLabel;
          if (isConnaught && resource?.options?.projectCustomerLabel)
            return resource.options.connaughtLabel;
          if (isFresno && resource?.options?.projectCustomerLabel)
            return resource.options.fresnoLabel;
          return resource?.options?.label || resource.name;
        };

        return (
          <>
            <MenuList style={{ marginBottom: '6rem' }}>
              {filteredDashboards.map((resource) => {
                // Show annual report tab Dec-Jan
                if (
                  resource.name === 'AnnualReportNumbers' &&
                  !(moment().month() >= 11 || moment().month() <= 0)
                ) {
                  return null;
                }
                return (
                  <MenuItemLink
                    name={resource.options && resource.options.label}
                    key={`dashboard-menu-item-${resource.name}`}
                    to={`/${
                      resource.name === 'Dashboard' ? '' : resource.name
                    }`}
                    primaryText={getDashboardName(resource)}
                    style={{ paddingTop: '8px', paddingBottom: '8px' }}
                    leftIcon={<Dashboard />}
                    onClick={onMenuClick}
                    sidebarIsOpen={open}
                  />
                );
              })}
              {Object.keys(resourceCategories).length > 0 && (
                <Divider key="divider-resources-start" />
              )}
              {Object.keys(resourceCategories)
                .map((resourceCategory) => {
                  const isCategoryExpanded =
                    categoryExpanded === resourceCategory;
                  const categoryLabel = getCategoryLabel(resourceCategory);
                  return [
                    <MenuItem
                      key={`category-menu-item-${categoryLabel}`}
                      name={categoryLabel}
                      button="true"
                      onClick={() =>
                        setCategoryExpanded(
                          isCategoryExpanded ? '' : resourceCategory
                        )
                      }
                    >
                      <ListItemIcon style={{ minWidth: '40px' }}>
                        {getCategoryIcon(resourceCategory)}
                      </ListItemIcon>
                      <ListItemText primary={categoryLabel} />
                      {isCategoryExpanded ? <ExpandLess /> : <ExpandMore />}
                    </MenuItem>,
                    <Collapse
                      in={isCategoryExpanded}
                      key={`collapse-${resourceCategory}`}
                    >
                      {resourceCategories[String(resourceCategory)]
                        .map((resource) => {
                          return (
                            resource.hasList && (
                              <MenuItemLink
                                name={
                                  resource.options && resource.options.label
                                }
                                style={{ marginLeft: '2.5rem' }}
                                key={`menu-item-${resourceCategory}-${resource.name}`}
                                to={`/${resource.name}`}
                                primaryText={
                                  (resource.options &&
                                    resource.options.label) ||
                                  resource.name
                                }
                                onClick={onMenuClick}
                                sidebarIsOpen={open}
                              />
                            )
                          );
                        })
                        .sort((a, b) =>
                          a && b && a.props.name > b.props.name ? 1 : -1
                        )
                        .concat(
                          resourceCategory === 'itUtilities'
                            ? [
                                <MenuItemLink
                                  key={`software-form`}
                                  name="Software"
                                  to={
                                    'https://docs.google.com/spreadsheets/d/10uMnGG576ao9-I9oPEsbjGsixOGaWJeEKjXUwFua97Y/edit#gid=0'
                                  }
                                  target="_blank"
                                  primaryText="Software"
                                  style={{ marginLeft: '2.5rem' }}
                                />,
                              ]
                            : []
                        )
                        .concat(
                          resourceCategory === 'marketing'
                            ? [
                                <MenuItemLink
                                  key="menu-item-brand-style-guide"
                                  name="Online Style Guide"
                                  to={'https://www.energea.com/styleguide'}
                                  target="_blank"
                                  primaryText="Brand Style Guide (Online)"
                                  style={{ marginLeft: '2.5rem' }}
                                />,
                                <MenuItemLink
                                  key={`brand-guide-menu-item`}
                                  name="Brand Guide"
                                  to="https://www.dropbox.com/scl/fi/la5e4n47dz8lg47b83rrw/Energea-Brand-Standards.pdf?rlkey=lcvb0pxclze4fev1efkhkgfcw&dl=0"
                                  target="_blank"
                                  primaryText="Brand Guide"
                                  style={{ marginLeft: '2.5rem' }}
                                />,
                                <MenuItemLink
                                  key={`creative-intake-menu-item`}
                                  name="Creative Intake Form"
                                  to="https://www.dropbox.com/scl/fo/qgaele4mvd1r8csw9dlpq/h?rlkey=p1ud92n6noc1k15ldeqlt6skz&dl=0"
                                  target="_blank"
                                  primaryText="Creative Intake Form"
                                  style={{ marginLeft: '2.5rem' }}
                                />,
                                <MenuItemLink
                                  key={`marketing-meeting-menu-item`}
                                  name="Marketing Meeting Agenda"
                                  to="https://docs.google.com/presentation/d/1R2lSX23072cwesrJcSEt7pGqrodJ1Yz1q67qOBt84l4/edit?slide=id.g31b3206a1ba_0_0#slide=id.g31b3206a1ba_0_0"
                                  target="_blank"
                                  primaryText="Marketing Meeting Agenda"
                                  style={{ marginLeft: '2.5rem' }}
                                />,
                              ]
                            : []
                        )
                        .concat(
                          resourceCategory === 'users'
                            ? [
                                <MenuItemLink
                                  key={`menu-item-customer-playbook`}
                                  name="Customer Playbook"
                                  to="https://docs.google.com/document/d/1q2DYe6qHpmiErmUmSQ7Dj7SeDN447uCVyWXsD_Vy_yg/edit?usp=sharing"
                                  target="_blank"
                                  style={{ marginLeft: '2.5rem' }}
                                  primaryText="Customer Playbook"
                                />,
                              ]
                            : []
                        )
                        .concat(
                          resourceCategory === 'hr'
                            ? [
                                <MenuItemLink
                                  key={`menu-item-new-employee-form`}
                                  name="New Employee Form"
                                  to={'https://forms.gle/EZHQuygRb8nTc8o78'}
                                  target="_blank"
                                  primaryText="New Employee Form"
                                  style={{ marginLeft: '2.5rem' }}
                                />,
                              ]
                            : []
                        )}
                    </Collapse>,
                  ];
                })
                .sort((a, b) => (a[0].props.name > b[0].props.name ? 1 : -1))}
              {isEmployee && [
                <Divider key={`divider-new-employee-form`} />,
                <MenuItem
                  key={`menu-item-global-handbook`}
                  name="Global Handbook"
                  component="a"
                  href={
                    'https://www.dropbox.com/scl/fo/u91io26i19udr8djzsjgo/h?rlkey=qth1kldr82hfeiflf868aivry&dl=0'
                  }
                  target="_blank"
                >
                  <ListItemIcon style={{ minWidth: '40px' }}>
                    <Info />
                  </ListItemIcon>
                  <ListItemText primary="Global Handbook" />
                </MenuItem>,
              ]}
            </MenuList>
            {isXSmall && logout}
          </>
        );
      }}
    />
  );
};

export default MyMenu;
