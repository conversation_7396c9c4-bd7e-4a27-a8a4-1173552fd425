import React, { useState } from 'react';
import jsonExport from 'jsonexport/dist';
import moment from 'moment';
import { useParams } from 'react-router-dom';

import {
  ArrayField,
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  CreateButton,
  downloadCSV,
  Datagrid,
  DateField,
  DateInput,
  DateTimeInput,
  Edit,
  ExportButton,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  Labeled,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  TopToolbar,
  UrlField,
  useDataProvider,
  useNotify,
  useRefresh,
  useListContext,
  usePermissions,
  useRecordContext,
  useResourceDefinition,
} from 'react-admin';
import {
  Backdrop,
  Button,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogActions,
  Grid,
  Typography,
} from '@mui/material';
import { CloudDownload, CloudUpload } from '@mui/icons-material';
import { <PERSON><PERSON>, AlertTitle } from '@mui/lab';

import { getEditable } from '../utils/applyRoleAuth';
import numeral from 'numeral';
import { EmailExportButton } from './EmailExportButton';
import { CustomNumberInput, LinkField } from './CustomFields';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Investment';
const TextArrayField = ({ source }) => {
  const record = useRecordContext();
  return (
    <>
      {record[String(source)] &&
        record[String(source)].map((item) => <Chip label={item} key={item} />)}
    </>
  );
};
TextArrayField.defaultProps = { addLabel: true };

export const InvestmentEdit = () => {
  const [copEversignDocumentOpen, setCOPEversignDocumentOpen] = useState(false);
  const [copEversignDocumentLoaded, setCOPEversignDocumentLoaded] =
    useState(false);
  const [loading, setLoading] = useState(false);
  const [subscriptionAgreementUploading, setSubscriptionAgreementUploading] =
    useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const saveToMTSFTPFolder = (investmentId) => {
    setLoading(true);
    dataProvider
      .update('MillenniumTrustSFTP', {
        data: {
          investmentId,
        },
      })
      .then(
        (res) => {
          if (res?.data) {
            notify('Subscription agreement successfully saved to SFTP folder', {
              type: 'success',
            });
          }
          refresh();
          setLoading(false);
        },
        (e) => {
          console.error(e);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          setLoading(false);
          notify(
            `Error saving Subscription Agreement to MT's SFTP folder. ${errorMsg}`,
            'error'
          );
        }
      );
  };

  const downloadSubscriptionAgreementAudit = (investmentId) => {
    setLoading(true);
    dataProvider
      .update('MillenniumTrustAuditPage', {
        data: {
          investmentId,
        },
      })
      .then(
        (res) => {
          setLoading(false);
          if (res?.data?.response) {
            notify(
              'Subscription agreement audit page successfully downloaded',
              'success'
            );
            const link = document.createElement('a');
            link.href = res.data.response;
            link.download = res.data.response;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        },
        (e) => {
          console.error(e);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          setLoading(false);
          notify(`Error downloading Audit Page. ${errorMsg}`, {
            type: 'error',
          });
        }
      );
  };

  const handleCOPForm = () => {
    dataProvider
      .create('MillenniumTrustCOPForm', {
        data: {
          investmentId: parseInt(id, 10),
        },
      })
      .then(
        (returnObj) => {
          if (!returnObj.data || !returnObj.data.url) {
            setCOPEversignDocumentOpen(false);
            return;
          }

          eversign.open({
            url: returnObj.data.url,
            containerID: 'eversign-container-cop-form',
            width: '100%',
            height: '100%',
            events: {
              loaded: () => {
                setCOPEversignDocumentLoaded(true);
              },
              signed: () => {
                notify('Document created and emailed to custodian', {
                  type: 'success',
                });
                dataProvider
                  .create('CompleteMillenniumTrustInvestment', {
                    data: {
                      investmentId: parseInt(id, 10),
                    },
                  })
                  .then(
                    () => {
                      setCOPEversignDocumentLoaded(true);
                      setCOPEversignDocumentOpen(false);
                    },
                    (err) => {
                      console.error(err);
                      setCOPEversignDocumentLoaded(true);
                      setCOPEversignDocumentOpen(false);
                    }
                  );
              },
              declined: () => {},
              error: () => {},
            },
          });
        },
        (e) => {
          setCOPEversignDocumentLoaded(true);
          setCOPEversignDocumentOpen(false);
          console.error('Error drafting MT COP form', e);
          notify(
            'Apologies! There was an error drafting COP form. Please try again later.',
            'error'
          );
        }
      );
  };

  const handleCancelMTCInvestment = () => {
    dataProvider
      .update('MillenniumTrustInvestmentTransfer', { id: parseInt(id, 10) })
      .then(
        () => {
          notify('Successfully cancelled MT investment', { type: 'success' });
        },
        (e) => {
          console.error('Error cancelling MT investment', e);
          notify(
            'Apologies! There was an error cancelling this MT investment.',
            'error'
          );
        }
      );
  };

  const sendAssetExchangeBuyDirectionDocument = () => {
    dataProvider
      .create('EntrustAssetExchangeBuyDirection', {
        data: {
          investmentId: parseInt(id, 10),
        },
      })
      .then(
        () => {
          notify(
            'Sent Asset Exchange Buy Direction document to investor',
            'success'
          );
        },
        (e) => {
          console.error(
            'Error sending asset exchange buy direction to investor',
            e
          );
          notify(
            'Apologies! There was an error sending asset exchange buy direction doc to investor.',
            'error'
          );
        }
      );
  };

  const uploadSubscriptionAgreementToS3 = (event, record) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `SubscriptionAgreements/Investment/${record.user.id}_${
          record.portfolio.id
        }_${record.value}_${moment(record.startDt || new Date()).format(
          'YYYYMMDDHHmmssSSS'
        )}`
      );
      setSubscriptionAgreementUploading(true);
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          dataProvider
            .update('Investment', {
              data: {
                id: parseInt(id, 10),
                documentAwsObjectKey: awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Subscription agreement uploaded', { type: 'success' });
                setSubscriptionAgreementUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading subscription agreement', {
                  type: 'error',
                });
                setSubscriptionAgreementUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading document to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Labeled fullWidth>
          <LinkField
            reference="User"
            linkSource="user.id"
            labelSource="user.fullName"
            label="User"
          />
        </Labeled>
        <Labeled fullWidth>
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.subtitle"
            label="Portfolio"
          />
        </Labeled>
        <Labeled fullWidth>
          <LinkField
            reference="Transfer"
            linkSource="transfer.id"
            labelSource="transfer.label"
            label="Transfer"
          />
        </Labeled>
        <Labeled fullWidth>
          <LinkField
            label="Sub-Account"
            linkSource="subAccount.id"
            labelSource="subAccount.name"
            reference="SubAccount"
          />
        </Labeled>
        <Labeled fullWidth>
          <LinkField
            reference="Dividend"
            linkSource="dividend.id"
            labelSource="dividend.label"
            label="Reinvested Dividend"
          />
        </Labeled>
        <Labeled fullWidth>
          <ArrayField source="shareTransfers" sortable={false}>
            <SingleFieldList>
              <LinkField
                reference="ShareTransfer"
                linkSource="id"
                labelSource="label"
                label="Share Transfer"
              />
            </SingleFieldList>
          </ArrayField>
        </Labeled>
        <br />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <BooleanInput
              source="historicalFlg"
              helperText="Turn this on for investments that happened outside of the platform. This will alert the platform not to worry about transfer status and assume that we handled it offline. **If this is not turned on, we will attempt to make a transfer of the value you specified."
              fullWidth
            />
            <Labeled fullWidth>
              <BooleanField label="Is Reinvestment" source="isReinvestment" />
            </Labeled>
            <DateTimeInput
              required
              fullWidth
              label="Created Date"
              source="startDt"
              helperText="This is the date that the investment was made and will determine when they  will start receiving dividends."
            />
            <DateInput
              label="Cancelled Date"
              source="cancelledDt"
              helperText="If this date is present, the investment won't be factored in to dividends, investment cap, etc."
              fullWidth
            />
            <DateInput
              label="MT Doc Upload Date"
              source="millenniumTrustDocumentUploadDt"
              helperText="The date that documents were uploaded to Millennium Trust's SFTP server."
              fullWidth
            />
            <CustomNumberInput required source="value" fullWidth />
            <CustomNumberInput
              required
              label="Number of Shares"
              source="shares"
              fullWidth
            />
            <TextInput
              label="Eversign Document ID"
              source="eversignDocumentId"
              fullWidth
            />
            <TextInput
              label="Subscription Agreement S3 Key"
              source="documentAwsObjectKey"
              fullWidth
            />
            <FunctionField
              label="Agreement"
              render={(record) => {
                const isMillenniumTrustInvestment =
                  record?.subAccount?.subAccountType?.id === 37;
                return (
                  <>
                    <Button
                      variant="contained"
                      startIcon={<CloudDownload />}
                      style={{ textTransform: 'none' }}
                      disabled={loading || !record.agreementDownloadUrl}
                      onClick={() =>
                        window.location.assign(record.agreementDownloadUrl)
                      }
                    >
                      Download Subscription Agreement
                    </Button>
                    <Button
                      color="primary"
                      variant="contained"
                      component="label" // https://stackoverflow.com/a/********
                      startIcon={<CloudUpload />}
                      style={{ textTransform: 'none', marginLeft: '1rem' }}
                      disabled={subscriptionAgreementUploading}
                    >
                      {subscriptionAgreementUploading ? (
                        <CircularProgress style={{ position: 'absolute' }} />
                      ) : null}
                      {record.agreementDownloadUrl
                        ? 'Overwrite Subscription Agreement'
                        : 'Upload Subscription Agreement'}
                      <input
                        type="file"
                        hidden
                        onChange={(event) =>
                          uploadSubscriptionAgreementToS3(event, record)
                        }
                        accept="application/pdf"
                      />
                    </Button>
                    {isMillenniumTrustInvestment && (
                      <Button
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{
                          textTransform: 'none',
                          marginLeft: '1rem',
                        }}
                        disabled={loading}
                        onClick={() =>
                          downloadSubscriptionAgreementAudit(
                            record.id,
                            dataProvider,
                            notify
                          )
                        }
                      >
                        Download Audit Page
                      </Button>
                    )}
                    {/* {isMillenniumTrustInvestment && (
                        <Button
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{
                            textTransform: 'none',
                            marginLeft: '1rem',
                          }}
                          disabled={loading}
                          onClick={() => saveToMTSFTPFolder(record.id)}
                        >
                          Save to Millennium Trust SFTP folder
                        </Button>
                      )} */}
                  </>
                );
              }}
            />
          </Grid>
        </Grid>
        <ArrayField
          label="Dwolla Transfers"
          fullWidth
          source="transfer.dwollaTransfers"
        >
          <Datagrid>
            <TextField source="id" />
            <UrlField target="_blank" label="Dwolla Link" source="dwollaUrl" />
            <TextField source="status" />
            <NumberField
              label="Amount"
              source="amount.value"
              options={{ style: 'currency', currency: 'USD' }}
            />
            <TextField label="Source" source="source.name" />
            <TextField label="Destination" source="destination.name" />
            <DateField source="created" />
          </Datagrid>
        </ArrayField>
        <FormDataConsumer>
          {({ formData, ...rest }) => {
            if (formData.subAccount?.subAccountType?.id === 37) {
              return (
                <Alert severity="info" style={{ margin: '1rem 0' }}>
                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>
                      The below button will open a new Confirmation of Purchase
                      form to let Millennium Trust know the funds have been
                      received. Only use this form with investments without a
                      'Complete' status.
                    </Typography>
                    <Typography gutterBottom>
                      Once you click finish, the form will be automatically
                      <NAME_EMAIL> and <EMAIL> will
                      be CC'ed.
                    </Typography>
                    <Typography gutterBottom>
                      Also, the transfer's completed date will be set and share
                      transfers will be issued if applicable.
                    </Typography>
                  </Grid>
                  <Button
                    onClick={() => {
                      setCOPEversignDocumentOpen(true);
                    }}
                    color="primary"
                    variant="contained"
                    style={{ textTransform: 'none' }}
                  >
                    Complete MTC Investment
                  </Button>
                  <Dialog
                    TransitionProps={{ onEnter: handleCOPForm }}
                    fullWidth
                    fullScreen
                    maxWidth="lg"
                    open={!!copEversignDocumentOpen}
                  >
                    <DialogContent
                      id="eversign-container-cop-form"
                      style={{ padding: 0, height: '1000px' }}
                    />
                    <DialogActions>
                      <Grid
                        container
                        style={{ width: '100%', padding: '0 1rem' }}
                        justifyContent="center"
                      >
                        <Grid item style={{ padding: 0 }}>
                          <Button
                            size="large"
                            onClick={() => {
                              setCOPEversignDocumentOpen(false);
                              setCOPEversignDocumentLoaded(false);
                            }}
                          >
                            Cancel
                          </Button>
                        </Grid>
                      </Grid>
                    </DialogActions>
                  </Dialog>
                  <Backdrop
                    style={{
                      zIndex: 1700,
                      background: 'rgba(255,255,255,1)',
                    }}
                    open={
                      !!(copEversignDocumentOpen && !copEversignDocumentLoaded)
                    }
                  >
                    <Grid
                      style={{
                        padding: '4rem',
                        textAlign: 'center',
                        alignItems: 'center',
                      }}
                      container
                      direction="column"
                      justifyContent="center"
                    >
                      <Grid item lg={6} md={10} xs={12}>
                        <Alert variant="outlined" severity="info">
                          <AlertTitle gutterBottom variant="h5">
                            Drafting Confirmation of Purchase Form
                          </AlertTitle>
                          <Typography>
                            Please wait while we draft the COP form.
                          </Typography>
                        </Alert>
                        <CircularProgress
                          style={{ marginTop: '2em' }}
                          color="primary"
                        />
                      </Grid>
                    </Grid>
                  </Backdrop>
                </Alert>
              );
            }
          }}
        </FormDataConsumer>
        <FormDataConsumer>
          {({ formData, ...rest }) => {
            if (formData.subAccount?.subAccountType?.id === 37) {
              return (
                <Grid xs={12} md={6}>
                  <Typography gutterBottom>
                    The below button will set the cancelledDt on this investment
                    in our database and also use MTC's API to cancel the
                    investment transfers. This will fail if the investment
                    transfer doesn't have one of the following statuses:
                    'Received', 'Awaiting Funds', 'Awaiting Resolution', 'In
                    Progress'
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleCancelMTCInvestment()}
                    style={{ textTransform: 'none' }}
                  >
                    Cancel MTC Investment Transfer
                  </Button>
                </Grid>
              );
            }
          }}
        </FormDataConsumer>
        <FormDataConsumer>
          {({ formData, ...rest }) => {
            if (
              formData.subAccount?.subAccountType?.id === 1 &&
              !formData.cancelledDt
            ) {
              return (
                <Grid xs={12} md={6}>
                  <Alert severity="info">
                    The below button will send an email to the investor with a
                    document to sign. <EMAIL> will be CC'ed on the
                    document created and document completed emails. We will send
                    this completed document along with the updated subscription
                    agreement to Entrust.
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => sendAssetExchangeBuyDirectionDocument()}
                      fullWidth
                    >
                      Send Asset Exchange Buy Dir. to Client for Signature
                    </Button>
                  </Alert>
                </Grid>
              );
            }
          }}
        </FormDataConsumer>
      </SimpleForm>
    </Edit>
  );
};

const exporter = (rows) => {
  const rowsForExport = rows
    // .filter((row) => !row.cancelledDt)
    .map((row) => {
      const returnRow = {};
      const dates = ['startDt', 'createdAt', 'completedDt'];
      Object.keys(row).forEach((attr) => {
        if (attr === '__typename') return;
        const data = row[String(attr)];
        if (data && data.__typename) {
          delete data.__typename;
        }
        if (dates.indexOf(attr) > -1) {
          returnRow[String(attr)] = moment(data).format('MM/DD/YYYY');
        } else {
          returnRow[String(attr)] = data;
        }
      });
      return returnRow;
    });
  jsonExport(
    rowsForExport,
    {
      rowDelimiter: ';',
    },
    (err, csv) => {
      downloadCSV(`sep=;\n${csv}`, entityName); // download as 'posts.csv` file
    }
  );
};

const arthurExporter = (rows) => {
  const rowsForExport = rows
    .filter((row) => !row.cancelledDt)
    .map((row) => {
      const returnRow = {};
      const dates = ['startDt'];
      const requiredFields = ['portfolio', 'user'];
      const numberFields = ['value', 'shares'];
      Object.keys(row).forEach((attr) => {
        if (attr === '__typename') return;
        const data = row[String(attr)];
        if (data && data.__typename) {
          delete data.__typename;
        }
        if (dates.indexOf(attr) > -1) {
          returnRow[String(attr)] = moment(data).format('DD/MM/YYYY');
        } else if (numberFields.indexOf(attr) > -1) {
          returnRow[String(attr)] = String(data).replace('.', ',');
        } else if (requiredFields.indexOf(attr) > -1) {
          returnRow[String(attr)] = data;
        }
      });
      return returnRow;
    });
  jsonExport(
    rowsForExport,
    {
      rowDelimiter: ';',
    },
    (err, csv) => {
      downloadCSV(`sep=;\n${csv}`, entityName); // download as 'posts.csv` file
    }
  );
};

const InvestmentFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Investor First Name or Last Name"
      source="q"
      alwaysOn
    />
    <TextInput
      style={{ minWidth: '200px' }}
      label="Investor's State (Ex: 'CT')"
      source="investorState"
    />
    <CustomNumberInput label="Investment Value" source="value" />
    <DateInput label="Start Date Lower Bound" source="startDtLowerBound" />
    <DateInput label="Start Date Upper Bound" source="startDtUpperBound" />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <SelectInput
      style={{ minWidth: '200px' }}
      label="Sub Account Filter"
      source="subAccountFilter"
      choices={[
        { id: 'all', name: 'All' },
        { id: 'mtOnly', name: 'IRA Investments' },
      ]}
    />
  </Filter>
);
const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      {/* NOTE: ExportButton results in javascript heap error for this list. If we do want regular export, we can lower maxResults */}
      {/* <ExportButton maxResults={100000} /> */}
      <EmailExportButton
        {...props}
        resource={resource}
        filterValues={filterValues}
      />
    </TopToolbar>
  );
};

export const InvestmentList = () => {
  const { permissions } = usePermissions();
  const styleRow = (record, index) => {
    const {
      cancelledDt,
      subAccount,
      millenniumTrustDocumentUploadDt,
      isReinvestment,
      historicalFlg,
    } = record;
    const errorStyle = {
      backgroundColor: 'rgba(255,0,0,.2)',
      color: '#fff',
    };
    const cancelledStyle = {
      backgroundColor: '#ddd',
      fontStyle: 'italic',
    };
    const warningStyle = {
      backgroundColor: 'lightyellow',
    };
    if (cancelledDt) {
      return cancelledStyle;
    }
    if (
      subAccount?.subAccountType?.id === 37 &&
      !millenniumTrustDocumentUploadDt &&
      !isReinvestment &&
      !historicalFlg
    ) {
      return errorStyle;
    }
    return {};
  };
  const isArthur = permissions && permissions.id === 63;

  return (
    <List
      actions={<ListActions />}
      sort={{ field: 'id', order: 'DESC' }}
      perPage={25}
      pagination={<CustomPagination />}
      exporter={isArthur ? arthurExporter : exporter}
      filters={<InvestmentFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
        rowStyle={styleRow}
      >
        <TextField source="id" />
        <DateField label="Date" source="startDt" />
        <DateField source="completedDt" showTime sortable={false} />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          reference="SubAccount"
          linkSource="subAccount.id"
          labelSource="subAccount.label"
          label="SubAccount"
        />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          label="Initial Investment"
          source="value"
        />
        <NumberField label="Shares" source="shares" />
        <NumberField source="naturalSharesPurchased" />
        <NumberField source="resoldSharesPurchased" />
        <FunctionField
          label="Realized Share Price"
          render={(record) => {
            return (
              <span
                style={
                  Math.abs(
                    record.actualSharePrice - record.calculatedSharePrice
                  ) > 0.003
                    ? { color: 'red', fontWeight: 'bold' }
                    : {}
                }
              >
                {numeral(record.calculatedSharePrice).format('$0,0.0000')}
              </span>
            );
          }}
        />
        <FunctionField
          label="Actual Share Price at the Time"
          render={(record) => {
            return (
              <span
                style={
                  Math.abs(
                    record.actualSharePrice - record.calculatedSharePrice
                  ) > 0.005
                    ? { color: 'red', fontWeight: 'bold' }
                    : {}
                }
              >
                {numeral(record.actualSharePrice).format('$0,0.0000')}
              </span>
            );
          }}
        />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          source="naturalSharesPurchaseValue"
        />
        <DateField source="cancelledDt" />
        <BooleanField label="Historical Flag" source="historicalFlg" />
        <BooleanField
          label="Is Reinvestment?"
          source="isReinvestment"
          sortable={false}
        />
        <BooleanField
          label="Is Referral?"
          source="isReferral"
          sortable={false}
        />
        <BooleanField label="Is Promo?" source="isPromo" sortable={false} />
        <DateField source="millenniumTrustDocumentUploadDt" showTime />
        {/* <DateField source="createdAt" showTime /> */}
      </Datagrid>
    </List>
  );
};

export const InvestmentCreate = () => {
  const dataProvider = useDataProvider();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const fetchData = (portfolioId) => {
    setLoading(true);
    dataProvider.getOne('Portfolio', { id: portfolioId }).then(
      (res) => {
        setData(res.data);
        setLoading(false);
      },
      (e) => {
        console.error('Error retrieving portfolio share price', e);
        notify('Error collecting current portfolio share price', {
          type: 'error',
        });
        setError(e);
        setLoading(false);
      }
    );
  };
  return (
    <Create title={`Create ${entityName}`}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <BooleanInput
              fullWidth
              source="historicalFlg"
              helperText="Turn this on for investments that happened outside of the platform. This will alert the platform not to worry about transfer status and assume that we handled it offline."
            />
            <BooleanInput
              source="preventEmailFlg"
              fullWidth
              helperText="Turn this on for investments that happened outside of the platform. This will alert the platform not to worry about transfer status and assume that we handled it offline."
            />
            <DateTimeInput required source="startDt" fullWidth />
            <CustomNumberInput required source="value" fullWidth />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                let helperText =
                  'Leaving this blank will automatically calculate the most recent share price';
                console.log('HIT PORT', data?.currentEquityData?.sharePrice);
                if (formData.value && data?.currentEquityData?.sharePrice) {
                  helperText = `${numeral(formData.value).format(
                    '$0,0.00'
                  )} at current share price (${numeral(
                    data?.currentEquityData?.sharePrice
                  ).format('$0,0.0000')}/share) = ${numeral(
                    formData.value / data?.currentEquityData?.sharePrice
                  ).format('0.********')} shares.`;
                }
                if (formData?.portfolio?.id && !data && !error && !loading) {
                  fetchData(formData.portfolio.id);
                }
                return (
                  <CustomNumberInput
                    source="shares"
                    helperText={helperText}
                    fullWidth
                  />
                );
              }}
            </FormDataConsumer>
            <TextInput
              label="'From' Account ID"
              source="fromAccountId"
              helperText="The Dwolla Funding Source ID that the investment cash is being made from."
              fullWidth
            />
            <CustomNumberInput
              label="SubAccount ID"
              helperText="The database ID of the subAccount that is being invested from (optional)"
              source="subAccountId"
              step={1}
            />
            <ReferenceInput
              perPage={10000}
              source="user.id"
              sortable={false}
              reference="UserLite"
            >
              <AutocompleteInput
                label="User (Investor)"
                required
                fullWidth
                allowEmpty
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            <ReferenceInput
              source="portfolio.id"
              sortable={false}
              reference="PortfolioLite"
            >
              <SelectInput
                label="Portfolio"
                required
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (formData.shares) {
                  return (
                    <BooleanInput
                      source="createSignedSubscriptionAgreement"
                      fullWidth
                      defaultValue={false}
                      // disabled={!formData.shares} // NOTE: disabling a BooleanInput has been causing problems 8/28/2024 -J
                      helperText="The number of shares must be specified in order to create the subscription agreement at this time."
                    />
                  );
                }
                return (
                  <Alert severity="info">
                    <Typography>
                      In order to flag this investment to create a subscription
                      agreement for this investment, you must first specify the
                      number of shares.
                    </Typography>
                  </Alert>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
