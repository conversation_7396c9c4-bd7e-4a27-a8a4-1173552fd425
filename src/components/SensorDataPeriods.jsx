import React from 'react';
import moment from 'moment';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  Edit,
  ReferenceInput,
  List,
  NumberField,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { SensorDataPeriodsUpload } from './SensorDataPeriodsUpload';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Sensor Snapshot';

export const SensorDataPeriodEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput source="sensor.id" reference="Sensor">
              <SelectInput
                label="Sensor"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <TextInput
              source="periodStartDt"
              required
              fullWidth
              helperText="Ex: 2021-11-08 18:14:00"
            />
            <TextInput
              source="timeUnit"
              defaultValue="HOUR"
              required
              fullWidth
            />
            <CustomNumberInput source="value" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const SensorDataPeriodList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      // sort={{ field: 'updatedAt', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          label="Sensor"
          linkSource="sensor.id"
          labelSource="sensor.name"
          reference="Sensor"
        />
        <TextField source="periodStartDt" />
        <NumberField source="value" />
        <TextField source="timeUnit" />
        <DateField source="createdAt" showTime={true} />
        <DateField source="updatedAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

const attrs = [
  {
    name: 'periodStartDt',
    label: 'Period Start Dt',
    align: 'center',
    dataFormat: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    name: 'sensorId',
    label: 'Sensor ID',
    align: 'center',
  },
  {
    name: 'value',
    label: 'Value',
    align: 'center',
  },
  {
    name: 'timeUnit',
    label: 'Time Unit',
    align: 'center',
  },
];

export const SensorDataPeriodCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <Grid container>
      <Grid item>
        <Typography>Upload from CSV:</Typography>
        <SensorDataPeriodsUpload attrs={attrs} />
      </Grid>
      <Grid item>
        <Typography>Create Manually:</Typography>
        <SimpleForm>
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <ReferenceInput source="sensor.id" reference="Sensor">
                <SelectInput
                  label="Sensor"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput
                source="periodStartDt"
                required
                fullWidth
                helperText="Ex: 2021-11-08 18:14:00"
              />
              <CustomNumberInput source="value" fullWidth />
              <TextInput
                source="timeUnit"
                defaultValue="HOUR"
                required
                fullWidth
              />
            </Grid>
          </Grid>
        </SimpleForm>
      </Grid>
    </Grid>
  </Create>
);
