import React, { useState } from 'react';
import { Alert } from '@mui/lab';

import {
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import moment from 'moment';
import { useParams } from 'react-router-dom';
import {
  Button,
  Chip,
  CircularProgress,
  Collapse,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  CheckCircle,
  CloudDownload,
  CloudUpload,
  Subject,
} from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import { uploadObjectToS3 } from '../utils/aws';
import theme from '../theme';
import { CustomNumberInput, DetailField, LinkField } from './CustomFields';
import { downloadSimpleExcelFromRows } from '../utils/excel';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'TUSD Invoice';

export const TUSDInvoiceEdit = () => {
  const [invoiceUploading, setInvoiceUploading] = useState(false);
  const { id } = useParams();
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  const uploadInvoiceToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      // const aFileName = file.name.split('.');
      // const fileExtension =
      //   aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `TUSDInvoices/${formData.project.id}_${formData.accountYear}_${
          formData.accountMonth
        }_${moment().valueOf()}_${file.name}`
      );
      setInvoiceUploading(true);
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          dataProvider
            .update('TUSDInvoice', {
              data: { id: parseInt(id, 10), awsObjectKey },
            })
            .then(
              (res) => {
                notify('Invoice uploaded', { type: 'success' });
                setInvoiceUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading invoice', { type: 'error' });
                setInvoiceUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading invoice to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <SelectInput
              source="accountMonth"
              label="Generation Month"
              fullWidth
              choices={[
                { id: 1, name: 'January' },
                { id: 2, name: 'February' },
                { id: 3, name: 'March' },
                { id: 4, name: 'April' },
                { id: 5, name: 'May' },
                { id: 6, name: 'June' },
                { id: 7, name: 'July' },
                { id: 8, name: 'August' },
                { id: 9, name: 'September' },
                { id: 10, name: 'October' },
                { id: 11, name: 'November' },
                { id: 12, name: 'December' },
              ]}
            />
            <CustomNumberInput source="accountYear" fullWidth />
            <CustomNumberInput
              source="consumptionCost"
              label="Cost of energy consumed (BRL)"
              fullWidth
            />
            <CustomNumberInput
              source="invoicedAmount"
              label="Invoiced Amount (BRL)"
              fullWidth
            />
            <CustomNumberInput
              source="peakConsumedEnergy"
              label="Peak Energy Consumed (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="offPeakConsumedEnergy"
              label="Off-peak Energy Consumed (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="peakGeneratedEnergy"
              label="Peak Energy Generated (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="offPeakGeneratedEnergy"
              label="Off-peak Energy Generated (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="peakOffPeakFactor"
              label="Peak/Off-peak Factor"
              fullWidth
            />
            <CustomNumberInput
              source="peakUGBalance"
              label="Peak UG Balance"
              fullWidth
            />
            <CustomNumberInput
              source="offPeakUGBalance"
              label="Off-peak UG Balance"
              fullWidth
            />
            <ReferenceInput
              source="project.id"
              reference="Project"
              sort={{ field: 'name', order: 'ASC' }}
              perPage={10000}
            >
              <SelectInput
                label="Project"
                fullWidth
                required
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="brBillingCycle.id"
              reference="BrBillingCycleLite"
              sort={{ field: 'billingMonth', order: 'DESC' }}
              perPage={10000}
            >
              <AutocompleteInput
                label="Billing Cycle"
                fullWidth
                required
                optionText="label"
              />
            </ReferenceInput>
            <BooleanInput
              source="discrepancyFlg"
              label="Discrepancy with utility company"
              helperText="Turn this on when we feel that the injected energy numbers are incorrect in the utility company's bill. When turned on, a note should be filled out as to why and how it is resolved."
            />
            <TextInput
              source="notes"
              multiline
              fullWidth
              helperText="Use this field for anything unusual about the TUSD bill (especially if the injected energy is wrong)."
            />
            {isIT ? <TextInput source="awsObjectKey" fullWidth /> : null}
            <FunctionField
              label="Invoice"
              render={(record) => {
                return (
                  <Grid container spacing={2}>
                    <Grid item>
                      <Button
                        disabled={!record.downloadUrl}
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{ textTransform: 'none' }}
                        onClick={() =>
                          window.location.assign(record.downloadUrl)
                        }
                      >
                        Download Invoice
                      </Button>
                    </Grid>
                    <Grid item>
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          return (
                            <Collapse
                              in={
                                formData.project?.id &&
                                formData.accountMonth &&
                                formData.accountYear &&
                                formData.peakConsumedEnergy !== null &&
                                formData.offPeakConsumedEnergy !== null &&
                                formData.peakGeneratedEnergy !== null &&
                                formData.offPeakGeneratedEnergy !== null
                              }
                            >
                              <Button
                                color="primary"
                                variant="contained"
                                component="label" // https://stackoverflow.com/a/********
                                startIcon={<CloudUpload />}
                                style={{ textTransform: 'none' }}
                                disabled={invoiceUploading}
                              >
                                {invoiceUploading ? (
                                  <CircularProgress
                                    style={{ position: 'absolute' }}
                                  />
                                ) : null}
                                {record.downloadUrl
                                  ? 'Overwrite Invoice'
                                  : 'Upload Invoice'}
                                <input
                                  type="file"
                                  hidden
                                  onChange={(event) =>
                                    uploadInvoiceToS3(event, formData)
                                  }
                                  accept="application/pdf"
                                />
                              </Button>
                              <Typography variant="body2">
                                This should be a PDF
                              </Typography>
                            </Collapse>
                          );
                        }}
                      </FormDataConsumer>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const TUSDInvoiceFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      source="project.id"
      reference="Project"
      label="Project"
      alwaysOn
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
  </Filter>
);

const Aside = () => {
  const [missingTUSDs, setMissingTUSDs] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState(null);
  const dataProvider = useDataProvider();

  if (!loading && !missingTUSDs && !errors) {
    setLoading(true);
    dataProvider.getMany('MissingTUSDInvoices', {}).then(
      (res) => {
        setLoading(false);
        setMissingTUSDs(res.data);
      },
      (e) => setErrors(e)
    );
  }
  let jsx;
  if (loading) {
    jsx = <CircularProgress />;
  }
  if (!loading && missingTUSDs?.length > 0) {
    missingTUSDs.sort((a, b) => {
      const aDate = moment(`${a.accountMonth}/${a.accountYear}`, 'MM/YYYY');
      const bDate = moment(`${b.accountMonth}/${b.accountYear}`, 'MM/YYYY');
      return aDate.isBefore(bDate) ? -1 : 1;
    });
    jsx = missingTUSDs.map((missingTUSD) => (
      <Grid item xs={12} key={`missing-invoice-chip-${missingTUSD.id}`}>
        <Chip
          label={`${missingTUSD.project.name} - ${missingTUSD.accountMonth}/${missingTUSD.accountYear}`}
          color="error"
        />
      </Grid>
    ));
  } else if (!loading && missingTUSDs?.length === 0) {
    jsx = <Alert severity="success">You're all up to date!</Alert>;
  }

  return (
    <div>
      <Grid container style={{ margin: '1rem', width: '260px' }} spacing={0.5}>
        <Grid item xs={12}>
          <Typography gutterBottom variant="h6">
            Missing Invoices ({loading ? '-' : missingTUSDs?.length})
          </Typography>
        </Grid>
        {jsx}
      </Grid>
    </div>
  );
};

const exporter = (rows) => {
  const keys = [];
  const rowsForExport = rows.map((row) => {
    const returnRow = {};
    const dates = ['createdAt', 'updatedAt'];
    Object.keys(row).forEach((attr) => {
      if (attr === '__typename') return;
      if (keys.indexOf(attr) === -1) {
        keys.push(attr);
      }
      const data = row[String(attr)];
      if (data && data.__typename) {
        delete data.__typename;
      }
      if (dates.indexOf(attr) > -1) {
        returnRow[String(attr)] = moment(data).format('MMM D, YYYY');
      } else if (attr === 'project') {
        returnRow[String(attr)] = data.name;
      } else {
        returnRow[String(attr)] = data;
      }
    });
    return returnRow;
  });
  return downloadSimpleExcelFromRows(rowsForExport, keys, `TUSDInvoices.xlsx`);
};

const styleRow = (record, index) => {
  const { downloadUrl } = record;
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (!downloadUrl && process.env.NODE_ENV === 'production') {
    return errorStyle;
  }
  return {};
};

export const TUSDInvoiceList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<TUSDInvoiceFilter />}
      aside={<Aside />}
      exporter={exporter}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField
          label="Generation Year"
          source="accountYear"
          textAlign="right"
        />
        <NumberField label="Generation Month" source="accountMonth" />
        <NumberField
          source="totalGeneratedEnergy"
          label="Energy Generated (kWh)"
        />
        <NumberField
          source="totalAdjustedGeneratedEnergy"
          label="Energy Generated (kWh) (adjusted)"
        />
        <NumberField
          source="totalConsumedEnergy"
          label="Energy Consumed (kWh)"
        />
        <NumberField
          source="totalAdjustedConsumedEnergy"
          label="Energy Consumed (kWh) (adjusted)"
        />
        <NumberField
          source="consumptionCost"
          label="Cost of Energy Consumed (BRL)"
        />
        <NumberField source="invoicedAmount" label="Invoiced Amount (BRL)" />
        <NumberField
          source="peakConsumedEnergy"
          label="Peak Energy Consumed (kWh)"
        />
        <NumberField
          source="offPeakConsumedEnergy"
          label="Off-peak Energy Consumed (kWh)"
        />
        <NumberField
          source="peakGeneratedEnergy"
          label="Peak Energy Generated (kWh)"
        />
        <NumberField
          source="offPeakGeneratedEnergy"
          label="Off-peak Energy Generated (kWh)"
        />
        <NumberField source="peakOffPeakFactor" label="Peak/Off-peak Factor" />
        <NumberField source="peakUGBalance" label="Peak UG Balance" />
        <NumberField source="offPeakUGBalance" label="Off-peak UG Balance" />
        <FunctionField
          label="Invoice"
          render={(record) => {
            return (
              <Button
                variant="contained"
                startIcon={<CloudDownload />}
                style={{ textTransform: 'none' }}
                onClick={(event) => {
                  if (!record.downloadUrl) return;
                  window.location.assign(record.downloadUrl);
                  event.stopPropagation();
                }}
                color={record.downloadUrl ? 'primary' : 'error'}
                disabled={!record.downloadUrl}
              >
                {record.downloadUrl ? 'Download Invoice' : 'Invoice Missing'}
              </Button>
            );
          }}
        />
        <LinkField
          reference="BrBillingCycle"
          linkSource="brBillingCycle.id"
          labelSource="brBillingCycle.label"
          label="Billing Cycle"
        />
        <BooleanField source="discrepancyFlg" />
        <DetailField source="notes" sortable={false} />
        <DateField source="updatedAt" showTime />
        <DateField source="createdAt" showTime />
        <FunctionField
          label="Issue Descriptions"
          render={(record) => {
            let issues = [];
            if (!record.downloadUrl && process.env.NODE_ENV === 'production') {
              issues.push('Missing Invoice');
            }
            return (
              <ol>
                {issues.map((el) => (
                  <li>{el}</li>
                ))}
              </ol>
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const TUSDInvoiceCreate = () => {
  const [invoiceAwsObjectKey, setInvoiceAwsObjectKey] = useState(null);
  const notify = useNotify();

  const MyCreateButton = () => {
    const dataProvider = useDataProvider();
    const { getValues } = useFormContext();
    const redirect = useRedirect();

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      const { id, ...data } = getValues();
      data.awsObjectKey = invoiceAwsObjectKey;
      dataProvider.create('TUSDInvoice', { id, data }).then(
        () => {
          notify('Element created');
          redirect('/TUSDInvoice');
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={!invoiceAwsObjectKey}
        />
      </Toolbar>
    );
  };

  const uploadInvoiceToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      // const aFileName = file.name.split('.');
      // const fileExtension =
      //   aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `TUSDInvoices/${formData.project.id}_${formData.accountYear}_${
          formData.accountMonth
        }_${moment().valueOf()}_${file.name}`
      );
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          setInvoiceAwsObjectKey(awsObjectKey);
        },
        (e) => {
          notify('Error uploading invoice to S3', { type: 'error' });
        }
      );
    }
  };
  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm toolbar={<MyCreateButton />}>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <SelectInput
              source="accountMonth"
              helperText="This is the month the generation was produced (not the month the invoice was created)."
              fullWidth
              required
              choices={[
                { id: 1, name: 'January' },
                { id: 2, name: 'February' },
                { id: 3, name: 'March' },
                { id: 4, name: 'April' },
                { id: 5, name: 'May' },
                { id: 6, name: 'June' },
                { id: 7, name: 'July' },
                { id: 8, name: 'August' },
                { id: 9, name: 'September' },
                { id: 10, name: 'October' },
                { id: 11, name: 'November' },
                { id: 12, name: 'December' },
              ]}
            />
            <CustomNumberInput
              source="accountYear"
              defaultValue={moment().year()}
              required
              fullWidth
            />
            <CustomNumberInput
              source="consumptionCost"
              label="Cost of energy consumed (BRL)"
              fullWidth
            />
            <CustomNumberInput
              source="invoicedAmount"
              label="Invoiced Amount (BRL)"
              fullWidth
            />
            <CustomNumberInput
              source="peakConsumedEnergy"
              label="Peak Energy Consumed (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="offPeakConsumedEnergy"
              label="Off-peak Energy Consumed (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="peakGeneratedEnergy"
              label="Peak Energy Generated (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="offPeakGeneratedEnergy"
              label="Off-peak Energy Generated (kWh)"
              fullWidth
            />
            <CustomNumberInput
              source="peakOffPeakFactor"
              label="Peak/Off-peak Factor"
              fullWidth
            />
            <CustomNumberInput
              source="peakUGBalance"
              label="Peak UG Balance"
              fullWidth
            />
            <CustomNumberInput
              source="offPeakUGBalance"
              label="Off-peak UG Balance"
              fullWidth
            />
            <ReferenceInput
              source="project.id"
              reference="Project"
              sort={{ field: 'name', order: 'ASC' }}
              perPage={10000}
            >
              <SelectInput
                label="Project"
                fullWidth
                required
                optionText="name"
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Collapse
                    in={
                      formData.project?.id &&
                      formData.accountMonth &&
                      formData.accountYear &&
                      formData.peakConsumedEnergy !== null &&
                      formData.offPeakConsumedEnergy !== null &&
                      formData.peakGeneratedEnergy !== null &&
                      formData.offPeakGeneratedEnergy !== null
                    }
                  >
                    {invoiceAwsObjectKey ? (
                      <Grid container alignItems="center">
                        <Grid item>
                          <CheckCircle
                            style={{ color: theme.palette.success.main }}
                          />
                        </Grid>
                        <Grid item>
                          <Typography
                            style={{
                              color: theme.palette.success.main,
                              paddingLeft: '.5rem',
                            }}
                          >
                            Invoice ready to upload. Click 'Save' below to
                            complete upload.
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <Button
                        color="primary"
                        disabled={invoiceAwsObjectKey}
                        variant="contained"
                        component="label" // https://stackoverflow.com/a/********
                      >
                        Upload Invoice
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            uploadInvoiceToS3(event, formData)
                          }
                          accept="application/pdf"
                        />
                      </Button>
                    )}
                  </Collapse>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
