import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { RichTextInput } from 'ra-input-rich-text';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Br Faq Entry';

export const BrFaqEntryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput multiline source="questionEN" fullWidth />
            <TextInput multiline source="questionPT" fullWidth />
            <RichTextInput multiline source="answerEN" fullWidth />
            <RichTextInput multiline source="answerPT" fullWidth />
            <CustomNumberInput source="orderNo" step={1} fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrFaqEntryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <NumberField source="orderNo" />
        <TextField source="questionEN" />
        <TextField source="questionPT" />
        <FunctionField
          label="Answer (EN)"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <div dangerouslySetInnerHTML={{ __html: record.answerEN }} />
            );
          }}
        />
        <FunctionField
          label="Answer (PT)"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <div dangerouslySetInnerHTML={{ __html: record.answerPT }} />
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const BrFaqEntryCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput multiline source="questionEN" fullWidth />
          <TextInput multiline source="questionPT" fullWidth />
          <RichTextInput multiline source="answerEN" fullWidth />
          <RichTextInput multiline source="answerPT" fullWidth />
          <CustomNumberInput source="orderNo" step={1} fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
