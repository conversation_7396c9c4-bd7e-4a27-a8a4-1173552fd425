import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>yField,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  Edit,
  FunctionField,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Event Type';

export const EventTypeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextField source="id" />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <BooleanInput
              source="hideFromPreferences"
              label="Hide from user notification preference settings"
            />
            <TextInput fullWidth source="name" />
            <TextInput fullWidth source="description" />
            <CustomNumberInput fullWidth source="orderNo" step={1} />
            <TextInput fullWidth source="iconClass" />
          </Grid>
        </Grid>
        <ArrayField fullWidth source="events">
          <Datagrid>
            <TextField source="id" />
            <TextField source="title" />
          </Datagrid>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const EventTypeList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'orderNo', order: 'ASC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <NumberField source="orderNo" />
        <BooleanField source="hideFromPreferences" />
        <FunctionField
          label="Icon"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <span>
                <i
                  style={{ marginRight: '6px' }}
                  className={record.iconClass}
                />
                {record.iconClass}
              </span>
            );
          }}
        />
        {/* <TextField source="events" /> */}
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const EventTypeCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
