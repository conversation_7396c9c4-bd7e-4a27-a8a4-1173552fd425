import React, { useState } from 'react';
import moment from 'moment';
import numeral from 'numeral';
import { useParams } from 'react-router-dom';
import {
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  Filter,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  Typography,
} from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { BudgetLineItemsUpload } from './BudgetLineItemsUpload';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Budget Line Item';

export const BudgetLineItemEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextField source="id" />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <DateInput source="transferDate" fullWidth />
            <CustomNumberInput source="netTransfer" fullWidth />
            <ReferenceInput
              source="leadSourceCategory.id"
              reference="LeadSourceCategory"
            >
              <SelectInput
                label="Lead Source Category"
                required
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const BudgetLineItemFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Lead Source Category"
      source="leadSourceCategory.id"
      reference="LeadSourceCategory"
      perPage={10_000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Lead Source Category" optionText="name" />
    </ReferenceInput>
  </Filter>
);
const BudgetLineItemPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);
const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      <ExportButton maxResults={10_000} />
    </TopToolbar>
  );
};

export const BudgetLineItemList = () => {
  const refresh = useRefresh();
  const [openDialog, setOpenDialog] = useState(false);
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();

  return (
    <>
      <Grid container justifyContent="flex-end">
        <Grid item>
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setOpenDialog(true)}
          >
            Undo latest upload
          </Button>
          <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
            <DialogTitle>Undo latest upload?</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to undo the latest upload? This cannot be
                undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenDialog(false)} color="primary">
                Cancel
              </Button>
              <Button
                onClick={() => {
                  dataProvider
                    .delete('BudgetLineItem', {
                      id: 0, // zero id indicates that you are deleting all most recently uploaded entries
                    })
                    .then(() => {
                      setOpenDialog(false);
                      refresh();
                    });
                }}
                color="primary"
                autoFocus
              >
                Undo
              </Button>
            </DialogActions>
          </Dialog>
        </Grid>
      </Grid>
      <List
        title="Budget Line Items"
        perPage={25}
        pagination={<BudgetLineItemPagination />}
        actions={<ListActions />}
        sort={{ field: 'id', order: 'DESC' }}
        filters={<BudgetLineItemFilter />}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="name" />
          <DateField source="transferDate" />
          <NumberField
            source="netTransfer"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <LinkField
            reference="LeadSourceCategory"
            linkSource="leadSourceCategory.id"
            labelSource="leadSourceCategory.name"
            label="Lead Source Category"
          />
          <DateField source="createdAt" />
        </Datagrid>
      </List>
    </>
  );
};

export const BudgetLineItemCreate = () => {
  const attrs = [
    { name: 'name', label: 'Name', align: 'center' },
    {
      name: 'transferDate',
      format: (val) => moment(val).format('MM/DD/YYYY'),
      label: 'Transfer Date',
      align: 'center',
    },
    {
      name: 'netTransfer',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Net Transfer',
      align: 'center',
    },
    {
      name: 'category',
      dataFormat: (val) => val,
      format: (val) => val,
      label: 'Category',
      align: 'center',
    },
  ];

  return (
    <Create title={`Create ${entityName}`}>
      <Grid item style={{ paddingLeft: '1rem' }}>
        <Typography style={{ fontWeight: 'bold' }}>
          Upload from Excel:
        </Typography>
      </Grid>
      <BudgetLineItemsUpload attrs={attrs} />
      <Divider style={{ width: '100%', margin: '1rem' }} />
      <Grid item style={{ paddingLeft: '1rem' }}>
        <Typography style={{ fontWeight: 'bold' }}>
          Create Singular Line Item:
        </Typography>
      </Grid>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="name"
              fullWidth
              required
              defaultValue="GOOGLE ADS"
            />
            <DateInput source="transferDate" fullWidth required />
            <CustomNumberInput
              source="netTransfer"
              label="Amount (USD)"
              fullWidth
              required
            />
            <ReferenceInput
              source="leadSourceCategory.id"
              reference="LeadSourceCategory"
            >
              <SelectInput
                label="Lead Source Category"
                required
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
