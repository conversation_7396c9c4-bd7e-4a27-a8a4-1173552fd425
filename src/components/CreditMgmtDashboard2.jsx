import React, { useState } from 'react';
import {
  Badge,
  <PERSON>ton,
  Card,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField as MuiTextField,
  Tooltip,
  Typography,
  useMediaQuery,
  DialogTitle,
  CardActionArea,
  CardContent,
} from '@mui/material';
import { Alert } from '@mui/lab';
import { Link, useDataProvider, useNotify, usePermissions } from 'react-admin';
import numeral from 'numeral';
import 'chartjs-adapter-moment';
import { withStyles } from '@mui/styles';
import {
  ArrowForward,
  Close,
  CloudDownload,
  CloudUpload,
  Description,
  GetApp,
  Refresh,
} from '@mui/icons-material';

import { downloadSimpleExcelFromRows } from '../utils/excel';
import { CreditMgmtBillingReportDialog } from './CreditMgmtBillingReportDialog2';
import { FaturamentosExpansionPanel } from './FaturamentosExpansionPanel2';
import { uploadObjectToS3 } from '../utils/aws';
import theme from '../theme';
import { Bar, Line } from 'react-chartjs-2';
import {
  getGradient,
  interpolateColors,
  lintAwsObjectKey,
} from '../utils/global';

const styles = (theme) => ({});

export default withStyles(styles)(() => {
  const fullScreen = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const [loading, setLoading] = useState(null);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const [billingDialogOpen, setBillingDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [downloadPendingId, setDownloadPendingId] = useState(null);
  const [utilityBillKeys, setUtilityBillKeys] = useState({});
  const [exportEmail, setExportEmail] = useState(null);
  const [emailExportOpen, setEmailExportOpen] = useState(false);
  const [portfolios, setPortfolios] = useState(null);
  const [selectedPortfolioId, setSelectedPortfolioId] = useState(null);

  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();
  const roleNames = permissions.roles?.map((role) => role.name) || [];
  const hasITWrite = roleNames.indexOf('ITWrite') !== -1;
  const hasCreditMgmtWrite = roleNames.indexOf('CreditMgmtWrite') !== -1;
  const isArthur =
    permissions.email === '<EMAIL>' ||
    permissions.email === '<EMAIL>' ||
    permissions.email === '<EMAIL>' ||
    permissions.email === '<EMAIL>';

  const isIT =
    roleNames.indexOf('ITWrite') !== -1 || roleNames.indexOf('ITRead') !== -1;

  const fetchData = () => {
    setLoading(true);
    dataProvider
      .getList('CreditMgmtDashboard2Data', {
        filter: {},
        pagination: {
          page: 1,
          perPage: 10_000,
        },
        sort: {
          field: 'orderNo',
          order: 'ASC',
        },
      })
      .then(
        (res) => {
          const aPortfolios = [];
          res.data.forEach((project) => {
            if (
              aPortfolios.filter((p) => p.id === project.portfolio.id)
                .length === 0
            ) {
              aPortfolios.push({
                id: project.portfolio.id,
                subtitle: project.portfolio.subtitle,
                numberOfProjects: res.data.filter(
                  (p2) => p2.portfolio.id === project.portfolio.id
                ).length,
              });
            }
          });
          setPortfolios(aPortfolios);
          setSelectedPortfolioId(aPortfolios[0].id);
          setData(res.data);
          setError(null);
          setLoading(false);
        },
        (err) => {
          setLoading(false);
          setError(err);
          console.error('Error fetching projects', err);
        }
      );
  };

  const handleEmailExport = () => {
    setLoading(true);
    dataProvider
      .getOne('ArthurCreditMgmtDashboard2Export', {
        email: exportEmail,
      })
      .then(
        (res) => {
          setLoading(false);
          notify('You will receive an email shortly', { type: 'success' });
          setEmailExportOpen(false);
        },
        (err) => {
          console.error(err);
          setLoading(false);
          notify('There was an issue exporting the data', { type: 'error' });
        }
      );
  };

  if (!data && !loading && !error) {
    setLoading(true);
    fetchData();
  }

  const calculateCreditAdjustmentTotal = (brBillingCycle) =>
    brBillingCycle?.brCreditAdjustments?.reduce(
      (acc, adjustment) => acc + adjustment?.creditAdjustment,
      0
    ) || 0;

  const tableData = [
    {
      label: 'Project',
      tooltipDescription: 'The name of the project as it is listed in the CMS',
      includeInDownload: true,
      downloadOrder: 11,
      arthurOnly: false,
      value: (project, isHistoricalRow) =>
        isHistoricalRow ? null : project?.name,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) =>
        value ? (
          <TableCell>
            <a href={`/SalesforceProject/${project.id}`}>{value}</a>
          </TableCell>
        ) : (
          <TableCell />
        ),
    },
    {
      label: 'Management Status',
      tooltipDescription:
        'Whether this project is managed by Energea or a third party.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project, isHistoricalRow) => {
        if (isHistoricalRow) return null;
        return project.creditManagementExternalFlg ? 'External' : 'Internal';
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) =>
        value ? <TableCell>{value}</TableCell> : <TableCell />,
    },
    {
      label: 'Consortium Type',
      tooltipDescription:
        'Whether this project is self-consumption (single off-taker) or distributed generation (consortium)',
      includeInDownload: false,
      arthurOnly: false,
      value: (project, isHistoricalRow) =>
        isHistoricalRow ? null : project.consortiumType,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => <TableCell>{value}</TableCell>,
    },
    {
      label: 'Billing Reference Month',
      tooltipDescription: 'The month in which the generation was invoiced.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.billingMonth,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => <TableCell>{value}</TableCell>,
    },
    {
      label: 'Generation Reference Month (Measurement Month)',
      tooltipDescription: 'The month in which the energy was generated.',
      includeInDownload: true,
      downloadOrder: 1,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.generationMonth,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => <TableCell>{value}</TableCell>,
    },
    {
      label: 'UG Balance (kWh)',
      tooltipDescription:
        'Calculated by summing peak and off peak UGs balances from a billing cycle',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.ugBalance,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Energy Generated (kWh) (CMS)',
      tooltipDescription:
        'The amount of energy generated according to the data that the CMS received from the data logger on site',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.generatedEnergyCMS,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Gross Energy Generated (kWh) (TUSD)',
      tooltipDescription:
        'The generated energy read from the TUSD bill. Calculated by summing peak and off peak generated energy using the peak/off-peak conversion factor',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        let value = 0;
        project.latestBrBillingCycle.tusdInvoices.forEach((invoice) => {
          const {
            peakGeneratedEnergy,
            offPeakGeneratedEnergy,
            peakOffPeakFactor,
          } = invoice;
          value +=
            (peakGeneratedEnergy || 0) * peakOffPeakFactor +
            (offPeakGeneratedEnergy || 0);
        });
        return value;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => {
        const cmsGeneration = project.latestBrBillingCycle?.generatedEnergyCMS;
        const allowedGenerationPercentageDiff = 0.05;
        const isMicroProject = project?.name
          ?.toLowerCase()
          ?.includes('micro -');
        const tusdGenerationDoesntMatchCMS =
          value &&
          cmsGeneration &&
          value < cmsGeneration * (1 - allowedGenerationPercentageDiff);
        return (
          <Tooltip
            arrow
            title={
              tusdGenerationDoesntMatchCMS
                ? `TUSD gross generation is more than ${numeral(
                    allowedGenerationPercentageDiff
                  ).format('0%')} below generation recorded in CMS. ${
                    isMicroProject
                      ? 'This can be expected for Micros projects because the TUSD invoice is not often read from the 1st of the month but rather may be a reading from the 15th to the 16th.'
                      : ''
                  }`
                : null
            }
          >
            <TableCell
              align="right"
              style={{
                backgroundColor: tusdGenerationDoesntMatchCMS
                  ? isMicroProject
                    ? 'rgb(255, 255, 179)'
                    : 'rgb(255, 204, 203)'
                  : undefined,
              }}
            >
              {value !== null && numeral(value).format('0,0')}
            </TableCell>
          </Tooltip>
        );
      },
    },
    {
      label: 'Net Generation (Energia Injetada TUSD kWh)',
      tooltipDescription:
        'The injected energy read from the TUSD bill. Calculated by summing peak and off peak injected energy using the peak/off-peak conversion factor',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        let value = 0;
        project.latestBrBillingCycle.tusdInvoices.forEach((invoice) => {
          const {
            peakInjectedEnergy,
            offPeakInjectedEnergy,
            peakOffPeakFactor,
          } = invoice;
          value +=
            (peakInjectedEnergy || 0) * peakOffPeakFactor +
            (offPeakInjectedEnergy || 0);
        });
        return value;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Start of Month UC Balance (kWh)',
      tooltipDescription:
        'Calculated by summing peak and off peak UCs balances from previous months billing cycle',
      includeInDownload: true,
      downloadOrder: 12,
      arthurOnly: false,
      value: (project) =>
        project.latestBrBillingCycle?.precedingBrBillingCycle?.ucBalance,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => {
        const creditAdjustmentTotal = calculateCreditAdjustmentTotal(
          project.latestBrBillingCycle?.precedingBrBillingCycle
        );
        const previousMonthAdjustedUCBalance =
          (project.latestBrBillingCycle?.precedingBrBillingCycle
            ?.availableCredits || 0) -
          (project.latestBrBillingCycle?.precedingBrBillingCycle
            ?.billedCredits || 0) +
          creditAdjustmentTotal;
        const thisMonthStartUCBalance = value;

        const allowedCreditDiff = 5; // Patrick suggested 5 credits difference is acceptable because we they don't use decimals 9/25/2024

        const creditsDontMatchPreviousMonth =
          Math.abs(thisMonthStartUCBalance - previousMonthAdjustedUCBalance) >
            allowedCreditDiff &&
          project?.latestBrBillingCycle?.precedingBrBillingCycle;
        return (
          <Tooltip
            arrow
            title={
              creditsDontMatchPreviousMonth
                ? "Start of month UC balance doesn't match previous month's adjusted UC balance"
                : null
            }
          >
            <TableCell
              align="right"
              style={{
                backgroundColor: creditsDontMatchPreviousMonth
                  ? 'rgb(255, 255, 179)'
                  : undefined,
              }}
            >
              {thisMonthStartUCBalance !== null &&
                numeral(thisMonthStartUCBalance).format('0,0')}
            </TableCell>
          </Tooltip>
        );
      },
    },
    {
      label: 'Total Allocated Credits',
      tooltipDescription:
        'The amount of injected energy this month plus the UC balance from the previous month',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.availableCredits,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Billed Credits',
      tooltipDescription:
        'The sum of injected electricity across all consumer units in this month for this project.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.billedCredits,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'UC Balance (kWh)',
      tooltipDescription:
        'Calculated by subtracting billed credits from available (allocated) credits.',
      includeInDownload: true,
      downloadOrder: 13,
      arthurOnly: false,
      value: (project) => {
        if (
          (project.latestBrBillingCycle?.availableCredits ||
            project.latestBrBillingCycle?.availableCredits === 0) &&
          (project.latestBrBillingCycle?.billedCredits ||
            project.latestBrBillingCycle?.billedCredits === 0)
        ) {
          return (
            project.latestBrBillingCycle.availableCredits -
            project.latestBrBillingCycle.billedCredits
          );
        }
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: '',
      tooltipDescription: '',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => null,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => {
        let borderColors = interpolateColors(1, null, null, 1);
        return (
          <TableCell align="right">
            <Line
              height={100}
              width={150}
              data={{
                datasets: [
                  {
                    label: project.name,
                    data: project.ucBalanceChartData?.map((month) => ({
                      x: month.billingMonth,
                      y: month.ucBalance,
                    })),
                    pointRadius: 0,
                    fill: true,
                    borderWidth: 3,
                    borderColor: borderColors[0],
                    backgroundColor: getGradient(borderColors[0], 0.5),
                  },
                ],
              }}
              options={{
                layout: {
                  padding: {
                    top: 1.5,
                    bottom: 1.5,
                    left: 1.5,
                    right: 1.5,
                  },
                },
                maintainAspectRatio: false,
                borderRadius: 4,
                plugins: {
                  legend: { display: false },
                  tooltip: { enabled: false },
                },
                scales: {
                  x: {
                    type: 'time',
                    grid: {
                      display: false,
                    },
                    display: false,
                    time: {
                      tooltipFormat: 'YYYY-MM',
                      unit: 'month',
                    },
                  },
                  y: {
                    beginAtZero: true,
                    grid: {
                      display: false,
                    },
                    display: false,
                  },
                },
              }}
            />
          </TableCell>
        );
      },
    },
    {
      label: 'Adjusted UC Balance (kWh)',
      tooltipDescription:
        'Calculated by subtracting billed credits from available (allocated) credits and summing manually input credit adjustments. Hover over the value to read notes from any adjustments made.',
      includeInDownload: true,
      downloadOrder: 14,
      arthurOnly: false,
      value: (project) => {
        if (
          (project.latestBrBillingCycle?.availableCredits ||
            project.latestBrBillingCycle?.availableCredits === 0) &&
          (project.latestBrBillingCycle?.billedCredits ||
            project.latestBrBillingCycle?.billedCredits === 0)
        ) {
          const creditAdjustmentTotal = calculateCreditAdjustmentTotal(
            project.latestBrBillingCycle
          );
          return (
            project.latestBrBillingCycle.availableCredits -
            project.latestBrBillingCycle.billedCredits +
            creditAdjustmentTotal
          );
        }
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <Tooltip
          title={
            project.latestBrBillingCycle?.brCreditAdjustments?.length ? (
              <Grid container direction="column">
                {project.latestBrBillingCycle?.brCreditAdjustments?.map(
                  (adjustment) => (
                    <Grid item key={`credit-adjustment-${adjustment.id}`}>
                      <Typography variant="caption">
                        {numeral(adjustment.creditAdjustment).format(
                          '0,0[.]00'
                        )}{' '}
                        credits - <i>{adjustment?.notes || 'No notes'}</i>
                      </Typography>
                    </Grid>
                  )
                )}
              </Grid>
            ) : null
          }
        >
          <TableCell align="right">
            <Badge
              badgeContent={
                project.latestBrBillingCycle?.brCreditAdjustments?.length
              }
              color="primary"
              overlap="circular"
            >
              <Grid item style={{ margin: '0.6rem' }}>
                {value !== null && numeral(value).format('0,0')}
              </Grid>
            </Badge>
          </TableCell>
        </Tooltip>
      ),
    },
    {
      label: 'Expected Revenue (R$)',
      tooltipDescription: `The sum of all invoices 'Amount due' values.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.expectedRevenue,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Realized Revenue (R$)',
      tooltipDescription: `The sum of all invoices 'Amount paid' values.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.currentRevenue,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Revenue from Over-Payment (R$)',
      tooltipDescription: `The sum of any excess paid (likely due to fees and interest) on all invoices.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.revenueDueToOverPayment,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Unpaid Revenue (R$)',
      tooltipDescription: `The sum of all unpaid invoice values excluding over-payment on individual invoices likely due to fees and interest.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) =>
        project.latestBrBillingCycle?.overdueRevenue +
        project.latestBrBillingCycle?.openRevenue,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <Tooltip
          title={
            <Grid container direction="column">
              <Grid item>
                <Typography variant="caption">
                  Open Revenue:{' '}
                  {numeral(project.latestBrBillingCycle?.openRevenue).format(
                    '0,0.00'
                  )}
                </Typography>
              </Grid>
              <Grid item>
                <Typography variant="caption">
                  Past Due Revenue:{' '}
                  {numeral(project.latestBrBillingCycle?.overdueRevenue).format(
                    '0,0.00'
                  )}
                </Typography>
              </Grid>
            </Grid>
          }
        >
          <TableCell align="right">
            {value !== null && numeral(value).format('0,0.00')}
          </TableCell>
        </Tooltip>
      ),
    },
    {
      label: '% Unpaid',
      tooltipDescription:
        'The sum of open revenue and past due revenue dividend by the expected revenue. Excludes over-payment on individual invoices.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        const amountUnpaid =
          (project.latestBrBillingCycle?.overdueRevenue || 0) +
          (project.latestBrBillingCycle?.openRevenue || 0);
        const expected = project.latestBrBillingCycle?.expectedRevenue;
        if (!expected || expected === 0) return null;
        return amountUnpaid / expected;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <TableCell
          align="right"
          style={{
            backgroundColor: project.creditManagementExternalFlg
              ? '#ddd'
              : null,
          }}
        >
          {value !== null && numeral(value).format('0,0.0%')}
        </TableCell>
      ),
    },
    {
      label: 'Default Rate',
      tooltipDescription:
        'Past due revenue (excludes over-payment on individual invoices) divided by the expected revenue.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        const pastDue = project.latestBrBillingCycle?.overdueRevenue || 0;
        const expected = project.latestBrBillingCycle?.expectedRevenue;
        if (!expected || expected === 0) return null;
        return pastDue / expected;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <TableCell
          align="right"
          style={{
            backgroundColor: project.creditManagementExternalFlg
              ? '#ddd'
              : null,
          }}
        >
          {value !== null && numeral(value).format('0,0.0%')}
        </TableCell>
      ),
    },
    {
      label: 'Cumulative Expected Revenue (R$)',
      tooltipDescription: `The sum of all invoice values excluding over-payment on individual invoices likely due to fees or interest from all billing months prior to and including this one.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) =>
        project.latestBrBillingCycle?.cumulativeExpectedRevenue,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Cumulative Unpaid Revenue (R$)',
      tooltipDescription: `The sum of all unpaid invoice values excluding over-payment on individual invoices likely due to fees or interest from all billing months prior to and including this one.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.cumulativeUnpaidRevenue,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Cumulative Uncollectible Revenue (R$)',
      tooltipDescription: `The sum of all unpaid invoice values that have been flagged as uncollectible from all billing months prior to and including this one.`,
      includeInDownload: false,
      arthurOnly: false,
      value: (project) =>
        project.latestBrBillingCycle?.cumulativeUncollectibleRevenue,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Cumulative Default Rate',
      tooltipDescription:
        'Cumulative unpaid revenue (excludes over-payment on individual invoices) divided by the cumulative expected revenue.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        const pastDue =
          project.latestBrBillingCycle?.cumulativeUnpaidRevenue || 0;
        const expected =
          project.latestBrBillingCycle?.cumulativeExpectedRevenue;
        if (!expected || expected === 0) return null;
        return pastDue / expected;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value, project) => (
        <TableCell
          align="right"
          style={{
            backgroundColor: project.creditManagementExternalFlg
              ? '#ddd'
              : null,
          }}
        >
          {value !== null && numeral(value).format('0,0.0%')}
        </TableCell>
      ),
    },
    {
      label: 'Expected MWh Price (R$/MWh)',
      tooltipDescription:
        'The expected MWh price is the expected revenue divided by the billed credits.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle) return null;
        if (!project.latestBrBillingCycle?.billedCredits) return null;
        return (
          project.latestBrBillingCycle.expectedRevenue /
          (project.latestBrBillingCycle.billedCredits / 1000)
        );
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Realized MWh Price (R$/MWh)',
      tooltipDescription:
        'The realized MWh price is the realized revenue divided by the billed credits.',
      includeInDownload: false,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle) return null;
        if (!project.latestBrBillingCycle?.billedCredits) return null;
        return (
          project.latestBrBillingCycle.currentRevenue /
          (project.latestBrBillingCycle.billedCredits / 1000)
        );
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Gross Generation (Energia Geracao TUSD kWh)',
      tooltipDescription:
        'The generated energy read from the TUSD bill. Calculated by summing peak and off peak generated energy using the peak/off-peak conversion factor',
      includeInDownload: true,
      downloadOrder: 2,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        let value = 0;
        project.latestBrBillingCycle.tusdInvoices.forEach((invoice) => {
          const {
            peakGeneratedEnergy,
            offPeakGeneratedEnergy,
            peakOffPeakFactor,
          } = invoice;
          value +=
            (peakGeneratedEnergy || 0) * peakOffPeakFactor +
            (offPeakGeneratedEnergy || 0);
        });
        return value;
      },
      labelJsx: (label) => (
        <TableCell
          style={{
            fontWeight: 'bold',
            borderLeft: '1px solid lightgray',
          }}
        >
          {label}
        </TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right" style={{ borderLeft: '1px solid lightgray' }}>
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Consumption (Energia Consumo TUSD kWh)',
      tooltipDescription:
        'The consumed energy read from the TUSD bill. Calculated by summing peak and off peak consumed energy using the peak/off-peak conversion factor',
      includeInDownload: true,
      downloadOrder: 3,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        let value = 0;
        project.latestBrBillingCycle.tusdInvoices.forEach((invoice) => {
          const {
            peakConsumedEnergy,
            offPeakConsumedEnergy,
            peakOffPeakFactor,
          } = invoice;
          value +=
            (peakConsumedEnergy || 0) * peakOffPeakFactor +
            (offPeakConsumedEnergy || 0);
        });
        return value;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Net Generation (Energia Injetada TUSD kWh)',
      tooltipDescription:
        'The injected energy read from the TUSD bill. Calculated by summing peak and off peak injected energy using the peak/off-peak conversion factor',
      includeInDownload: true,
      downloadOrder: 4,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        let value = 0;
        project.latestBrBillingCycle.tusdInvoices.forEach((invoice) => {
          const {
            peakInjectedEnergy,
            offPeakInjectedEnergy,
            peakOffPeakFactor,
          } = invoice;
          value +=
            (peakInjectedEnergy || 0) * peakOffPeakFactor +
            (offPeakInjectedEnergy || 0);
        });
        return value;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Consumption (Energia Fornecida/Consumida kWh)',
      tooltipDescription:
        'The injected energy summed with the amount provided by the utility company across all consumer units',
      includeInDownload: true,
      downloadOrder: 5,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.totalGrossConsumption,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Demand Charge (TUSD R$)',
      tooltipDescription:
        'The demand charge (invoiced amount) read from the TUSD bill',
      includeInDownload: true,
      downloadOrder: 6,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        return project.latestBrBillingCycle.tusdInvoices.reduce(
          (acc, invoice) => acc + invoice.invoicedAmount,
          0
        );
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Credits Created (Injected Energy kWh)',
      tooltipDescription:
        'The injected energy read from the TUSD bill. Calculated by summing peak and off peak injected energy using the peak/off-peak conversion factor',
      includeInDownload: true,
      downloadOrder: 7,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle?.tusdInvoices?.length) return null;
        let value = 0;
        project.latestBrBillingCycle.tusdInvoices.forEach((invoice) => {
          const {
            peakInjectedEnergy,
            offPeakInjectedEnergy,
            peakOffPeakFactor,
          } = invoice;
          value +=
            (peakInjectedEnergy || 0) * peakOffPeakFactor +
            (offPeakInjectedEnergy || 0);
        });
        return value;
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Credits Redeemed (Energia Compensada kWh)',
      tooltipDescription:
        'The number of billed credits summed across all consumer units',
      includeInDownload: true,
      downloadOrder: 8,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.billedCredits,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      label: 'Weighted Contracted Rate (R$/MWh)',
      tooltipDescription:
        'The expected MWh price is the expected revenue divided by the billed credits.',
      includeInDownload: true,
      downloadOrder: 9,
      arthurOnly: false,
      value: (project) => {
        if (!project.latestBrBillingCycle) return null;
        if (!project.latestBrBillingCycle?.billedCredits) return null;
        return (
          project.latestBrBillingCycle.expectedRevenue /
          (project.latestBrBillingCycle.billedCredits / 1000)
        );
      },
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0.00')}
        </TableCell>
      ),
    },
    {
      label: 'Number of Consumer Units',
      tooltipDescription: 'The number of consumer units we billed this month',
      includeInDownload: true,
      downloadOrder: 10,
      arthurOnly: false,
      value: (project) => project.latestBrBillingCycle?.numberOfConsumerUnits,
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) => (
        <TableCell align="right">
          {value !== null && numeral(value).format('0,0')}
        </TableCell>
      ),
    },
    {
      includeInDownload: false,
      arthurOnly: true,
      label: 'Download',
      tooltipDescription:
        'Download an excel will all historical months for this project with a subset of the columns in this table',
      value: (project, isHistoricalRow) => (isHistoricalRow ? null : project),
      labelJsx: (label) => (
        <TableCell style={{ fontWeight: 'bold' }}>{label}</TableCell>
      ),
      valueJsx: (value) =>
        !value ? (
          <TableCell />
        ) : (
          <TableCell>
            <IconButton
              onClick={() => {
                setDownloadPendingId(value.id);
              }}
              disabled={downloadPendingId === value.id}
            >
              {downloadPendingId === value.id ? (
                <CircularProgress
                  style={{ position: 'absolute', color: 'white' }}
                />
              ) : null}
              <CloudDownload />
            </IconButton>
          </TableCell>
        ),
    },
  ];

  const renderConsortiumProjects = () => {
    if (error) {
      return (
        <Alert severity="error">
          There was an issue loading consortium projects.
        </Alert>
      );
    }

    if (loading || !data) {
      return (
        <Grid container style={{ width: '100%' }} justifyContent="center">
          <CircularProgress />
        </Grid>
      );
    }

    const filterColumns = (column) => {
      if (column.arthurOnly) {
        return (
          isArthur ||
          permissions.roles?.map((role) => role.name)?.includes('ITRead')
        );
      }
      return true;
    };

    return (
      <Grid container style={{ width: '100%' }}>
        <Grid item container spacing={4} style={{ padding: '1rem 0' }}>
          {portfolios.map((portfolio) => {
            const selected = portfolio.id === selectedPortfolioId;
            return (
              <Grid key={`portfolio-container-${portfolio.id}`} item>
                <Card
                  elevation={selected ? 0 : 10}
                  style={{
                    height: '6rem',
                    border: selected
                      ? `solid ${theme.palette.primary.main} 3px`
                      : '',
                    backgroundColor: selected ? 'rgba(230, 230, 230, 0.3)' : '',
                    boxSizing: 'border-box',
                    borderRadius: theme.shape.borderRadius,
                  }}
                >
                  <CardActionArea
                    onClick={() => {
                      setSelectedPortfolioId(portfolio.id);
                      setSelectedProject(null);
                    }}
                    aria-label={`${portfolio.subtitle} tab`}
                    style={{
                      cursor: 'pointer',
                      height: '100%',
                      padding: selected ? null : '3px',
                    }}
                  >
                    <CardContent>
                      <Grid
                        container
                        justifyContent="center"
                        alignItems="center"
                        item
                        xs={12}
                      >
                        <Grid item xs={12}>
                          <Typography
                            variant="h5"
                            style={{ fontWeight: 'normal' }}
                          >
                            {portfolio.subtitle}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography
                            variant="body2"
                            style={{ fontWeight: 'normal' }}
                          >
                            {portfolio.numberOfProjects} projects
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            );
          })}
        </Grid>
        <Card style={{ margin: fullScreen ? null : '.5rem', width: '100%' }}>
          <TableContainer sx={{ maxHeight: '85vh' }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  {tableData
                    .filter((column) => filterColumns(column))
                    .map((column) => (
                      <Tooltip arrow title={column.tooltipDescription}>
                        {column.labelJsx(column.label)}
                      </Tooltip>
                    ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {data
                  .filter((project) =>
                    selectedPortfolioId
                      ? project.portfolio.id === selectedPortfolioId
                      : true
                  )
                  .map((project) => {
                    const rowSelected = selectedProject?.id === project?.id;
                    return (
                      <>
                        <TableRow
                          key={`project-row-${project.id}`}
                          hover
                          onClick={() =>
                            setSelectedProject(rowSelected ? null : project)
                          }
                          style={{ cursor: 'pointer' }}
                          selected={rowSelected}
                        >
                          {tableData
                            .filter((column) => filterColumns(column))
                            .map((column) =>
                              column.valueJsx(column.value(project), project)
                            )}
                        </TableRow>
                        <FaturamentosExpansionPanel
                          open={rowSelected}
                          salesforceProjectId={project.id}
                          tableData={tableData.filter((column) =>
                            filterColumns(column)
                          )}
                          handleHistoricalDataQuerySuccess={
                            handleHistoricalDataQuerySuccess
                          }
                        />
                      </>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      </Grid>
    );
  };

  const handleHistoricalDataQuerySuccess = (projectId, brBillingCycles) => {
    if (!downloadPendingId || downloadPendingId !== projectId) {
      return null;
    }

    setDownloadPendingId(null);
    const rowsForExport = [];
    const reportDownloadKeys = [];
    brBillingCycles.map((brBillingCycle, index) => {
      const returnRow = {};
      tableData.forEach((column) => {
        if (column.includeInDownload) {
          if (
            reportDownloadKeys.filter((k) => k.label === column.label)
              .length === 0
          ) {
            reportDownloadKeys.push({
              label: column.label,
              order: column.downloadOrder,
            });
          }
          returnRow[column.label] = column.value({
            ...brBillingCycle.project,
            latestBrBillingCycle: brBillingCycle,
          });
        }
      });
      rowsForExport.push(returnRow);
    });
    reportDownloadKeys.sort((a, b) => (a.order < b.order ? -1 : 1));
    return downloadSimpleExcelFromRows(
      rowsForExport,
      reportDownloadKeys.map((k) => k.label),
      `Billing Cycles.xlsx`
    );
  };

  const uploadBillsToS3 = async (event, utilityCompany) => {
    const {
      target: { validity, files },
    } = event;
    const fileArray = Array.from(files);
    if (validity.valid) {
      setLoading(true);
      const prefix = `${utilityCompany.toUpperCase()}_BILL_CONVERSION/${new Date().getTime()}`;
      for (let index = 0; index < fileArray.length; index += 1) {
        const file = fileArray[parseInt(index, 10)];
        const fileSize = file.size / 1024 / 1024; // in MiB
        if (fileSize > 10) {
          notify('PDF must be less than 10MB', { type: 'error' });
          return;
        }
        const awsObjectKey = lintAwsObjectKey(`${prefix}/${file.name}`);
        await uploadObjectToS3(
          file,
          awsObjectKey,
          process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
        ).then(
          () => {
            utilityBillKeys[String(awsObjectKey)] = true;
            setUtilityBillKeys({
              ...utilityBillKeys,
            });
            if (Object.keys(utilityBillKeys).length === files.length) {
              dataProvider
                .create('UtilityBillConversion', {
                  data: {
                    awsObjectKeys: Object.keys(utilityBillKeys),
                    email: permissions.email,
                    utilityCompany,
                  },
                })
                .then(() => {
                  notify(
                    `Conversion initiated. Email will be sent to ${permissions.email}`,
                    { type: 'success' }
                  );
                  setLoading(false);
                  setUtilityBillKeys({});
                });
              setLoading(false);
            }
          },
          (err) => {
            console.error(err);
            notify('Error uploading a bill to S3', { type: 'error' });
            setUtilityBillKeys({});
            setLoading(false);
          }
        );
      }
    }
  };

  return (
    <Grid container style={{ margin: fullScreen ? null : '1rem' }}>
      <Grid
        container
        item
        xs={12}
        justifyContent="space-between"
        alignItems="center"
      >
        <Grid item>
          <Grid container alignItems="center">
            <Grid item xs={12}>
              <Typography variant="h5">Credit Management Dashboard</Typography>
            </Grid>
            <Grid item xs={12} style={{ margin: '0 .5rem' }}>
              <Button
                variant="text"
                endIcon={<ArrowForward />}
                component={Link}
                to={'/SalesforceProject'}
                style={{ paddingLeft: 0, textTransform: 'none' }}
              >
                <b>View Additional Project Details</b>
              </Button>
            </Grid>
            {/* <Grid item style={{ margin: '0 .5rem' }}>
              <Alert severity="info">
                Notes: <br />- Patrick is currently in the process of uploading
                data for the self-consumption projects
              </Alert>
            </Grid> */}
          </Grid>
        </Grid>
        <Grid item>
          <Grid container>
            {isArthur || isIT ? (
              <>
                <Button
                  onClick={() => {
                    setEmailExportOpen(true);
                  }}
                  startIcon={<GetApp />}
                  variant="contained"
                  style={{ textTransform: 'none', marginRight: '1rem' }}
                >
                  Export All Data
                </Button>
                <Dialog open={emailExportOpen} fullWidth>
                  <DialogTitle>Export All Data</DialogTitle>
                  <DialogContent>
                    <MuiTextField
                      label="Email"
                      value={exportEmail || ''}
                      onChange={(event) => {
                        setExportEmail(event.target.value);
                      }}
                      fullWidth
                      required
                    />
                  </DialogContent>
                  <DialogActions>
                    <Button
                      onClick={() => setEmailExportOpen(false)}
                      color="primary"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleEmailExport}
                      color="primary"
                      variant="contained"
                      disabled={!exportEmail || loading}
                    >
                      Export
                    </Button>
                  </DialogActions>
                </Dialog>
              </>
            ) : null}
            {hasITWrite || hasCreditMgmtWrite ? (
              <>
                <Grid item>
                  <Button
                    color="primary"
                    variant="contained"
                    component="label" // https://stackoverflow.com/a/54043619
                    startIcon={<CloudUpload />}
                    style={{ textTransform: 'none', marginRight: '1rem' }}
                  >
                    Convert Light Bills
                    <input
                      type="file"
                      hidden
                      onChange={(event) => uploadBillsToS3(event, 'light')}
                      accept="application/pdf"
                      multiple
                    />
                  </Button>
                </Grid>
                <Grid item>
                  <Button
                    color="primary"
                    variant="contained"
                    component="label" // https://stackoverflow.com/a/54043619
                    startIcon={<CloudUpload />}
                    style={{ textTransform: 'none', marginRight: '1rem' }}
                  >
                    Convert Energisa Bills
                    <input
                      type="file"
                      hidden
                      onChange={(event) => uploadBillsToS3(event, 'energisa')}
                      accept="application/pdf"
                      multiple
                    />
                  </Button>
                </Grid>
                <Grid item>
                  <Button
                    onClick={() => setBillingDialogOpen(true)}
                    variant="contained"
                    startIcon={<Description />}
                    style={{ textTransform: 'none' }}
                    disabled={!(hasITWrite || hasCreditMgmtWrite)}
                  >
                    Process Billing
                  </Button>
                </Grid>
              </>
            ) : null}
            <Grid item>
              <IconButton onClick={() => fetchData()}>
                <Refresh />
              </IconButton>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {renderConsortiumProjects()}
      <CreditMgmtBillingReportDialog
        open={billingDialogOpen}
        handleClose={() => setBillingDialogOpen(false)}
      />
    </Grid>
  );
});
