import React, { useState } from 'react';
import moment from 'moment';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  UrlField,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';

import { Button, Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'Buy Direction';

export const BuyDirectionEdit = () => {
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const save = () => {
    setLoading(true);
    dataProvider
      .create('BuyDirectionTransfer', {
        data: { buyDirectionId: parseInt(id, 10) },
      })
      .then(
        (record) => {
          notify(`Successfully initiated transfer`);
          setLoading(false);
          refresh();
        },
        (e) => {
          console.log('HIT ERROR', e);
          notify(e.message, { type: 'error' });
          setLoading(false);
        }
      );
  };

  return (
    <Grid style={{ width: '100%' }} container spacing={5}>
      <Grid item xs={12}>
        <Edit title={`${entityName} #${id}`} undoable={false}>
          <SimpleForm>
            <FunctionField
              label="Create Transfer"
              render={(record) => {
                if (
                  record.dwollaTransfers &&
                  record.dwollaTransfers.length > 0
                ) {
                  return (
                    <Typography style={{ color: 'darkgreen' }}>
                      Transfer successfully initiated for this buy direction on{' '}
                      <b>
                        {moment(record.dwollaTransfers[0].created).format(
                          'lll'
                        )}
                      </b>
                    </Typography>
                  );
                }
                return (
                  <Button
                    color="secondary"
                    variant="contained"
                    onClick={save}
                    disabled={loading}
                  >
                    Initiate Transfer
                  </Button>
                );
              }}
            />

            <TextField source="id" />
            <NumberField source="amount" />
            <UrlField source="eversignDocumentUrl" />
            <ArrayField source="dwollaTransfers">
              <Datagrid>
                <TextField label="Status" source="status" />
                <TextField label="Value" source="amount.value" />
                <DateField label="Created" source="created" />
                <TextField label="correlationId" source="correlationId" />
              </Datagrid>
            </ArrayField>
            <NumberField label="Sub account id" source="subAccount.id" />
            <DateInput source="cancelledAt" />
            <DateInput source="completedAt" />
            <DateField source="createdAt" />
            <DateField source="updatedAt" />
          </SimpleForm>
        </Edit>{' '}
      </Grid>{' '}
    </Grid>
  );
};

const BuyDirectionFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const BuyDirectionList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<BuyDirectionFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="subAccount.user.id"
          labelSource="subAccount.user.fullName"
          label="User"
        />
        <NumberField
          source="amount"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <TextField
          label="Transfer status"
          source="dwollaTransferStatus.statusText"
        />
        <UrlField
          label="Signed Buy Direction"
          source="eversignDocumentUrl"
          sortable={false}
        />
        <DateField source="cancelledAt" />
        <DateField source="completedAt" />
        <DateField source="completedDt" showTime={true} />
        <DateField source="createdAt" />
        <DateField source="updatedAt" />
      </Datagrid>
    </List>
  );
};

export const BuyDirectionCreate = () => (
  <Create
    title={`Create ${entityName}`}
    helperText="This can be edited at any time so no need to be perfect."
  >
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
