import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FormDataConsumer,
  FormTab,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Button,
  CircularProgress,
  Divider,
  FormHelperText,
  Grid,
  Typography,
} from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'SCADA System';

export const ScadaSystemEdit = () => {
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const backfillSiteGeneration = (startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ProductionPeriod', {
        input: {
          backfillFromAPI: true,
          scadaSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('SCADA System site generation successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site generation', { type: 'error' });
      });
  };

  const backfillSiteExpectedGeneration = (formData) => {
    const {
      backfillExpectedStartDt,
      backfillExpectedEndDt,
      project: { id: projectId },
    } = formData;
    if (backfillExpectedStartDt > backfillExpectedEndDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ExpectedProductionPeriod', {
        input: {
          backfillFromAPI: true,
          projectId: parseInt(projectId, 10),
          startDt: backfillExpectedStartDt,
          endDt: backfillExpectedEndDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Project expected generation successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error saving project expected generation', { type: 'error' });
      });
  };

  const removeDuplicateSensorData = () => {
    dataProvider
      .update('ScadaSystem', {
        data: {
          id: parseInt(id, 10),
          removeDuplicateSensorDataPeriods: true,
        },
      })
      .then(() => {
        setLoading(false);
        notify(
          "Backend is working on it...check the log tails out and don't run concurrent cleanup tasks!"
        );
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error deleting duplicate sensor data', { type: 'error' });
      });
  };

  const removeDuplicateInverterData = () => {
    dataProvider
      .update('ScadaSystem', {
        data: {
          id: parseInt(id, 10),
          removeDuplicateInverterProductionPeriods: true,
        },
      })
      .then(() => {
        setLoading(false);
        notify(
          "Backend is working on it...check the log tails out and don't run concurrent cleanup tasks!"
        );
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error deleting duplicate inverter data', { type: 'error' });
      });
  };

  const backfillSiteSensorHistory = (sensorId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('SensorDataPeriod', {
        input: {
          backfillFromAPI: true,
          scadaSystemId: parseInt(id, 10),
          sensorId,
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('SCADA site sensor history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site sensor history', { type: 'error' });
      });
  };

  const backfillInverterPower = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('InverterProductionPeriod', {
        input: {
          backfillFromAPI: true,
          inverterId,
          scadaSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('SCADA System inverter production history backfill initiated');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify(
          'Error initiating inverter production history backfill',
          'error'
        );
      });
  };

  const backfillDailyInverterGeneration = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('DailyInverterGeneration', {
        input: {
          backfillFromAPI: true,
          inverterId,
          scadaSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify(
          'SCADA System daily inverter generation history backfill initiated'
        );
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify(
          'Error initiating daily inverter generation history backfill',
          'error'
        );
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <ReferenceInput
                source="project.id"
                reference="Project"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
              >
                <SelectInput label="Project" fullWidth optionText="name" />
              </ReferenceInput>
              <TextInput source="database" fullWidth />
              <TextInput source="publicIPAddress" fullWidth />
              <TextInput source="username" fullWidth />
              <TextInput source="password" fullWidth />
              <BooleanInput source="isLivePollingEnabled" fullWidth />
              <TextInput source="timezone" fullWidth />
              <CustomNumberInput
                source="noCommsLimitMins"
                fullWidth
                label="No Comms Alert Limit (minutes)"
                helperText="If you want to disable the 'No Comms' alert for this site, set to null."
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Backfill">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Generation Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="backfillStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="backfillEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(formData.backfillStartDt && formData.backfillEndDt)
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteGeneration(
                          formData.backfillStartDt,
                          formData.backfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Site Generation Data'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Expected Generation Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="backfillExpectedStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="backfillExpectedEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.backfillExpectedStartDt &&
                          formData.backfillExpectedEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteExpectedGeneration(formData);
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Expected Generation Data'
                      )}
                    </Button>
                    <FormHelperText style={{ marginLeft: '14px' }}>
                      Irradiance, inverter power, and module temperature
                      backfills need to be complete before expected generation
                      backfills can be calculated.
                    </FormHelperText>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Sensor Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillSensorStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillSensorEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="sensor.id"
                reference="Sensor"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ scadaSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Sensor"
                  helperText="Select the sensor you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.siteBackfillSensorStartDt &&
                          formData.siteBackfillSensorEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteSensorHistory(
                          formData.sensor && formData.sensor.id,
                          formData.siteBackfillSensorStartDt,
                          formData.siteBackfillSensorEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Sensor History'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Inverter Power Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="inverterBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="inverterBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ scadaSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.inverterBackfillStartDt &&
                          formData.inverterBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillInverterPower(
                          formData.inverter && formData.inverter.id,
                          formData.inverterBackfillStartDt,
                          formData.inverterBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Inverter History'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Daily Inverter Generation Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="dailyInverterGenerationBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="dailyInverterGenerationBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ scadaSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.dailyInverterGenerationBackfillStartDt &&
                          formData.dailyInverterGenerationBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillDailyInverterGeneration(
                          formData.inverter && formData.inverter.id,
                          formData.dailyInverterGenerationBackfillStartDt,
                          formData.dailyInverterGenerationBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Daily Inverter Generation'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Production Upload">
          <Grid container style={{ minWidth: '450px' }}>
            <Typography>
              To upload data from CSV,{' '}
              <a href="/ProductionPeriod/create">click here</a>.
            </Typography>
          </Grid>
        </FormTab>
        <FormTab label="Data Integrity">
          <Grid container style={{ width: '100%' }} spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Remove Duplicates
              </Typography>
            </Grid>
            {/* <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillSensorStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillSensorEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
              source="sensor.id"
              reference="Sensor"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
              filter={{ scadaSystemId: parseInt(id, 10) }}
              >
              <SelectInput
                  label="Sensor"
                  helperText="Select the sensor you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid> */}
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={false}
                      onClick={() => {
                        removeDuplicateSensorData();
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Remove Duplicate Sensor Data'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={false}
                      onClick={() => {
                        removeDuplicateInverterData();
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Remove Duplicate Inverter Data and Consolidate to 5 min intervals'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

export const ScadaSystemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        {/* <BooleanField
        source="pingConnectionStatus.alive"
        label="Ping Connection Established"
      />
      <NumberField
        label="Ping Response Time"
        source="pingConnectionStatus.time"
      />
      <NumberField
        label="Ping Packet Loss"
        source="pingConnectionStatus.packetLoss"
      /> */}
        <BooleanField
          source="canEstablishDatabaseConnection"
          label="Database Connection Established"
        />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="database" />
        <TextField source="publicIPAddress" label="Public IP Address" />
        <TextField source="username" />
        <TextField source="password" />
        <BooleanField source="isLivePollingEnabled" />
        <TextField source="timezone" />
        <NumberField source="noCommsLimitMins" />
        <BooleanField
          source="isReadingDailyGenerationFromMeter"
          sortable={false}
        />
      </Datagrid>
    </List>
  );
};

export const ScadaSystemCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            perPage={10000}
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput label="Project" fullWidth required optionText="name" />
          </ReferenceInput>
          <TextInput source="database" required fullWidth />
          <TextInput source="timezone" required fullWidth />
          <TextInput
            source="publicIPAddress"
            label="Public IP Address"
            required
            fullWidth
          />
          <TextInput source="username" required fullWidth />
          <TextInput source="password" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
