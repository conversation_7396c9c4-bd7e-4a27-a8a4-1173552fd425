import React, { useState } from 'react';
import moment from 'moment';
import numeral from 'numeral';
import { Alert } from '@mui/lab';
import {
  FormDataConsumer,
  FunctionField,
  useDataProvider,
  useNotify,
  useRefresh,
} from 'react-admin';
import {
  Avatar,
  Checkbox,
  CircularProgress,
  Divider,
  Fab,
  Grid,
  LinearProgress,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Tooltip,
  Typography,
} from '@mui/material';
import MatList from '@mui/material/List';
import { Add, Check, Clear, Info } from '@mui/icons-material';

export const MonthlyPortfolioFinancialActualDividends = (props) => {
  const [
    monthlyPortfolioFinancialActualDividends,
    setMonthlyPortfolioFinancialActualDividends,
  ] = useState(null);
  const dataProvider = useDataProvider();
  const [dividendsToIssue, setDividendsToIssue] = useState([]);

  const { id } = props;

  const fetchData = () => {
    dataProvider
      .getOne('MonthlyPortfolioFinancialActualWithDividends', {
        id: parseInt(id, 10),
      })
      .then(
        (resp) => {
          setMonthlyPortfolioFinancialActualDividends(resp.data);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  if (!monthlyPortfolioFinancialActualDividends) {
    fetchData();
  }

  const infoIconStyle = {
    fontSize: '1.3rem',
    marginRight: '4px',
    marginTop: '-4px',
    verticalAlign: 'middle',
  };

  const getTotals = (record) => {
    const { dividendQualifyingInvestments } =
      monthlyPortfolioFinancialActualDividends;
    const { payoutData, grossCafd } = record;
    let totalShares = 0;
    let paidShares = 0;
    let unpaidShares = 0;
    let toBePaidTotal = 0;
    if (dividendQualifyingInvestments) {
      dividendQualifyingInvestments.forEach((userEquityObj) => {
        totalShares += userEquityObj.currentSharesOwned;
        toBePaidTotal +=
          grossCafd *
          (userEquityObj.currentSharesOwned /
            parseFloat(payoutData.totalNetShareCount));
      });
    }
    return { totalShares, paidShares, unpaidShares, toBePaidTotal };
  };

  const DividendButton = ({
    dividends,
    clearSelectedDividends,
    accountantReviewedFlg,
    effectiveDtSetFlg,
    dividendsAlreadyBeingProcessed,
  }) => {
    const dataProvider = useDataProvider();
    const notify = useNotify();
    const refresh = useRefresh();
    const [pending, setPending] = useState(false);

    const createDividends = (dividendCreateInputs) => {
      return (event) => {
        if (dividendsAlreadyBeingProcessed) {
          notify(
            'Dividends are already being processed for this actual. Try again in a couple moments...',
            'error'
          );
          refresh();
          fetchData();
          return;
        }
        setPending(true);
        event.preventDefault();
        dataProvider
          .create('DividendsMany', {
            data: dividendCreateInputs,
          })
          .then(
            (record) => {
              notify(
                'Dividends are being issued... once complete more info sent to Platform-events Slack channel...',
                'success'
              );
              refresh();
              fetchData();
              clearSelectedDividends();
              setPending(false);
            },
            (e) => {
              console.error('ERROR', e);
              notify(e.message, { type: 'error' });
              refresh();
              fetchData();
              clearSelectedDividends();
              setPending(false);
            }
          );
      };
    };

    const dividendCount = Object.keys(dividends).length;

    return (
      <Tooltip arrow title="Click to issue dividends">
        <Fab
          color="primary"
          variant="extended"
          onClick={createDividends(dividends)}
          disabled={
            dividendCount === 0 ||
            !accountantReviewedFlg ||
            pending ||
            !effectiveDtSetFlg ||
            dividendsAlreadyBeingProcessed
          }
          edge="end"
          aria-label="create dividends"
        >
          <Add style={{ marginRight: '4px' }} />{' '}
          {pending ? (
            <CircularProgress />
          ) : (
            `Issue ${dividendCount} Dividend(s)`
          )}
        </Fab>
      </Tooltip>
    );
  };

  const handleSelectAllDividends = (record) => {
    const { dividendQualifyingInvestments } =
      monthlyPortfolioFinancialActualDividends;
    const { payoutData, grossCafd } = record;

    const toIssue = [];
    dividendQualifyingInvestments.forEach((investment) => {
      if (investment.dividends.length === 0) {
        const equity =
          (payoutData &&
            investment.currentSharesOwned / payoutData.totalNetShareCount) ||
          0;
        const value = Math.round(equity * grossCafd * 100) / 100;

        toIssue.push({
          value,
          monthlyPortfolioFinancialActualId: record.id,
          userId: investment.userId,
          subAccountId: investment.subAccount && investment.subAccount.id,
          createTransfer: true,
        });
      }
    });

    setDividendsToIssue(toIssue);
  };

  const handleDividendChecked = (record, formData, value) => {
    const dividends = [...dividendsToIssue];
    const alreadySelectedIndex = dividends.findIndex(
      (d) =>
        d.userId === record.userId &&
        d.subAccountId === (record.subAccount && record.subAccount.id)
    );
    if (alreadySelectedIndex !== -1) {
      dividends.splice(alreadySelectedIndex, 1);
    } else {
      dividends.push({
        value,
        monthlyPortfolioFinancialActualId: formData.id,
        userId: record.userId,
        subAccountId: record.subAccount && record.subAccount.id,
        createTransfer: true,
      });
    }

    setDividendsToIssue(dividends);
  };

  const renderFunctionMonthlyPortfolioFinancialActualDividends = () => {
    return (record) => {
      if (
        !monthlyPortfolioFinancialActualDividends?.dividendQualifyingInvestments
      ) {
        return <CircularProgress />;
      }
      const unissuedDividends =
        monthlyPortfolioFinancialActualDividends?.dividendQualifyingInvestments.filter(
          (investment) =>
            investment.dividends && investment.dividends.length === 0
        );
      const allSelected =
        unissuedDividends.length === dividendsToIssue.length &&
        unissuedDividends.length > 0;
      return (
        <Grid
          container
          justifyContent="space-between"
          style={{ width: '100%', marginTop: '2em' }}
        >
          <Grid item>
            <Tooltip arrow title="Click to select all dividends">
              <Fab
                color="primary"
                variant="extended"
                disabled={
                  !unissuedDividends.length || record.dividendsPendingFlg
                }
                onClick={() =>
                  allSelected
                    ? setDividendsToIssue([])
                    : handleSelectAllDividends(record)
                }
                edge="end"
                size="large"
                aria-label="select all dividends"
              >
                {allSelected ? 'Unselect All' : 'Select All'}
              </Fab>
            </Tooltip>
          </Grid>
          <Grid item>
            <Tooltip
              arrow
              title="Click to issue dividend. *Make sure numbers have been reviewed."
            >
              <DividendButton
                dividends={dividendsToIssue}
                clearSelectedDividends={() => setDividendsToIssue([])}
                accountantReviewedFlg={record.accountantReviewedFlg}
                effectiveDtSetFlg={!!record.effectiveDt}
                dividendsAlreadyBeingProcessed={!!record.dividendsPendingFlg}
              />
            </Tooltip>
          </Grid>
        </Grid>
      );
    };
  };

  if (monthlyPortfolioFinancialActualDividends) {
    return (
      <FormDataConsumer>
        {({ formData, ...rest }) => {
          if (!formData.effectiveDt) {
            return (
              <Alert severity="warning">
                No effectiveDt has been set. Go to 'Details' tab and click
                'Confirm Actuals'
              </Alert>
            );
          }
          return (
            <>
              {formData.dividendsPendingFlg ? (
                <Alert severity="error">
                  <Typography style={{ fontWeight: 'bold' }}>
                    Dividends are currently being processed for this actual!
                  </Typography>
                  <Typography>
                    Please wait until this process completes. Feel free to
                    refresh this page for a status update at any time. A Slack
                    message will be sent to platform-events upon completion.
                  </Typography>
                </Alert>
              ) : null}
              <FunctionField
                fullWidth
                label="Review"
                render={(record) => {
                  const shareObj = getTotals(record);
                  return (
                    <>
                      <Grid container direction="column">
                        <Alert severity="info" style={{ marginBottom: '2em' }}>
                          <Grid item>
                            <Typography gutterBottom>
                              The CAFD for this month{' '}
                              <b>
                                **
                                {numeral(record.grossCafd).format('$0,0.00')}
                                **
                              </b>{' '}
                              will be paid out to investments received up until
                              this entry's 'effectiveDt'{' '}
                              <b>
                                **
                                {moment(record.effectiveDt).format(
                                  'MMMM Do YYYY, h:mm:ss a'
                                )}
                                **
                              </b>
                              . All numbers below reflect only the investment
                              data up until this effective date.
                            </Typography>
                          </Grid>
                          <Divider style={{ margin: '1em 0' }} />

                          <Grid item>
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Total gross shares sold (including the resale of shares)"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Shares Bought (Gross) :{' '}
                                {numeral(
                                  record.payoutData &&
                                    record.payoutData.totalGrossShareCount
                                ).format('0,0.[000]')}
                              </Typography>
                            </Tooltip>
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Total shares that have been transferred from investor to investor"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Transferred Shares :{' '}
                                {numeral(
                                  record.payoutData &&
                                    record.payoutData.totalTransferShareCount
                                ).format('0,0.[000]')}
                              </Typography>
                            </Tooltip>
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Shares owned by investors to date. (Combination of numbers above numbers)"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Owned Shares :{' '}
                                {numeral(
                                  record.payoutData &&
                                    record.payoutData.totalNetShareCount
                                ).format('0,0.[000]')}
                              </Typography>
                            </Tooltip>
                            <Divider style={{ margin: '1em 0' }} />
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Total Gross Invested (Including Transferred Shares)"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Gross Invested (Including Transferred
                                Shares) :{' '}
                                {numeral(
                                  record.payoutData &&
                                    record.payoutData.totalGrossInvested
                                ).format('$0,0.00')}
                              </Typography>
                            </Tooltip>
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Money Transferred from Investor to Investor"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Resold Investments :{' '}
                                {numeral(
                                  record.payoutData &&
                                    record.payoutData.totalInvestedResold
                                ).format('$0,0.00')}
                              </Typography>
                            </Tooltip>
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Money raised to date (net of resold shares)"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Net Invested :{' '}
                                {numeral(
                                  record.payoutData &&
                                    record.payoutData.totalNetInvested
                                ).format('$0,0.00')}
                              </Typography>
                            </Tooltip>
                            <Divider style={{ margin: '1em 0' }} />
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Total Shares Represented Below"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total Shares Check :{' '}
                                <span
                                  style={{
                                    color:
                                      parseInt(shareObj.totalShares) !==
                                      parseInt(
                                        record.payoutData &&
                                          record.payoutData.totalNetShareCount
                                      )
                                        ? 'red'
                                        : 'green',
                                  }}
                                >
                                  {numeral(shareObj.totalShares).format(
                                    '0,0.[000]'
                                  )}
                                </span>
                              </Typography>
                            </Tooltip>
                            <Tooltip
                              arrow
                              placement="bottom-start"
                              title="Total To Be Paid Represented Below"
                            >
                              <Typography style={{ fontWeight: 'bold' }}>
                                <Info style={infoIconStyle} />
                                Total To Be Paid Check :{' '}
                                <span
                                  style={{
                                    color:
                                      parseInt(record.grossCafd) !==
                                      parseInt(shareObj.toBePaidTotal)
                                        ? 'red'
                                        : 'green',
                                  }}
                                >
                                  {numeral(shareObj.toBePaidTotal).format(
                                    '$0,0'
                                  )}
                                </span>
                              </Typography>
                            </Tooltip>
                          </Grid>
                        </Alert>
                      </Grid>
                    </>
                  );
                }}
              />
              <FunctionField
                align="center"
                source="currentDividendPayoutSum"
                label=""
                style={{ width: '100%' }}
                render={(record) => (
                  <Grid container>
                    <Grid item>
                      <Typography variant="h6">
                        Currently paid out{' '}
                        <b>
                          {numeral(record.currentDividendPayoutSum).format(
                            '$0,0.00'
                          )}
                        </b>{' '}
                        of <b>{numeral(record.grossCafd).format('$0,0.00')}</b>
                      </Typography>
                      <LinearProgress
                        style={{
                          height: '.75rem',
                          borderRadius: '.5rem',
                        }}
                        color="secondary"
                        variant="determinate"
                        value={
                          (record.currentDividendPayoutSum / record.grossCafd) *
                          100
                        }
                      />
                    </Grid>
                  </Grid>
                )}
              />
              <FunctionField
                fullWidth
                label={` `}
                style={{ width: '100%' }}
                render={renderFunctionMonthlyPortfolioFinancialActualDividends()}
              />
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  return (
                    <MatList>
                      {monthlyPortfolioFinancialActualDividends.dividendQualifyingInvestments?.map(
                        (record) => {
                          let dividendSum = 0;
                          const hasDividend =
                            record.dividends && record.dividends.length > 0;
                          if (hasDividend) {
                            record.dividends.forEach((dividend) => {
                              dividendSum += dividend.value;
                            });
                          }
                          const equity =
                            (formData.payoutData &&
                              record.currentSharesOwned /
                                formData.payoutData.totalNetShareCount) ||
                            0;
                          const value =
                            Math.round(equity * formData.grossCafd * 100) / 100;
                          let secondaryText;
                          let secondaryTextColor;
                          if (!hasDividend) {
                            secondaryTextColor = 'inherit';
                          } else if (
                            dividendSum ===
                            Math.round(equity * formData.grossCafd * 100) / 100
                          ) {
                            secondaryTextColor = 'green';
                          } else {
                            secondaryTextColor = 'red';
                          }
                          if (hasDividend) {
                            secondaryText = (
                              <span
                                style={{ color: secondaryTextColor }}
                              >{`${numeral(dividendSum).format(
                                '$0,0.00'
                              )} paid.`}</span>
                            );
                          } else if (!record.currentSharesOwned) {
                            secondaryText = 'No shares present';
                          } else {
                            secondaryText = `Dividend payout not initiated. Payout will be ${numeral(
                              value
                            ).format('$0,0.00')}`;
                          }
                          const label = record.subAccount
                            ? `Sub Account: ${record.subAccount.name} (User ID: ${record.userId})`
                            : `User ID: ${record.userId}`;
                          const checked =
                            dividendsToIssue &&
                            dividendsToIssue.findIndex((d) => {
                              return (
                                d.userId === record.userId &&
                                d.subAccountId ===
                                  (record.subAccount && record.subAccount.id)
                              );
                            }) > -1;
                          return (
                            <ListItem
                              classes={{
                                container: 'energea-full-width',
                              }}
                              style={{ width: '100%' }}
                              dense
                            >
                              {hasDividend ? null : (
                                <Checkbox
                                  checked={checked}
                                  onChange={(event) => {
                                    event.stopPropagation();
                                    handleDividendChecked(
                                      record,
                                      formData,
                                      value
                                    );
                                  }}
                                />
                              )}
                              <ListItemAvatar>
                                <Avatar
                                  style={{
                                    backgroundColor: hasDividend
                                      ? 'green'
                                      : 'red',
                                  }}
                                >
                                  {hasDividend ? <Check /> : <Clear />}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                style={{
                                  color: 'rgba(0,0,0,.8)',
                                }}
                                primary={`${label}, Shares: ${numeral(
                                  record.currentSharesOwned
                                ).format('0,0.[000]')}, Equity: ${numeral(
                                  equity * 100
                                ).format(
                                  '0.0000'
                                )}%, Expected Payout: ${numeral(
                                  equity * formData.grossCafd
                                ).format('$0,0.00')}`}
                                secondary={secondaryText}
                              />{' '}
                            </ListItem>
                          );
                        }
                      )}
                    </MatList>
                  );
                }}
              </FormDataConsumer>
            </>
          );
        }}
      </FormDataConsumer>
    );
  } else {
    return <CircularProgress />;
  }
};
