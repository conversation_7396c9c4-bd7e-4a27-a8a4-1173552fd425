import React from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'DTC Participant Number';

export const DtcParticipantNumberEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="institution" fullWidth />
            <TextInput source="code" fullWidth />
            <BooleanInput
              source="custodianRequiresWetSignatureFlg"
              fullWidth
              helperText="This flag will prevent users from submitting ACATs to the API on the front end and instead will tell them <NAME_EMAIL> requesting an IRA transfer form."
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const DtcParticipantNumberList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <Typography>
        If the institution you are looking for is not on the list below, you can
        find a more complete list{' '}
        <a
          href="https://www.dtcc.com/-/media/Files/Downloads/client-center/DTC/DTC-Participant-in-Numerical-Sequence-1.pdf"
          target="_blank"
        >
          here
        </a>{' '}
        or reach out to MTC (Lauren Reeps on the Incoming Transfers -
        <EMAIL>).
      </Typography>
      <List title={entityName} perPage={25}>
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="institution" />
          <TextField source="code" />
          <BooleanField source="custodianRequiresWetSignatureFlg" />
        </Datagrid>
      </List>
    </>
  );
};

export const DtcParticipantNumberCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          If the institution you are looking for is not on{' '}
          <a
            href="https://www.dtcc.com/-/media/Files/Downloads/client-center/DTC/DTC-Participant-in-Numerical-Sequence-1.pdf"
            target="_blank"
          >
            this list
          </a>
          , reach out to MTC (Lauren Reeps on the Incoming Transfers -
          <EMAIL>).
          <TextInput source="institution" required fullWidth />
          <TextInput source="code" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
