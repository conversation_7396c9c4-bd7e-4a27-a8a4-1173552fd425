import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON>reate,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Self Consumption Offtaker';

export const BrSelfConsumptionOfftakerEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <ReferenceInput
              source="salesforceProject.id"
              reference="SalesforceProject"
              perPage={10_000}
              sort={{ field: 'name', order: 'ASC' }}
              filter={{ consortiumType: 'Self-consumption' }}
            >
              <SelectInput optionText="name" label="Project" fullWidth />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrSelfConsumptionOfftakerList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <LinkField
          reference="SalesforceProject"
          linkSource="salesforceProject.id"
          labelSource="salesforceProject.name"
          label="Project"
        />
      </Datagrid>
    </List>
  );
};

export const BrSelfConsumptionOfftakerCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <ReferenceInput
            source="salesforceProject.id"
            reference="SalesforceProject"
            perPage={10_000}
            sort={{ field: 'name', order: 'ASC' }}
            filter={{ consortiumType: 'Self-consumption' }}
          >
            <SelectInput optionText="name" label="Project" fullWidth />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
