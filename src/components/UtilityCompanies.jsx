import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Utility Company';

export const UtilityCompanyEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <ReferenceInput
              source="defaultSalesforceProject.id"
              reference="SalesforceProject"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
              filter={{
                utilityCompany: { id: parseInt(id, 10) },
                consortiumType: 'Distributed generation',
              }}
            >
              <SelectInput
                optionText="name"
                label="Default customer project"
                fullWidth
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const UtilityCompanyList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <LinkField
          reference="SalesforceProject"
          linkSource="defaultSalesforceProject.id"
          labelSource="defaultSalesforceProject.name"
          label="Default customer project"
        />
      </Datagrid>
    </List>
  );
};

export const UtilityCompanyCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
