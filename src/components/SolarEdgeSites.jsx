import React, { useState } from 'react';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FormDataConsumer,
  FormTab,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import {
  Button,
  CircularProgress,
  Divider,
  FormHelperText,
  Grid,
  Typography,
} from '@mui/material';

import 'chartjs-adapter-moment';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'SolarEdge Site';

export const SolarEdgeSiteEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [loading, setLoading] = useState(false);
  const { id } = useParams();

  const backfillSiteHistoryFromSolarEdge = (startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ProductionPeriod', {
        input: {
          backfillFromAPI: true,
          solarEdgeSiteId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('SolarEdge site production history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site production history', { type: 'error' });
      });
  };

  const backfillSiteExpectedGeneration = (formData) => {
    const {
      backfillExpectedStartDt,
      backfillExpectedEndDt,
      project: { id: projectId },
    } = formData;
    if (backfillExpectedStartDt > backfillExpectedEndDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ExpectedProductionPeriod', {
        input: {
          backfillFromAPI: true,
          projectId: parseInt(projectId, 10),
          startDt: backfillExpectedStartDt,
          endDt: backfillExpectedEndDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Project expected generation successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error saving project expected generation', { type: 'error' });
      });
  };

  const backfillSiteSensorHistoryFromSolarEdge = (startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('SensorDataPeriod', {
        input: {
          backfillFromAPI: true,
          solarEdgeSiteId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('SolarEdge site sensor history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site sensor history', { type: 'error' });
      });
  };

  const removeDuplicateSensorData = () => {
    dataProvider
      .update('SolarEdgeSite', {
        data: {
          id: parseInt(id, 10),
          removeDuplicateSensorDataPeriods: true,
        },
      })
      .then(() => {
        setLoading(false);
        notify(
          "Backend is working on it...check the log tails out and don't run concurrent cleanup tasks!"
        );
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error deleting duplicate sensor data', { type: 'error' });
      });
  };

  const removeDuplicateInverterData = () => {
    dataProvider
      .update('SolarEdgeSite', {
        data: {
          id: parseInt(id, 10),
          removeDuplicateInverterProductionPeriods: true,
        },
      })
      .then(() => {
        setLoading(false);
        notify(
          "Backend is working on it...check the log tails out and don't run concurrent cleanup tasks!"
        );
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error deleting duplicate inverter data', { type: 'error' });
      });
  };

  const backfillInverterHistoryFromSolarEdge = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('InverterProductionPeriod', {
        input: {
          backfillFromAPI: true,
          inverterId,
          solarEdgeSiteId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('SolarEdge inverter production history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error back-filling inverter production history', {
          type: 'error',
        });
      });
  };

  const backfillDailyInverterGeneration = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('DailyInverterGeneration', {
        input: {
          backfillFromAPI: true,
          inverterId,
          solarEdgeSiteId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Daily inverter generation history backfill initiated');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify(
          'Error initiating daily inverter generation history backfill',
          'error'
        );
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm redirect={false}>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <BooleanInput source="isLivePollingEnabled" />
              <TextInput
                source="siteId"
                label="SolarEdge site id"
                helperText="This should be the ID of the site in the SolarEdge system"
                fullWidth
              />
              <TextInput source="apiKey" required fullWidth />
              <ReferenceInput
                source="project.id"
                reference="Project"
                sort={{ field: 'name', order: 'ASC' }}
                perPage={10000}
              >
                <SelectInput
                  label="Project"
                  required
                  fullWidth
                  helperText="Select the project associated with this SolarEdge site."
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput
                source="name"
                label="SolarEdge site name"
                fullWidth
                disabled
              />
              <TextInput
                source="timezone"
                label="SolarEdge site timezone"
                fullWidth
                disabled
              />
              <CustomNumberInput
                source="noCommsLimitMins"
                fullWidth
                label="No Comms Alert Limit (minutes)"
                helperText="If you want to disable the 'No Comms' alert for this site, set to null."
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Backfill">
          <Grid item xs={12}>
            <Typography variant="h5" gutterBottom>
              Backfill Expected Generation Data
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <TextInput
              label="From"
              source="backfillExpectedStartDt"
              type="datetime-local"
              helperText="Enter date in the timezone of the site"
              style={{ marginRight: '2rem' }}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <TextInput
              label="To"
              source="backfillExpectedEndDt"
              type="datetime-local"
              helperText="Enter date in the timezone of the site"
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <FormDataConsumer>
              {({ formData, ...rest }) => (
                <>
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={
                      loading ||
                      !(
                        formData.backfillExpectedStartDt &&
                        formData.backfillExpectedEndDt
                      )
                    }
                    onClick={() => {
                      setLoading(true);
                      backfillSiteExpectedGeneration(formData);
                    }}
                  >
                    {loading ? (
                      <CircularProgress />
                    ) : (
                      'Backfill Expected Generation Data'
                    )}
                  </Button>
                  <FormHelperText style={{ marginLeft: '14px' }}>
                    Irradiance, inverter power, and module temperature backfills
                    need to be complete before expected generation backfills can
                    be calculated.
                  </FormHelperText>
                </>
              )}
            </FormDataConsumer>
          </Grid>
          <Divider style={{ width: '100%', margin: '2em 0' }} />
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Production Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillStartDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillEndDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.siteBackfillStartDt &&
                          formData.siteBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteHistoryFromSolarEdge(
                          formData.siteBackfillStartDt,
                          formData.siteBackfillEndDt
                        );
                      }}
                    >
                      {loading ? <CircularProgress /> : 'Backfill Site History'}
                    </Button>
                    <FormHelperText style={{ marginLeft: '14px' }}>
                      Backfilling production history will fetch production data
                      from SolarEdge for every hour interval within the timespan
                      provided above. If the timespan above is longer than a
                      month, backfilling will make more than 1 API call.
                    </FormHelperText>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Sensor Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillSensorStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillSensorEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.siteBackfillSensorStartDt &&
                          formData.siteBackfillSensorEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteSensorHistoryFromSolarEdge(
                          formData.siteBackfillSensorStartDt,
                          formData.siteBackfillSensorEndDt
                        );
                      }}
                    >
                      {loading ? <CircularProgress /> : 'Backfill Site History'}
                    </Button>
                    <FormHelperText style={{ marginLeft: '14px' }}>
                      Backfilling sensor history will fetch sensor data from
                      SolarEdge for every 5 minute interval within the timespan
                      provided above. If the timespan above is longer than a
                      week, backfilling will make more than 1 API call.
                    </FormHelperText>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Inverter Power Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="inverterBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="inverterBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ solarEdgeSiteId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.inverterBackfillStartDt &&
                          formData.inverterBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillInverterHistoryFromSolarEdge(
                          formData.inverter && formData.inverter.id,
                          formData.inverterBackfillStartDt,
                          formData.inverterBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Inverter History'
                      )}
                    </Button>
                    <FormHelperText style={{ marginLeft: '14px' }}>
                      Backfilling inverter production history will fetch
                      production data from SolarEdge for every 5 minute interval
                      within the timespan provided above. If the timespan above
                      is longer than a week, backfilling will make more than 1
                      API call.
                    </FormHelperText>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Daily Inverter Generation Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="dailyInverterGenerationBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="dailyInverterGenerationBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ scadaSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.dailyInverterGenerationBackfillStartDt &&
                          formData.dailyInverterGenerationBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillDailyInverterGeneration(
                          formData.inverter && formData.inverter.id,
                          formData.dailyInverterGenerationBackfillStartDt,
                          formData.dailyInverterGenerationBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Daily Inverter Generation'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Production Upload">
          <Grid container style={{ minWidth: '450px' }}>
            <Typography>
              To upload data from CSV,{' '}
              <a href="/ProductionPeriod/create">click here</a>.
            </Typography>
          </Grid>
        </FormTab>
        <FormTab label="Data Integrity">
          <Grid container style={{ width: '100%' }} spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Remove Duplicates
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={false}
                      onClick={() => {
                        removeDuplicateSensorData();
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Remove Duplicate Sensor Data'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={false}
                      onClick={() => {
                        removeDuplicateInverterData();
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Remove Duplicate Inverter Data'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

export const SolarEdgeSiteList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="siteId" label="SolarEdge Site Id" sortable={false} />
        <BooleanField source="isLivePollingEnabled" />
        <TextField source="name" />
        <TextField source="timezone" sortable={false} />
        <NumberField source="noCommsLimitMins" fullWidth />
      </Datagrid>
    </List>
  );
};

export const SolarEdgeSiteCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            sort={{ field: 'name', order: 'ASC' }}
            perPage={10000}
          >
            <SelectInput
              label="Project"
              required
              fullWidth
              helperText="Select the project associated with this SolarEdge site."
              optionText="name"
            />
          </ReferenceInput>
          <TextInput
            required
            source="siteId"
            label="SolarEdge site id"
            helperText="This should be the ID of the site in the SolarEdge system"
            fullWidth
          />
          <TextInput required source="apiKey" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
