import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useResourceDefinitions, useDataProvider } from 'react-admin';
import { alpha } from '@mui/material/styles';
import { makeStyles } from '@mui/styles';
import {
  Autocomplete,
  Grid,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Collections,
  LocationOn,
  Person,
  Public,
  Help,
} from '@mui/icons-material';
import { createFilterOptions } from '@mui/material/Autocomplete';

import { getCategoryIcon } from './MyMenu';

const useStyles = makeStyles((theme) => ({
  inputRoot: {
    color: 'inherit',
    marginTop: 0,
    position: 'relative',
    borderRadius: theme.shape.borderRadius,
    backgroundColor: alpha(theme.palette.common.white, 0.15),
    '&:hover': {
      backgroundColor: alpha(theme.palette.common.white, 0.25),
    },
    marginRight: theme.spacing(2),
    marginLeft: 0,
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      marginLeft: theme.spacing(3),
      // width: 'auto',
    },
    marginTop: '.25rem',
    marginBottom: '.25rem',
  },
}));

export default function MySearchBar() {
  const [options, setOptions] = useState(null);

  const classes = useStyles();
  const dataProvider = useDataProvider();

  const resourcesDefinitions = useResourceDefinitions();
  const resources = Object.keys(resourcesDefinitions).map(
    (name) => resourcesDefinitions[String(name)]
  );
  const resourceOptions = resources
    .filter((resource) => resource.hasList && resource.options.label)
    .map((resource) => ({
      type: 'page',
      icon: getCategoryIcon(resource.options.category),
      label: resource.options.label,
      path: `/${resource.name}`,
    }));

  if (!options) {
    dataProvider.getOne('CMSSearchBarData', {}).then((record) => {
      const userOptions =
        (resourcesDefinitions['User'] &&
          record?.data?.users?.map((user) => ({
            type: 'user',
            icon: <Person />,
            label: user.label,
            path: `/User/${user.id}`,
          }))) ||
        [];
      const marketOptions =
        (resourcesDefinitions['Market'] &&
          record?.data?.markets?.map((market) => ({
            type: 'market',
            icon: <Public />,
            label: market.title,
            path: `/Market/${market.id}`,
          }))) ||
        [];
      const projectOptions =
        (resourcesDefinitions['Project'] &&
          record?.data?.projects?.map((project) => ({
            type: 'project',
            icon: <LocationOn />,
            label: project.name,
            path: `/Project/${project.id}`,
          }))) ||
        [];
      const portfolioOptions =
        (resourcesDefinitions['Portfolio'] &&
          record?.data?.portfolios?.map((portfolio) => ({
            type: 'portfolio',
            icon: <Collections />,
            label: `${portfolio.subtitle} - ${portfolio.name}`,
            path: `/Portfolio/${portfolio.id}`,
          }))) ||
        [];
      const faqOptions =
        (resourcesDefinitions['FaqEntry'] &&
          record?.data?.faqEntries?.map((faqEntry) => ({
            type: 'faq',
            icon: <Help />,
            label: `${faqEntry.question}`,
            path: `/FaqEntry/${faqEntry.id}`,
          }))) ||
        [];
      setOptions([
        ...resourceOptions,
        ...userOptions,
        ...portfolioOptions,
        ...projectOptions,
        ...marketOptions,
        ...faqOptions,
      ]);
    });
  }

  if (options) {
    const filterOptions = createFilterOptions({
      limit: 50,
    });

    return (
      <Tooltip
        title="Search pages, portfolios, projects, markets, users, or FAQs..."
        arrow
      >
        <Autocomplete
          blurOnSelect={true}
          clearOnEscape
          clearOnBlur
          filterOptions={filterOptions}
          classes={{
            root: classes.inputRoot,
          }}
          size="small"
          options={options}
          groupBy={(option) => option.type}
          getOptionLabel={(option) => option.label}
          style={{ width: '20rem', marginLeft: '1rem' }}
          renderOption={(props, option, state) => (
            <Grid
              container
              component={Link}
              to={option.path}
              style={{ width: '100%' }}
              {...props}
            >
              <Grid item xs={1} style={{ paddingLeft: '.5rem' }}>
                {option.icon}
              </Grid>
              <Grid item xs={11}>
                <Typography style={{ marginLeft: '1rem' }}>
                  {option.label}
                </Typography>
              </Grid>
            </Grid>
          )}
          renderInput={(params) => (
            <TextField
              size="small"
              variant="outlined"
              {...params}
              label={<span style={{ color: 'white' }}>Search...</span>}
              style={{ marginTop: 0 }}
            />
          )}
        />
      </Tooltip>
    );
  }
  return null;
}
