import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>ield,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, DetailField } from './CustomFields';

const entityName = 'Development Status';

export const ProjectStatusEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput multiline source="description" fullWidth />
            <CustomNumberInput
              label="Order No."
              source="orderNo"
              fullWidth
              step={1}
            />
            <BooleanInput source="inactive" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const ProjectStatusList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} sort={{ field: 'orderNo', order: 'ASC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <DetailField source="description" sortable={false} />
        <NumberField label="Order No." source="orderNo" />
        <BooleanField source="inactive" />
      </Datagrid>
    </List>
  );
};

export const ProjectStatusCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
