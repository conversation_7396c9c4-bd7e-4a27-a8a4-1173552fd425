import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import numeral from 'numeral';
import {
  ArrayField,
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import {
  Card,
  CardContent,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';

import {
  CustomNumberInput,
  CustomReferenceField,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { PromoCodeUpload } from './PromoCodeUpload';

const entityName = 'Promo Code';

export const PromoCodeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="promoType" required fullWidth />
            <ReferenceInput
              perPage={10000}
              source="user.id"
              reference="UserLite"
            >
              <AutocompleteInput
                allowEmpty={true}
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
                label="User (Investor)"
                fullWidth
              />
            </ReferenceInput>
            <TextInput source="code" fullWidth />
            <DateInput source="expirationDt" fullWidth />
            <DateInput source="completedDt" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const PromoCodesFilter = (props) => {
  return (
    <Filter {...props}>
      <TextInput
        style={{ minWidth: '420px' }}
        label="Search by First Name or Last Name"
        source="q"
        alwaysOn
      />
      <TextInput
        style={{ minWidth: '420px' }}
        label="Promo Type"
        source="promoType"
      />
    </Filter>
  );
};

export const PromoCodeList = () => {
  const [promoTypes, setPromoTypes] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();
  const notify = useNotify();

  if (!promoTypes && !loading && !error) {
    setLoading(true);
    dataProvider.getList('PromoType', {}).then(
      (res) => {
        setPromoTypes(res.data);
        setLoading(false);
        setError(null);
      },
      (err) => {
        setLoading(false);
        notify('Error: could not fetch Promo Codes Status', { type: 'error' });
        setError(err);
      }
    );
  }

  return (
    <>
      {!loading ? (
        <Card style={{ marginTop: '2rem' }}>
          <CardContent>
            <Grid container>
              <Grid item xs={12}>
                <Typography variant="h6">Promo Program Status</Typography>
              </Grid>
              <Grid item xs={12}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell style={{ fontWeight: 'bold' }}>
                        Promo Type
                      </TableCell>
                      <TableCell style={{ fontWeight: 'bold' }}>
                        Incomplete Open Promos
                      </TableCell>
                      <TableCell style={{ fontWeight: 'bold' }}>
                        Completed Promos
                      </TableCell>
                      <TableCell style={{ fontWeight: 'bold' }}>
                        Total Dollars Invested
                      </TableCell>
                      <TableCell style={{ fontWeight: 'bold' }}>
                        Total Dollars Rewarded
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {promoTypes?.length
                      ? promoTypes.map((promoType) => (
                          <TableRow key={`promo-type-${promoType.id}`}>
                            <TableCell style={{ fontWeight: 'bold' }}>
                              {promoType.name}
                            </TableCell>
                            <TableCell>
                              {numeral(promoType.openIncompleteCount).format(
                                '0,0'
                              )}
                            </TableCell>
                            <TableCell>
                              {numeral(promoType.completedCount).format('0,0')}
                            </TableCell>
                            <TableCell>
                              {numeral(promoType.totalDollarsInvested).format(
                                '$0,0'
                              )}
                            </TableCell>
                            <TableCell>
                              {numeral(promoType.totalDollarsGifted).format(
                                '$0,0'
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      : null}
                  </TableBody>
                </Table>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      ) : (
        <CircularProgress />
      )}
      <List
        title={entityName}
        sort={{ field: 'id', order: 'DESC' }}
        filters={<PromoCodesFilter />}
        perPage={25}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="promoType" />
          <LinkField
            reference="User"
            linkSource="user.id"
            labelSource="user.fullName"
            label="User"
          />
          <TextField source="code" />
          <DateField source="expirationDt" />
          <DateField source="completedDt" />
          <ArrayField
            source="triggeringInvestments"
            label="Triggering (Qualifying) Investments"
          >
            <SingleFieldList>
              <CustomReferenceField source="label" />
            </SingleFieldList>
          </ArrayField>
          <LinkField
            reference="Investment"
            linkSource="rewardInvestment.id"
            labelSource="rewardInvestment.label"
            label="Reward (Gifted) Investment"
          />
        </Datagrid>
      </List>
    </>
  );
};

const attrs = [
  {
    name: 'promoType',
    label: 'Promo Type',
    align: 'center',
  },
  {
    name: 'userId',
    label: 'User ID',
    align: 'center',
  },
  {
    name: 'code',
    label: 'Code',
    align: 'center',
  },
  {
    name: 'expirationDt',
    label: 'Expiration Date',
    align: 'center',
    dataFormat: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss'),
  },
];

export const PromoCodeCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <Grid container>
      <Grid item>
        <Typography>Upload from CSV:</Typography>
        <PromoCodeUpload attrs={attrs} />
      </Grid>
      <Grid item>
        <Typography>Create Manually:</Typography>
        <SimpleForm>
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="promoType" required fullWidth />
              <ReferenceInput
                perPage={100}
                source="user.id"
                reference="UserLite"
              >
                <AutocompleteInput
                  allowEmpty={true}
                  optionText="fullName"
                  shouldRenderSuggestions={(value) => value.trim().length > 0}
                  label="User (Investor)"
                  fullWidth
                />
              </ReferenceInput>
              <TextInput source="code" fullWidth />
              <DateInput source="expirationDt" fullWidth />
              <DateInput source="completedDt" fullWidth />
            </Grid>
          </Grid>
        </SimpleForm>
      </Grid>
    </Grid>
  </Create>
);
