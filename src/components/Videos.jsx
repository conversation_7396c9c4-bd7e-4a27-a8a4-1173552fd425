import React from 'react';
import { Video } from 'cloudinary-react';

import { useDataProvider, useNotify, useRefresh } from 'react-admin';
import { Button, Divider, Grid, Link, Typography, Paper } from '@mui/material';

import Config from '../config/config';

import { openUploadWidget, url } from '../utils/CloudinaryService';

export const VideoList = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const uploadVideoWithCloudinary = (uploadOptions) => {
    return () => {
      openUploadWidget(uploadOptions, (error, resp) => {
        if (!error && resp.event === 'success') {
          console.log('Successfully uploaded video', resp);
          notify('Video successfully uploaded');
          refresh();
        }
      });
    };
  };

  const generateSignature = (callback, props) => {
    return dataProvider
      .getOne('VideoSignature', {
        data: JSON.stringify(props),
      })
      .then(
        (resp) => {
          return callback(resp.data.signature);
        },
        (e) => {
          console.error(e);
        }
      );
  };

  return (
    <Paper style={{ padding: '1em' }}>
      <Grid container direction="column">
        <Typography gutterBottom variant="h6">
          Homepage Banner
        </Typography>
        <Video
          cloudName={Config.cloud_name}
          publicId="energea/global-videos/home_banner"
          sourceTypes={['mp4']}
          width="200"
          crop="scale"
          controls="true"
          // autoPlayMode="always"
        />
        <Button
          variant="outlined"
          style={{ width: '200px' }}
          onClick={uploadVideoWithCloudinary({
            multiple: false,
            resourceType: 'video',
            showPoweredBy: false,
            invalidate: true,
            eager: [
              { width: 200, crop: 'scale' },
              { width: 960, crop: 'scale' },
              { width: 1280, crop: 'scale' },
              { width: 1920, crop: 'scale' },
            ],
            // eslint-disable-next-line camelcase
            eager_async: true,
            showCompletedButton: true,
            apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
            cropping: true,
            uploadSignature: generateSignature,
            cloudName: Config.cloud_name,
            showAdvancedOptions: true,
            uploadPreset: Config.global_video_upload_preset,
          })}
        >
          Update Home Page Banner Video
        </Button>
        <Divider
          style={{ width: '200px', marginTop: '2em', marginBottom: '2em' }}
        />
        <Typography gutterBottom variant="h6">
          Homepage Mobile Banner
        </Typography>
        <Video
          cloudName={Config.cloud_name}
          publicId="energea/global-videos/home_banner"
          sourceTypes={['mp4']}
          width="200"
          crop="scale"
          controls="true"
          // autoPlayMode="always"
        />
        <Button
          variant="outlined"
          style={{ width: '200px' }}
          onClick={uploadVideoWithCloudinary({
            multiple: false,
            resourceType: 'video',
            showPoweredBy: false,
            invalidate: true,
            eager: [
              { width: 200, crop: 'scale' },
              { width: 960, crop: 'scale' },
              { width: 1280, crop: 'scale' },
              { width: 1920, crop: 'scale' },
            ],
            // eslint-disable-next-line camelcase
            eager_async: true,
            showCompletedButton: true,
            apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
            cropping: true,
            uploadSignature: generateSignature,
            cloudName: Config.cloud_name,
            showAdvancedOptions: true,
            uploadPreset: Config.global_video_upload_preset,
          })}
        >
          Update Home Page MOBILE Banner Video
        </Button>
        <Divider
          style={{ width: '200px', marginTop: '2em', marginBottom: '2em' }}
        />
        <Typography gutterBottom variant="h6">
          How It Works
        </Typography>
        <Video
          cloudName={Config.cloud_name}
          publicId="energea/global-videos/l4ztljjgd0fb0tljlvuq"
          muted={false}
          sourceTypes={['mp4']}
          width="200"
          crop="scale"
          controls="true"
          // autoPlayMode="always"
        />
        <Link
          style={{ marginTop: '.5rem' }}
          href={url('energea/global-videos/l4ztljjgd0fb0tljlvuq.mp4', {
            resourceType: 'video',
          })}
        >
          Highest Quality (Original File)
        </Link>
        <Link
          style={{ marginTop: '.5rem' }}
          href={url('energea/global-videos/l4ztljjgd0fb0tljlvuq.mp4', {
            resourceType: 'video',
            width: 1920,
            crop: 'scale',
          })}
        >
          High Quality (1920px width)
        </Link>
        <Link
          style={{ marginTop: '.5rem' }}
          href={url('energea/global-videos/l4ztljjgd0fb0tljlvuq.mp4', {
            resourceType: 'video',
            width: 1280,
            crop: 'scale',
          })}
        >
          Medium Quality (1280px width)
        </Link>
        <Link
          style={{ marginTop: '.5rem', marginBottom: '.5rem' }}
          href={url('energea/global-videos/l4ztljjgd0fb0tljlvuq.mp4', {
            resourceType: 'video',
            width: 960,
            crop: 'scale',
          })}
        >
          Low Quality (960px Width)
        </Link>
        <Button
          variant="outlined"
          style={{ width: '200px' }}
          onClick={uploadVideoWithCloudinary({
            multiple: false,
            resourceType: 'video',
            showPoweredBy: false,
            invalidate: true,
            eager: [
              { width: 200, crop: 'scale' },
              { width: 400, crop: 'scale' },
              { width: 960, crop: 'scale' },
              { width: 1280, crop: 'scale' },
              { width: 1920, crop: 'scale' },
            ],
            // eslint-disable-next-line camelcase
            eager_async: true,
            // publicId: 'how_it_works',
            showCompletedButton: true,
            apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
            cropping: true,
            uploadSignature: generateSignature,
            cloudName: Config.cloud_name,
            showAdvancedOptions: true,
            uploadPreset: Config.global_video_upload_preset,
          })}
        >
          Update How It Works Video
        </Button>
      </Grid>
    </Paper>
  );
};
