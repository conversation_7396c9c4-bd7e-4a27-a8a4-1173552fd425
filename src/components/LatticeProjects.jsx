import React, { useState } from 'react';
import numeral from 'numeral';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  ArrayInput,
  BooleanInput,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  FormDataConsumer,
  FormTab,
  FunctionField,
  TabbedForm,
  List,
  Show,
  SimpleFormIterator,
  SimpleShowLayout,
  SingleFieldList,
  required,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  useRefresh,
  useRecordContext,
} from 'react-admin';

import {
  Button,
  Divider,
  FormHelperText,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Delete,
  Description,
  Folder,
  GetApp,
  Help,
  PictureAsPdf,
} from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import { Alert } from '@mui/lab';

import { Image, Video, Transformation } from 'cloudinary-react';

import Config from '../config/config';
import {
  CustomReferenceField,
  DetailField,
  CustomBooleanField,
  CustomNumberInput,
  LinkField,
} from './CustomFields';

import { openUploadWidget } from '../utils/CloudinaryService';
import theme from '../theme';
import EPCConversionGrid from './EPCConversionGrid';

const entityName = 'Project';

const Title = () => {
  const record = useRecordContext();
  return (
    <span>
      {entityName}
      {record ? ` #${record.id} - ${record.name}` : ''}
    </span>
  );
};

export const LatticeProjectEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [editDropboxLocation, setEditDropboxLocation] = useState(false);
  const { id } = useParams();

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['projects'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.project_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.project_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideosUploaded([resp.info]);
      }
    });
  };

  const uploadDocumentWithCloudinary = (id, type) => {
    return () => {
      const uploadOptions = {
        tags: [type, id],
        showPoweredBy: false,
        multiple: false,
        cloudName: Config.cloud_name,
        sources: ['local', 'url'],
        // dropboxAppKey: '1y1wsyzgzfb5f2g',
        uploadPreset: Config.project_document_upload_preset,
      };
      openUploadWidget(uploadOptions, (error, resp) => {
        if (!error && resp.event === 'success') {
          onDocumentUploaded(resp.info, type);
        }
      });
    };
  };

  const handleMakeVideoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryVideo: resource.id,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Videos successfully updated');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error updating videos', { type: 'error' });
        });
    };
  };

  const handleMakePhotoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryImage: resource.id,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Images successfully updated');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error updating images', { type: 'error' });
        });
    };
  };

  const handleRemovePhoto = (resource) => {
    return () => {
      dataProvider
        .delete('ProjectImage', {
          projectId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Image successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing image', { type: 'error' });
        });
    };
  };

  const handleRemoveDocument = (documentId) => {
    return () => {
      dataProvider
        .delete('ProjectDocument', {
          id: documentId,
        })
        .then(() => {
          notify('Document successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing document', { type: 'error' });
        });
    };
  };

  const handleRemoveVideo = (resource) => {
    return () => {
      dataProvider
        .delete('ProjectVideo', {
          projectId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Video successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing video', { type: 'error' });
        });
    };
  };

  const onPhotosUploaded = (aPhotos) => {
    const photos = aPhotos.map((photo) => {
      return {
        public_id: photo.public_id,
        projectId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('ProjectImage', {
        input: photos,
      })
      .then((record) => {
        notify('Image successfully uploaded');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading image.', { type: 'error' });
      });
  };

  const onVideosUploaded = (videos) => {
    videos = videos.map((video) => {
      return {
        public_id: video.public_id,
        projectId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('ProjectVideo', {
        input: videos,
      })
      .then((record) => {
        notify('Video uploaded successfully');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading video.', { type: 'error' });
      });
  };

  const onDocumentUploaded = (document, type) => {
    const docObj = document;
    delete docObj.id;
    docObj.title = `${type}-(${id})`;
    dataProvider
      .update('Project', {
        data: { id: parseInt(id, 10), [type]: docObj },
      })
      .then(() => {
        notify('Document successfully added');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error adding document', { type: 'error' });
      });
  };

  const handleDownloadDropboxFile = (fileLocation) => {
    dataProvider
      .getOne('DropboxFile', {
        fileLocation,
      })
      .then((res) => {
        if (!res.data) {
          notify('Error getting dropbox download url', { type: 'error' });
          return null;
        }
        const link = document.createElement('a');
        link.href = res.data.downloadUrl;
        link.download = true;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch((e) => {
        console.error(e);
        notify('Error getting dropbox download url', { type: 'error' });
      });
  };

  const handleDeleteDropboxFile = (fileLocation) => {
    dataProvider
      .delete('DropboxFile', {
        fileLocation,
      })
      .then((res) => {
        notify('File deleted from Dropbox', { type: 'success' });
        refresh();
      })
      .catch((e) => {
        console.error(e);
        notify('Error deleting file from Dropbox', { type: 'error' });
      });
  };

  const handleUploadFileToDropbox = (event, dropboxDirectory) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 100) {
        notify('Document must be less than 100MB', { type: 'error' });
        return;
      }
      dataProvider
        .create('DropboxFile', {
          fileLocation: `${dropboxDirectory}/${file.name}`,
        })
        .then((res) => {
          const uploadUrl = res.data;
          const formData = new FormData();
          formData.append('File', file);
          fetch(uploadUrl, {
            method: 'POST',
            body: formData,
            headers: { 'Content-type': 'application/octet-stream' },
          })
            .then((response) => response.json())
            .then((result) => {
              console.log('Success:', result);
              notify('File uploaded to Dropbox', { type: 'success' });
              refresh();
            })
            .catch((error) => {
              console.error('Error:', error);
              notify('Error uploading file to Dropbox', { type: 'error' });
              refresh();
            });
        })
        .catch((e) => {
          console.error(e);
          notify('Error getting file upload url from Dropbox', {
            type: 'error',
          });
        });
    }
  };

  return (
    <Edit title={<Title />} undoable={false}>
      <TabbedForm redirect={false}>
        <FormTab label="Summary">
          <Grid
            container
            style={{ width: '100%', marginTop: '.5rem' }}
            spacing={5}
          >
            <Grid item xs={12} md={6}>
              <Typography variant="h5">Details</Typography>
              <TextInput
                label="Name"
                validate={required()}
                source="name"
                fullWidth
              />
              <ReferenceInput source="portfolio.id" reference="PortfolioLite">
                <SelectInput label="Portfolio" fullWidth optionText="name" />
              </ReferenceInput>
              <TextInput
                required
                source="shortSummary"
                fullWidth
                helperText="Shows up on project popup tile."
              />
              <TextInput
                source="shortSummaryPT"
                fullWidth
                helperText="Project description used only on the .br site (only required for consortium projects)."
              />
              {/* <TextInput multiline source="summary" fullWidth helperText="Not currently in use." /> */}
              <ReferenceInput
                source="installationType.id"
                reference="InstallationType"
              >
                <SelectInput
                  label="Installation Type"
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput>
              <DateInput
                label="NTP date"
                helperText="Notice to proceed date. If this is not set, or the date is prior to the current date, than the project will not be included in the portfolio's investment cap."
                source="ntpDt"
                fullWidth
              />
              <CustomNumberInput
                required
                source="debt"
                fullWidth
                helperText="Portion of total project cost funded by debt."
              />
              <CustomNumberInput
                required
                source="sponsorEquity"
                fullWidth
                helperText="Portion of total project cost funded by sponsor equity (crowd funding)."
              />
              <CustomNumberInput
                required
                source="taxEquity"
                fullWidth
                helperText="Portion of total project cost funded by tax equity."
              />
              {/* <CustomNumberInput
                source="projectedAnnualProduction"
                label="Projected Annual Production (kWh AC)"
                fullWidth
              /> */}
              <CustomNumberInput
                label="Proj. Sys Size (MW) AC"
                helperText="MW AC"
                source="systemSizeAC"
                fullWidth
              />
              <CustomNumberInput
                label="Proj. Sys Size (MW) DC"
                helperText="MW DC"
                source="systemSizeDC"
                fullWidth
              />
              <CustomNumberInput source="percentageOwnership" fullWidth />
              <DateInput
                label="Projected COD"
                source="projectedCOD"
                fullWidth
              />
              <DateInput label="Actual COD" source="actualCOD" fullWidth />
              <TextInput
                label="Internal Notes"
                multiline
                source="internalNotes"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h5">Status</Typography>
              <ReferenceInput
                source="projectInvestmentStatus.id"
                reference="ProjectInvestmentStatus"
                filter={{ activeOnly: true }}
              >
                <SelectInput
                  label="Investment Status"
                  fullWidth
                  optionText="name"
                  helperText="Adds badge on project list in portfolio detail page"
                />
              </ReferenceInput>
              <BooleanInput
                source="isPublic"
                label="Is Public?"
                helperText="Determines whether or not the project is included in it's parent portfolio...if it is not public, it essentially does not exist."
                fullWidth
              />
              <BooleanInput
                source="newFlg"
                label="Is New?"
                helperText="Determines whether or not the project is marked as new in it's parent portfolio."
                fullWidth
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Address">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="address1" fullWidth />
              <TextInput source="address2" fullWidth />
              <TextInput source="city" fullWidth />
              <TextInput source="postalCode" fullWidth />
              <TextInput source="state" fullWidth />
              <ReferenceInput source="country.id" reference="Country">
                <SelectInput
                  label="Country"
                  required
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput>
              <CustomNumberInput required source="latitude" fullWidth />
              <CustomNumberInput required source="longitude" fullWidth />
              <FormHelperText>
                To get longitude and latitude in decimal form{' '}
                <a
                  href="https://www.fcc.gov/media/radio/dms-decimal"
                  target="_blank"
                >
                  go here
                </a>
                ).
              </FormHelperText>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Media">
          <Grid style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Alert severity="info">
                <Typography>
                  All images : 1600px X 1200px (landscape)
                </Typography>
              </Alert>
            </Grid>
          </Grid>
          <Typography variant="h4" gutterBottom>
            Images
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.images || formData?.images?.length === 0)
                return 'No photos';
              return formData.images.map((record) => (
                <Grid style={{ paddingBottom: '1rem' }}>
                  <Image
                    style={{}}
                    cloud_name={Config.cloud_name}
                    publicId={record.public_id}
                  >
                    <Transformation width="200" crop="scale" />
                  </Image>
                  <Grid>
                    {/* <Button
                      color="primary"
                      style={
                        record.primaryFlg
                          ? {
                              backgroundColor: 'green',
                              color: 'white',
                            }
                          : {}
                      }
                      disabled={record.primaryFlg}
                      variant={record.primaryFlg ? 'contained' : 'outlined'}
                      onClick={handleMakePhotoPrimary(record)}
                    >
                      Make Primary Photo
                    </Button> */}
                    {/* <Button
                      style={{ float: 'right' }}
                      onClick={handleRemovePhoto(record)}
                    >
                      <Delete />
                    </Button> */}
                  </Grid>
                </Grid>
              ));
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button variant="outlined" onClick={uploadImageWithCloudinary}>
              Add photo
            </Button>
          </div>
          <Divider style={{ width: '100%', margin: '2em 0' }} />
          <Typography variant="h4" gutterBottom>
            Videos
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.videos || formData?.videos.length === 0)
                return 'No videos';
              return formData.videos.map((record) => {
                if (!record?.public_id) return null;
                return (
                  <Grid style={{ paddingBottom: '1rem' }}>
                    <Video
                      cloud_name={Config.cloud_name}
                      publicId={record.public_id}
                      muted
                      width="200"
                      sourceTypes={['mp4']}
                      controls
                    >
                      {record.public_id ? (
                        <Transformation width={200} crop="scale" />
                      ) : null}
                    </Video>
                    <Grid>
                      {/* <Button
                        color="primary"
                        style={
                          record.primaryFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        disabled={record.primaryFlg}
                        variant={record.primaryFlg ? 'contained' : 'outlined'}
                        onClick={handleMakeVideoPrimary(record)}
                      >
                        Make Primary Video
                      </Button> */}
                      {/* <Button
                        style={{ float: 'right' }}
                        onClick={handleRemoveVideo(record)}
                      >
                        <Delete />
                      </Button> */}
                    </Grid>
                  </Grid>
                );
              });
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button variant="outlined" onClick={uploadVideoWithCloudinary}>
              Add video
            </Button>
          </div>
        </FormTab>
        <FormTab label="Documents">
          <Typography>IC Memo</Typography>
          <FunctionField
            source="ICMemo"
            label="ICMemo"
            render={(record) => {
              return (
                <>
                  <div>
                    {!record.ICMemo ? (
                      <>
                        <Button
                          // style={{ float: 'right' }}
                          variant="contained"
                          color="primary"
                          onClick={uploadDocumentWithCloudinary(
                            record.id,
                            'ICMemo'
                          )}
                        >
                          Add IC-Memo
                        </Button>
                        <FormHelperText>
                          IC-Memo must be in pdf form and don't forget to reduce
                          your file size before uploading (
                          <a
                            href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                            target="_blank"
                          >
                            Mac directions
                          </a>
                          ).
                        </FormHelperText>
                      </>
                    ) : (
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          // eslint-disable-next-line security/detect-non-literal-fs-filename
                          window.open(record.ICMemo.url, '_blank');
                        }}
                      >
                        <PictureAsPdf /> - View file
                      </Button>
                    )}
                    {record.ICMemo ? (
                      <Button
                        // style={{ float: 'right' }}
                        onClick={handleRemoveDocument(record.ICMemo.id)}
                      >
                        <Delete />
                      </Button>
                    ) : null}
                  </div>
                  {record.ICMemo ? (
                    <TextInput
                      label="Title"
                      helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - IC Memo')"
                      source="ICMemo.title"
                    />
                  ) : null}
                </>
              );
            }}
          />

          <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />

          <Typography>Project Model</Typography>
          <FunctionField
            source="projectModel"
            label="Project Model"
            render={(record) => {
              return (
                <>
                  <div>
                    {!record.projectModel ? (
                      <>
                        <Button
                          // style={{ float: 'right' }}
                          variant="contained"
                          color="primary"
                          onClick={uploadDocumentWithCloudinary(
                            record.id,
                            'projectModel'
                          )}
                        >
                          Add Project Model
                        </Button>

                        <FormHelperText>
                          Project Model must be in pdf form. Don't forget to
                          reduce your file size before uploading (
                          <a
                            href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                            target="_blank"
                          >
                            Mac directions
                          </a>
                          ).
                        </FormHelperText>
                      </>
                    ) : (
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          // eslint-disable-next-line security/detect-non-literal-fs-filename
                          window.open(record.projectModel.url, '_blank');
                        }}
                      >
                        <PictureAsPdf /> - View file
                      </Button>
                    )}
                    {record.projectModel ? (
                      <Button
                        // style={{ float: 'right' }}
                        onClick={handleRemoveDocument(record.projectModel.id)}
                      >
                        <Delete />
                      </Button>
                    ) : null}
                  </div>
                  {record.projectModel ? (
                    <TextInput
                      label="Title"
                      helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - Financial Model')"
                      source="projectModel.title"
                    />
                  ) : null}
                </>
              );
            }}
          />
        </FormTab>
        <FormTab label="Monitoring" path="monitoring">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <DateInput label="Actual COD" source="actualCOD" fullWidth />
              <DateInput
                label="Monitoring Start Dt"
                source="monitoringStartDt"
                helperText="This date is used to discount expected production when it is after the Actual COD on the Asset Management Dashboard"
                fullWidth
              />
              <ReferenceInput
                source="projectMonitoringStatus.id"
                reference="ProjectMonitoringStatus"
              >
                <SelectInput
                  label="Monitoring Status"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput source="monitoringNotes" multiline fullWidth />
              <CustomNumberInput
                label="Degradation constant"
                source="degradationConstant"
                fullWidth
              />
              <CustomNumberInput
                source="transformerLoss"
                helperText="After changing, run 'Apply Transformer Loss to Production Periods' on the CMS Dashboard to update production values. Input the decimal form of the percentage of power loss for projects where the meter is before the transformer. Write 5% as 0.05."
                fullWidth
              />
              <CustomNumberInput
                label="January P50 Production (kWh)"
                source="janP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="February P50 Production (kWh)"
                source="febP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="March P50 Production (kWh)"
                source="marP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="April P50 Production (kWh)"
                source="aprP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="May P50 Production (kWh)"
                source="mayP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="June P50 Production (kWh)"
                source="junP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="July P50 Production (kWh)"
                source="julP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="August P50 Production (kWh)"
                source="augP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="September P50 Production (kWh)"
                source="sepP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="October P50 Production (kWh)"
                source="octP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="November P50 Production (kWh)"
                source="novP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="December P50 Production (kWh)"
                source="decP50Prod"
                fullWidth
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="O&M" path={'O&M'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12}>
              <ReferenceInput
                source="projectManager.id"
                reference="EmployeeLite"
                disabled
                sort={{ field: 'firstName', order: 'ASC' }}
              >
                <SelectInput
                  label="Project Manager"
                  fullWidth
                  allowEmpty
                  optionText="fullName"
                  disabled
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6">
                <b>Platform Credentials</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="omPlatformCredentials"
                id="om-platform-credentials"
              >
                <SimpleFormIterator>
                  <TextInput
                    fullWidth
                    required
                    label="Platform name"
                    source="name"
                  />
                  <TextInput label="Platform url" source="url" fullWidth />
                  <TextInput label="Username" source="username" fullWidth />
                  <TextInput label="Password" source="password" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6">
                <b>Contacts</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectContacts"
                id="project-contacts"
              >
                <SimpleFormIterator>
                  <TextInput
                    fullWidth
                    required
                    label="Label"
                    helperText="ex: O&M Contractor"
                    source="name"
                  />
                  <TextInput
                    label="Primary Contact Name"
                    source="primaryContactName"
                    fullWidth
                  />
                  <TextInput label="Phone" source="phone" fullWidth />
                  <TextInput label="Email" source="email" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6">
                <b>Equipment</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectEquipmentItems"
                id="project-equipment"
              >
                <SimpleFormIterator>
                  <ReferenceInput
                    source="equipmentItem.id"
                    reference="EquipmentItem"
                  >
                    <SelectInput
                      label="Equipment"
                      required
                      fullWidth
                      optionText="model"
                    />
                  </ReferenceInput>
                  <CustomNumberInput
                    required
                    label="Quantity"
                    source="quantity"
                    fullWidth
                  />
                </SimpleFormIterator>
              </ArrayInput>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/EquipmentItem/create"
              >
                Add new equipment
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Insurance Policies</b>
              </Typography>
              <ArrayField source="projectInsurancePolicies" sortable={false}>
                <SingleFieldList>
                  <CustomReferenceField
                    source="label"
                    color={() => 'primary'}
                  />
                </SingleFieldList>
              </ArrayField>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/InsurancePolicy/create"
              >
                Add new insurance policy
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contracts</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  // if (!formData.omContractsDropboxLocation && !editDropboxLocation) {
                  //   setEditDropboxLocation(true)
                  // }
                  return (
                    <>
                      {!editDropboxLocation ? (
                        <Grid container>
                          <Grid item xs={11} style={{ paddingLeft: '1rem' }}>
                            <Typography variant="body2" gutterBottom>
                              <b>Dropbox directory:</b>{' '}
                              {formData.omContractsDropboxLocation ||
                                'None specified. Click the pencil to edit.'}
                            </Typography>
                          </Grid>
                          <Grid item xs={1}>
                            <Grid container item justifyContent="flex-end">
                              <IconButton
                                onClick={() => {
                                  setEditDropboxLocation(true);
                                }}
                                size="large"
                              >
                                <EditIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Grid>
                      ) : (
                        <TextInput
                          source="omContractsDropboxLocation"
                          fullWidth
                          helperText="The '8.1.1 O&M Agreement' directory for this project. Ex: '/Energea Global/Market II - USA/Portfolio/Projects/Portfolio 4 - MA - Waltham/8. Asset Management/8.1 O&M/8.1.1 O&M Agreement'"
                        />
                      )}
                      <Table>
                        <TableBody>
                          {formData.contracts &&
                          formData.contracts.length > 0 ? (
                            formData.contracts.map((contract, index) => (
                              <TableRow key={`contracts-row-${contract.id}`}>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none', width: '1rem' }
                                      : { width: '1rem' }
                                  }
                                >
                                  {contract.isDirectory ? (
                                    <Folder />
                                  ) : contract.isFile ? (
                                    <Description />
                                  ) : (
                                    <Help />
                                  )}
                                </TableCell>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <b>{contract.name}</b>
                                </TableCell>
                                <TableCell
                                  align="right"
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <Tooltip title="Download">
                                    <IconButton
                                      color="primary"
                                      disabled={!contract.isDownloadable}
                                      onClick={() =>
                                        handleDownloadDropboxFile(
                                          contract.location
                                        )
                                      }
                                      size="large"
                                    >
                                      <GetApp />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      style={{
                                        color: !contract.isFile
                                          ? null
                                          : theme.palette.error.main,
                                      }}
                                      disabled={!contract.isFile}
                                      onClick={() => {
                                        if (
                                          window.confirm(
                                            `Clicking 'OK' will PERMANENTLY DELETE this document from Dropbox. Are you sure you wish to delete '${contract.name}'?`
                                          )
                                        ) {
                                          handleDeleteDropboxFile(
                                            contract.location
                                          );
                                        }
                                      }}
                                      size="large"
                                    >
                                      <Delete />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <Alert severity="info">
                              No files found in Dropbox directory
                            </Alert>
                          )}
                        </TableBody>
                      </Table>
                      <Button
                        style={{ marginTop: '1rem' }}
                        variant="contained"
                        color="secondary"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        Upload Contract to Dropbox
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            handleUploadFileToDropbox(
                              event,
                              formData.omContractsDropboxLocation
                            )
                          }
                        />
                      </Button>
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Env. Impact">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Projected</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} will create{' '}
                      <b>
                        {numeral(
                          formData.lifetimeEnergyProjection / 1000
                        ).format('0,0')}{' '}
                        MWh
                      </b>{' '}
                      in its lifetime. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.lifetimeEnergyProjection / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Actual</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} has created{' '}
                      <b>
                        {numeral(formData.allTimeActual / 1000).format('0,0.0')}{' '}
                        MWh
                      </b>{' '}
                      so far. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.allTimeActual / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const ShowContent = () => {
  return (
    <Grid container>
      <Grid item xs={12}>
        <Alert severity="warning">
          Page Under Construction ( To Lattice Capital : page will be cleaned up
          shortly. Thanks for your patience! -Gray )
        </Alert>
      </Grid>
    </Grid>
  );
};

export const LatticeProjectShow = () => {
  return (
    <Show title={<Title />}>
      <>
        <ShowContent />
        <SimpleShowLayout>
          <TextField source="name" />
          <TextField source="shortSummary" />
          <TextField source="shortSummaryPT" />
          <TextField source="summary" />
          <CustomBooleanField source="isPublic" />
          <CustomBooleanField source="newFlg" />
          <NumberField source="debt" />
          <NumberField source="sponsorEquity" />
          <NumberField source="taxEquity" />
          <NumberField source="costAssessment" />
          {/* <NumberField source="projectedAnnualProduction" /> */}
          <NumberField source="systemSizeAC" />
          <NumberField source="systemSizeDC" />
          <DateField label="Projected COD" source="projectedCOD" />
          <DateField label="Actual COD" source="actualCOD" />
          <TextField multiline source="internalNotes" />
          <TextField source="primaryImage" />
          <Typography variant="h6">PVSyst</Typography>
          <NumberField
            label="Degradation constant"
            source="degradationConstant"
            fullWidth
          />
          <NumberField
            source="transformerLoss"
            helperText="After changing, run 'Apply Transformer Loss to Production Periods' on the CMS Dashboard to update production values. Input the decimal form of the percentage of power loss for projects where the meter is before the transformer. Write 5% as 0.05."
            fullWidth
          />
          <NumberField
            label="January P50 Production (kWh)"
            source="janP50Prod"
            fullWidth
          />
          <NumberField
            label="February P50 Production (kWh)"
            source="febP50Prod"
            fullWidth
          />
          <NumberField
            label="March P50 Production (kWh)"
            source="marP50Prod"
            fullWidth
          />
          <NumberField
            label="April P50 Production (kWh)"
            source="aprP50Prod"
            fullWidth
          />
          <NumberField
            label="May P50 Production (kWh)"
            source="mayP50Prod"
            fullWidth
          />
          <NumberField
            label="June P50 Production (kWh)"
            source="junP50Prod"
            fullWidth
          />
          <NumberField
            label="July P50 Production (kWh)"
            source="julP50Prod"
            fullWidth
          />
          <NumberField
            label="August P50 Production (kWh)"
            source="augP50Prod"
            fullWidth
          />
          <NumberField
            label="September P50 Production (kWh)"
            source="sepP50Prod"
            fullWidth
          />
          <NumberField
            label="October P50 Production (kWh)"
            source="octP50Prod"
            fullWidth
          />
          <NumberField
            label="November P50 Production (kWh)"
            source="novP50Prod"
            fullWidth
          />
          <NumberField
            label="December P50 Production (kWh)"
            source="decP50Prod"
            fullWidth
          />
          <FunctionField
            label="Main Image"
            render={(record) => {
              if (!record.primaryImage) return null; // TODO return placeholder
              return (
                <Image
                  cloud_name={Config.cloud_name}
                  publicId={record.primaryImage.public_id}
                >
                  <Transformation width="120" crop="scale" />
                </Image>
              );
            }}
          />
          <FunctionField
            label="Main Image"
            render={(record) => {
              // if (!formData.omContractsDropboxLocation && !editDropboxLocation) {
              //   setEditDropboxLocation(true)
              // }
              return (
                <>
                  <Grid container>
                    <Grid item xs={11} style={{ paddingLeft: '1rem' }}>
                      <Typography variant="body2" gutterBottom>
                        <b>Dropbox directory:</b>{' '}
                        {record.omContractsDropboxLocation ||
                          'None specified. Click the pencil to edit.'}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Table>
                    <TableBody>
                      {record.contracts && record.contracts.length > 0 ? (
                        record.contracts.map((contract, index) => (
                          <TableRow key={`contracts-row-${contract.id}`}>
                            <TableCell
                              style={
                                index === record.contracts.length - 1
                                  ? { borderBottom: 'none', width: '1rem' }
                                  : { width: '1rem' }
                              }
                            >
                              {contract.isDirectory ? (
                                <Folder />
                              ) : contract.isFile ? (
                                <Description />
                              ) : (
                                <Help />
                              )}
                            </TableCell>
                            <TableCell
                              style={
                                index === record.contracts.length - 1
                                  ? { borderBottom: 'none' }
                                  : {}
                              }
                            >
                              <b>{contract.name}</b>
                            </TableCell>
                            <TableCell
                              align="right"
                              style={
                                index === formData.contracts.length - 1
                                  ? { borderBottom: 'none' }
                                  : {}
                              }
                            >
                              <Tooltip title="Download">
                                <IconButton
                                  color="primary"
                                  disabled={!contract.isDownloadable}
                                  onClick={() =>
                                    handleDownloadDropboxFile(contract.location)
                                  }
                                  size="large"
                                >
                                  <GetApp />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <IconButton
                                  style={{
                                    color: !contract.isFile
                                      ? null
                                      : theme.palette.error.main,
                                  }}
                                  disabled={!contract.isFile}
                                  onClick={() => {
                                    if (
                                      window.confirm(
                                        `Clicking 'OK' will PERMANENTLY DELETE this document from Dropbox. Are you sure you wish to delete '${contract.name}'?`
                                      )
                                    ) {
                                      handleDeleteDropboxFile(
                                        contract.location
                                      );
                                    }
                                  }}
                                  size="large"
                                >
                                  <Delete />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <Alert severity="info">
                          No files found in Dropbox directory
                        </Alert>
                      )}
                    </TableBody>
                  </Table>
                  {/* <Button
                  style={{ marginTop: '1rem' }}
                  variant="contained"
                  color="secondary"
                  component="label" // https://stackoverflow.com/a/54043619
                >
                  Upload Contract to Dropbox
                  <input
                    type="file"
                    hidden
                    onChange={(event) =>
                      handleUploadFileToDropbox(
                        event,
                        formData.omContractsDropboxLocation
                      )
                    }
                  />
                </Button> */}
                </>
              );
            }}
          />
        </SimpleShowLayout>
      </>
    </Show>
  );
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <ExportButton maxResults={100000} />
    </TopToolbar>
  );
};

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);
export const LatticeProjectList = () => (
  <List
    perPage={25}
    pagination={<CustomPagination />}
    actions={<ListActions />}
  >
    <Datagrid rowClick={'show'}>
      <TextField source="id" />
      <TextField source="name" />
      <DetailField source="shortSummary" sortable={false} />
      {/* <DetailField source="shortSummaryPT" sortable={false} /> */}
      <CustomBooleanField source="isPublic" />
      <DateField label="NTP" source="ntpDt" />
      <DateField label="Projected COD" source="projectedCOD" />
      <DateField label="Actual COD" source="actualCOD" />
      {/* <LinkField
        reference="Portfolio"
        linkSource="portfolio.id"
        labelSource="portfolio.name"
        label="Portfolio"
      /> */}
      <LinkField
        label="Status"
        linkSource="projectInvestmentStatus.id"
        labelSource="projectInvestmentStatus.name"
        reference="ProjectInvestmentStatus"
      />
      <LinkField
        label="Project Manager"
        linkSource="projectManager.id"
        labelSource="projectManager.fullName"
        reference="Employee"
      />
      <CustomBooleanField
        label="P50 Data Inputted"
        source="P50ProdCompleteFlg"
        sortable={false}
      />
      <CustomBooleanField
        label="Has primary video?"
        source="hasPrimaryVideo"
        sortable={false}
      />
      <FunctionField
        label="Main Image"
        render={(record) => {
          if (!record.primaryImage) return null; // TODO return placeholder
          return (
            <Image
              cloud_name={Config.cloud_name}
              publicId={record.primaryImage.public_id}
            >
              <Transformation width="120" crop="scale" />
            </Image>
          );
        }}
      />
      <NumberField
        label="Cost Assessment"
        source="costAssessment"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField label="Proj. Sys Size (MW) DC" source="systemSizeDC" />
      <NumberField label="Proj. Sys Size (MW) AC" source="systemSizeAC" />
      <DetailField source="internalNotes" sortable={false} />
    </Datagrid>
  </List>
);
