import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Icon } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Project Monitoring Status';

export const ProjectMonitoringStatusEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
            <TextInput source="color" helperText="Ex: #ffffff" fullWidth />
            <TextInput
              source="iconClassName"
              helperText="Ex: fas fa-bolt"
              fullWidth
            />
            <CustomNumberInput source="orderNo" />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const ProjectMonitoringStatusList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <FunctionField
          label="Preview"
          render={(record) => (
            <Icon
              style={{ color: record.color }}
              className={record.iconClassName}
            />
          )}
        />
        <TextField source="color" />
        <TextField source="iconClassName" />
        <NumberField source="orderNo" />
      </Datagrid>
    </List>
  );
};

export const ProjectMonitoringStatusCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
          <TextInput source="description" fullWidth />
          <TextInput source="color" helperText="Ex: #ffffff" fullWidth />
          <TextInput
            source="iconClassName"
            helperText="Ex: fas fa-bolt"
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
