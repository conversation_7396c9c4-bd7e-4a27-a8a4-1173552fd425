import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Button,
  Chip,
  CircularProgress,
  Grid,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { ArrowRight, Info } from '@mui/icons-material';

import { useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment-timezone';

import { withStyles } from '@mui/styles';

import theme from '../theme';
import { constants } from '../utils/global';
import { Scatter } from 'react-chartjs-2';

const styles = (theme) => ({});

export default withStyles(styles)((args) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  const fetchData = () => {
    setLoading(true);
    dataProvider.getOne('AnnualReportNumbers', { year: 2024 }).then(
      (res) => {
        setData(res.data);
        setLoading(false);
      },
      (e) => {
        console.error('Error retrieving data', e);
        notify('Error collecting data', { type: 'error' });
        setError(e);
        setLoading(false);
      }
    );
  };

  if (!data && !error && !loading) {
    fetchData();
  }

  if (!data && !error && !loading) {
    return null;
  }
  if (loading) {
    return (
      <Grid
        style={{
          position: 'fixed',
          top: '50%',
          width: '100%',
          textAlign: 'center',
        }}
      >
        <CircularProgress />
      </Grid>
    );
  }

  if (error) {
    return null;
  }

  const {
    getCMSDashboardData: {
      aum,
      latestNAVBasedIRR: { navBasedIRR },
    },
    allInvestorReturnsCached,
    platformLifetimeEnergyProjection,
    platformGrossInvested,
    platformGrossInvestedInYear,
    platformGrossDividendsPaid,
    platformGrossDividendsPaidInYear,
    getNewUserCountAsOfYearEnd,
    getNewUserCountInYear,
    getNewInvestorCountAsOfYearEnd,
    getNewInvestorCountInYear,
    allPublicPortfolios,
    getPublicInstallCapacityUnderConstruction,
    getPublicInstalledCapacityCashFlowing,
  } = data;

  const annualReportNumbers = [
    {
      label: 'AUM',
      value: numeral(aum).format('$0,0'),
      description:
        'Total principal invested by crowd + total institutional investments and debt - total sold share value. AUM is currently including sold portfolios like Commercial Solar in Brazil.',
    },
    {
      label: 'MW DC Under Construction',
      value: numeral(getPublicInstallCapacityUnderConstruction).format(
        '0,0.00'
      ),
      description:
        "All 'public' projects that are in the 'investment status' of 'construction' and has a non-zero DC system size field. This is slightly higher than the Asset Management dashboard value because it includes Pedro Texeira which is not on the AM Dashboard",
    },
    {
      label: 'MW DC Cash Flowing (Installed Capacity)',
      value: numeral(getPublicInstalledCapacityCashFlowing).format('0,0.00'),
      description:
        "All 'public' projects that are in the 'investment status' of 'cash-flowing' and has a non-zero DC system size field.",
    },
    {
      label: 'Est. Lifetime Production (MWh)',
      value: numeral(platformLifetimeEnergyProjection).format('0,0[.]00'),
      description:
        'Calculated by using monthly P50 values along with the degradation constant and the COD -> project projected end date. If there is no projected end date, then we default to 25 years. This includes all public projects. This does not factor in any actual generation.',
    },
    {
      label: 'Est. Lifetime Tons of CO2 Emissions Avoided',
      value: numeral(
        platformLifetimeEnergyProjection *
          constants.epcConversionMultipliers.tonsCarbonEmissionsReduced
      ).format('0,0.[00]a'),
      description: `This uses the conversion multiplier for lifetime energy produced (MWh) to Tons of C02 emissions avoided as per the EPC conversion site (${constants.epcConversionMultipliers.tonsCarbonEmissionsReduced}).`,
    },
    {
      label: 'Est. Lifetime U.S. Homes Powered for 1 Year',
      value: numeral(
        platformLifetimeEnergyProjection *
          constants.epcConversionMultipliers.homesPowered
      ).format('0,0.[00]a'),
      description: `This uses the conversion multiplier for lifetime energy produced (MWh) to U.S. Homes Powered for 1 year as per the EPC conversion site (${constants.epcConversionMultipliers.homesPowered}).`,
    },
    {
      label: 'Total Users',
      value: numeral(getNewUserCountAsOfYearEnd).format('0,0'),
      description:
        'All new platform users as of year end at midnight. Excludes deleted accounts, but includes all others (such as suspended).',
    },
    {
      label: 'New Users in year',
      value: numeral(getNewUserCountInYear).format('0,0'),
      description:
        'All new platform users in past year. Excludes deleted accounts, but includes all others (such as suspended).',
    },
    {
      label: 'Total Investors',
      value: numeral(getNewInvestorCountAsOfYearEnd).format('0,0'),
      description:
        'All new platform investors as of year end at midnight. Includes referral-only investors as well as investors with pending transfers of their first investment. Does not include users with only failed investments.',
    },
    {
      label: 'New Investors in year',
      value: numeral(getNewInvestorCountInYear).format('0,0'),
      description:
        'All new platform investors in year. Includes referral-only investors as well as investors with pending transfers of their first investment. Does not include users with only failed investments.',
    },
    {
      label: 'Platform Gross Invested',
      value: numeral(platformGrossInvested).format('$0,0.00'),
      description:
        'Gross principal invested on platform as of year end at midnight. Does not include institutional investments/debt. Includes Energea. Includes purchases of sold shares. Does not include failed investments. Includes referrals.',
    },
    {
      label: 'Platform Gross Invested in Year',
      value: numeral(platformGrossInvestedInYear).format('$0,0.00'),
      description:
        'Gross principal invested on platform in year. Does not include institutional investments/debt. Includes Energea. Includes purchases of sold shares. Does not include failed investments. Includes referrals.',
    },
    {
      label: 'Platform Dividends Paid',
      value: numeral(platformGrossDividendsPaid).format('$0,0.00'),
      description:
        'Total dividends paid as of year end at midnight. Includes Energea dividends as well as sold project events such as Commercial Solar.',
    },
    {
      label: 'Platform Dividends Paid in Year',
      value: numeral(platformGrossDividendsPaidInYear).format('$0,0.00'),
      description:
        'Total dividends paid in year. Includes Energea dividends as well as sold project events such as Commercial Solar.',
    },
    {
      label: 'Realized IRR',
      value: `${numeral(navBasedIRR).format('0.000')}%`,
      description:
        'Crowd-only IRR calculated using all cash events on platform to date and assuming a sell event of all current holdings at the current NAV. Excludes all Energea activity. This is a current number and does not cut off at the end of year.',
    },
    // {
    //   label: 'Platform Crowd ROI',
    //   value: `${numeral(platformROIAsOfYearEnd).format('0.000')}%`,
    // },
    {
      label: 'Projects (unsold & public)',
      value: numeral(
        allPublicPortfolios.reduce(
          (acc, v) => acc + v.activeNonSoldProjects?.length || 0,
          0
        )
      ).format('0,0'),
      description: 'Count of all projects that are public and not sold.',
    },
  ];

  return (
    <Grid container spacing={2} style={{ padding: '1rem' }}>
      <Grid item xs={12}>
        <Typography variant="h4" gutterBottom>
          Annual Report Numbers
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          Platform:
        </Typography>
      </Grid>
      <Grid container spacing={2} style={{ padding: '1rem' }}>
        {annualReportNumbers.map((stat) => (
          <Tooltip arrow title={stat.description}>
            <Grid item xs={12} md={4} lg={3} style={{ cursor: 'pointer' }}>
              <Grid
                container
                direction="column"
                alignItems="center"
                justifyContent="center"
              >
                {stat.link ? (
                  <Button
                    component={Link}
                    to={stat.link}
                    variant="outlined"
                    endIcon={<ArrowRight />}
                  >
                    {stat.linkDesc}
                  </Button>
                ) : (
                  <Grid item>
                    <Typography
                      style={{
                        color: theme.palette.primary.main,
                        fontWeight: 'bold',
                        textAlign: 'center',
                      }}
                      variant="h5"
                    >
                      {stat.value}
                    </Typography>
                  </Grid>
                )}
                <Grid item>
                  <Grid container alignItems="center">
                    <Typography
                      style={{
                        fontWeight: 'bold',
                        color: 'rgba(0,0,0,.6)',
                        minHeight: '40px',
                        position: 'relative',
                      }}
                      variant="body2"
                    >
                      {stat.label}{' '}
                      <Info
                        style={{
                          color: '#999',
                          fontSize: '1rem',
                          position: 'absolute',
                          right: -19,
                          top: 1,
                        }}
                      />
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Tooltip>
        ))}
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h6">IRR by Age of Account</Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="body2" style={{ fontStyle: 'italic' }}>
          For the sake of this chart, account age is calculated by using the
          date a user first invested
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Scatter
          height={400}
          data={{
            datasets: [
              {
                label: 'Account ROIs',
                data: allInvestorReturnsCached
                  .filter((investorReturn) => {
                    const { irr, showIRR } = investorReturn;
                    const lintedIRR = irr / 100;
                    if (showIRR && lintedIRR > 0.02 && lintedIRR < 0.4) {
                      return true;
                    }
                    return false;
                  })
                  .map((investorReturn) => ({
                    x: investorReturn.yearsInvested,
                    y: investorReturn.irr / 100,
                    accountName: investorReturn.user.fullName,
                    firstInvestmentDt: investorReturn.firstInvestmentDt,
                  })),
                backgroundColor: 'rgba(21, 48, 76, 0.3)',
                // borderColor: theme.palette.primary.main,
                // fill: true,
                // tension: 0.5,
              },
            ],
          }}
          options={{
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                mode: 'nearest',
                intersect: false,
                callbacks: {
                  label: (tooltipItem) => [
                    `User: ${tooltipItem.raw.accountName}`,
                    `IRR: ${numeral(tooltipItem.raw.y).format('%0,0[.]00')}`,
                    `First Investment Dt: ${moment(
                      tooltipItem.raw.firstInvestmentDt
                    ).format('MMM D, YYYY')}`,
                  ],
                },
              },
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: (value) => numeral(value).format('%0,0[.]00'),
                },
                title: {
                  display: true,
                  text: 'IRR',
                },
              },
              x: {
                title: {
                  display: true,
                  text: 'Account Age (Years since invested)',
                },
                // type: 'time',
                // time: {
                //   tooltipFormat: 'MMM YYYY',
                //   unit: 'month',
                // },
              },
            },
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5">Portfolios:</Typography>
      </Grid>
      <Grid item xs={12}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell style={{ fontWeight: 'bold' }}>Portfolio</TableCell>
              <TableCell style={{ fontWeight: 'bold' }}>Net Raised</TableCell>
              <Tooltip
                arrow
                title="Current investment cap + tax equity + debt. (investment cap may be out of sync with sponsor equity)"
              >
                <TableCell style={{ fontWeight: 'bold' }}>Total Cost</TableCell>
              </Tooltip>
              <Tooltip arrow title="Sum of Tax Equity of all public projects.">
                <TableCell style={{ fontWeight: 'bold' }}>Tax Equity</TableCell>
              </Tooltip>
              <Tooltip
                arrow
                title="Sum of all project level debt of public projects as well as portfolio level institutional investments marked as debt."
              >
                <TableCell style={{ fontWeight: 'bold' }}>Debt</TableCell>
              </Tooltip>
              <Tooltip
                arrow
                title="Sum of 'sponsor equity' value of all public projects."
              >
                <TableCell style={{ fontWeight: 'bold' }}>Equity</TableCell>
              </Tooltip>
              <Tooltip arrow title="Sum of all projects' System Sizes.">
                <TableCell style={{ fontWeight: 'bold' }}>
                  Install Capacity
                </TableCell>
              </Tooltip>
              <Tooltip
                arrow
                title="IRR calculated up until the end of the year. Excludes Energea. Excludes early exit penalties."
              >
                <TableCell style={{ fontWeight: 'bold' }}>
                  All Time Crowd Only Actual IRR
                </TableCell>
              </Tooltip>
              <Tooltip
                arrow
                title="IRR calculated only considering cash events from this year. Excludes Energea. Excludes early exit penalties."
              >
                <TableCell style={{ fontWeight: 'bold' }}>
                  Annual Crowd Only Actual IRR
                </TableCell>
              </Tooltip>
              <Tooltip
                arrow
                title="Sum of all-time generation data across all projects in a portfolio for CMS monitored projects"
              >
                <TableCell style={{ fontWeight: 'bold' }}>
                  Actual Total Historical Generation (MWh)
                </TableCell>
              </Tooltip>
              <Tooltip
                arrow
                title="CO2 Emissions avoided calculated from the sum of all-time generation data across all projects in a portfolio for CMS monitored projects as per the EPC conversion site"
              >
                <TableCell style={{ fontWeight: 'bold' }}>
                  Actual Total Historical Tons of CO2 Emissions Avoided
                </TableCell>
              </Tooltip>
              <TableCell style={{ fontWeight: 'bold' }}>
                Est. Lifetime Generation (MWh)
              </TableCell>
              <TableCell style={{ fontWeight: 'bold' }}>
                Est. Lifetime Tons of CO2 Emissions Avoided
              </TableCell>
              <TableCell style={{ fontWeight: 'bold' }}>Projects</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {allPublicPortfolios.map((portfolio) => {
              let portfolioTaxEquity = 0;
              let portfolioDebt = portfolio.totalDebt || 0;
              let portfolioSponsorEquity =
                portfolio.currentEquityDataAsOfYearEnd?.investmentCap || 0;
              portfolio.activeNonSoldProjects.forEach((project) => {
                portfolioTaxEquity += project?.taxEquity || 0;
                // portfolioDebt += project?.debt || 0;
                // portfolioSponsorEquity += project?.sponsorEquity || 0;
              });

              return (
                <TableRow>
                  <TableCell>{portfolio.subtitle}</TableCell>
                  <TableCell>
                    {numeral(
                      portfolio.currentEquityDataAsOfYearEnd.totalNetRaised
                    ).format('$0,0')}
                  </TableCell>
                  <TableCell>
                    {numeral(
                      portfolio.currentEquityDataAsOfYearEnd.investmentCap +
                        portfolioDebt +
                        portfolioTaxEquity
                    ).format('$0,0')}
                  </TableCell>
                  <TableCell>
                    {numeral(portfolioTaxEquity).format('$0,0')}
                  </TableCell>
                  <TableCell>{numeral(portfolioDebt).format('$0,0')}</TableCell>
                  <TableCell>
                    {numeral(portfolioSponsorEquity).format('$0,0')}
                  </TableCell>
                  <TableCell>{`${numeral(portfolio.systemSizeSumDC).format(
                    '0,0.00'
                  )}MWdc`}</TableCell>
                  <TableCell>{`${numeral(
                    portfolio.allTimeCrowdOnlyActualIRR
                  ).format('0,0.00')}%`}</TableCell>
                  <TableCell>{`${numeral(
                    portfolio.crowdOnlyActualIRRInYear
                  ).format('0,0.00')}%`}</TableCell>
                  <TableCell>
                    {numeral(
                      portfolio.allTimeActualEnergyProduced / 1000
                    ).format('0,0[.]000')}
                  </TableCell>
                  <TableCell>
                    {numeral(
                      (portfolio.allTimeActualEnergyProduced / 1000) *
                        constants.epcConversionMultipliers
                          .tonsCarbonEmissionsReduced
                    ).format('0,0[.]000')}
                  </TableCell>
                  <TableCell>
                    {numeral(portfolio.lifetimeEnergyProjection / 1000).format(
                      '0,0[.]000'
                    )}
                  </TableCell>
                  <TableCell>
                    {numeral(
                      (portfolio.lifetimeEnergyProjection / 1000) *
                        constants.epcConversionMultipliers
                          .tonsCarbonEmissionsReduced
                    ).format('0,0.[00]a')}
                  </TableCell>
                  <TableCell>
                    {portfolio.activeNonSoldProjects.map((project) => (
                      <Chip
                        label={`${project.name} (${numeral(
                          project.systemSizeDC
                        ).format('0,0[.]00')} MW DC)`}
                        color="primary"
                        style={{ margin: '2px' }}
                      />
                    ))}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </Grid>
    </Grid>
  );
});
