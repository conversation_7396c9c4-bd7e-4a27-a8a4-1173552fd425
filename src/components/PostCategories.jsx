import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SingleFieldList,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { CustomNumberInput, DetailField } from './CustomFields';
import { CustomReferenceField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Post Category';

export const PostCategoryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth />
            <TextInput source="description" multiline fullWidth />
          </Grid>
        </Grid>
        <ArrayField sortable={false} source="posts">
          <SingleFieldList>
            <CustomReferenceField source="title" />
          </SingleFieldList>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const PostCategoryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <NumberField source="orderNo" />
        <ArrayField source="posts" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="title" />
          </SingleFieldList>
        </ArrayField>
        <DetailField source="description" sortable={false} />
      </Datagrid>
    </List>
  );
};

export const PostCategoryCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="name"
            helperText="This can be edited at any time so no need to be perfect."
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
