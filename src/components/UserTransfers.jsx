import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useDataProvider } from 'react-admin';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import numeral from 'numeral';
import {
  CircularProgress,
  Collapse,
  Grid,
  ListItem,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material';
import {
  AccountBalance,
  AccountBalanceWallet,
  ExpandLess,
  ExpandMore,
  Warning,
} from '@mui/icons-material';
import MuiList from '@mui/material/List';
import { Image } from 'cloudinary-react';

import { constants } from '../utils/global';

export const UserTransfers = () => {
  const [userTransfers, setUserTransfers] = useState(null);
  const [filter, setFilter] = useState('all');
  const [selectedTransferId, setSelectedTransferId] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [count, setCount] = useState(0);
  const dataProvider = useDataProvider();
  const { id } = useParams();

  const handleChangePage = (event, page) => {
    setPage(page);
    fetchUserTransfers({
      pageNum: page,
      perPage: rowsPerPage,
      transferType: filter,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    setPage(0);
    setRowsPerPage(event.target.value);
    fetchUserTransfers({
      pageNum: 0,
      perPage: event.target.value,
      transferType: filter,
    });
  };

  const fetchUserTransfers = ({ pageNum, perPage, transferType }) => {
    dataProvider
      .getList('UserTransfer', {
        filter: {
          userId: parseInt(id, 10),
          transferType,
        },
        pagination: { page: pageNum, perPage },
      })
      .then(
        (resp) => {
          setUserTransfers(resp.data);
          setCount(resp.total);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  if (!userTransfers) {
    fetchUserTransfers({
      pageNum: page,
      perPage: rowsPerPage,
      transferType: filter,
    });
  }

  if (userTransfers) {
    const groupRowsByCorrelation = (rows) => {
      const groupingObj = {};
      const returnObj = [];
      if (!rows) return [];
      rows.forEach((row) => {
        if (row.correlationId) {
          if (groupingObj[String(row.correlationId)]) {
            groupingObj[String(row.correlationId)].push(row);
          } else {
            groupingObj[String(row.correlationId)] = [row];
          }
        } else {
          returnObj.push(row);
        }
      });
      Object.keys(groupingObj).forEach((correlationId) => {
        let mainTransfer;
        const relatedTransfers = [];
        const array = groupingObj[String(correlationId)];
        array.forEach((el) => {
          if (!mainTransfer && el.individualAchId) {
            mainTransfer = el;
          } else {
            relatedTransfers.push(el);
          }
        });
        if (mainTransfer) {
          if (relatedTransfers.length > 0) {
            mainTransfer.relatedTransfers = relatedTransfers;
          }
        } else {
          mainTransfer = relatedTransfers[0];
        }
        returnObj.push(mainTransfer);
      });
      return (
        returnObj.sort((a, b) => new Date(a.created) > new Date(b.created)) ||
        []
      );
    };

    const renderFundingSource = (transfer, dwollaTransfer, srcOrDest, key) => {
      const fundingSrc = dwollaTransfer[String(srcOrDest)];
      const { type } = transfer;
      if (!fundingSrc) return null;
      const { resourceType } = fundingSrc;
      let jsx = null;
      if (resourceType === 'customer') {
        jsx = (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
          >
            <AccountBalanceWallet
              style={{ marginBottom: '-6px', marginRight: '8px' }}
            />
            {fundingSrc.name}
          </Typography>
        );
      } else if (resourceType === 'funding-source') {
        jsx = (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
          >
            <AccountBalance
              style={{ marginBottom: '-6px', marginRight: '8px' }}
            />
            {fundingSrc.name}
          </Typography>
        );
      }
      if (type === 'investment' && srcOrDest === 'destination') {
        jsx = transfer.investment ? (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
            component={Link}
            to={`/investment/${transfer.investment.portfolio.id}`}
          >
            <Image
              style={{ marginRight: '8px' }}
              width={18}
              crop="scale"
              cloud_name={constants.cloud_name}
              publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
            />
            {transfer.investment.portfolio.subtitle}
          </Typography>
        ) : (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
          >
            <Image
              style={{ marginRight: '8px' }}
              width={18}
              crop="scale"
              cloud_name={constants.cloud_name}
              publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
            />
            Investment cancelled
          </Typography>
        );
      }
      if (type === 'shareTransfer' && srcOrDest === 'source') {
        jsx = transfer.shareTransfer.sellOrder.portfolio ? (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
            component={Link}
            to={`/investment/${transfer.shareTransfer.sellOrder.portfolio.id}`}
          >
            <Image
              style={{ marginRight: '8px' }}
              width={18}
              crop="scale"
              cloud_name={constants.cloud_name}
              publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
            />
            {transfer.shareTransfer.sellOrder.portfolio.subtitle}
          </Typography>
        ) : (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
          >
            <Image
              style={{ marginRight: '8px' }}
              width={18}
              crop="scale"
              cloud_name={constants.cloud_name}
              publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
            />
            Energea
          </Typography>
        );
      }
      if (type === 'dividend' && srcOrDest === 'source') {
        jsx =
          transfer.dividend &&
          transfer.dividend.monthlyPortfolioFinancialActual &&
          transfer.dividend.monthlyPortfolioFinancialActual.portfolio ? (
            <Typography
              key={`transfer-funding-src-typography-key-${transfer.id}`}
              variant="body2"
              component={Link}
              to={`/investment/${transfer.dividend.monthlyPortfolioFinancialActual.portfolio.id}`}
            >
              <Image
                style={{ marginRight: '8px' }}
                width={18}
                crop="scale"
                cloud_name={constants.cloud_name}
                publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
              />
              {
                transfer.dividend.monthlyPortfolioFinancialActual.portfolio
                  .subtitle
              }
            </Typography>
          ) : (
            <Typography
              variant="body2"
              key={`transfer-funding-src-typography-key-${transfer.id}`}
            >
              <Image
                style={{ marginRight: '8px' }}
                width={18}
                crop="scale"
                cloud_name={constants.cloud_name}
                publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
              />
              Investment cancelled
            </Typography>
          );
      }
      if (type === 'buyDirection' && srcOrDest === 'source') {
        jsx = (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
          >
            <Image
              style={{ marginRight: '8px' }}
              width={18}
              crop="scale"
              cloud_name={constants.cloud_name}
              publicId="energea/partner-logos/Entrust-Group-favicon" // eslint-disable-line camelcase
            />
            {transfer.buyDirection.subAccount.subAccountType.name} ( #
            {transfer.buyDirection.subAccount.accountId})
          </Typography>
        );
      }
      if (type === 'buyDirection' && srcOrDest === 'destination') {
        jsx = (
          <Typography
            variant="body2"
            key={`transfer-funding-src-typography-key-${transfer.id}`}
          >
            <AccountBalanceWallet
              style={{ marginBottom: '-6px', marginRight: '8px' }}
            />
            Energea IRA Balance (#
            {transfer.buyDirection.subAccount.accountId})
          </Typography>
        );
      }
      return jsx;
    };

    const renderType = (type, key) => {
      if (!type) return <span key={`type-span-${key}`}>Unknown</span>;
      if (type === 'deposit')
        return <span key={`type-span-${key}`}>Transfer to Wallet</span>;
      if (type === 'buyDirection') return <span>IRA Transfer</span>;
      return (
        <span key={`type-span-${key}`}>
          {type.charAt(0).toUpperCase() + type.slice(1)}
        </span>
      );
    };

    // NOTE: This filter value is passed to the server to filter off of the transfers table's 'type' column.
    const transferFilters = [
      { label: 'All Transfers', filter: 'all' },
      { label: 'Dividends', filter: 'dividend' },
      { label: 'Investments', filter: 'investment' },
      { label: 'Withdrawals', filter: 'withdrawal' },
      { label: 'Deposits to Wallet', filter: 'deposit' },
      { label: 'Share Transfers', filter: 'shareTransfer' },
      { label: 'IRA Transfers', filter: 'buyDirection' },
    ];

    const getTransferAmount = (transfer) => {
      let amount = 0;
      if (transfer.millenniumTrustInvestmentTransfer) {
        amount = parseFloat(transfer.millenniumTrustInvestmentTransfer.amount);
      } else if (transfer.dwollaTransfers) {
        transfer.dwollaTransfers.forEach((dwollaTransfer) => {
          amount += parseFloat(
            (dwollaTransfer &&
              dwollaTransfer.amount &&
              dwollaTransfer.amount.value) ||
              0
          );
        });
      }
      return amount;
    };

    const pendingMTInvestmentStatuses = [
      'Awaiting FCM Account Open',
      'Awaiting Funds',
      'Awaiting Pre-Custody',
      'Awaiting Resolution',
      'Reviewing',
      'Received',
    ];

    const lintStatus = (status) => {
      if (!status) return null;
      if (pendingMTInvestmentStatuses.indexOf(status) > -1) {
        return 'pending';
      }
      return status;
    };

    return (
      <Grid container>
        <Grid item container justifyContent="space-between">
          <Grid item>
            <ToggleButtonGroup
              size="medium"
              value={filter}
              exclusive
              color="primary"
            >
              {transferFilters.map((transferFilter) => (
                <ToggleButton
                  key={`transfer-toggle-button-${transferFilter.filter}`}
                  onClick={() => {
                    setFilter(transferFilter.filter);
                    setPage(0);
                    fetchUserTransfers({
                      pageNum: 0,
                      perPage: rowsPerPage,
                      transferType: transferFilter.filter,
                    });
                  }}
                  color="primary"
                  value={transferFilter.filter}
                >
                  {transferFilter.label}
                </ToggleButton>
              ))}
            </ToggleButtonGroup>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <MuiList>
            {groupRowsByCorrelation(userTransfers).map((transfer) => {
              const open = selectedTransferId === transfer.id;
              const transferAmount = getTransferAmount(transfer);
              return (
                <React.Fragment key={`transfer-fragment-${transfer.id}`}>
                  <ListItem
                    key={`transfer-list-item-${transfer.id}`}
                    button
                    onClick={() =>
                      setSelectedTransferId(open ? null : transfer.id)
                    }
                  >
                    <ListItemText
                      primary={[
                        moment(transfer.createdAt).format('M/D/Y'),
                        ' - ',
                        renderType(transfer.type, transfer.id),
                      ]}
                      secondary={
                        transferAmount
                          ? numeral(transferAmount).format('$0,0.00')
                          : '..error'
                      }
                    />
                    {open ? <ExpandLess /> : <ExpandMore />}
                  </ListItem>
                  <Collapse
                    key={`transfer-list-item-collapse-${transfer.id}`}
                    in={open}
                    timeout="auto"
                    unmountOnExit
                  >
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Source</TableCell>
                            <TableCell>Destination</TableCell>
                            <TableCell>Amount</TableCell>
                            <TableCell>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {transfer.dwollaTransfers &&
                            transfer.dwollaTransfers.map(
                              (dwollaTransfer, index) => (
                                <TableRow
                                  key={`dwolla-transfer-${transfer.id}-${dwollaTransfer.id}`}
                                >
                                  <TableCell>
                                    {renderFundingSource(
                                      transfer,
                                      dwollaTransfer,
                                      'source',
                                      index
                                    ) || <Warning style={{ color: '#ccc' }} />}
                                  </TableCell>
                                  <TableCell>
                                    {renderFundingSource(
                                      transfer,
                                      dwollaTransfer,
                                      'destination'
                                    ) || <Warning style={{ color: '#ccc' }} />}
                                  </TableCell>
                                  <TableCell>
                                    {(dwollaTransfer &&
                                      dwollaTransfer.amount &&
                                      numeral(
                                        dwollaTransfer.amount.value
                                      ).format('$0,0.00')) || (
                                      <Warning style={{ color: '#ccc' }} />
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    {dwollaTransfer.status || (
                                      <Warning style={{ color: '#ccc' }} />
                                    )}
                                  </TableCell>
                                </TableRow>
                              )
                            )}
                          {transfer.millenniumTrustInvestmentTransfer && (
                            <TableRow
                              key={`ira-investment-transfer-${transfer.id}`}
                            >
                              <TableCell>
                                {transfer &&
                                transfer.millenniumTrustInvestmentTransfer &&
                                transfer.millenniumTrustInvestmentTransfer
                                  .source ? (
                                  <Typography variant="body2">
                                    <AccountBalance
                                      style={{
                                        marginBottom: '-6px',
                                        marginRight: '8px',
                                      }}
                                    />
                                    {
                                      transfer.millenniumTrustInvestmentTransfer
                                        .source
                                    }
                                  </Typography>
                                ) : (
                                  <Warning style={{ color: '#ccc' }} />
                                )}
                              </TableCell>
                              <TableCell>
                                {transfer && transfer.investment ? (
                                  <Typography
                                    variant="body2"
                                    component={Link}
                                    to={`/investment/${transfer.investment.portfolio.id}`}
                                  >
                                    <Image
                                      alt="Energea logo"
                                      style={{ marginRight: '8px' }}
                                      width={18}
                                      crop="scale"
                                      quality="auto"
                                      cloud_name={constants.cloud_name}
                                      publicId="energea/energea-global-favicon" // eslint-disable-line camelcase
                                    />
                                    {transfer.investment.portfolio.subtitle}
                                  </Typography>
                                ) : (
                                  <Warning style={{ color: '#ccc' }} />
                                )}
                              </TableCell>
                              <TableCell>
                                {numeral(
                                  transfer.millenniumTrustInvestmentTransfer
                                    .amount
                                ).format('$0,0.00') || (
                                  <Warning style={{ color: '#ccc' }} />
                                )}
                              </TableCell>
                              <TableCell>
                                {lintStatus(
                                  transfer?.millenniumTrustInvestmentTransfer
                                    ?.status
                                ) || <Warning style={{ color: '#ccc' }} />}
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Collapse>
                </React.Fragment>
              );
            })}
          </MuiList>
          <TablePagination
            rowsPerPageOptions={[10, 25, 50]}
            rowsPerPage={rowsPerPage}
            component="div"
            count={count}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Grid>
      </Grid>
    );
  } else {
    return <CircularProgress />;
  }
};
