import React from 'react';

import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'State';

export const StateEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item md={6} xs={12}>
            <TextInput
              source="id"
              label="State Abbreviation"
              disabled
              fullWidth
            />
            <TextInput source="name" label="State Name" disabled fullWidth />
            <CustomNumberInput source="threshold" fullWidth />
            <CustomNumberInput source="divisor" fullWidth />
            <CustomNumberInput source="divisorFee" fullWidth />
            <CustomNumberInput source="divisorFeeCap" fullWidth />
            <CustomNumberInput source="processingFee" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const StateList = () => {
  const { permissions } = usePermissions();
  return (
    <List>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" label="State Abbreviation" />
        <TextField source="name" label="State Name" />
        <NumberField source="threshold" />
        <NumberField source="divisor" />
        <NumberField source="divisorFee" />
        <NumberField source="divisorFeeCap" />
        <NumberField source="processingFee" />
      </Datagrid>
    </List>
  );
};

export const StateCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="id"
            label="State Abbreviation (Ex: CT)"
            required
            fullWidth
          />
          <TextInput source="name" label="State Full Name" required fullWidth />
          <CustomNumberInput source="threshold" fullWidth />
          <CustomNumberInput source="divisor" fullWidth />
          <CustomNumberInput source="divisorFee" fullWidth />
          <CustomNumberInput source="divisorFeeCap" fullWidth />
          <CustomNumberInput source="processingFee" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
