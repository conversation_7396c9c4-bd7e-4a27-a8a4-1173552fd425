import React, { useState } from 'react';
import {
  Card,
  CardActionArea,
  CardContent,
  CircularProgress,
  Grid,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Alert } from '@mui/lab';
import { useDataProvider, useNotify } from 'react-admin';
import 'chartjs-adapter-moment';
import { withStyles } from '@mui/styles';

import CreditMgmtSalesProject from './CreditMgmtSalesProject';
import CreditMgmtSalesOverviewChart from './CreditMgmtSalesOverviewChart';
import CreditMgmtSalesOverviewChartByState from './CreditMgmtSalesOverviewChartByState';
import theme from '../theme';

const styles = () => ({});

export default withStyles(styles)(() => {
  const fullScreen = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [projects, setProjects] = useState(null);
  const [loading, setLoading] = useState(null);
  const [error, setError] = useState(null);
  const [portfolios, setPortfolios] = useState(null);
  const [selectedPortfolioId, setSelectedPortfolioId] = useState(null);

  const notify = useNotify();
  const dataProvider = useDataProvider();

  const fetchData = () => {
    dataProvider
      .getList('SalesforceProject', {
        pagination: { page: 1, perPage: 1000 },
        sort: { field: 'orderNo', order: 'ASC' },
        filter: { consortiumType: 'Distributed generation' },
      })
      .then(
        (res) => {
          const aPortfolios = [];
          res.data.forEach((project) => {
            if (
              aPortfolios.filter((p) => p.id === project.portfolio.id)
                .length === 0
            ) {
              aPortfolios.push({
                id: project.portfolio.id,
                subtitle: project.portfolio.subtitle,
                numberOfProjects: res.data.filter(
                  (p2) => p2.portfolio.id === project.portfolio.id
                ).length,
              });
            }
          });
          setPortfolios(aPortfolios);
          setSelectedPortfolioId(aPortfolios[0].id);
          setLoading(false);
          setError(null);
          setProjects(res.data);
        },
        (err) => {
          console.error(err);
          setError(err);
          setLoading(false);
          notify('Failed to fetch list of projects', {
            type: 'error',
          });
        }
      );
  };

  if (!loading && !projects && !error) {
    fetchData();
  }

  const renderConsortiumProjects = () => {
    if (error) {
      return (
        <Alert severity="error">
          There was an issue loading consortium projects.
        </Alert>
      );
    }

    if (loading || !projects) {
      return (
        <Grid container style={{ width: '100%' }} justifyContent="center">
          <CircularProgress />
        </Grid>
      );
    }

    return (
      <Grid container item xs={12} style={{ marginTop: '2rem' }}>
        <Grid
          item
          container
          spacing={4}
          // style={{ padding: '1rem 0' }}
        >
          {portfolios.map((portfolio) => {
            const selected = portfolio.id === selectedPortfolioId;
            return (
              <Grid key={`portfolio-container-${portfolio.id}`} item>
                <Card
                  elevation={selected ? 0 : 10}
                  style={{
                    height: '6rem',
                    border: selected
                      ? `solid ${theme.palette.primary.main} 3px`
                      : '',
                    backgroundColor: selected ? 'rgba(230, 230, 230, 0.3)' : '',
                    boxSizing: 'border-box',
                    borderRadius: theme.shape.borderRadius,
                  }}
                >
                  <CardActionArea
                    onClick={() => {
                      setSelectedPortfolioId(portfolio.id);
                    }}
                    aria-label={`${portfolio.subtitle} tab`}
                    style={{
                      cursor: 'pointer',
                      height: '100%',
                      padding: selected ? null : '3px',
                    }}
                  >
                    <CardContent>
                      <Grid
                        container
                        justifyContent="center"
                        alignItems="center"
                        item
                        xs={12}
                      >
                        <Grid item xs={12}>
                          <Typography
                            variant="h5"
                            style={{ fontWeight: 'normal' }}
                          >
                            {portfolio.subtitle}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography
                            variant="body2"
                            style={{ fontWeight: 'normal' }}
                          >
                            {portfolio.numberOfProjects} projects
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            );
          })}
        </Grid>
        {projects
          // .sort((a, b) => {
          //   // Calculate currentCapacity for project a
          //   const currentCapacityA =
          //     a.projects?.reduce((acc, project) => {
          //       return acc + (project.avgP50Prod || 0);
          //     }, 0) || 0;

          //   // Calculate currentCapacity for project b
          //   const currentCapacityB =
          //     b.projects?.reduce((acc, project) => {
          //       return acc + (project.avgP50Prod || 0);
          //     }, 0) || 0;

          //   // Sort in descending order (highest capacity first)
          //   return currentCapacityB - currentCapacityA;
          // })
          .filter((project) =>
            selectedPortfolioId
              ? project.portfolio.id === selectedPortfolioId
              : true
          )
          .map((project) => (
            <CreditMgmtSalesProject
              key={`project-sales-chart-${project.id}`}
              name={project.name}
              salesforceProjectId={project.id}
            />
          ))}
      </Grid>
    );
  };

  return (
    <Grid container style={{ margin: fullScreen ? null : '1rem' }}>
      <Grid item xs={12}>
        <Typography variant="h4" gutterBottom>
          Credit Management Sales Dashboard
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography
          variant="body2"
          style={{ fontStyle: 'italic' }}
          gutterBottom
        >
          The below chart shows the gross total capacity sold per month broken
          down by sales partner. These numbers are calculated using the total
          unadjusted average monthly consumption (MWh) for each consumer unit
          that has a signed terms of adhesion with a start date in a given
          month. This chart only includes the addition of new signed ToAs and
          does not subtract consumption for lost customers. The goal of this
          chart is to give an high level overview of sales partner performance
          as well as the opportunity to visualize volume and potential
          seasonality of sales. This relies on the accuracy of the start dates
          assigned to the <a href={'/BrTermsOfAdhesion'}>terms of adhesions</a>{' '}
          associated with each consumer unit. Data prior to June 2024 is
          excluded from this chart because the start dates of TOAs are less
          reliable for those that were copied over from Salesforce.
        </Typography>
      </Grid>
      {/* xs={12} results in chart forever expanding to the right */}
      <Grid item xs={11} style={{ marginBottom: '2rem' }}>
        <CreditMgmtSalesOverviewChart />
      </Grid>
      <Grid item xs={12}>
        <Typography
          variant="body2"
          style={{ fontStyle: 'italic' }}
          gutterBottom
        >
          The below chart shows the gross total capacity sold per day broken
          down by state. These numbers are calculated using the total unadjusted
          average monthly consumption (MWh) for each consumer unit that has a
          signed terms of adhesion with a start date on a given date. This chart
          only includes the addition of new signed ToAs and does not subtract
          consumption for lost customers. The goal of this chart is to give an
          high level overview of state by state performance as well as the
          opportunity to visualize volume and potential seasonality of sales.
          This relies on the accuracy of the start dates assigned to the{' '}
          <a href={'/BrTermsOfAdhesion'}>terms of adhesions</a> associated with
          each consumer unit. Data prior to June 2024 is excluded from this
          chart because the start dates of TOAs are less reliable for those that
          were copied over from Salesforce.
        </Typography>
      </Grid>
      <Grid item xs={11} style={{ marginBottom: '2rem' }}>
        <CreditMgmtSalesOverviewChartByState />
      </Grid>
      <Grid item xs={12}>
        <Typography
          variant="body2"
          style={{ fontStyle: 'italic' }}
          gutterBottom
        >
          The below charts use the average first year P50 production per project
          for the dotted line and the total average monthly consumption adjusted
          by the voltage phase for each consumer unit during the length of their
          terms of adhesion for the solid line. This shows hows full projects
          are at any given time in the past and relies on the accuracy of the
          start and end dates assigned to the{' '}
          <a href={'/BrTermsOfAdhesion'}>terms of adhesions</a> associated with
          each consumer unit.
        </Typography>
      </Grid>
      {renderConsortiumProjects()}
    </Grid>
  );
});
