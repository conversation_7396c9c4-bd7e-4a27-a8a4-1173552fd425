import React, { useState } from 'react';
import { Alert } from '@mui/lab';

import { useDataProvider, useNotify, useRedirect } from 'react-admin';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  MenuItem,
  Select,
  Step,
  StepLabel,
  Stepper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';

import {
  ArrowBack,
  ArrowForward,
  Check,
  Close,
  CloudDownload,
  Error,
  GetApp,
  Refresh,
} from '@mui/icons-material';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';
import moment from 'moment';
import theme from '../theme';

const BillingDataRow = (props) => {
  const [validationComplete, setValidationComplete] = useState(false);
  const [validationLoading, setValidationLoading] = useState(false);
  const [validationErrorMsg, setValidationErrorMsg] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();

  const {
    attrs,
    row,
    salesforceProjectId,
    billingReferenceMonth,
    disableContinueAction,
  } = props;

  attrs.forEach((attr) => {
    if (
      !validationErrorMsg &&
      attr.validate &&
      !attr.validate(row[String(attr.name)], row)
    ) {
      setValidationErrorMsg(`Invalid value for ${attr.label}`);
      disableContinueAction();
    }
  });

  const fetchData = () => {
    setValidationLoading(true);
    dataProvider
      .getOne('BrInvoiceInputValidation', {
        input: {
          installationCode: row['Número de Instalação'],
          discount: row['Desconto'],
          salesforceProjectId,
          billingReferenceMonth,
        },
      })
      .then(
        (res) => {
          setValidationLoading(false);
          if (res.data.validationSuccess) {
            setValidationErrorMsg(null);
          } else {
            setValidationErrorMsg(res.data.message);
            disableContinueAction();
          }
          setValidationComplete(true);
        },
        (err) => {
          setValidationLoading(false);
          setValidationComplete(true);
          setValidationErrorMsg('Error validating row');
          disableContinueAction();
          notify('Error validating row', { type: 'error' });
        }
      );
  };

  if (!validationErrorMsg && !validationLoading && !validationComplete) {
    fetchData();
  }
  const cellJsx = attrs.map((attr) => {
    const val = row[String(attr.name)];
    return (
      <TableCell
        key={`billing-val-cell-${attr.name}`}
        align={attr.align || 'center'}
      >
        {attr.format ? attr.format(val) : val}
      </TableCell>
    );
  });

  let validationCellJsx = null;
  if (validationLoading) {
    validationCellJsx = <CircularProgress />;
  } else if (validationErrorMsg) {
    validationCellJsx = (
      <Tooltip title={validationErrorMsg} arrow>
        <Error color="error" />
      </Tooltip>
    );
  } else {
    validationCellJsx = <Check color="success" />;
  }

  return (
    <TableRow
      key={`billingRow-${row['Número de Instalação']}`}
      style={{
        backgroundColor: validationLoading
          ? null
          : validationErrorMsg
          ? theme.palette.error.light
          : 'lightgreen',
      }}
    >
      <TableCell>{validationCellJsx}</TableCell>
      {cellJsx}
      <TableCell>
        <IconButton onClick={() => fetchData()}>
          <Refresh />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};
export const CreditMgmtBillingReportDialog = (props) => {
  const [loading, setLoading] = useState(false);
  const [reviewed, setReviewed] = useState(false);
  const [billingData, setBillingData] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [projectId, setProjectId] = useState(null);
  const [invoiceItemError, setInvoiceItemError] = useState(false);
  const [addToExistingBillingCycleFlg, setAddToExistingBillingCycleFlg] =
    useState(false);
  const [billingReferenceMonth, setBillingReferenceMonth] = useState(
    parseInt(moment().format('M'), 10)
  );
  const [billingReferenceYear, setBillingReferenceYear] = useState(
    parseInt(moment().format('YYYY'), 10)
  );
  const [generationReferenceMonth, setGenerationReferenceMonth] = useState(
    parseInt(moment().subtract(1, 'month').format('M'), 10)
  );
  const [generationReferenceYear, setGenerationReferenceYear] = useState(
    parseInt(moment().subtract(1, 'month').format('YYYY'), 10)
  );
  const [projects, setProjects] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();

  if (!loading && !projects) {
    dataProvider
      .getList('SalesforceProject', {
        pagination: { page: 1, perPage: 1000 },
        sort: { field: 'name', order: 'ASC' },
      })
      .then(
        (res) => {
          setLoading(false);
          setProjects(res.data);
        },
        (err) => {
          console.error(err);
          notify('Failed to fetch list of salesforce projects', {
            type: 'error',
          });
        }
      );
  }

  const { open } = props;

  const lintData = (data, attrs) => {
    return data
      .map((entry) => {
        const returnObj = {};
        Object.keys(entry).forEach((entryAttr) => {
          const entryAttrData = findWithAttr(attrs, 'name', entryAttr);
          if (!entryAttrData) {
            console.log('Missing attr detected', entryAttr);
          } else if (entryAttrData.dataFormat) {
            returnObj[String(entryAttr)] = entryAttrData.dataFormat(
              entry[String(entryAttr)]
            );
          } else {
            returnObj[String(entryAttr)] = entry[String(entryAttr)];
          }
        });
        return returnObj;
      })
      .filter((el) => !!el);
  };

  const submitBillingData = () => {
    setLoading(true);
    dataProvider
      .create('BrBillingCycleUpload', {
        data: {
          salesforceProjectId: projectId,
          billingMonth: moment(
            `${billingReferenceMonth}-${billingReferenceYear}-1`,
            'M-YYYY'
          ).format('YYYY-MM-DD'),
          generationMonth: moment(
            `${generationReferenceMonth}-${generationReferenceYear}-1`,
            'M-YYYY'
          ).format('YYYY-MM-DD'),
          addToExistingBillingCycleFlg,
          brCreditCompensations: billingData.map((entry) => {
            return {
              additionalUtilityCharges: entry['CIP + Outros'],
              installationCode: entry['Número de Instalação'],
              compensatedCreditsValue: entry['EEINI - Valor'],
              discountRate: entry['Desconto'],
              grossConsumption: entry['EEI - Qtd'] + entry['EEF - Qtd'],
              injectedElectricity: entry['EEI - Qtd'],
              injectedElectricityPrice: entry['EEI - Tarifa'],
              injectedElectricityTaxPrice: entry['EEINI - Tarifa'],
              injectedElectricityTaxValue: entry['EEINI - Valor'],
              offPeakUCBalance: entry['Saldo FP'],
              peakUCBalance: entry['Saldo P'],
              utilityElectricityPrice: entry['EEF - Tarifa'],
              utilityElectricitySupplied: entry['EEF - Qtd'],
              utilityElectricityValue: entry['EEF - Valor'],
              eqvEmArvores: entry['Eqv em Árvores '],
              co2Pourpado: entry['CO2 Poupado'],
              totalSemEnergea: entry['Total Sem Energea'],
              totalComEnergea: entry['Total Com Energea'],
              tipoDeCobranca: entry['Tipo de Cobrança'],
              brInvoice: {
                amountDue: entry['Total Energea com Desconto'],
                dueDt: entry['Vencimento Energea'],
                originalDueDt: entry['Vencimento Energea'],
              },
            };
          }),
        },
      })
      .then(
        (res) => {
          setLoading(false);
          setReviewed(false);
          setActiveStep(activeStep + 1);
          notify('Success', { type: 'success' });
        },
        (e) => {
          console.error(e);
          setLoading(false);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
        }
      );
  };

  const handleNextClicked = async () => {
    switch (activeStep) {
      case 0:
        setActiveStep(1);
        break;
      case 1:
        setActiveStep(activeStep + 1);
        break;
      case 2:
        if (
          window.confirm(
            `Clicking 'OK' will created invoices in Stripe for CUs listed on this page except the ones flagged for offline invoicing. This will not send emails to the customer - that is done from the Billing Cycle list view. Do you wish to continue?`
          )
        ) {
          submitBillingData();
        }
        break;
      case 3:
        handleDialogClosed();
        break;
      default:
        break;
    }
  };

  const handleBackClicked = async () => {
    switch (activeStep) {
      case 0:
        handleDialogClosed();
        break;
      case 1:
        setActiveStep(activeStep - 1);
        setProjectId(null);
        setBillingReferenceMonth(null);
        setBillingReferenceYear(null);
        setGenerationReferenceMonth(null);
        setAddToExistingBillingCycleFlg(false);
        setGenerationReferenceYear(null);
        break;
      case 2:
        setActiveStep(activeStep - 1);
        setLoading(false);
        setReviewed(false);
        setBillingData(null);
        setInvoiceItemError(false);
        break;
      case 3:
        handleDialogClosed();
        break;
      default:
        break;
    }
  };

  const getNextBtnDisabled = () => {
    switch (activeStep) {
      case 0:
        return false;
      case 1:
        return (
          !billingReferenceMonth ||
          !billingReferenceYear ||
          !generationReferenceMonth ||
          !generationReferenceYear ||
          !projectId
        );
      case 2:
        return !reviewed || loading;
      case 3:
        return false;
      default:
        console.log(`getNextBtnDisabled doesn't handle step ${activeStep}`);
        return true;
    }
  };

  const getBackBtnDisabled = () => {
    switch (activeStep) {
      case 0:
        return true;
      case 1:
      case 2:
      case 3:
        return loading;
      default:
        console.log(`getBackBtnDisabled doesn't handle step ${activeStep}`);
        return true;
    }
  };

  const renderNextBtn = () => {
    const disabled = getNextBtnDisabled();
    let nextBtnLabel = 'Continue';
    if (activeStep === steps.length - 1) {
      nextBtnLabel = 'Close';
    } else if (activeStep === 0) {
      nextBtnLabel = 'Start';
    }
    return (
      <Button
        onClick={() => handleNextClicked()}
        disabled={disabled}
        variant="contained"
        size="large"
        color="secondary"
        endIcon={<ArrowForward />}
      >
        {loading ? <CircularProgress style={{ position: 'fixed' }} /> : null}
        {nextBtnLabel}
      </Button>
    );
  };

  const renderBackBtn = () => {
    const disabled = getBackBtnDisabled();
    return (
      <Button
        onClick={() => handleBackClicked()}
        disabled={disabled}
        variant="outlined"
        size="large"
        // color="secondary"
        startIcon={<ArrowBack />}
      >
        {loading ? <CircularProgress style={{ position: 'fixed' }} /> : null}
        Back
      </Button>
    );
  };

  const renderReviewDataAlert = () => {
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!reviewed}
                onChange={() => setReviewed(!reviewed)}
                disabled={invoiceItemError}
              />
            }
            label="I have checked the records below."
          />
          {renderNextBtn()}
        </Alert>
      </>
    );
  };

  const renderIntro = () => {
    return (
      <Grid container style={{ paddingTop: '1rem' }}>
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Credit Management Consortium Billing
          </Typography>
          <Typography gutterBottom>
            In this workflow, the Credit Management billing team will upload the
            information needed in order for Energea to generate and email
            consortium members' invoices.
          </Typography>
        </Grid>
        <Grid container justifyContent="center">
          <Grid item>{renderNextBtn()}</Grid>
        </Grid>
      </Grid>
    );
  };

  const renderBillingReportUpload = () => {
    const attrs = [
      {
        name: 'Número de Instalação',
        label: 'Número de Instalação',
        align: 'center',
        dataFormat: (val) => String(val),
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Desconto',
        label: 'Desconto',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Créditos Compensáveis',
        label: 'Créditos Compensáveis',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Rateio',
        label: 'Rateio',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Modalidade',
        label: 'Modalidade',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Tipo de Cobrança',
        label: 'Tipo de Cobrança',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Origem da Tarifa',
        label: 'Origem da Tarifa',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Saldo P',
        label: 'Saldo P',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Saldo FP',
        label: 'Saldo FP',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEF - Qtd',
        label: 'EEF - Qtd',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEF - Tarifa',
        label: 'EEF - Tarifa',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEF - Valor',
        label: 'EEF - Valor',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEINI - Qtd',
        label: 'EEINI - Qtd',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEINI - Tarifa',
        label: 'EEINI - Tarifa',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEINI - Valor',
        label: 'EEINI - Valor',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEI - Qtd',
        label: 'EEI - Qtd',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEI - Tarifa',
        label: 'EEI - Tarifa',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'EEI - Valor',
        label: 'EEI - Valor',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'CIP + Outros',
        label: 'CIP + Outros',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Tarifa Energea com Desconto',
        label: 'Tarifa Energea com Desconto',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Total Energea com Desconto',
        label: 'Total Energea com Desconto',
        align: 'center',
        validate: (val, row) => {
          const eeiValue = parseFloat(row['EEI - Valor'] || 0);
          const savings = parseFloat(row['Economia'] || 0);
          const expectedTotal = eeiValue - savings;
          return (
            val !== null &&
            val !== undefined &&
            val !== '' &&
            Math.abs(parseFloat(val) - expectedTotal) <= 0.02
          );
        },
      },
      {
        name: 'Vencimento Energea',
        label: 'Vencimento Energea',
        align: 'center',
        dataFormat: (val) => moment(val).format('YYYY-MM-DD'),
        validate: (val) =>
          val !== null &&
          val !== undefined &&
          val !== '' &&
          moment(val).isValid() &&
          moment(val).isAfter(moment()),
      },
      {
        name: 'Total Sem Energea',
        label: 'Total Sem Energea',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Total Com Energea',
        label: 'Total Com Energea',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Economia',
        label: 'Economia',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'Eqv em Árvores ',
        label: 'Eqv em Árvores',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
      {
        name: 'CO2 Poupado',
        label: 'CO2 Poupado',
        align: 'center',
        validate: (val) => val !== null && val !== undefined && val !== '',
      },
    ];

    const handleBillingData = (data) => {
      const lintedData = lintData(data, attrs);
      setBillingData(lintedData);
    };

    const renderBillingReportData = () => (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell />
            {attrs.map((attr) => (
              <TableCell
                key={`billing-header-cell-${attr.name}`}
                align={attr.align || 'center'}
              >
                <b>{attr.label}</b>
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {billingData.map((row, index) => {
            return (
              <BillingDataRow
                key={`billingRow-${index}`}
                row={row}
                attrs={attrs}
                salesforceProjectId={projectId}
                billingReferenceMonth={moment(
                  `${billingReferenceYear}-${billingReferenceMonth}-1`
                ).format('YYYY-MM-DD')}
                disableContinueAction={() => setInvoiceItemError(true)}
              />
            );
          })}
        </TableBody>
      </Table>
    );

    return (
      <Grid container style={{ paddingTop: '1rem' }}>
        <Grid item xs={12}>
          <Typography gutterBottom>
            Upload the Billing.xlsx file here. This file should include all
            information needed in order to generate all of the invoices.
          </Typography>
          <Typography variant="body2">
            This file can be found in Dropbox at the following location: Energea
            Global/Market 1 - BRAZIL/Portfolio/Projects/Portfolio 2 - MG -
            Iguatama/8. Asset Management/8.4 Credit Management/Billing and
            Commissions/3. Billing
          </Typography>
        </Grid>
        <Grid xs={12} item style={{ padding: '1em' }}>
          <Button
            component="a"
            variant="contained"
            href={`/csv-templates/creditMgmtBilling.xlsx`}
            download
          >
            <GetApp />
            Click to download a sample xlsx file
          </Button>
        </Grid>
        <ExcelReader handleData={handleBillingData} />
        <Grid item xs={12}>
          {billingData ? renderBillingReportData() : null}
        </Grid>
        <Grid item xs={12} style={{ margin: '1em' }}>
          {billingData ? renderReviewDataAlert() : null}
        </Grid>
        <Grid item style={{ marginTop: '1rem' }}>
          {renderBackBtn()}
        </Grid>
      </Grid>
    );
  };

  const renderBillingCycleInputs = () => {
    const months = [
      { value: 1, label: 'January' },
      { value: 2, label: 'February' },
      { value: 3, label: 'March' },
      { value: 4, label: 'April' },
      { value: 5, label: 'May' },
      { value: 6, label: 'June' },
      { value: 7, label: 'July' },
      { value: 8, label: 'August' },
      { value: 9, label: 'September' },
      { value: 10, label: 'October' },
      { value: 11, label: 'November' },
      { value: 12, label: 'December' },
    ];
    return (
      <Grid container style={{ paddingTop: '1rem' }}>
        <Grid item xs={12}>
          <FormControl margin="normal" required>
            <Typography>Select the project:</Typography>
            <Select
              label="filter"
              value={projectId || ''}
              onChange={(event) => setProjectId(event.target.value)}
              style={{ width: '250px' }}
            >
              {projects?.map((project) => (
                <MenuItem value={project.id} key={`project-${project.id}`}>
                  {project.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <FormControl margin="normal" required>
            <Typography>Select the billing month and year:</Typography>
            <Grid container spacing={2}>
              <Grid item>
                <Select
                  label="month"
                  value={billingReferenceMonth || ''}
                  onChange={(event) =>
                    setBillingReferenceMonth(event.target.value)
                  }
                  style={{ width: '150px' }}
                >
                  {months.map((month) => (
                    <MenuItem value={month.value} key={`month-${month.value}`}>
                      {month.label}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item>
                <Select
                  label="month"
                  value={billingReferenceYear || ''}
                  onChange={(event) =>
                    setBillingReferenceYear(event.target.value)
                  }
                >
                  {[
                    moment().year() - 1,
                    moment().year(),
                    moment().year() + 1,
                  ].map((year) => (
                    <MenuItem value={year} key={`year-${year}`}>
                      {year}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
            </Grid>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <FormControl margin="normal" required>
            <Typography>Select the generation month and year:</Typography>
            <Grid container spacing={2}>
              <Grid item>
                <Select
                  label="month"
                  value={generationReferenceMonth || ''}
                  onChange={(event) =>
                    setGenerationReferenceMonth(event.target.value)
                  }
                  style={{ width: '150px' }}
                >
                  {months.map((month) => (
                    <MenuItem value={month.value} key={`month-${month.value}`}>
                      {month.label}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item>
                <Select
                  label="month"
                  value={generationReferenceYear || ''}
                  onChange={(event) =>
                    setGenerationReferenceYear(event.target.value)
                  }
                >
                  {[
                    moment().year() - 1,
                    moment().year(),
                    moment().year() + 1,
                  ].map((year) => (
                    <MenuItem value={year} key={`year-${year}`}>
                      {year}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
            </Grid>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!addToExistingBillingCycleFlg}
                onChange={() =>
                  setAddToExistingBillingCycleFlg(!addToExistingBillingCycleFlg)
                }
              />
            }
            label="Check this box if you are adding data to an existing billing
              cycle"
          />
        </Grid>
        <Grid item style={{ marginTop: '1rem' }}>
          {renderBackBtn()}
          {renderNextBtn()}
        </Grid>
      </Grid>
    );
  };

  const renderComplete = () => {
    return (
      <Grid container style={{ paddingTop: '1rem' }}>
        <Grid item>
          <Typography variant="h6" gutterBottom>
            Invoices have been sent!
          </Typography>
          <Typography gutterBottom>
            This project billing has been completed. The invoices have been
            created and finalized in Stripe and the data has been uploaded to
            the CMS.
          </Typography>
        </Grid>
        <Grid container justifyContent="center">
          <Grid item>{renderNextBtn()}</Grid>
        </Grid>
      </Grid>
    );
  };

  const steps = [
    { label: 'Introduction' },
    { label: 'Billing Cycle Inputs' },
    { label: 'Upload Billing Export' },
    { label: 'Complete' },
  ];

  const handleDialogClosed = () => {
    setActiveStep(0);
    setReviewed(false);
    setLoading(false);
    setBillingData(null);
    setProjectId(null);
    setInvoiceItemError(false);
    props.handleClose();
  };

  const renderStepper = () => {
    let content;
    switch (activeStep) {
      case 0:
        content = renderIntro();
        break;
      case 1:
        content = renderBillingCycleInputs();
        break;
      case 2:
        content = renderBillingReportUpload();
        break;
      case 3:
        content = renderComplete();
        break;
      default:
        break;
    }
    return content;
  };

  return (
    <Dialog open={open} maxWidth="xl">
      <Grid container style={{ padding: '2rem' }}>
        <Grid container justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Billing Processing</Typography>
          </Grid>
          <Grid item>
            <IconButton onClick={handleDialogClosed}>
              <Close />
            </IconButton>
          </Grid>
        </Grid>
        <Grid
          container
          item
          justifyContent="center"
          style={{ marginTop: '1rem' }}
        >
          <Stepper
            style={{ paddingTop: 0 }}
            alternativeLabel
            activeStep={activeStep}
          >
            {steps.map((step) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="body2">{step.label}</Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
          <Grid
            container
            justifyContent="center"
            alignItems="center"
            style={{ minHeight: '360px' }}
          >
            <Grid
              item
              container
              xs={12}
              lg={10}
              // spacing={5}
              justifyContent="center"
              style={{ height: '100%' }}
              // alignItems="center"
            >
              {renderStepper()}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Dialog>
  );
};
