import React from 'react';
import { Link } from 'react-router-dom';
import moment from 'moment';
import {
  ArrayField,
  BooleanInput,
  Datagrid,
  Filter,
  FunctionField,
  List,
  SingleFieldList,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  useRefresh,
  NumberField,
  BulkDeleteButton,
} from 'react-admin';

import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import MuiTextField from '@mui/material/TextField';
import {
  CheckCircle,
  EditSharp,
  Launch,
  VisibilityOff,
  Visibility,
  WatchLater,
  Cancel,
} from '@mui/icons-material';

import { DetailField, CustomReferenceField } from './CustomFields';
import theme from '../theme';
import { useState } from 'react';

const entityName = 'IRA Account Statuses';

const IRAAccountStatusFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Investor First Name or Last Name"
      source="q"
      alwaysOn
    />
    <BooleanInput label="Show hidden" source="showHidden" />
  </Filter>
);

const CustomBulkActionButtons = () => (
  <>
    <BulkDeleteButton label="Hide" icon={<VisibilityOff />} />
  </>
);

export const IRAAccountStatusList = () => {
  const [editIRANotesUser, setEditIRANotesUser] = useState(null);
  const [iraNotes, setIRANotes] = useState(null);

  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();

  const handleEditIRANotes = () => {
    dataProvider
      .update('User', {
        data: {
          id: parseInt(editIRANotesUser.id, 10),
          iraNotes,
        },
      })
      .then(
        () => {
          setEditIRANotesUser(null);
          setIRANotes(null);
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error occured update user IRA notes', { type: 'error' });
          refresh();
        }
      );
  };

  const handleHide = (id) => {
    dataProvider
      .update('MillenniumTrustAuthSession', {
        data: {
          id: parseInt(id, 10),
          iraTrackerHiddenFlg: true,
        },
      })
      .then(
        () => {
          notify('User hidden', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error hiding user', { type: 'error' });
          refresh();
        }
      );
  };

  const handleUnhide = (id) => {
    dataProvider
      .update('MillenniumTrustAuthSession', {
        data: {
          id: parseInt(id, 10),
          iraTrackerHiddenFlg: false,
        },
      })
      .then(
        () => {
          notify('Record unhidden');
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error unhiding record', { type: 'error' });
          refresh();
        }
      );
  };

  return (
    <>
      <List
        title={entityName}
        perPage={25}
        filters={<IRAAccountStatusFilter />}
        bulkActionButtons={<CustomBulkActionButtons />}
      >
        <Datagrid>
          <FunctionField
            label="User"
            render={(record) => (
              <Typography
                variant="body2"
                component={Link}
                to={`/User/${record.user.id}`}
              >
                {`${record.user?.firstName} ${record.user?.lastName}${
                  record.user?.dwollaCustomer?.status !== 'verified'
                    ? ` (${record.user?.dwollaCustomer?.status?.toUpperCase()})`
                    : ''
                }`}
              </Typography>
            )}
          />
          <FunctionField
            label="Step 1: Contact Verification"
            render={(record) => {
              const verificationSessionStatus =
                record.verificationSession?.status;
              const statusSince = record.verificationSession
                ?.verificationStatusUpdatedDt
                ? `(${moment(
                    record.verificationSession?.verificationStatusUpdatedDt
                  ).format('MMM D, YYYY')})`
                : '';
              if (verificationSessionStatus?.status === 'Verified') {
                return (
                  <Tooltip title="Verified">
                    <Grid container>
                      <Grid item>
                        <CheckCircle
                          style={{ color: theme.palette.green.dark }}
                        />
                      </Grid>
                      <Grid item style={{ paddingLeft: '0.5rem' }}>
                        <Typography variant="body2">
                          Verified {statusSince}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Tooltip>
                );
              }

              if (verificationSessionStatus?.status === 'Declined') {
                return (
                  <Tooltip title="Declined">
                    <Grid container>
                      <Grid item>
                        <Cancel style={{ color: theme.palette.error.dark }} />
                      </Grid>
                      <Grid item style={{ paddingLeft: '0.5rem' }}>
                        <Typography variant="body2">
                          Declined {statusSince}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Tooltip>
                );
              }

              if (verificationSessionStatus?.status === 'Rejected') {
                return (
                  <Tooltip title="Rejected">
                    <Grid container>
                      <Grid item>
                        <Cancel style={{ color: theme.palette.error.dark }} />
                      </Grid>
                      <Grid item style={{ paddingLeft: '0.5rem' }}>
                        <Typography variant="body2">
                          Rejected {statusSince}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Tooltip>
                );
              }

              const requiresDocs =
                verificationSessionStatus?.status ===
                  'Awaiting Documentation' ||
                verificationSessionStatus?.requiredDocuments?.filter(
                  (d) => !d.isUploaded
                )?.length > 0;
              let tooltipText = record.verificationSession?.comments
                ? `Comments: ${record.verificationSession?.comments}...\n`
                : '';
              if (requiresDocs) {
                verificationSessionStatus?.requiredDocuments?.forEach((d) => {
                  tooltipText += `${d.docType}: ${d.status}.\n`;
                });
              }

              return (
                <Tooltip title={tooltipText}>
                  <Grid container>
                    <Grid item>
                      <WatchLater
                        style={{ color: theme.palette.warning.dark }}
                      />
                    </Grid>
                    <Grid item style={{ paddingLeft: '0.5rem' }}>
                      <Typography variant="body2">
                        {requiresDocs
                          ? 'Awaiting Documentation'
                          : verificationSessionStatus?.status}{' '}
                        {statusSince}
                      </Typography>
                      <Typography
                        variant="caption"
                        style={{ fontStyle: 'italic' }}
                      >
                        {tooltipText}
                      </Typography>
                    </Grid>
                  </Grid>
                </Tooltip>
              );
            }}
          />
          <FunctionField
            label="Step 2: Account Opening"
            render={(record) => {
              if (record.accountOpeningComplete) {
                return (
                  <Tooltip title="Account open">
                    <Grid
                      container
                      alignItems="center"
                      onClick={(event) => {
                        event.stopPropagation();
                      }}
                    >
                      <Grid item>
                        <CheckCircle
                          style={{ color: theme.palette.green.dark }}
                        />
                      </Grid>
                      <Grid item style={{ paddingLeft: '0.5rem' }}>
                        <Typography
                          variant="body2"
                          component={Link}
                          to={`/SubAccount/${record.subAccount.id}`}
                        >
                          {record.subAccount.name}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Tooltip>
                );
              }
              if (record.accountOpeningStatus === 'Pending') {
                return (
                  <Tooltip title="Account 'Pending'. Millennium typically opens within 24 hours.">
                    <Grid
                      container
                      alignItems="center"
                      onClick={(event) => {
                        event.stopPropagation();
                      }}
                    >
                      <Grid item>
                        <WatchLater
                          style={{ color: theme.palette.warning.dark }}
                        />
                      </Grid>
                      <Grid item style={{ paddingLeft: '0.5rem' }}>
                        <Typography
                          variant="body2"
                          component={Link}
                          to={`/SubAccount/${record.subAccount.id}`}
                        >
                          Pending: {record.subAccount.name}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Tooltip>
                );
              }
              if (record.accountOpeningStatus === 'Closed') {
                return (
                  <Tooltip title="Account 'Closed'. This is either due to inactivity or client/Energea request.">
                    <Grid
                      container
                      alignItems="center"
                      onClick={(event) => {
                        event.stopPropagation();
                      }}
                    >
                      <Grid item>
                        <Cancel style={{ color: theme.palette.error.main }} />
                      </Grid>
                      <Grid item style={{ paddingLeft: '0.5rem' }}>
                        <Typography
                          variant="body2"
                          component={Link}
                          to={`/SubAccount/${record.subAccount.id}`}
                        >
                          Closed: {record.subAccount.name}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Tooltip>
                );
              }
              return null;
              // return (
              //   <Grid container>
              //     <Grid item>
              //       <Cancel style={{ color: '#aaa' }} />
              //     </Grid>
              //     <Grid item style={{ paddingLeft: '0.5rem' }}>
              //       <Typography variant="body2">
              //         {record.accountOpeningStatus}
              //       </Typography>
              //     </Grid>
              //   </Grid>
              // )
            }}
          />
          <FunctionField
            label="Step 3: Account Funding"
            render={(record) => {
              if (!record?.fundingTransfers?.length) {
                return null;
              }
              return (
                <ArrayField source="fundingTransfers" sortable={false}>
                  <SingleFieldList>
                    <CustomReferenceField
                      color={(record) =>
                        record.transferStatus === 'Complete' ||
                        (record.transferStatus === 'Offline' &&
                          record.toMillenniumTransferCompletedDt)
                          ? 'primary'
                          : 'default'
                      }
                      source="label"
                    />
                  </SingleFieldList>
                </ArrayField>
              );
            }}
          />
          <FunctionField
            label="Step 4: Investment Transfers from MT"
            render={(record) => {
              if (!record?.investments?.length) {
                return null;
              }
              return (
                <ArrayField source="investments" sortable={false}>
                  <SingleFieldList>
                    <CustomReferenceField
                      color={(record) =>
                        record?.millenniumTrustInvestmentLabel?.includes(
                          'Complete'
                        )
                          ? 'primary'
                          : 'default'
                      }
                      source="millenniumTrustInvestmentLabel"
                    />
                  </SingleFieldList>
                </ArrayField>
              );
            }}
          />
          <NumberField
            source="subAccount.mtcEstimatedInvestableBalance"
            label="Est. Investable Balance"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <DetailField source="nextSteps" />
          <FunctionField
            label="Notes"
            render={(record) => (
              <Grid container alignItems="center">
                <Grid item>
                  <IconButton
                    onClick={(event) => {
                      setEditIRANotesUser({
                        id: record.user.id,
                        fullName: `${record.user.firstName} ${record.user.lastName}`,
                      });
                      setIRANotes(record.user.iraNotes);
                      event.stopPropagation();
                    }}
                    size="large"
                  >
                    <EditSharp />
                  </IconButton>
                </Grid>
                <Grid item style={{ maxWidth: '250px' }}>
                  <TextField source="user.iraNotes" />
                </Grid>
              </Grid>
            )}
          />
          <FunctionField
            label="HubSpot Link"
            render={(record) => (
              <a
                onClick={(event) => {
                  event.stopPropagation();
                }}
                href={record.user.hubSpotContactUrl}
                target="_blank"
              >
                <Launch />
              </a>
            )}
          />
          <FunctionField
            label="Hide/Unhide"
            render={(record) => (
              <IconButton
                onClick={(event) => {
                  event.stopPropagation();
                  if (record.isHidden) {
                    handleUnhide(record.id.split('-')[0]);
                  } else {
                    handleHide(record.id.split('-')[0]);
                  }
                }}
                size="large"
              >
                {record.isHidden ? (
                  <Tooltip title="Unhide">
                    <Visibility color="primary" />
                  </Tooltip>
                ) : (
                  <Tooltip title="Hide">
                    <VisibilityOff color="primary" />
                  </Tooltip>
                )}
              </IconButton>
            )}
          />
        </Datagrid>
      </List>
      {editIRANotesUser ? (
        <Dialog
          open={editIRANotesUser}
          onClose={() => {
            setEditIRANotesUser(null);
            setIRANotes(null);
          }}
          fullWidth
        >
          <DialogTitle>IRA Notes for {editIRANotesUser.fullName}</DialogTitle>
          <DialogContent>
            <MuiTextField
              label="User IRA Notes"
              value={iraNotes || ''}
              onChange={(event) => {
                setIRANotes(event.target.value);
              }}
              fullWidth
              multiline
            />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setEditIRANotesUser(null);
                setIRANotes(null);
              }}
              color="primary"
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditIRANotes}
              color="primary"
              variant="contained"
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      ) : null}
    </>
  );
};
