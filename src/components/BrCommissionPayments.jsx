import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FormDataConsumer,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Commission Payment';

export const BrCommissionPaymentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="brSalesPerson.id"
              reference="BrSalesPerson"
              perPage={10_000}
              required
              sort={{ field: 'id', order: 'ASC' }}
            >
              <AutocompleteInput
                required
                optionText="name"
                label="Sales Partner"
                fullWidth
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData }) => {
                if (!formData.brSalesPerson?.id) {
                  return null;
                }
                return (
                  <ReferenceInput
                    source="salesPersonBrContact.id"
                    reference="BrContact"
                    perPage={10_000}
                    sort={{ field: 'id', order: 'ASC' }}
                    filter={{
                      brSalesPerson: { id: formData.brSalesPerson.id },
                    }}
                  >
                    <AutocompleteInput
                      optionText="fullName"
                      label="Sales Person / Vendedor"
                      fullWidth
                      allowEmpty
                    />
                  </ReferenceInput>
                );
              }}
            </FormDataConsumer>
            <ReferenceInput
              source="salesforceProject.id"
              reference="SalesforceProject"
              perPage={10_000}
              required
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                required
                optionText="name"
                label="Project"
                fullWidth
              />
            </ReferenceInput>
            <CustomNumberInput source="amount" fullWidth required />
            <DateInput source="paidDt" fullWidth required />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="salesforceProject.id"
      reference="SalesforceProject"
      perPage={10_000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Sales Partner"
      source="brSalesPerson.id"
      reference="BrSalesPerson"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <AutocompleteInput
        allowEmpty={true}
        optionText="name"
        label="Sales Partner"
        fullWidth
      />
    </ReferenceInput>
    <ReferenceInput
      source="salesPersonBrContact.id"
      reference="BrContact"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
      filter={{ isSalesPerson: true }}
    >
      <AutocompleteInput optionText="fullName" label="Sales Person" fullWidth />
    </ReferenceInput>
  </Filter>
);

export const BrCommissionPaymentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} filters={<CustomFilter />}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrSalesPerson"
          linkSource="brSalesPerson.id"
          labelSource="brSalesPerson.name"
          label="Sales Partner"
        />
        <LinkField
          reference="BrContact"
          linkSource="salesPersonBrContact.id"
          labelSource="salesPersonBrContact.fullName"
          label="Sales Person"
        />
        <LinkField
          reference="SalesforceProject"
          linkSource="salesforceProject.id"
          labelSource="salesforceProject.name"
          label="Project"
        />
        <NumberField source="amount" />
        <DateField source="paidDt" />
      </Datagrid>
    </List>
  );
};

export const BrCommissionPaymentCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="brSalesPerson.id"
            reference="BrSalesPerson"
            perPage={10_000}
            required
            sort={{ field: 'id', order: 'ASC' }}
          >
            <AutocompleteInput
              required
              optionText="name"
              label="Sales Partner"
              fullWidth
            />
          </ReferenceInput>
          <FormDataConsumer>
            {({ formData }) => {
              if (!formData.brSalesPerson?.id) {
                return null;
              }
              return (
                <ReferenceInput
                  source="salesPersonBrContact.id"
                  reference="BrContact"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                  filter={{
                    brSalesPerson: { id: formData.brSalesPerson.id },
                  }}
                >
                  <AutocompleteInput
                    optionText="fullName"
                    label="Sales Person / Vendedor"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
              );
            }}
          </FormDataConsumer>
          <ReferenceInput
            source="salesforceProject.id"
            reference="SalesforceProject"
            perPage={10_000}
            required
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput required optionText="name" label="Project" fullWidth />
          </ReferenceInput>
          <CustomNumberInput source="amount" fullWidth required />
          <DateInput source="paidDt" fullWidth required />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
