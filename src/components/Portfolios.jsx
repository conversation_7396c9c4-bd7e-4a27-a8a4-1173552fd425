import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import moment from 'moment';
import momentTimezone from 'moment-timezone';
import {
  ArrayField,
  ArrayInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  DateTimeInput,
  Edit,
  FormDataConsumer,
  FormTab,
  FunctionField,
  List,
  NumberField,
  NumberInput,
  ReferenceField,
  ReferenceInput,
  SelectInput,
  Show,
  SimpleFormIterator,
  SimpleShowLayout,
  SingleFieldList,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  useRefresh,
  usePermissions,
  useRecordContext,
  useResourceDefinition,
} from 'react-admin';

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  ListItem,
  ListItemAvatar,
  ListItemText,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import MuiTextField from '@mui/material/TextField';
import { withStyles } from '@mui/styles';

import MatList from '@mui/material/List';
import TextFieldCustom from '@mui/material/TextField';
import { Alert } from '@mui/lab';
import {
  ArrowForward,
  CancelOutlined,
  CheckCircleOutlineOutlined,
  Close,
  CloudDownload,
  Delete,
  ExpandLess,
  ExpandMore,
  InfoRounded,
  PictureAsPdf,
  Subject,
} from '@mui/icons-material';

import numeral from 'numeral';

import 'chart.js/auto';
import { Chart } from 'chart.js';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Doughnut, Line } from 'react-chartjs-2';

import theme from '../theme';
import { getEditable } from '../utils/applyRoleAuth';

import {
  CustomNumberInput,
  CustomReferenceField,
  DetailField,
  LinkField,
} from './CustomFields';

import Config from '../config/config';

import { openUploadWidget } from '../utils/CloudinaryService';
import PortfolioAnalytics from './PortfolioAnalytics';
import PortfolioHubSpotProduct from './PortfolioHubSpotProduct';
import PortfolioMedia from './PortfolioMedia';
import { PortfolioInvestments } from './PortfolioInvestments';
import { interpolateColors } from '../utils/global';
import EPCConversionGrid from './EPCConversionGrid';
import { RichTextInput } from 'ra-input-rich-text';
import ProjectList from './PortfolioProjectList';

Chart.register(zoomPlugin);

const entityName = 'Portfolio';

const Title = () => {
  const record = useRecordContext();
  return (
    <span>
      {entityName}
      {record ? ` #${record.id} - ${record.name}` : ''}
    </span>
  );
};

export const PortfolioEdit = () => {
  const dataProvider = useDataProvider();
  const [createBankAccountInfo, setCreateBankAccountInfo] = useState({
    showAddBankAccount: false,
    name: null,
    accountNumber: null,
    routingNumber: null,
    bankAccountType: null,
  });

  const [
    isAcceptingInvestmentsDropdownOpen,
    setIsAcceptingInvestmentsDropdownOpen,
  ] = useState(false);
  const [isPublicDropdownOpen, setIsPublicDropdownOpen] = useState(false);
  const [portfolioTaxStatusData, setPortfolioTaxStatusData] = useState(null);
  const [taxInfoPeriod, setTaxInfoPeriod] = useState('current');

  const notify = useNotify();
  const refresh = useRefresh();

  const { id } = useParams();

  const addBankAccountChange = (event) => {
    const { name, value } = event.currentTarget;
    const changedState = { ...createBankAccountInfo };
    changedState[String(name)] = value;
    setCreateBankAccountInfo(changedState);
  };

  const addBankAccount = () => {
    const { name, accountNumber, routingNumber, bankAccountType } =
      createBankAccountInfo;
    dataProvider
      .create('PortfolioFundingSource', {
        input: {
          portfolioId: parseInt(id, 10),
          name,
          accountNumber,
          routingNumber,
          bankAccountType,
        },
      })
      .then(
        (record) => {
          notify('Successfully added bank account');
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error adding bank account', { type: 'error' });
        }
      );
  };

  const createSharePriceFiling = () => {
    dataProvider
      .create('SharePriceFiling', {
        data: {
          portfolioId: parseInt(id, 10),
        },
      })
      .then(
        (record) => {
          notify('Successfully created share price filing');
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error creating share price filing', { type: 'error' });
        }
      );
  };

  const deleteSharePriceFiling = (id) => {
    dataProvider
      .delete('SharePriceFiling', {
        id: parseInt(id, 10),
      })
      .then((record) => {
        notify('Successfully deleted share price filing');
        refresh();
      });
  };

  const onDocumentUploaded = (aDocuments) => {
    const documents = aDocuments.map((document) => ({
      title: document.original_filename,
      public_id: document.public_id,
      portfolioId: parseInt(id, 10),
    }));
    dataProvider
      .update('Portfolio', {
        data: { id: parseInt(id, 10), documents },
      })
      .then(
        () => {
          notify('Document successfully added. Rename document if needed.', {
            type: 'success',
          });
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error adding document', { type: 'error' });
        }
      );
  };

  const uploadDocumentWithCloudinary = () => {
    const uploadOptions = {
      tags: ['portfolios'],
      cloudName: Config.cloud_name,
      showPoweredBy: false,
      multiple: false,
      sources: ['local', 'url', 'dropbox', 'camera'],
      dropboxAppKey: '1y1wsyzgzfb5f2g',
      uploadPreset: Config.portfolio_document_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onDocumentUploaded([resp.info]);
      }
    });
  };

  const handleDownloadInvestmentSubAgrmtTemplate = () => {
    dataProvider
      .getOne('PortfolioInvestmentSubscriptionAgreementTemplate', {
        id: parseInt(id, 10),
      })
      .then(
        (res) => {
          window.location.assign(res.data);
        },
        (e) => {
          console.error('ERROR', e);
          notify(
            'Error downloading investment subscription agreement template',
            'error'
          );
        }
      );
  };

  const handleDownloadAutoInvestSubAgrmtTemplate = () => {
    dataProvider
      .getOne('PortfolioAutoInvestSubscriptionAgreementTemplate', {
        id: parseInt(id, 10),
      })
      .then(
        (res) => {
          window.location.assign(res.data);
        },
        (e) => {
          console.error('ERROR', e);
          notify(
            'Error downloading auto invest subscription agreement template',
            'error'
          );
        }
      );
  };

  const handleDownloadAutoReinvestSubAgrmtTemplate = () => {
    dataProvider
      .getOne('PortfolioAutoReinvestmentAgreementTemplate', {
        id: parseInt(id, 10),
      })
      .then(
        (res) => {
          window.location.assign(res.data);
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error downloading auto reinvest agreement template', {
            type: 'error',
          });
        }
      );
  };

  const handleRemoveDocument = (id) => {
    return () => {
      dataProvider
        .delete('PortfolioDocument', {
          // portfolioId: parseInt(id, 10),
          id,
        })
        .then(
          () => {
            refresh();
            notify('Document successfully removed', { type: 'success' });
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error removing document', { type: 'error' });
          }
        );
    };
  };

  const {
    showAddBankAccount,
    name,
    routingNumber,
    bankAccountType,
    accountNumber,
  } = createBankAccountInfo;
  // const Aside = () => (
  //   <div style={{ width: 200, margin: '1em' }}>
  //     <Typography gutterBottom variant="h6">
  //       {entityName} notes
  //     </Typography>
  //     {/* <Typography variant="body2"></Typography> */}
  //   </div>
  // );

  const lengthRange = (min, max) => {
    return (value) => {
      if (value && value.length <= max && value.length >= min) {
        return undefined;
      }
      if (value && value.length > max) {
        return `Input must not exceed ${max} characters (${value.length})`;
      }
      if (value && value.length < min) {
        return `Input must be at least ${min} characters (${value.length})`;
      }
    };
  };

  const isPublicRequirements = [
    {
      label: 'Name',
      field: 'name',
      isComplete: (val) => !!val,
    },
    {
      label: 'Subtitle',
      field: 'subtitle',
      isComplete: (val) => !!val,
    },
    {
      label: 'Description',
      field: 'description',
      isComplete: (val) => !!val,
    },
    {
      label: 'Country',
      field: 'country',
      isComplete: (val) => !!val,
    },
    {
      label: 'Regulation',
      field: 'regulation',
      isComplete: (val) => !!val,
    },
    {
      label: 'At least 1 Project',
      field: 'projects',
      isComplete: (val) => val && val.length > 0,
    },
    {
      label: 'HubSpot Wait List',
      field: 'hubSpotWaitListId',
      isComplete: (val) => !!val,
    },
    {
      label: 'Desktop Banner Image',
      field: 'bannerImage',
      isComplete: (val) => !!val,
    },
    {
      label: 'Tile Image',
      field: 'primaryImage',
      isComplete: (val) => !!val,
    },
    {
      label: 'Mobile Banner Image',
      field: 'mobileImage',
      isComplete: (val) => !!val,
    },
  ];

  const isAcceptingInvestmentsRequirements = [
    ...isPublicRequirements,
    {
      label: 'Banner Video',
      field: 'hasPrimaryVideo',
      isComplete: (val) => !!val,
    },
    {
      label: 'HubSpot Product',
      field: 'hubSpotProductId',
      isComplete: (val) => !!val,
    },
    {
      label: 'Dwolla Funding Source',
      field: 'dwollaFundingSourceId',
      isComplete: (val) => !!val,
    },
    {
      label: 'Projected Return Minimum (%)',
      field: 'projectedCOCYieldMin',
      isComplete: (val) => !!val,
    },
    {
      label: 'Projected Return Maximum (%)',
      field: 'projectedCOCYieldMax',
      isComplete: (val) => !!val,
    },
    {
      label: 'Offering Circular Date',
      field: 'offeringCircularDt',
      isComplete: (val) => !!val,
    },
    {
      label: 'LLC Agreement Date',
      field: 'llcAgreementDt',
      isComplete: (val) => !!val,
    },
  ];

  const validateRequiredFields = (record, requirements) => {
    const reqFieldStatus = {};
    requirements.forEach((requirement) => {
      reqFieldStatus[String(requirement.label)] = requirement.isComplete(
        record[requirement.field]
      );
    });
    return reqFieldStatus;
  };

  // No longer need complex transform since portfolioShareClasses is read-only
  // The backend will handle the data structure properly

  return (
    <Edit title={<Title />} undoable={false} redirect="edit">
      <TabbedForm redirect={false}>
        <FormTab label="Status">
          <FunctionField
            label="Is Public"
            fullWidth
            render={(record) => {
              const requiredFieldStatus = validateRequiredFields(
                record,
                isPublicRequirements
              );
              const requiredFields = Object.keys(requiredFieldStatus);
              const trueCount = Object.values(requiredFieldStatus).filter(
                (val) => val
              ).length;
              return (
                <>
                  <Grid container style={{ width: '100%' }}>
                    <Grid item xs={12} md={7}>
                      <BooleanInput
                        fullWidth
                        disabled={
                          !record.isPublic && trueCount < requiredFields.length
                        }
                        helperText="Determines whether or not the portfolio is made visible in the crowdfunding app."
                        source="isPublic"
                      />
                    </Grid>
                    <Grid item xs={10} md={7}>
                      <LinearProgress
                        style={{
                          height: '.5rem',
                          borderRadius: '.5rem',
                          marginTop: '1.25rem',
                        }}
                        color="secondary"
                        variant="determinate"
                        value={
                          (trueCount * 100) /
                          Object.keys(requiredFieldStatus).length
                        }
                      />
                    </Grid>
                    <Grid item>
                      {isPublicDropdownOpen ? (
                        <IconButton size="large">
                          <ExpandLess
                            onClick={() => setIsPublicDropdownOpen(false)}
                          />
                        </IconButton>
                      ) : (
                        <IconButton size="large">
                          <ExpandMore
                            onClick={() => setIsPublicDropdownOpen(true)}
                          />
                        </IconButton>
                      )}
                    </Grid>
                  </Grid>
                  {isPublicDropdownOpen ? (
                    <MatList>
                      {Object.keys(requiredFieldStatus).map((label) => {
                        return (
                          <ListItem
                            key={`isPublic-dd-${label}`}
                            style={{ paddingTop: 0, paddingBottom: 0 }}
                          >
                            <ListItemAvatar
                              style={{ minWidth: 30, marginBottom: '-5px' }}
                            >
                              {requiredFieldStatus[String(label)] ? (
                                <CheckCircleOutlineOutlined
                                  style={{ color: theme.palette.green.main }}
                                />
                              ) : (
                                <CancelOutlined color="error" />
                              )}
                            </ListItemAvatar>
                            <ListItemText>
                              <Typography variant="body2">{label}</Typography>
                            </ListItemText>
                          </ListItem>
                        );
                      })}
                    </MatList>
                  ) : null}
                </>
              );
            }}
          />
          <FunctionField
            label="Is Accepting Investments"
            fullWidth
            render={(record) => {
              const requiredFieldStatus = validateRequiredFields(
                record,
                isAcceptingInvestmentsRequirements
              );
              const requiredFields = Object.keys(requiredFieldStatus);
              const trueCount = Object.values(requiredFieldStatus).filter(
                (val) => val
              ).length;
              return (
                <>
                  <Grid container style={{ width: '100%' }}>
                    <Grid item xs={12} md={7}>
                      <BooleanInput
                        fullWidth
                        disabled={
                          !record.isAcceptingInvestments &&
                          trueCount < requiredFields.length
                        }
                        helperText={`Determines if users can invest in this portfolio. If turned off, invest button will be disabled. **This will not override the portfolio being fully funded, or the coming soon flag being turned on.`}
                        source="isAcceptingInvestments"
                      />
                    </Grid>
                    <Grid item xs={10} md={7}>
                      <LinearProgress
                        style={{
                          height: '.5rem',
                          borderRadius: '.5rem',
                          marginTop: '1.25rem',
                        }}
                        color="secondary"
                        variant="determinate"
                        value={
                          (trueCount * 100) /
                          Object.keys(requiredFieldStatus).length
                        }
                      />
                    </Grid>
                    <Grid item>
                      {isAcceptingInvestmentsDropdownOpen ? (
                        <IconButton size="large">
                          <ExpandLess
                            onClick={() =>
                              setIsAcceptingInvestmentsDropdownOpen(false)
                            }
                          />
                        </IconButton>
                      ) : (
                        <IconButton size="large">
                          <ExpandMore
                            onClick={() =>
                              setIsAcceptingInvestmentsDropdownOpen(true)
                            }
                          />
                        </IconButton>
                      )}
                    </Grid>
                  </Grid>
                  {isAcceptingInvestmentsDropdownOpen ? (
                    <MatList>
                      {Object.keys(requiredFieldStatus).map((label) => {
                        return (
                          <ListItem
                            key={`isAccepting-dd-${label}`}
                            style={{ paddingTop: 0, paddingBottom: 0 }}
                          >
                            <ListItemAvatar
                              style={{ minWidth: 30, marginBottom: '-5px' }}
                            >
                              {requiredFieldStatus[String(label)] ? (
                                <CheckCircleOutlineOutlined
                                  style={{ color: theme.palette.green.main }}
                                />
                              ) : (
                                <CancelOutlined color="error" />
                              )}
                            </ListItemAvatar>
                            <ListItemText>
                              <Typography variant="body2">{label}</Typography>
                            </ListItemText>
                          </ListItem>
                        );
                      })}
                    </MatList>
                  ) : null}
                </>
              );
            }}
          />
          <Grid style={{ width: '100%' }} container spacing={4}>
            <Grid item md={6} xs={12}>
              <CustomNumberInput
                label="Order"
                source="orderNo"
                step={1}
                helperText="The order these show up on the marketplace"
              />
              <BooleanInput
                label="Is featured"
                helperText="Determines if the portfolio is included on homepage."
                source="featuredFlg"
              />
              <BooleanInput
                label="Is Coming Soon"
                helperText="Determines whether or not the portfolio is included in the 'coming soon' section of the investments page"
                source="comingSoonFlg"
              />
              <BooleanInput
                label="Frontend Only Closed"
                helperText="Determines whether the investment is closed to new investments ON THE FRONTEND ONLY. This does not affect previously scheduled investments or dividend reinvestments."
                source="closeFrontendFlg"
              />
              <BooleanInput
                label="Is Closed"
                helperText="Determines whether or not the portfolio has exited or been abandoned permanently"
                source="isClosed"
              />
              <CustomNumberInput
                label="Closing IRR"
                helperText="Only if closed status above is checked."
                source="closedIRR"
              />
              <BooleanInput label="Is Consortium" source="consortiumFlg" />
              <BooleanInput label="Is Institutional" source="isInstitutional" />
              <BooleanInput
                label="Asset Management Flag"
                helperText="Determines whether or not the portfolio gets included in the monitoring dashboard."
                source="assetManagementFlg"
              />

              <BooleanInput
                label="Is Selling Unissued Shares First"
                source="sellNaturalSharesFirstFlg"
                helperText="Determines whether or not unissued are sold before selling from open sell orders."
              />
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (formData.comingSoonFlg) {
                    return (
                      <DateTimeInput
                        label="Projected Coming Soon Go Live Date"
                        source="startAcceptingInvestmentsDt"
                        helperText="This field is only relevant for coming soon portfolios. Leave this blank if we don't want to advertise this go live date."
                      />
                    );
                  }
                }}
              </FormDataConsumer>
              <BooleanInput
                helperText="Determines whether or not to hide the projections calculator on the portfolio detail page."
                source="hideProjectionCalculatorFlg"
              />
              <BooleanInput
                label="Show Event Image Feed"
                helperText="Determines whether or not to show the image gallery."
                source="showEventImageFeedFlg"
              />
              <Divider
                style={{
                  width: '100%',
                  marginTop: '1em',
                  marginBottom: '1em',
                }}
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput
                // margin="none"
                helperText="Main title on banner of portfolio detail page."
                source="name"
                fullWidth
              />
              <TextInput
                // margin="none"
                helperText="To be displayed just under name on the portfolio tile"
                source="subtitle"
                fullWidth
              />
              <TextInput
                // margin="none"
                multiline
                source="summary"
                validate={[lengthRange(50, 130)]}
                fullWidth
              />
              <TextInput
                // margin="none"
                multiline
                source="description"
                helperText="The 'Why We Like It' text that goes above the dividends calculator"
                validate={[lengthRange(200, 550)]}
                fullWidth
              />
              <RichTextInput
                // margin="none"
                multiline
                source="investmentDescription"
                helperText="The 'The Investment' text that goes above the dividends calculator"
                validate={[lengthRange(50, 5000)]}
                fullWidth
              />
              <CustomNumberInput
                label="Projected Return Minimum (%)"
                // margin="none"
                source="projectedCOCYieldMin"
                fullWidth
              />
              <CustomNumberInput
                label="Projected Return Maximum (%)"
                // margin="none"
                source="projectedCOCYieldMax"
                fullWidth
              />
              <CustomNumberInput
                label="Target Raise Amount"
                // margin="none"
                source="targetRaiseAmount"
                fullWidth
                helperText="Shows up on the portfolio stat sheet. If left blank, the stat sheet will default to the portfolio model's investment cap."
              />
              <ReferenceInput
                // margin="none"
                source="portfolioStatus.id"
                reference="PortfolioStatus"
              >
                <SelectInput
                  label="PortfolioStatus"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                // margin="none"
                source="regulation.id"
                reference="Regulation"
              >
                <SelectInput label="Regulation" fullWidth optionText="name" />
              </ReferenceInput>
              <ReferenceInput
                // margin="none"
                source="market.id"
                reference="Market"
              >
                <SelectInput label="Market" fullWidth optionText="title" />
              </ReferenceInput>
              <CustomNumberInput
                source="mapZoom"
                helperText="Number 1-10 that overrides the default map zoom. (1 is zoomed all the way out)"
                fullWidth
              />
              <ReferenceInput
                source="country.id"
                reference="Country"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
              >
                <SelectInput
                  label="Country"
                  fullWidth
                  optionText="name"
                  helperText="Determines the country flag to be displayed on the portfolio tile"
                />
              </ReferenceInput>
              <TextInput
                // margin="none"
                source="cik"
                validate={[lengthRange(5, 10)]}
                fullWidth
              />
              <TextInput
                // margin="none"
                helperText="Should be formatted as it you want it displayed on SEC filings. IE: XX-XXXXXXX"
                source="ein"
                validate={[lengthRange(5, 10)]}
                fullWidth
              />
              <DateInput
                // margin="none"
                helperText="Date on the offering circular. Used on subscription agreements."
                source="offeringCircularDt"
                fullWidth
                parse={(value) => {
                  // Parse the UTC date, ensure no timezone shift
                  const localDate = momentTimezone
                    .utc(value)
                    .format('YYYY-MM-DD');
                  return localDate;
                }}
                format={(value) =>
                  value ? momentTimezone.utc(value).format('YYYY-MM-DD') : ''
                }
              />
              <DateInput
                // margin="none"
                helperText="Date on the LLC agreement document. Used on subscription agreements."
                source="llcAgreementDt"
                fullWidth
              />
              <TextInput
                // margin="none"
                helperText="The ID for this investment in Millennium Trust's system. NOTE: Setting this property will enable investments for this portfolio from Millennium Trust sub accounts. Removing it will disable future investments from being created."
                source="millenniumTrustAssetId"
                label="Millennium Trust Asset ID (Ex: '06fb6620-25e2-45b0-8ec4-d79b9e8282dc')"
                fullWidth
              />
              <TextInput
                // margin="none"
                multiline
                source="internalNotes"
                fullWidth
              />
              <Typography>Portfolio Risks</Typography>
              <ArrayField source="portfolioRisks">
                <SingleFieldList>
                  <CustomReferenceField source="title" />
                </SingleFieldList>
              </ArrayField>
              <Typography>Projects</Typography>
              <ArrayField source="projects">
                <SingleFieldList>
                  <CustomReferenceField
                    color={(resource) =>
                      resource.isPublic ? 'primary' : 'default'
                    }
                    source="name"
                  />
                </SingleFieldList>
              </ArrayField>
              <FunctionField
                label="Agreement Templates"
                render={(record) => {
                  return (
                    <Grid container direction="column" spacing={1}>
                      <Grid item>
                        <Button
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={handleDownloadInvestmentSubAgrmtTemplate}
                        >
                          Investment Subscription Agreement Template
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={handleDownloadAutoInvestSubAgrmtTemplate}
                        >
                          Auto Invest Subscription Agreement Template
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={handleDownloadAutoReinvestSubAgrmtTemplate}
                        >
                          Dividend Reinvest Agreement Template
                        </Button>
                      </Grid>
                    </Grid>
                  );
                }}
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Media">
          <PortfolioMedia />
        </FormTab>
        <FormTab label="HubSpot Product">
          <TextInput
            label="HubSpot Wait List ID"
            margin="none"
            source="hubSpotWaitListId"
            fullWidth
          />
          <PortfolioHubSpotProduct />
        </FormTab>
        {/* NOTE: disabled until we refresh these tiles to match production */}
        {/* <FormTab label="Preview">
          <FunctionField
            align="center"
            label="Desktop Preview"
            render={(record) => {
              const fullyFunded = !(
                record.currentEquityData &&
                record.currentEquityData.investmentCap >
                  record.currentEquityData
                    .totalNetRaisedMinusUnsoldShareValue &&
                record.isAcceptingInvestments
              );
              const comingSoon = record.comingSoonFlg;

              return (
                <PortfolioTileSmall
                  fullyFunded={fullyFunded}
                  portfolio={record}
                />
              );
            }}
          />
          <FunctionField
            align="center"
            label="Mobile Preview"
            render={(record) => {
              const fullyFunded = !(
                record.currentEquityData &&
                record.currentEquityData.investmentCap >
                  record.currentEquityData
                    .totalNetRaisedMinusUnsoldShareValue &&
                record.isAcceptingInvestments
              );
              const comingSoon = record.comingSoonFlg;

              let status = 'open';
              if (comingSoon) {
                status = 'comingSoon';
              } else if (fullyFunded) {
                status = 'fullyFunded';
              }

              return (
                <MobilePortfolioTile
                  preferImage
                  status={status}
                  data={record}
                />
              );
            }}
          />
        </FormTab> */}
        <FormTab label="Documents">
          <div className="actions">
            <Button
              style={{ marginBottom: '1rem' }}
              variant="outlined"
              onClick={uploadDocumentWithCloudinary}
            >
              Add document
            </Button>
          </div>
          <ArrayInput source="documents" style={{ width: '50%' }}>
            <SimpleFormIterator
              disableRemove
              disableAdd
              TransitionProps={{ enter: false, exit: false }}
            >
              <CustomNumberInput
                label="Order #"
                helperText="Files will be ordered by this number"
                source="orderNo"
                step={1}
                fullWidth
                style={{ marginTop: '1rem' }}
              />
              <TextInput
                label="Document Title"
                helperText="This is the filename the user will see. Please note that our system will automatically prepend the portfolio name to the document, so you don't need to include that here. (ie: 'Offering Circular (Form 1-A), 'Notice of Qualification', etc.)"
                source="title"
                sx={{
                  marginTop: '1rem',
                }}
                fullWidth
              />
              <ReferenceInput
                margin="none"
                source="portfolioDocumentType.id"
                reference="PortfolioDocumentType"
              >
                <SelectInput
                  label="SEC Document Type"
                  fullWidth
                  allowEmpty
                  optionText="title"
                />
              </ReferenceInput>
              <BooleanInput
                label="Show on investment checkout page?"
                helperText="If set to 'true', this document will be shown to the user at the time of investment creation. Currently, we are only showing the IC-Memo for each portfolio to save space."
                source="requiredFlg"
                fullWidth
              />
              <FunctionField
                align="center"
                style={{ width: '100%' }}
                render={(record, path) => {
                  const document = record?.documents?.[path?.split('.')[1]];
                  return (
                    <Grid
                      container
                      justifyContent="space-between"
                      spacing={3}
                      style={{ padding: '1rem 0' }}
                    >
                      <Grid item>
                        <Button
                          color="primary"
                          variant="contained"
                          onClick={() => {
                            // eslint-disable-next-line security/detect-non-literal-fs-filename
                            window.open(document.url, '_blank');
                          }}
                        >
                          <PictureAsPdf /> - View file
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          style={{ float: 'right' }}
                          disabled={!document?.id}
                          onClick={handleRemoveDocument(document?.id)}
                        >
                          <Delete />
                        </Button>
                      </Grid>
                    </Grid>
                  );
                }}
              />
              <FunctionField
                render={(record) => {
                  return (
                    <Typography variant="caption" paragraph>
                      <i>
                        Created At: {moment(record.createdAt).format('lll')}
                      </i>
                    </Typography>
                  );
                }}
              />
              {/* <DateField label="Created At" source="createdAt" /> */}
            </SimpleFormIterator>
          </ArrayInput>
        </FormTab>
        <FormTab label="Bank Accounts">
          <Grid style={{ width: '100%' }} container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info">
                For now Gray will manage these details. Please don&apos;t make
                any edits. Thanks!
              </Alert>
            </Grid>
            <Grid style={{ marginTop: '1.5em' }} item md={6} xs={12}>
              <TextInput
                label="Dwolla Funding Source Id"
                helperText="Currently this id needs to be manually entered once the bank account has been created."
                source="dwollaFundingSourceId"
                fullWidth
              />
              <Grid hidden={!!showAddBankAccount} item xs={12}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() =>
                    setCreateBankAccountInfo({ showAddBankAccount: true })
                  }
                >
                  Create Bank Account
                </Button>
              </Grid>
              <Grid item xs={12} hidden={!showAddBankAccount}>
                <TextFieldCustom
                  fullWidth
                  margin="dense"
                  onChange={addBankAccountChange}
                  name="name"
                  label="Name"
                  variant="filled"
                  value={name}
                />
                <TextFieldCustom
                  fullWidth
                  margin="dense"
                  onChange={addBankAccountChange}
                  name="accountNumber"
                  label="Account #"
                  variant="filled"
                  value={accountNumber}
                />
                <TextFieldCustom
                  fullWidth
                  onChange={addBankAccountChange}
                  margin="dense"
                  name="bankAccountType"
                  label="Account Type ('checking' or 'savings')"
                  variant="filled"
                  value={bankAccountType}
                />
                <TextFieldCustom
                  fullWidth
                  margin="dense"
                  onChange={addBankAccountChange}
                  name="routingNumber"
                  label="Routing #"
                  variant="filled"
                  value={routingNumber}
                />
                <Button
                  onClick={() =>
                    setCreateBankAccountInfo({ showAddBankAccount: false })
                  }
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  disabled={
                    !name ||
                    !bankAccountType ||
                    !routingNumber ||
                    !accountNumber
                  }
                  onClick={addBankAccount}
                >
                  Add
                </Button>
              </Grid>
            </Grid>
            {/* TODO: this was slowing up the page load...reimplement as separate query */}
            {/* <Grid item md={6} xs={12}>
              <Typography variant="h6">Funding Source Details</Typography>
              <TextInput
                margin="none"
                disabled
                label="Id"
                source="dwollaFundingSource.id"
                fullWidth
              />
              <TextInput
                margin="none"
                disabled
                label="Name"
                source="dwollaFundingSource.name"
                fullWidth
              />
              <TextInput
                disabled
                margin="none"
                label="Bank Name"
                source="dwollaFundingSource.bankName"
              />
              <TextInput
                disabled
                margin="none"
                label="Type"
                source="dwollaFundingSource.bankAccountType"
                fullWidth
              />
              <TextInput
                disabled
                margin="none"
                label="Status"
                source="dwollaFundingSource.status"
                fullWidth
              />
            </Grid> */}
          </Grid>
        </FormTab>
        <FormTab label="Sell Orders">
          <BooleanInput
            label="Is Selling Unissued Shares First"
            source="sellNaturalSharesFirstFlg"
            fullWidth
          />
          <NumberInput
            source="maxSellOrderValue"
            options={{ style: 'currency', currency: 'USD' }}
            helperText="The max amount you can request to withdraw at any given time as per the offering. Leave blank for no limit."
          />
          {/* <CustomNumberInput
            label="Order"
            source="maxSellOrderValue"
            // step={1}
            helperText="The order these show up on the marketplace"
          /> */}
          <Typography variant="caption">Unissued Shares Remaining:</Typography>
          <NumberField source="currentEquityData.naturalShares" />
          <Grid container style={{ width: '100%' }} spacing={5}>
            <Grid item xs={12} md={6}>
              <FormDataConsumer>
                {({ formData }) => {
                  return (
                    <>
                      <Typography variant="h5" gutterBottom>
                        Queue ({formData?.sellOrderQueue?.length || 0})
                      </Typography>
                      {!formData.sellOrderQueue ||
                      formData.sellOrderQueue.length === 0 ? (
                        <Alert severity="info">
                          No active sell orders currently in the Queue
                        </Alert>
                      ) : (
                        <MatList>
                          {formData.sellOrderQueue?.map((record) => {
                            const label = record.subAccount
                              ? `Sub Account: ${record.user.fullName} (${record.subAccount.name})`
                              : `User: ${record.user.fullName}`;
                            return (
                              <ListItem
                                classes={{
                                  container: 'energea-full-width',
                                }}
                                style={{ width: '100%' }}
                                dense
                                key={`sell-order-queue-${record.id}`}
                                component={Link}
                                to={`/sellOrder/${record.id}`}
                              >
                                <ListItemAvatar>
                                  <Doughnut
                                    width={50}
                                    data={{
                                      datasets: [
                                        {
                                          data: [
                                            record.soldShares || 0,
                                            record.soldShares - record.shares ||
                                              0,
                                          ],
                                          backgroundColor: ['green', 'grey'],
                                        },
                                      ],
                                    }}
                                    options={{
                                      animation: {
                                        duration: 0,
                                      },
                                      plugins: {
                                        tooltip: { enabled: false },
                                        legend: { display: false },
                                      },
                                    }}
                                  />
                                </ListItemAvatar>
                                <ListItemText
                                  style={{
                                    color: 'rgba(0,0,0,.8)',
                                    marginLeft: '1rem',
                                  }}
                                  primary={<b>{label}</b>}
                                  secondary={
                                    <>
                                      <a href={`/sellOrder/${record.id}`}>
                                        {`Sell Order: ${record.id} ${
                                          record.priority
                                            ? ` - Priority: ${record.priority}`
                                            : ''
                                        }`}
                                      </a>
                                      <br />
                                      {`Shares Requested to Sell: ${numeral(
                                        record.shares
                                      ).format('0,0.[00]')}`}
                                      <br />
                                      {`Shares Sold: ${numeral(
                                        record.soldShares
                                      ).format('0,0.[00]')}`}
                                      <br />
                                      {`Days in Queue: ${numeral(
                                        record.daysInQueue || 0
                                      ).format('0,0.[0]')}`}
                                    </>
                                  }
                                />{' '}
                              </ListItem>
                            );
                          })}
                        </MatList>
                      )}
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
            {/* <Grid item xs={12} md={6}>
              <FormDataConsumer>
                {({ formData }) => {
                  return (
                    <>
                      <Typography variant="h5" gutterBottom>
                        Closed Orders ({formData?.closedSellOrders?.length || 0}
                        )
                      </Typography>
                      {!formData.closedSellOrders ||
                      formData.closedSellOrders.length === 0 ? (
                        <Alert severity="info">No closed sell orders</Alert>
                      ) : (
                        <MatList>
                          {formData.closedSellOrders?.map((record) => {
                            const label = record.subAccount
                              ? `Sub Account: ${record.user.fullName} (${record.subAccount.name})`
                              : `User: ${record.user.fullName}`;
                            return (
                              <ListItem
                                classes={{
                                  container: 'energea-full-width',
                                }}
                                style={{ width: '100%' }}
                                dense
                                component={Link}
                                key={`closed-sell-order-queue-${record.id}`}
                                to={`/sellOrder/${record.id}`}
                              >
                                <ListItemAvatar>
                                  <Doughnut
                                    width={50}
                                    data={{
                                      datasets: [
                                        {
                                          data: [
                                            record.soldShares || 0,
                                            record.soldShares - record.shares ||
                                              0,
                                          ],
                                          backgroundColor: ['green', 'grey'],
                                        },
                                      ],
                                    }}
                                    options={{
                                      animation: {
                                        duration: 0,
                                      },
                                      plugins: {
                                        tooltip: { enabled: false },
                                        legend: { display: false },
                                      },
                                    }}
                                  />
                                </ListItemAvatar>
                                <ListItemText
                                  style={{
                                    color: 'rgba(0,0,0,.8)',
                                    marginLeft: '1rem',
                                  }}
                                  primary={<b>{label}</b>}
                                  secondary={
                                    <>
                                      <a href={`/sellOrder/${record.id}`}>
                                        {`Sell Order: ${record.id}`}
                                      </a>
                                      <br />
                                      {`Shares Requested to Sell: ${numeral(
                                        record.shares
                                      ).format('0,0.[00]')}`}
                                      <br />
                                      {`Shares Sold: ${numeral(
                                        record.soldShares
                                      ).format('0,0.[00]')}`}
                                      <br />
                                      {`Days took to Sell: ${numeral(
                                        record.daysInQueue || 0
                                      ).format('0,0.[0]')}`}
                                    </>
                                  }
                                />{' '}
                              </ListItem>
                            );
                          })}
                        </MatList>
                      )}
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid> */}
          </Grid>
        </FormTab>
        <FormTab label="investments">
          <PortfolioInvestments />
        </FormTab>
        {/* NOTE: Removing due to weight of query in this component -GR 7/5/2023 */}
        {/* <FormTab label="Investors" path="investors">
          <PortfolioInvestors />
        </FormTab> */}
        <FormTab label="Share Classes">
          <Typography variant="h6" gutterBottom>
            Portfolio Share Classes
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            View the share classes associated with this portfolio. Use the
            dedicated Portfolio Share Classes management pages to add, edit, or
            remove share classes.
          </Typography>

          <Grid container spacing={2} style={{ marginBottom: '1rem' }}>
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                component={Link}
                to="/portfolioShareClass/create"
                startIcon={<ArrowForward />}
              >
                Add New Portfolio Share Class
              </Button>
            </Grid>
            <Grid item>
              <Button
                variant="outlined"
                component={Link}
                to="/portfolioShareClass"
              >
                Manage All Portfolio Share Classes
              </Button>
            </Grid>
          </Grid>

          <ArrayField source="portfolioShareClasses">
            <Datagrid bulkActionButtons={false} rowClick="edit">
              <TextField source="id" />
              <ReferenceField
                source="shareClassId"
                reference="ShareClass"
                link="show"
              >
                <TextField source="name" />
              </ReferenceField>
              <FunctionField
                label="Discount %"
                render={(record) => {
                  if (record.sharePriceDiscount == null) return '';
                  return `${record.sharePriceDiscount}%`;
                }}
              />
              <BooleanField source="isClosed" />
              <FunctionField
                label="Actions"
                render={(record) => (
                  <Button
                    size="small"
                    component={Link}
                    to={`/portfolioShareClass/${record.id}`}
                  >
                    Edit
                  </Button>
                )}
              />
            </Datagrid>
          </ArrayField>
        </FormTab>
        <FormTab label="Share Price">
          <BooleanInput
            fullWidth
            helperText="Set to true to automatically run share price updates"
            source="autoUpdateSharePriceFlg"
          />
          <BooleanInput
            fullWidth
            helperText="Set to true to exclude Energea investments from the share price calculation method."
            source="excludeEnergeaFromSharePriceFlg"
          />
        </FormTab>
        <FormTab label="Env. Impact">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Projected</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} will create{' '}
                      <b>
                        {numeral(
                          formData.lifetimeEnergyProjection / 1000
                        ).format('0,0')}{' '}
                        MWh
                      </b>{' '}
                      in its lifetime. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.lifetimeEnergyProjection / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Actual</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} has created{' '}
                      <b>
                        {numeral(
                          formData.allTimeActualEnergyProduced / 1000
                        ).format('0,0.0')}{' '}
                        MWh
                      </b>{' '}
                      so far. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.allTimeActualEnergyProduced / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Analytics">
          <PortfolioAnalytics dataProvider={dataProvider} />
        </FormTab>
        <FormTab label="SEC Compliance">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom>
                <b>SEC Credentials</b>
              </Typography>
              <TextInput
                fullWidth
                source="secFileNo"
                label="SEC File No."
                helperText="The filer number for the portfolio's most recent Offering Circular"
              />
              <DateInput
                // margin="none"
                helperText="Date on the offering circular. Used on subscription agreements."
                source="offeringCircularDt"
                fullWidth
                parse={(value) => {
                  // Parse the UTC date, ensure no timezone shift
                  const localDate = momentTimezone
                    .utc(value)
                    .format('YYYY-MM-DD');
                  return localDate;
                }}
                format={(value) =>
                  value ? momentTimezone.utc(value).format('YYYY-MM-DD') : ''
                }
              />
              <TextInput
                fullWidth
                source="ein"
                label="EIN"
                helperText="Should be formatted as it you want it displayed on SEC filings. IE: XX-XXXXXXX"
              />
              <TextInput fullWidth source="cik" label="CIK" />
              <TextInput fullWidth source="ccc" label="CCC" />
              <TextInput fullWidth source="secPassword" label="Password" />
              <CustomNumberInput
                fullWidth
                step={1}
                source="secLatestSupplementNo"
                label="Latest Supplement No."
                helperText="This is the LATEST supplement number, not the new supplement number. Every time a 253(g) is filed it increments the supplement number for the offering."
              />
              <Grid
                container
                justifyContent="space-between"
                alignItems="center"
                style={{ marginTop: '1rem' }}
              >
                <Grid item>
                  <Typography variant="h5" gutterBottom>
                    <b>SEC Share Price Filings</b>
                  </Typography>
                </Grid>
                <Grid item>
                  <Button
                    onClick={createSharePriceFiling}
                    variant="contained"
                    color="primary"
                  >
                    Create Share Price Filing
                  </Button>
                </Grid>
              </Grid>
              <Grid
                item
                component={Alert}
                severity="info"
                style={{ margin: '1rem 0' }}
              >
                <Typography>
                  Steps to file a share price change. Files will represent the
                  current share price and must be created during the day in EST.
                  For example, if you're in another timezone and it's 1am the
                  next day in EST, the files will generate using tomorrow's date
                </Typography>
                <ol>
                  <li>
                    Review information above (current supplement, filer number,
                    CIK, CCC, Password)
                  </li>
                  <li>Click "CREATE SHARE PRICE FILING" above</li>
                  <li>
                    Download forms 1-U and 253(g) from the newly created row.
                  </li>
                  <li>
                    Go to{' '}
                    <a
                      href="https://www.edgarfiling.sec.gov/Welcome/EDGARLogin.htm"
                      target="_blank"
                    >
                      Edgar Filing Site
                    </a>
                  </li>
                  <li>Login using the portfolio's credentials above</li>
                  <li>Click "Regulation A" on left menu</li>
                  <li>
                    Fill in Submission Contact : (Kathy Koser : **********)
                  </li>
                  <li>
                    Fill in Filer Information from above and no Co-Registrant
                  </li>
                  <li>
                    Go to Documents Tab and click "Add Document" to add the
                    downloaded file.
                  </li>
                  <li>
                    Choose the file 'type', check the box and click 'Doc
                    Validation'
                  </li>
                  <li>
                    If there are 0 errors, then view the file to confirm that it
                    looks right and click "submit".
                  </li>
                  <li>
                    After submitting, delete downloaded doc from your download
                    folder so it doesn't mess up the file name of the next
                    download.
                  </li>
                </ol>
                <Typography>
                  If you have any issues reach <NAME_EMAIL>
                </Typography>
              </Grid>
              <ArrayField fullWidth source="sharePriceFilings">
                <Datagrid
                  bulkActionButtons={false}
                  isRowSelectable={() => false}
                  rowClick={false}
                >
                  <TextField source="id" />
                  <DateField source="date" />
                  <FunctionField
                    label="1U"
                    render={(record) => {
                      const match = record.portfolio.name?.match(/\d+/);
                      const portNumber = match ? parseInt(match[0], 10) : null;
                      if (record.downloadUrl1U) {
                        return (
                          <Button
                            variant="contained"
                            startIcon={<CloudDownload />}
                            style={{ textTransform: 'none' }}
                            onClick={() => {
                              fetch(record.downloadUrl1U)
                                .then((response) => response.blob())
                                .then((blob) => {
                                  const url = window.URL.createObjectURL(
                                    new Blob([blob])
                                  );
                                  const link = document.createElement('a');
                                  link.href = url;
                                  link.setAttribute(
                                    'download',
                                    `${
                                      record.portfolio.id === 1
                                        ? 'proj'
                                        : 'port'
                                    }${portNumber}_1u.htm`
                                  ); // Replace with your desired file name and extension
                                  document.body.appendChild(link);
                                  link.click();
                                  link.parentNode.removeChild(link);
                                });
                            }}
                          >
                            Download 1U
                          </Button>
                        );
                      }
                      return null;
                    }}
                  />
                  <FunctionField
                    label="253G"
                    render={(record) => {
                      const match = record.portfolio.name?.match(/\d+/);
                      const portNumber = match ? parseInt(match[0], 10) : null;
                      if (record.downloadUrl253G) {
                        return (
                          <Button
                            variant="contained"
                            startIcon={<CloudDownload />}
                            style={{ textTransform: 'none' }}
                            onClick={() => {
                              fetch(record.downloadUrl253G)
                                .then((response) => response.blob())
                                .then((blob) => {
                                  const url = window.URL.createObjectURL(
                                    new Blob([blob])
                                  );
                                  const link = document.createElement('a');
                                  link.href = url;
                                  link.setAttribute(
                                    'download',
                                    `${
                                      record.portfolio.id === 1
                                        ? 'proj'
                                        : 'port'
                                    }${portNumber}_253g2.htm`
                                  ); // Replace with your desired file name and extension
                                  document.body.appendChild(link);
                                  link.click();
                                  link.parentNode.removeChild(link);
                                });
                            }}
                          >
                            Download 253g(2)
                          </Button>
                        );
                      }
                      return null;
                    }}
                  />
                  <FunctionField
                    label=""
                    render={(record) => {
                      if (!record.hasBeenFiled) {
                        return (
                          <IconButton
                            onClick={() => {
                              deleteSharePriceFiling(record.id);
                            }}
                          >
                            <Delete />
                          </IconButton>
                        );
                      }
                      return null;
                    }}
                  />
                </Datagrid>
              </ArrayField>
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
  // }
};

const SharePriceChangeForm = (props) => {
  const { portfolio, setSharePriceChangeDialogOpen } = props;
  const dataProvider = useDataProvider();
  const notify = useNotify();
  return (
    <Alert>
      <Typography>
        Steps to file a share price change. If you have any questions, or
        receive any errors, contact <EMAIL>
      </Typography>
      <ol>
        <li>
          Review information above (current supplement, file number, CIK, CCC,
          Password)
        </li>
        <li>Download forms 1-U and 253(g) from below.</li>
        <li>
          Go to{' '}
          <a
            href="https://www.edgarfiling.sec.gov/Welcome/EDGARLogin.htm"
            target="_blank"
          >
            Edgar Filing Site
          </a>
        </li>
        <li>Login using the portfolio's credentials above</li>
        <li>Click "Regulation A" on left menu</li>
        <li>Select Form Type (The order of the 1U / 253G does not matter)</li>
        Contact Info = Kathy Koser : **********
      </ol>
    </Alert>
  );
};

export const PortfolioShow = () => (
  <Show title={<Title />}>
    <SimpleShowLayout>
      <TextField source="name" />
      <TextField source="subtitle" />
      <TextField source="summary" />
      <TextField multiline source="description" />
      <BooleanField source="isAcceptingInvestments" />
      <NumberField source="orderNo" />
      <LinkField
        label="Regulation"
        linkSource="regulation.id"
        labelSource="regulation.name"
        reference="Regulation"
      />
      <ArrayField source="projects">
        <SingleFieldList>
          <CustomReferenceField source="name" />
        </SingleFieldList>
      </ArrayField>
    </SimpleShowLayout>
  </Show>
);

const styles = () => ({});

export const PortfolioList = withStyles(styles, { withTheme: true })(() => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const { permissions } = usePermissions();

  const [
    portfolioProjectedPayoutDialogOpen,
    setPortfolioProjectedPayoutDialogOpen,
  ] = useState(false);
  const [projectDialogOpen, setProjectDialogOpen] = useState(null);
  const [selectedProjectPortfolio, setSelectedProjectPortfolio] =
    useState(null);
  const [projectionVersions, setProjectionVersions] = useState(null);
  const [currentEquityData, setCurrentEquityData] = useState(null);
  const [shareBreakdownDialogOpen, setShareBreakdownDialogOpen] =
    useState(false);
  const [shareBreakdownAsOfDt, setShareBreakdownAsOfDt] = useState(
    moment().format('yyyy-MM-DD')
  );
  const [projectionSets, setProjectionSets] = useState([]);
  const [selectedPortfolio, setSelectedPortfolio] = useState(null);
  const [portfolioProjectData, setPortfolioProjectData] = useState(null);
  const [sharePriceChangeDialogOpen, setSharePriceChangeDialogOpen] =
    useState(false);

  const getPortfolioProjectListData = (portfolioId) => {
    dataProvider
      .getOne('PortfolioProjectListData', {
        id: portfolioId,
      })
      .then(
        (res) => {
          const lintedPortfolioProjectListData = res.data;
          setPortfolioProjectData(lintedPortfolioProjectListData);
        },
        (e) => console.log(e)
      );
  };
  const getCurrentEquityData = (portfolioId, asOfDt) => {
    setShareBreakdownDialogOpen(true);
    dataProvider
      .getOne('PortfolioWithCurrentEquityData', {
        id: portfolioId,
        asOfDt,
      })
      .then((res) => {
        const lintedCurrentEquityData = res.data.currentEquityData;
        lintedCurrentEquityData.operationalEquity = res.data.operationalEquity;
        setCurrentEquityData(lintedCurrentEquityData);
      });
  };

  const getProjectionVersions = (record) => {
    setPortfolioProjectedPayoutDialogOpen(true);
    dataProvider
      .getList('UniqueMonthlyPortfolioFinancialProjectionCreatedAtDates', {
        filter: { portfolioId: record.id },
        sort: {},
        pagination: {},
      })
      .then((res) => {
        setProjectionVersions(res.data);
        if (res.data?.length) {
          fetchVersionProjections(res.data[0], record.id);
        }
      });
  };

  const fetchVersionProjections = (version, portfolioId) => {
    dataProvider
      .getList('MonthlyPortfolioFinancialActualProjections', {
        filter: {
          createdAtReference: { id: version.id, date: version.date },
          portfolio: { id: portfolioId },
        },
        pagination: { page: 1, perPage: 10000 },
        sort: { field: 'effectiveDt', order: 'ASC' },
      })
      .then(
        (res) => {
          const newVersion = {
            version,
            projections: res.data,
          };
          const updatedProjectionSets = projectionSets;
          updatedProjectionSets.push(newVersion);
          setProjectionSets([...updatedProjectionSets]);
        },
        (err) => {
          console.error(err);
          notify('Error getting recent projection', { type: 'error' });
        }
      );
  };

  const projectionSetColors = interpolateColors(
    projectionSets.length,
    null,
    null,
    1
  );

  const projectionsWithNotes = [];
  projectionSets.forEach((projectionSet) => {
    projectionSet.projections.forEach((projection) => {
      if (projection.notes) {
        projectionsWithNotes.push({
          version: projectionSet.version,
          projection,
        });
      }
    });
  });
  const getPortfolioProjectListDataDialogContent = () => {
    const portfolio = portfolioProjectData;
    return <ProjectList parent={this} projects={portfolio.activeProjects} />;
    // return 'HELLO WORLD';
  };

  const getCurrentEquityDataDialogContent = () => {
    const shareBreakdownAsOfDtText =
      moment(shareBreakdownAsOfDt).format('MM/DD/YYYY');
    const data = [
      {
        label: 'Remaining Shares for Sale',
        value: numeral(currentEquityData.sharesForSale).format('0,0.[000]'),
        definition: `The number of outstanding shares that are for sale by either Energea or the Crowd as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Owned Shares',
        value: numeral(currentEquityData.ownedShares).format('0,0.[000]'),
        definition: `The number of shares owned by investors (including Energea) as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Crowd Owned Shares',
        value: numeral(currentEquityData.crowdOwnedShares).format('0,0.[000]'),
        definition: `The number of shares owned by investors (excluding Energea) as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Energea Owned Shares',
        value: numeral(currentEquityData.energeaOwnedShares).format(
          '0,0.[000]'
        ),
        definition: `The number of shares owned by Energea Global as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Sold Shares',
        value: numeral(currentEquityData.soldShares).format('0,0.[000]'),
        definition: `The number of shares that have been sold (by either Energea or the crowd) as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Remaining Unissued Shares',
        value: numeral(currentEquityData.naturalShares).format('0,0.[000]'),
        definition: `The number of shares still available to be issued before running out of cap space as of ${shareBreakdownAsOfDtText}. Please note this is based off of the latest modeled cap space, not the total shares "authorized" to be sold by the SEC.`,
      },
      {
        label: 'Max Shares',
        value: numeral(currentEquityData.maxShares).format('0,0.[000]'),
        definition: `The total number of shares that can be issued in this portfolio at the given share price while staying under the Investment Cap as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Share Price',
        value: numeral(currentEquityData.sharePrice).format('$0,0.000000'),
        definition: `The price of a single share as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'NAV',
        value: numeral(currentEquityData.nav).format('$0,0.00'),
        definition: `The Net Asset Value of the portfolio ([owned shares] * [share price]) as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Net Raised',
        value: numeral(currentEquityData.totalNetRaised).format('$0,0.00'),
        definition: `The total amount of money raised by the portfolio net of resold shares as of ${shareBreakdownAsOfDtText}. In other words, the amount paid for newly issued shares.`,
      },
      {
        label: 'Net Raised Minus Unsold',
        value: numeral(
          currentEquityData.totalNetRaisedMinusUnsoldShareValue
        ).format('$0,0.00'),
        definition: `The total amount of money raised by the portfolio net of resold shares and unsold shares as of ${shareBreakdownAsOfDtText}. In other words, the amount paid for newly issued shares minus the current value of unsold shares assuming the given share price.`,
      },
      {
        label: 'Cap Space',
        value: numeral(
          currentEquityData.investmentCap -
            currentEquityData.totalNetRaisedMinusUnsoldShareValue
        ).format('$0,0.00'),
        definition: `The amount of money that can still be raised by the portfolio including money that can be raised through the sale of currently owned shares up for sale as of ${shareBreakdownAsOfDtText}.`,
      },
      {
        label: 'Investment Cap',
        value: numeral(currentEquityData.investmentCap).format('$0,0.00'),
        definition: `The maximum amount of money that can be raised by the portfolio net of resold shares (newly issued shares only). This should be equivalent to the sum of project level and portfolio level costs as of ${shareBreakdownAsOfDtText}. This number is driven by the "Total Equity" imported from the portfolio models into the "Monthly Financial Actuals"`,
      },
      // {
      //   label: 'Shares Flagged for Auto Reinvest',
      //   value: numeral(
      //     sharesFlaggedForAutoReinvest /
      //       currentEquityData.ownedShares
      //   ).format('%0.00'),
      // },
    ];
    const tableJsx = (
      <TableContainer>
        <Table size="small" aria-label="a dense table">
          <TableBody>
            {data.map((dataPoint) => (
              <TableRow key={`data-point-key-${dataPoint.label}`}>
                <TableCell
                  component="th"
                  scope="row"
                  style={{ width: '400px' }}
                >
                  <Tooltip arrow title={dataPoint.definition}>
                    <Typography variant="body2" style={{ cursor: 'pointer' }}>
                      {dataPoint.label}{' '}
                      <InfoRounded
                        style={{ color: '#ccc', cursor: 'pointer' }}
                      />
                    </Typography>
                  </Tooltip>
                </TableCell>
                <TableCell component="th" scope="row" align="right">
                  <Typography variant="body2">{dataPoint.value}</Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );

    const managerOwnershipData = [
      {
        label: 'Energea Global LLC',
        shares: currentEquityData.energeaOwnedShares,
        percentage:
          currentEquityData.energeaOwnedShares / currentEquityData.ownedShares,
      },
      {
        label: 'Michael Silvestrini',
        shares: currentEquityData.mikeOwnedShares,
        percentage:
          currentEquityData.mikeOwnedShares / currentEquityData.ownedShares,
      },
      {
        label: 'Chris Sattler',
        shares: currentEquityData.chrisOwnedShares,
        percentage:
          currentEquityData.chrisOwnedShares / currentEquityData.ownedShares,
      },
      {
        label: 'Gray Reinhard',
        shares: currentEquityData.grayOwnedShares,
        percentage:
          currentEquityData.grayOwnedShares / currentEquityData.ownedShares,
      },
      {
        label:
          'All directors and executive officers of our Manager as a group (3 persons)',
        shares:
          currentEquityData.grayOwnedShares +
          currentEquityData.mikeOwnedShares +
          currentEquityData.chrisOwnedShares,
        percentage:
          (currentEquityData.grayOwnedShares +
            currentEquityData.mikeOwnedShares +
            currentEquityData.chrisOwnedShares) /
          currentEquityData.ownedShares,
      },
    ];

    const shareOwnershipDoughnutJsx = (
      <div style={{ width: '10em' }}>
        <Doughnut
          data={{
            labels: ['Crowd', 'Energea', 'Natural'],
            datasets: [
              {
                data: [
                  (currentEquityData && currentEquityData.crowdOwnedShares) ||
                    0,
                  (currentEquityData && currentEquityData.energeaOwnedShares) ||
                    0,
                  (currentEquityData && currentEquityData.naturalShares) || 0,
                ],
                backgroundColor: [
                  theme.palette.green.main,
                  theme.palette.primary.main,
                  '#ccc',
                ],
              },
            ],
          }}
          options={{
            animation: {
              duration: 0,
            },
            plugins: {
              legend: { display: false },
              tooltip: {
                callbacks: {
                  label: (tooltipItem) => {
                    return [
                      `${tooltipItem.label} shares:`,
                      `Shares: ${tooltipItem.formattedValue}`,
                      `NAV: ${numeral(
                        tooltipItem.raw * currentEquityData.sharePrice
                      ).format('$0,0.00')}`,
                      `Equity: ${numeral(
                        (tooltipItem.raw / currentEquityData.maxShares) * 100
                      ).format('0.[00]')}%`,
                    ];
                  },
                },
              },
            },
            layout: {
              padding: {
                right: 50,
              },
            },
          }}
        />
      </div>
    );

    const denom =
      currentEquityData && currentEquityData.investmentCap
        ? currentEquityData.investmentCap -
          (currentEquityData.totalNetRaisedMinusUnsoldShareValue || 0)
        : 1;
    const capVsReceivedDoughnutJsx = (
      <div style={{ width: '10em' }}>
        <Doughnut
          data={{
            labels: ['Received', 'Cap Space'],
            datasets: [
              {
                data: [
                  (currentEquityData &&
                    currentEquityData.totalNetRaisedMinusUnsoldShareValue) ||
                    0,
                  denom,
                ],
                backgroundColor: [
                  theme.palette.green.main,
                  denom < 0 ? 'red' : '#ccc',
                ],
              },
            ],
          }}
          options={{
            animation: {
              duration: 0,
            },
            plugins: {
              legend: { display: false },
              tooltip: {
                callbacks: {
                  label: (tooltipItem) =>
                    numeral(tooltipItem.raw).format('$0,0.00'),
                },
              },
            },
            layout: {
              padding: {
                right: 50,
              },
            },
          }}
        />
      </div>
    );

    const equityContributingToDividends =
      currentEquityData.operationalEquity / currentEquityData.totalNetRaised;

    return (
      <Grid container>
        <Grid item xs={12} md={6}>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
          >
            <Grid item>
              <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                Crowd vs. Energea vs. Newly Issued Shares
              </Typography>
            </Grid>
            <Grid item>{shareOwnershipDoughnutJsx}</Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
          >
            <Grid item>
              <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                Investments Received vs. Cap
              </Typography>
            </Grid>
            <Grid item>{capVsReceivedDoughnutJsx}</Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} style={{ marginTop: '1.5rem' }}>
          <Typography variant="body2" align="center">
            <b>Percentage Equity Contributing to Dividends:</b>{' '}
            {numeral(equityContributingToDividends || 0).format('0,0%')}
          </Typography>
        </Grid>
        <Grid item xs={12} style={{ marginTop: '1.5rem' }}>
          {tableJsx}
        </Grid>
        <Grid item xs={12} style={{ marginTop: '1.5rem' }}>
          <Accordion
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          >
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">Ownership of Managers :</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Name of Beneficial Owner</b>
                    </TableCell>
                    <TableCell>
                      <b>Number of Shares Beneficially Owned</b>
                    </TableCell>
                    <TableCell>
                      <b>Percent of all Shares</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {managerOwnershipData.map((el) => (
                    <TableRow>
                      <TableCell>
                        <b>{el.label}</b>
                      </TableCell>
                      <TableCell>
                        {numeral(el.shares).format('0,0[.]0000')}
                      </TableCell>
                      <TableCell>
                        {numeral(el.percentage).format('%0,0[.]0000')}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </AccordionDetails>
          </Accordion>
        </Grid>
        <Grid item xs={12} style={{ marginTop: '1.5rem' }}>
          <Accordion
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          >
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">Glossary :</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {data.map((el) => (
                <Typography variant="body2" gutterBottom>
                  <b>{el.label}</b> : {el.definition}
                </Typography>
              ))}
            </AccordionDetails>
          </Accordion>
        </Grid>
      </Grid>
    );
  };

  return (
    <>
      <List perPage={4} sort={{ field: 'orderNo', order: 'ASC' }}>
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="name" />
          <TextField source="subtitle" />
          <BooleanField source="isPublic" />
          <BooleanField source="isAcceptingInvestments" />
          {/* <TextField
      label="Bank Acct. Status"
      source="dwollaFundingSource.status"
    /> */}
          <FunctionField
            label="Current Share Price"
            sortable={false}
            render={(record) =>
              record.latestMonthlyPortfolioFinancialActual &&
              numeral(
                record.latestMonthlyPortfolioFinancialActual.sharePrice
              ).format('$0,0.000000')
            }
          />
          <FunctionField
            sortable={false}
            label="Actual IRR"
            render={(record) =>
              `${numeral(record.actualIRR).format('0,0.000')}%`
            }
          />
          <FunctionField
            sortable={false}
            label="Actual IRR (Crowd Only)"
            render={(record) =>
              `${numeral(record.crowdOnlyActualIRR).format('0,0.000')}%`
            }
          />
          <FunctionField
            sortable={false}
            label="Projected IRR"
            render={(record) =>
              `${numeral(record.projectedIRR).format('0,0.000')}%`
            }
          />
          <FunctionField
            // align="center"
            label="Marketed Projected Return"
            style={{ width: '100%', textAlign: 'center' }}
            render={(record) => {
              let color = null;
              if (
                record.projectedIRR &&
                (record.projectedIRR > record.projectedCOCYieldMax ||
                  record.projectedIRR < record.projectedCOCYieldMin)
              ) {
                color = 'red';
              }
              return (
                <span style={{ color, fontWeight: color ? 'bold' : null }}>
                  {record.projectedCOCYield}
                </span>
              );
            }}
          />
          {/* <NumberField source="paybackPeriodMonths" /> */}
          {/* <NumberField source="orderNo" /> */}
          <DateField
            sortable={false}
            label="SharePrice Updated"
            source="latestMonthlyPortfolioFinancialActual.updatedAt"
          />
          {/* <NumberField
            label="Unaccredited Investor Count"
            sortable={false}
            source="unaccreditedInvestorCount"
          /> */}
          <FunctionField
            align="center"
            label="Share Breakdown"
            style={{ width: '100%' }}
            render={(record) => {
              return (
                <>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={(event) => {
                      event.stopPropagation();
                      setShareBreakdownDialogOpen(true);
                      setSelectedPortfolio(record);
                      getCurrentEquityData(record.id, shareBreakdownAsOfDt);
                    }}
                    endIcon={<ArrowForward />}
                  >
                    Fund Financials
                  </Button>
                </>
              );
            }}
          />
          <FunctionField
            align="center"
            label="Projected payout schedule"
            style={{ width: '100%' }}
            render={(record) => {
              return (
                <>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={(event) => {
                      event.stopPropagation();
                      setPortfolioProjectedPayoutDialogOpen(true);
                      setSelectedPortfolio(record);
                      getProjectionVersions(record);
                    }}
                    endIcon={<ArrowForward />}
                  >
                    Projected Payout
                  </Button>
                </>
              );
            }}
          />
          <FunctionField
            align="center"
            label="Projects"
            style={{ width: '100%' }}
            render={(record) => {
              return (
                <>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={(event) => {
                      event.stopPropagation();
                      setProjectDialogOpen(true);
                      setSelectedProjectPortfolio(record);
                      getPortfolioProjectListData(record.id);
                    }}
                    endIcon={<ArrowForward />}
                  >
                    Projects
                  </Button>
                </>
              );
            }}
          />
          <BooleanField label="Is featured" source="featuredFlg" />
          <BooleanField label="Is coming soon" source="comingSoonFlg" />
          <BooleanField label="Is consortium" source="consortiumFlg" />
          <BooleanField
            label="Selling unissued shares first"
            source="sellNaturalSharesFirstFlg"
          />
          <BooleanField source="hasBannerImage" sortable={false} />
          <BooleanField source="hasPrimaryVideo" sortable={false} />
          <BooleanField source="hasMobileImage" sortable={false} />
          {/* <ArrayField source="projects" sortable={false}>
            <SingleFieldList>
              <CustomReferenceField
                color={(resource) =>
                  resource.isPublic ? 'primary' : 'default'
                }
                source="name"
              />
            </SingleFieldList>
          </ArrayField> */}
          <LinkField
            label="Development Status"
            linkSource="portfolioStatus.id"
            labelSource="portfolioStatus.name"
            reference="PortfolioStatus"
          />
          <DetailField source="description" sortable={false} />
          <DetailField
            source="investmentDescription"
            richText={true}
            sortable={false}
          />
          <NumberField
            source="totalFutureFees"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalDiscountedFees"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <FunctionField
            align="center"
            label="LFDI"
            style={{ width: '100%' }}
            render={(record) => {
              if (
                !record.latestMonthlyPortfolioFinancialActual ||
                !record.latestMonthlyPortfolioFinancialActual.totalEquity ||
                !record.totalFutureFees
              ) {
                return '';
              }
              return numeral(
                record.totalFutureFees /
                  record.latestMonthlyPortfolioFinancialActual.totalEquity
              ).format('$0,0.0000');
            }}
          />
          <FunctionField
            align="center"
            label="Discounted Fees per Dollar"
            style={{ width: '100%' }}
            render={(record) => {
              if (
                !record.latestMonthlyPortfolioFinancialActual ||
                !record.latestMonthlyPortfolioFinancialActual.totalEquity ||
                !record.totalDiscountedFees
              ) {
                return '';
              }
              return numeral(
                record.totalDiscountedFees /
                  record.latestMonthlyPortfolioFinancialActual.totalEquity
              ).format('$0,0.0000');
            }}
          />
          <NumberField
            source="totalDebt"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalTaxEquity"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalSponsorEquity"
            options={{ style: 'currency', currency: 'USD' }}
          />
        </Datagrid>
      </List>
      {selectedPortfolio && portfolioProjectedPayoutDialogOpen ? (
        <Dialog
          fullWidth
          maxWidth="false"
          open={portfolioProjectedPayoutDialogOpen}
          onClose={() => {
            setPortfolioProjectedPayoutDialogOpen(false);
            setProjectionVersions(null);
            setSelectedPortfolio(null);
            setProjectionSets([]);
          }}
        >
          <DialogTitle>
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
                <Typography variant="h5">Projected Payouts</Typography>
                <Typography>{selectedPortfolio.subtitle}</Typography>
              </Grid>
              <Grid item>
                <IconButton
                  size="large"
                  onClick={() => setPortfolioProjectedPayoutDialogOpen(false)}
                >
                  <Close />
                </IconButton>
              </Grid>
            </Grid>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container>
              {projectionVersions ? (
                <Grid container>
                  <Grid
                    container
                    item
                    justifyContent="space-between"
                    alignItems="center"
                  >
                    <Grid item>
                      <FormControl fullWidth>
                        <InputLabel id="projection-version-select-label">
                          Projection Versions
                        </InputLabel>
                        <Select
                          labelId="projection-version-select-label"
                          onChange={(event) => {
                            fetchVersionProjections(
                              event.target.value,
                              selectedPortfolio.id
                            );
                          }}
                          style={{ width: '200px' }}
                          value=""
                        >
                          {projectionVersions &&
                            projectionVersions.map((version) => (
                              <MenuItem
                                value={version}
                                key={`proj-v-${version}`}
                              >
                                {moment(version.date).format('MMM D, YYYY')}
                              </MenuItem>
                            ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    {projectionSets.length > 0 ? (
                      <Grid item>
                        {/* Resetting the zoom has proven difficult. Using "clear" button works but isn't a perfect solution. */}
                        {/* <Button
                          variant="text"
                          color="primary"
                          onClick={() => {
                            handleResetZoom();
                          }}
                        >
                          Reset Zoom
                        </Button> */}
                        <Button
                          variant="text"
                          color="primary"
                          onClick={() => {
                            setProjectionSets([]);
                          }}
                        >
                          Clear
                        </Button>
                      </Grid>
                    ) : null}
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    style={{ maxHeight: '500px', marginTop: '1rem' }}
                  >
                    <Line
                      key={`projected-cafd-versions-line-chart`}
                      height={300}
                      responsive="true"
                      data={{
                        // labels: projectionSets.map((projectionSet) =>
                        //   projectionSet.projections.map((el) => el.grossCafd)
                        // ),
                        datasets: projectionSets.map((projectionSet) => ({
                          label: moment(projectionSet.version.date).format(
                            'MMM D, YYYY'
                          ),
                          // fill: true,
                          // backgroundColor: 'rgba(21, 48, 76, 0.5)',
                          data: projectionSet.projections
                            // .filter((x) => x.id % 7 === 0)
                            .map((el) => ({
                              x: el.effectiveDt,
                              y: el.grossCafd,
                            })),
                          pointRadius: 0,
                          // borderWidth: 3,
                          borderColor: projectionSetColors.shift(),
                          // backgroundColor: aumChartColors.shift(),
                        })),
                      }}
                      options={{
                        maintainAspectRatio: false,
                        plugins: {
                          // legend: { display: false },
                          tooltip: {
                            mode: 'x',
                            intersect: false,
                            callbacks: {
                              label: (tooltipItem) => {
                                return `${tooltipItem.dataset.label}: ${numeral(
                                  tooltipItem.formattedValue
                                ).format('$0,0')}`;
                              },
                            },
                          },
                          zoom: {
                            zoom: {
                              mode: 'x',
                              scaleMode: 'x',
                              drag: {
                                enabled: true,
                              },
                              pinch: {
                                enabled: true,
                              },
                            },
                          },
                        },
                        scales: {
                          x: {
                            type: 'time',
                            time: {
                              tooltipFormat: 'MMM YYYY',
                              unit: 'month',
                            },
                          },
                          y: {
                            title: {
                              text: 'Projected CAFD',
                              display: true,
                            },
                            ticks: {
                              callback: (value) =>
                                numeral(value).format('$0,0.[0]a'),
                            },
                          },
                        },
                      }}
                    />
                  </Grid>
                  {projectionsWithNotes.length > 0 ? (
                    <Grid item xs={12}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>
                              <b>Projection Version</b>
                            </TableCell>
                            <TableCell>
                              <b>Effective Dt</b>
                            </TableCell>
                            <TableCell>
                              <b>Note</b>
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {projectionsWithNotes.map((projection) => (
                            <TableRow
                              key={`projection-${projection.version.id}-note-table-row`}
                            >
                              <TableCell>
                                {moment(
                                  projection.projection.effectiveDt
                                ).format('MMM D, YYYY')}
                              </TableCell>
                              <TableCell>
                                {moment(projection.version.date).format(
                                  'MMM D, YYYY'
                                )}
                              </TableCell>
                              <TableCell>
                                {projection.projection.notes}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </Grid>
                  ) : null}
                </Grid>
              ) : (
                <CircularProgress style={{ margin: 'auto' }} />
              )}
            </Grid>
          </DialogContent>
        </Dialog>
      ) : null}
      {selectedProjectPortfolio && projectDialogOpen ? (
        <Dialog
          fullWidth
          open={projectDialogOpen}
          onClose={() => {
            setProjectDialogOpen(false);
            setSelectedProjectPortfolio(null);
            setPortfolioProjectData(null);
          }}
        >
          <DialogTitle>
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
                <Typography variant="h5">Project List</Typography>
                <Typography>{selectedProjectPortfolio.subtitle}</Typography>
              </Grid>
              <Grid item>
                <IconButton
                  size="large"
                  onClick={() => setProjectDialogOpen(false)}
                >
                  <Close />
                </IconButton>
              </Grid>
            </Grid>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container>
              {portfolioProjectData ? (
                getPortfolioProjectListDataDialogContent()
              ) : (
                <CircularProgress />
              )}
            </Grid>
          </DialogContent>
        </Dialog>
      ) : null}
      {selectedPortfolio && shareBreakdownDialogOpen ? (
        <Dialog
          fullWidth
          open={shareBreakdownDialogOpen}
          onClose={() => {
            setShareBreakdownDialogOpen(false);
            setSelectedPortfolio(null);
            setCurrentEquityData(null);
          }}
        >
          <DialogTitle>
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
                <Typography variant="h6">Share Breakdown</Typography>
                <Typography>{selectedPortfolio.subtitle}</Typography>
              </Grid>
              <Grid item>
                <IconButton
                  size="large"
                  onClick={() => setShareBreakdownDialogOpen(false)}
                >
                  <Close />
                </IconButton>
              </Grid>
            </Grid>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container>
              <Grid item xs={12} style={{ marginBottom: '1rem' }}>
                <MuiTextField
                  id="asOfDt"
                  label="As of date"
                  type="date"
                  value={shareBreakdownAsOfDt || ''}
                  onChange={(event) => {
                    const newDate = moment(event.target.value).format(
                      'yyyy-MM-DD'
                    );
                    if (newDate !== shareBreakdownAsOfDt) {
                      setShareBreakdownAsOfDt(newDate);
                      getCurrentEquityData(selectedPortfolio.id, newDate);
                    }
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  InputProps={{
                    inputProps: {
                      max: moment().format('yyyy-MM-DD'),
                    },
                  }}
                />
              </Grid>
              {currentEquityData ? (
                getCurrentEquityDataDialogContent()
              ) : (
                <CircularProgress />
              )}
            </Grid>
          </DialogContent>
        </Dialog>
      ) : null}
    </>
  );
});

export const PortfolioCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
