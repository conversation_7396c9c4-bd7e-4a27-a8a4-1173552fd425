import React from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Portfolio Document Type';

export const PortfolioDocumentTypeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="title" fullWidth />
            <BooleanInput source="acknowledgementRequired" />
            <BooleanInput source="secDocFlg" />
            <CustomNumberInput source="orderNo" step={1} fullWidth />
            <TextInput source="summary" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const PortfolioDocumentTypeList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="title" />
        <BooleanField source="acknowledgementRequired" />
        <BooleanField source="secDocFlg" />
        <NumberField source="orderNo" />
        <TextField source="summary" />
      </Datagrid>
    </List>
  );
};

export const PortfolioDocumentTypeCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="title" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
