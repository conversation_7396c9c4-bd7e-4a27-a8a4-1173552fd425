import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON><PERSON>ield,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'HubSpot Social Channel';

export const HubSpotSocialChannelEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <DateInput source="date" fullWidth />
            <CustomNumberInput source="amount" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const HubSpotSocialChannelList = () => (
  <List>
    <Datagrid rowClick="show">
      <TextField source="id" />
      <TextField source="accountGuid" />
      <TextField source="accountType" />
      <TextField source="avatarUrl" />
      <TextField source="channelGuid" />
      <TextField source="channelKey" />
      <TextField source="channelSlug" />
      <TextField source="profileUrl" />
      <TextField source="username" />
      <BooleanField source="shared" />
      <BooleanField source="active" />
      <BooleanField source="canPublish" />
      <BooleanField source="hidden" />
    </Datagrid>
  </List>
);

export const HubSpotSocialChannelCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <DateInput source="date" required fullWidth />
          <CustomNumberInput source="amount" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
