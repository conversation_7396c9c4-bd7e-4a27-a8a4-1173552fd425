import React, { useState } from 'react';
import { useDataProvider } from 'react-admin';
import {
  Alert,
  Avatar,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  ListSubheader,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Typography,
} from '@mui/material';
import { Business, Close, CloudDownload } from '@mui/icons-material';

import Config from '../config/config';

import cloudinary from 'cloudinary-core';

const cl = new cloudinary.Cloudinary({
  cloud_name: Config.cloud_name,
  secure: true,
});

const openFile = (publicId) => {
  window.open(cl.url(publicId), '_blank');
};

export const ThirdPartyReportDialog = (props) => {
  const { reportId, open, setOpen, fullScreen } = props;
  const [loading, setLoading] = useState(false);
  const [report, setReport] = useState(null);
  const dataProvider = useDataProvider();

  const fetchOMReport = () => {
    setLoading(true);
    dataProvider.getOne('OMReport', { id: reportId }).then((res) => {
      setReport(res.data);
      setLoading(false);
    });
  };

  if (
    (!report && !loading) ||
    (report?.id && !loading && report?.id !== reportId)
  ) {
    fetchOMReport();
  }

  return (
    <Dialog
      open={open}
      maxWidth="lg"
      fullScreen={fullScreen}
      onClose={() => setOpen(false)}
    >
      <DialogTitle>
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
          spacing={2}
        >
          <Grid item>Third Party Report</Grid>
          <Grid item>
            <IconButton onClick={() => setOpen(false)}>
              <Close />
            </IconButton>
          </Grid>
        </Grid>
      </DialogTitle>
      <DialogContent style={{ minWidth: '540px' }}>
        {!report ? (
          <CircularProgress />
        ) : (
          <>
            <Grid container>
              <Grid item xs={12}>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar>
                      <Business />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={report.thirdPartyCompanyName}
                    secondary="3rd Party Company Name"
                  />
                </ListItem>
              </Grid>
            </Grid>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Grid container justifyContent="space-between">
          {report?.thirdPartyReportEnglishPublicId && (
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CloudDownload />}
                onClick={() => openFile(report.thirdPartyReportEnglishPublicId)}
              >
                View Third Party Report
                <br />
                (English)
              </Button>
            </Grid>
          )}
          {report?.thirdPartyReportPortuguesePublicId && (
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CloudDownload />}
                onClick={() =>
                  openFile(report.thirdPartyReportPortuguesePublicId)
                }
              >
                View Third Party Report
                <br />
                (Portuguese)
              </Button>
            </Grid>
          )}
        </Grid>
      </DialogActions>
    </Dialog>
  );
};
