import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button, useDataProvider, useNotify } from 'react-admin';
import { useParams } from 'react-router-dom';
import {
  Alert,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { Add, CloudDownload, Delete } from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

export const UserBeneficiaries = () => {
  const [userBeneficiaries, setUserBeneficiaries] = useState(null);
  const [todDocuments, setTodDocuments] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();
  const { id } = useParams();

  const createTodDocument = (event) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(`TODDocuments/${id}/${file.name}`);
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          dataProvider
            .create('TodDocument', {
              data: {
                userId: parseInt(id, 10),
                awsObjectKey: awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Document uploaded', { type: 'success' });
                fetchUserBeneficiaries();
              },
              (err) => {
                console.error(err);
                notify('Error uploading document', {
                  type: 'error',
                });
                fetchUserBeneficiaries();
              }
            );
        },
        (e) => {
          notify('Error uploading document to S3', { type: 'error' });
        }
      );
    }
  };

  const deleteTODDocument = (documentId) => {
    dataProvider
      .delete('TodDocument', {
        id: documentId,
      })
      .then(
        (res) => {
          notify('Document deleted', { type: 'success' });
          fetchUserBeneficiaries();
        },
        (err) => {
          console.error(err);
          notify('Error deleting document', {
            type: 'error',
          });
          fetchUserBeneficiaries();
        }
      );
  };

  const fetchUserBeneficiaries = () => {
    setLoading(true);
    dataProvider
      .getOne('UserWithBeneficiaries', {
        id: parseInt(id, 10),
      })
      .then(
        (resp) => {
          setUserBeneficiaries(resp.data.beneficiaries);
          setTodDocuments(resp.data.todDocuments);
          setLoading(false);
          setError(null);
        },
        (e) => {
          setLoading(false);
          setError(e);
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  };

  if (!userBeneficiaries && !loading) {
    fetchUserBeneficiaries();
  }

  if (loading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Alert severity="error">An error occurred</Alert>;
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Typography variant="h5">Beneficiaries</Typography>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <b>Account holder</b>
              </TableCell>
              <TableCell>
                <b>Beneficiary type</b>
              </TableCell>
              <TableCell>
                <b>Name</b>
              </TableCell>
              <TableCell>
                <b>Entity/Trust name</b>
              </TableCell>
              <TableCell>
                <b>DOB/formation date</b>
              </TableCell>
              <TableCell>
                <b>Email</b>
              </TableCell>
              <TableCell>
                <b>Phone</b>
              </TableCell>
              <TableCell>
                <b>Primary Flg</b>
              </TableCell>
              <TableCell>
                <b>Edit</b>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {userBeneficiaries?.map((beneficiary) => {
              return (
                <TableRow key={`beneficiary-list-${beneficiary.id}`}>
                  <TableCell>{beneficiary.user.fullName}</TableCell>
                  <TableCell>{beneficiary.type.name}</TableCell>
                  <TableCell>
                    {beneficiary.firstName} {beneficiary.lastName}
                  </TableCell>
                  <TableCell>
                    {beneficiary.entityName || beneficiary.trustName}
                  </TableCell>
                  <TableCell>
                    {beneficiary.dateOfBirth || beneficiary.formationDt}
                  </TableCell>
                  <TableCell>{beneficiary.email}</TableCell>
                  <TableCell>{beneficiary.phone}</TableCell>
                  <TableCell>{beneficiary.primaryFlg ? 'Yes' : 'No'}</TableCell>
                  <TableCell>
                    <a href={`/Beneficiary/${beneficiary.id}`}>
                      <EditIcon />
                    </a>
                  </TableCell>
                </TableRow>
              );
            })}
            <TableRow>
              <TableCell colSpan={9}>
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<Add />}
                  component={Link}
                  to={`/Beneficiary/create?userId=${id}`}
                  style={{ textTransform: 'none' }}
                >
                  Create new beneficiary
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h5">Transfer-on-death documents</Typography>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <b>Document name</b>
              </TableCell>
              <TableCell>
                <b>Download</b>
              </TableCell>
              <TableCell>
                <b>Delete</b>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {todDocuments?.map((document) => {
              return (
                <TableRow key={`tod-documents-list-${document.id}`}>
                  <TableCell>{document.name}</TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={<CloudDownload />}
                      style={{ textTransform: 'none' }}
                      onClick={() => {
                        window.location.assign(document.downloadUrl);
                      }}
                    >
                      Download
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      color="secondary"
                      endIcon={<Delete />}
                      onClick={() => deleteTODDocument(document.id)}
                      style={{ textTransform: 'none' }}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
            <TableRow>
              <TableCell colSpan={3}>
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<Add />}
                  component="label" // https://stackoverflow.com/a/54043619
                  style={{ textTransform: 'none' }}
                >
                  Upload new document
                  <input
                    type="file"
                    hidden
                    onChange={(event) => createTodDocument(event)}
                    accept="application/pdf"
                  />
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Grid>
    </Grid>
  );
};
