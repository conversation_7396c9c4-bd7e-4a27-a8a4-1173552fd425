import React from 'react';
import moment from 'moment';
import jsonExport from 'jsonexport/dist';

import {
  Datagrid,
  downloadCSV,
  Filter,
  List,
  NumberField,
  TextField,
  TextInput,
} from 'react-admin';
import { LinkField } from './CustomFields';

const entityName = 'User Portfolio Tax Summary';

const exporter = (rows) => {
  const rowsForExport = rows.map((row) => {
    const returnRow = {};
    const dates = [
      'startDt',
      'date',
      'sellDt',
      'firstSoldInvestmentDt',
      'lastSellDt',
    ];
    const strings = ['ssnEin', 'postalCode'];
    const attrs = [
      'id',
      'exemptFlg',
      'consolidatedTaxEntityType',
      // 'dividends',
      // 'investments',
      // 'shareTransfers',
      'consolidatedTaxName',
      'lastName',
      'firstName',
      // 'email',
      // 'address1',
      // 'address2',
      'consolidatedAddress',
      'city',
      'state',
      'postalCode',
      'countryCode',
      'ssnEin',
      'tinType',
      'consolidatedTaxEntityType',
      // 'equity',
      // 'weightedEquity',
      'distributionPercentage',
      'beginningCapitalAccount',
      'endingCapitalAccount',
      'totalNewlyInvested',
      'totalNewDividends',
      'docId',
      'newCostBasisDeltaFromSoldShares',
      'newProfitLossFromSoldShares',
      'dividendDistribution',
      'nonDividendDistribution',
      'totalInvested',
      'totalNetOwnedShares',
      // 'totalNetNewlyOwnedShares',
      // 'totalGrossOwnedShares',
      // 'totalGrossNewlyOwnedShares',
      // 'totalGrossEarned',
      // 'totalNewlyGrossEarned',
      // 'totalDividends',
      // 'totalSoldShares',
      // 'totalNewlySoldShares',
      // 'totalSoldValue',
      // 'totalNewlySoldValue',
      'costBasisDeltaFromSoldShares',
      'endOfYearCostBasisSnapshot',
      'capitalGains',
      'user',
      'taxYear',
      'portfolio',
      'firstSoldInvestmentDt',
      'requires1099BFlg',
      'propertyDescription',
      'lastSellDt',
      'shortTermCapitalGainsFlg',
    ];
    const subAttrs = {
      dividends: ['date', 'value'],
      investments: ['startDt', 'shares', 'value'],
      shareTransfers: ['soldShares', 'value', 'sellDt'],
    };
    Object.keys(row).forEach((attr) => {
      if (attrs.indexOf(attr) === -1) {
        return;
      }
      const data = row[String(attr)];
      if (dates.indexOf(attr) > -1) {
        returnRow[String(attr)] = moment(data).format('YYYYMMDD');
        return;
      }
      if (strings.indexOf(attr) > -1) {
        returnRow[String(attr)] = `\"${data} \" `;
        return;
      }
      if (attr === 'user') {
        const { id, __typename, ...allOtherAttrs } = data;
        // allOtherAttrs.postalCode = `${allOtherAttrs.postalCode} `;
        // allOtherAttrs.ssnEin = `${allOtherAttrs.ssnEin} `;
        returnRow[String(attr)] = allOtherAttrs;
        return;
      }
      if (attr === 'portfolio') {
        const { id, __typename, ...allOtherAttrs } = data;
        returnRow[String(attr)] = allOtherAttrs;
        return;
      }
      if (Array.isArray(data)) {
        const subData = data.map((subDataRow) => {
          const returnSubRow = {};
          Object.keys(subDataRow).forEach((subDataRowKey) => {
            const subDataAttrData = subDataRow[String(subDataRowKey)];
            if (
              !subAttrs[String(attr)] ||
              subAttrs[String(attr)].indexOf(subDataRowKey) === -1
            ) {
              return;
            }
            if (dates.indexOf(subDataRowKey) > -1) {
              returnSubRow[String(subDataRowKey)] =
                moment(subDataAttrData).format('MM/DD/YYYY');
              return;
            }
            returnSubRow[String(subDataRowKey)] = subDataAttrData;
          });
          return returnSubRow;
        });
        returnRow[String(attr)] = subData;
        return;
      }
      returnRow[String(attr)] = data;
    });
    return returnRow;
  });
  jsonExport(
    rowsForExport,
    {
      headers: ['id'],
    },
    (err, csv) => {
      downloadCSV(csv, entityName); // download as 'posts.csv` file
    }
  );
};

const UserPortfolioTaxSummaryFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const UserPortfolioTaxSummaryList = () => (
  <List
    exporter={exporter}
    title={entityName}
    pagination={false}
    filters={<UserPortfolioTaxSummaryFilter />}
  >
    <Datagrid rowClick="show">
      <TextField source="id" />
      <TextField source="docId" />
      <TextField source="reconciliationId" />
      <TextField source="portfolio.name" />
      {/* <LinkField
        reference="Portfolio"
        linkSource="portfolio.id"
        labelSource="portfolio.name"
        label="Portfolio"
      /> */}
      <LinkField
        reference="User"
        linkSource="user.id"
        labelSource="user.fullName"
        label="User"
      />
      <TextField source="user.consolidatedAddress" />
      <TextField source="consolidatedTaxEntityType" />
      <TextField source="taxYear" />
      {/* <NumberField source="equity" />
      <NumberField source="weightedEquity" /> */}
      <NumberField source="distributionPercentage" />
      <NumberField
        source="totalInvested"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="totalNewlyInvested"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField source="totalNetOwnedShares" />
      <NumberField source="totalNetNewlyOwnedShares" />
      <NumberField source="totalGrossOwnedShares" />
      <NumberField source="totalGrossNewlyOwnedShares" />
      <NumberField
        source="totalGrossEarned"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="totalNewlyGrossEarned"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="totalDividends"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="totalNewDividends"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField source="totalSoldShares" />
      <NumberField source="totalNewlySoldShares" />
      <NumberField
        source="totalSoldValue"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="totalNewlySoldValue"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="newCostBasisDeltaFromSoldShares"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="newProfitLossFromSoldShares"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField
        source="beginningCapitalAccount"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <TextField source="" />
    </Datagrid>
  </List>
);
