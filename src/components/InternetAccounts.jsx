import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  PasswordInput,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  UrlField,
  usePermissions,
  useNotify,
  useResourceDefinition,
  useDataProvider,
} from 'react-admin';
import { ContentCopy, Close } from '@mui/icons-material';
import { Alert } from '@mui/lab';

import { Button, Collapse, Grid, IconButton } from '@mui/material';

import { LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Internet Account';

export const InternetAccountEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <FunctionField
              label="ISP"
              render={(record) => {
                return (
                  record?.internetServiceProvider?.id === 1 && (
                    <>
                      <TextInput
                        label="ID Starlink"
                        source="starlinkId"
                        // required
                        fullWidth
                      />
                      <TextInput
                        source="satelliteSerialNumber"
                        label="Satellite S/N"
                        // required
                        fullWidth
                      />
                      <TextInput
                        source="starlinkKitNumber"
                        label="Starlink KIT Number"
                        // required
                        fullWidth
                      />
                    </>
                  )
                );
              }}
            />

            <TextInput source="username" fullWidth defaultValue="" />
            <PasswordInput source="password" defaultValue="" />
            <ReferenceInput
              source="internetServiceProvider.id"
              reference="InternetServiceProvider"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                required
                optionText="name"
                label="Internet Service Provider"
                fullWidth
                helperText={
                  <span>
                    If you don't see the Internet Service Provider in this list,
                    create it <a href="/InternetServiceProvider/create">here</a>
                    .
                  </span>
                }
              />
            </ReferenceInput>
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'name', order: 'DESC' }}
            >
              <SelectInput
                optionText="name"
                label="Project"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const InternetAccountList = () => {
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const [internetAccountMetaData, setInternetAccountMetaData] = useState(null);
  const [internetAccountMetaDataLoading, setInternetAccountMetaDataLoading] =
    useState(false);
  const [accountCreationInfoOpen, setAccountCreationInfoOpen] = useState(true);
  const [missingProjectsInfoOpen, setMissingProjectsInfoOpen] = useState(true);
  const { permissions } = usePermissions();

  const fetchInternetAccountMetaData = () => {
    setInternetAccountMetaDataLoading(true);
    dataProvider.getOne('InternetAccountMetaData', {}).then(
      (res) => {
        setInternetAccountMetaData(res.data);
        setInternetAccountMetaDataLoading(false);
      },
      (e) => {
        notify(e.message, { type: 'error' });
        setInternetAccountMetaData({});
        setInternetAccountMetaDataLoading(false);
      }
    );
  };
  if (!internetAccountMetaData && !internetAccountMetaDataLoading) {
    fetchInternetAccountMetaData();
  }

  const metaDataComponent = (
    <Collapse in={internetAccountMetaData}>
      {accountCreationInfoOpen && (
        <Alert
          severity="info"
          action={
            <IconButton
              size="small"
              onClick={() =>
                setAccountCreationInfoOpen(!accountCreationInfoOpen)
              }
            >
              <Close />
            </IconButton>
          }
        >
          Starlink Account Creation Process: <br />
          <ol>
            <li>
              Project Manager requests Starlink account to accountant (Vivian).
            </li>
            <li>
              Accountant goes to https://www.starlink.com/ to purchase a new
              satellite dish using an available pre-made user name below. These
              should be "personal" accounts and can use the information of the
              accountant as they will be managing payments.{' '}
              <b>
                **UPDATE** Starlink is temporarily enforcing 1 satellite dish
                per 'account'. What this means is we need a unique CPF for each
                satellite dish. We also need a credit card that is consistent
                with the CPF used.
              </b>
            </li>
            <li>
              The new Starlink Account information is then entered into this
              table by the accountant using the "Create" button above.
            </li>
          </ol>
          {internetAccountMetaData?.availableStarlinkEmails.length > 0 ? (
            <>
              Available Starlink user names :{' '}
              <strong>
                {internetAccountMetaData?.availableStarlinkEmails.join(', ')}
              </strong>
            </>
          ) : (
            <>
              There are no more available Starlink user names. Reach out to
              <EMAIL> to create more.
            </>
          )}
        </Alert>
      )}
      {missingProjectsInfoOpen &&
        (internetAccountMetaData?.projectsMissingInternetAccount.length > 0 ? (
          <Alert
            severity={'warning'}
            action={
              <IconButton
                size="small"
                onClick={() =>
                  setMissingProjectsInfoOpen(!missingProjectsInfoOpen)
                }
              >
                <Close />
              </IconButton>
            }
          >
            Projects under O&M contract without an internet account: <br />
            <strong style={{ width: '100%' }}>
              {internetAccountMetaData?.projectsMissingInternetAccount
                .map((el) => el.name)
                .join(', ')}
            </strong>
          </Alert>
        ) : (
          <Alert severity="success">
            All O&M projects have internet accounts associated with them. Nice
            work!
          </Alert>
        ))}
    </Collapse>
  );

  return (
    <List title={entityName} perPage={25}>
      {metaDataComponent}
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <LinkField
          reference="InternetServiceProvider"
          linkSource="internetServiceProvider.id"
          labelSource="internetServiceProvider.name"
          label="ISP"
        />
        <UrlField
          target="_blank"
          onClick={(event) => {
            // event.preventDefault();
            event.stopPropagation();
          }}
          label="ISP Login"
          source="internetServiceProvider.url"
        />
        {/* <TextField source="username" /> */}
        <FunctionField
          label="Username"
          render={({ username }) => {
            if (!username || username === '') return null;
            return (
              <Button
                variant="text"
                endIcon={<ContentCopy />}
                onClick={(event) => {
                  navigator.clipboard.writeText(username);
                  event.preventDefault();
                  event.stopPropagation();

                  notify('Username copied to clipboard', { type: 'success' });
                }}
                size="small"
              >
                <span style={{ textTransform: 'none' }}>{username}</span>
              </Button>
            );
          }}
        />
        <FunctionField
          label="Password"
          render={({ password }) => {
            return (
              <Button
                variant="text"
                endIcon={<ContentCopy />}
                onClick={(event) => {
                  navigator.clipboard.writeText(password);
                  event.preventDefault();
                  event.stopPropagation();

                  notify('Password copied to clipboard', { type: 'success' });
                }}
                size="small"
              >
                *******
              </Button>
            );
          }}
        />
        <TextField label="ID Starlink" source="starlinkId" />
        <TextField label="Satellite S/N" source="satelliteSerialNumber" />
        <TextField label="Starlink KIT Number" source="starlinkKitNumber" />
      </Datagrid>
    </List>
  );
};

export const InternetAccountCreate = () => {
  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="internetServiceProvider.id"
              reference="InternetServiceProvider"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                required
                optionText="name"
                helperText={
                  <span>
                    If you don't see the Internet Service Provider in this list,
                    create it <a href="/InternetServiceProvider/create">here</a>
                    .
                  </span>
                }
                label="Internet Service Provider"
                fullWidth
              />
            </ReferenceInput>
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                required
                optionText="name"
                label="Project"
                fullWidth
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
