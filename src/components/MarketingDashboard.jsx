import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  Card,
  CardActionArea,
  CardContent,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Select,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Skeleton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { Alert } from '@mui/lab';
import {
  ArrowDropDown,
  ArrowDropUp,
  ExpandMore,
  Subject,
} from '@mui/icons-material';
import { Title, useDataProvider, usePermissions } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment';

import { <PERSON>, <PERSON>, Scatter } from 'react-chartjs-2';

import 'chart.js/auto';
import Gauge<PERSON>hart from 'react-gauge-chart';
import { withStyles } from '@mui/styles';

import theme from '../theme';
import UserDemographicsMap from './UserDemographicsMap';

const demographicsMapUserInvestedMaxDefaultValue = 999999999;

const styles = (theme) => ({
  root: { overflow: 'visible !important' },
});
const getLTVColor = (num) => {
  if (num > 3) {
    return theme.palette.green.main;
  } else if (num > 1) {
    return theme.palette.yellow.main;
  }
  return theme.palette.error.main;
};
export default withStyles(styles)((args) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [upcomingInvestmentData, setUpcomingInvestmentData] = useState(null);
  const [dailyActiveUserData, setDailyActiveUserData] = useState(null);
  // const [visibleChart, setVisibleChart] = useState(null);
  const [fromDt, setFromDt] = useState(null);
  const [toDt, setToDt] = useState(null);
  const [prevFromDt, setPrevFromDt] = useState(null);
  const [prevToDt, setPrevToDt] = useState(null);
  const [investmentRangeLabel, setInvestmentRangeLabel] = useState(null);
  const [openCustomDateDialog, setOpenCustomDateDialog] = useState(false);
  const [presetTimeFrame, setPresetTimeFrame] = useState('last30');
  const [autoSelectPreviousDates, setAutoSelectPreviousDates] = useState(false);
  const [selectedMonthlyLtvCacChartData, setSelectedMonthlyLtvCacChartData] =
    useState(null);
  const [selectedLTVMultiplier, setSelectedLTVMultiplier] = useState(null);
  const [selectedAvgLFDI, setSelectedAvgLFDI] = useState(null);
  const [ltvMultiplierShowData, setLtvMultiplierShowData] =
    useState('ltvMultiplier');

  // LTV by Initial Investment Chart State vbls
  const [ltvByInitialInvestmentData, setLTVByInitialInvestmentData] =
    useState(null);
  const [ltvByInitialInvestmentLoading, setLTVByInitialInvestmentLoading] =
    useState(false);

  // Demographics state vbls
  const [demographicsData, setDemographicsData] = useState(null);
  const [demographicsLoading, setDemographicsLoading] = useState(false);
  const [
    userDemographicsAccreditedFilter,
    setUserDemographicsAccreditedFilter,
  ] = useState('allInvestors');
  const [userDemographicsInvestorsOnly, setUserDemographicsInvestorsOnly] =
    useState(true);
  const [
    userDemographicsEnvironmentallyFocusedFilter,
    setUserDemographicsEnvironmentallyFocusedFilter,
  ] = useState('all');
  const [userDemographicsScatterXAxis, setUserDemographicsScatterXAxis] =
    useState('age');
  const [userDemographicsScatterYAxis, setUserDemographicsScatterYAxis] =
    useState('totalInvested');
  const [
    userDemographicsTotalInvestedRangeMax,
    setUserDemographicsTotalInvestedRangeMax,
  ] = useState(demographicsMapUserInvestedMaxDefaultValue);
  const [
    userDemographicsTotalInvestedRangeMin,
    setUserDemographicsTotalInvestedRangeMin,
  ] = useState(0);
  const [ltvToCacData, setLtvToCacData] = useState(null);
  const [marketingChartData, setMarketingChartData] = useState(null);

  const dataAttrs = [
    'totalVisitors',
    'prevTotalVisitors',
    'totalNewVisitors',
    'prevTotalNewVisitors',
    'newContacts',
    'prevNewContacts',
    'newUsers',
    'prevNewUsers',
    'newInvestors',
    'prevNewInvestors',
    'newUserInvestmentTotal',
    'prevNewUserInvestmentTotal',
    'existingUserInvestmentTotal',
    'prevExistingUserInvestmentTotal',
    'investmentTotal',
    'prevInvestmentTotal',
  ];
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  const getInvestmentRangeFromLabel = (label) => {
    let investmentRangeMin = null;
    let investmentRangeMax = null;
    if (label) {
      const splitLabel = label
        .split(',')
        .join('')
        .split('+')
        .join('')
        .split('-');
      if (splitLabel.length === 2) {
        investmentRangeMin = parseInt(splitLabel[0], 10);
        investmentRangeMax = parseInt(splitLabel[1], 10);
      } else if (splitLabel.length === 1) {
        investmentRangeMin = parseInt(splitLabel[0], 10);
      }
    }
    return {
      investmentRangeMin,
      investmentRangeMax,
    };
  };

  const fetchDashboardData = (input = {}) => {
    dataProvider
      .getOne('CMSMarketingDashboardData', {
        input: {
          presetTimeFrame: input.presetTimeFrame ?? presetTimeFrame,
          toDt,
          fromDt,
          prevToDt,
          prevFromDt,
        },
      })
      .then(
        (resp) => {
          setData(resp.data);
          setLoading(false);
        },
        (e) => {
          // eslint disable-next-line no-console
          console.error('Error retrieving dashboard data', e);
          setLoading(false);
        }
      );
  };

  const fetchLTVByInitialInvestmentData = () => {
    setLTVByInitialInvestmentLoading(true);
    dataProvider.getList('InvestorsWithInitialAndTotalInvested', {}).then(
      (resp) => {
        setLTVByInitialInvestmentData(resp.data);
        setLTVByInitialInvestmentLoading(false);
      },
      (e) => {
        // eslint disable-next-line no-console
        console.error('Error retrieving dashboard data', e);
        setLTVByInitialInvestmentLoading(false);
      }
    );
  };

  const fetchUpcomingInvestmentData = () => {
    dataProvider.getOne('UpcomingInvestmentData', { data: null }).then(
      (resp) => {
        setUpcomingInvestmentData(resp.data);
      },
      (e) => {
        // eslint disable-next-line no-console
        console.error('Error retrieving upcoming investment data', e);
      }
    );
  };

  const fetchDailyActiveUserData = () => {
    dataProvider.getOne('DailyActiveUserData', { data: null }).then(
      (resp) => {
        setDailyActiveUserData(resp.data.dailyActiveUserData);
      },
      (e) => {
        // eslint disable-next-line no-console
        console.error('Error retrieving daily active user data', e);
      }
    );
  };

  const fetchChartData = (input = {}) => {
    dataProvider
      .getOne('MarketingChartData', {
        input: {
          data: null,
        },
        avgDaysInvestmentRange: getInvestmentRangeFromLabel(
          Object.keys(input).indexOf('newLabel') === -1
            ? investmentRangeLabel
            : input.newLabel
        ),
      })
      .then(
        (resp) => {
          setMarketingChartData(resp.data);
        },
        (e) => {
          // eslint disable-next-line no-console
          console.error('Error retrieving marketing chart data', e);
        }
      );
  };

  const fetchLtvToCacData = () => {
    dataProvider.getOne('LtvToCacData', { data: null }).then(
      (resp) => {
        setLtvToCacData(resp.data);
        if (!selectedMonthlyLtvCacChartData) {
          setSelectedMonthlyLtvCacChartData(
            resp.data.getMonthlyLtvCacChartData
          );
          setSelectedLTVMultiplier(resp.data.getLTVMultiplier);
          setSelectedAvgLFDI(resp.data.getAvgLFDI);
        }
      },
      (e) => {
        // eslint disable-next-line no-console
        console.error('Error retrieving LTV/CAC data', e);
      }
    );
  };

  const fetchDemographicsData = ({
    accreditedFilter,
    investorsOnly,
    environmentallyFocusedFilter,
    totalInvestedMin,
    totalInvestedMax,
  }) => {
    setDemographicsLoading(true);
    const maxInvested =
      totalInvestedMax || userDemographicsTotalInvestedRangeMax;
    const minInvested =
      totalInvestedMin || userDemographicsTotalInvestedRangeMin;
    dataProvider
      .getOne('CMSMarketingDemographicData', {
        input: {
          investorsOnly: investorsOnly || userDemographicsInvestorsOnly,
          accreditedFilter:
            accreditedFilter || userDemographicsAccreditedFilter,
          environmentallyFocusedFilter:
            environmentallyFocusedFilter ||
            userDemographicsEnvironmentallyFocusedFilter,
          totalInvestedMin: minInvested,
          totalInvestedMax: maxInvested === -1 ? null : maxInvested,
        },
      })
      .then(
        (resp) => {
          setDemographicsData(resp.data.getCMSMarketingDemographicsData);
          setDemographicsLoading(false);
        },
        (e) => {
          // eslint disable-next-line no-console
          console.error('Error retrieving demographics data', e);
          setDemographicsLoading(false);
        }
      );
  };

  const loadingJsx = (
    <Grid
      style={{
        position: 'fixed',
        top: '50%',
        width: '100%',
        textAlign: 'center',
      }}
    >
      <CircularProgress />
    </Grid>
  );

  if (!data) {
    fetchDashboardData();
    return loadingJsx;
  }
  const cmsDashboardData = data.getCMSMarketingDashboardData;

  // NOTE: Don't fetch below the fold queries until the above the fold completes
  if (data) {
    if (!ltvToCacData) {
      fetchLtvToCacData();
    } else {
      if (!upcomingInvestmentData) {
        fetchUpcomingInvestmentData();
      } else {
        if (!dailyActiveUserData) {
          fetchDailyActiveUserData();
        } else {
          if (!ltvByInitialInvestmentData && !ltvByInitialInvestmentLoading) {
            fetchLTVByInitialInvestmentData();
          } else {
            if (!demographicsData && !demographicsLoading) {
              fetchDemographicsData({});
            } else {
              if (!marketingChartData) {
                fetchChartData();
              }
            }
          }
        }
      }
    }
  }

  let getUserCount = null;
  let getInvestorCount = null;
  let latestBudgetLineItemDate = null;
  let getLTVMultiplier = null;
  let getAvgLFDI = null;
  // let getPaybackPeriodMonths = null;
  let getMonthlyLtvCacChartData = null;
  let allLeadSourceCategories = null;
  if (ltvToCacData) {
    latestBudgetLineItemDate = ltvToCacData.getLatestBudgetLineItemDate;
    getUserCount = ltvToCacData.getUserCount;
    getInvestorCount = ltvToCacData.getInvestorCount;
    getLTVMultiplier = ltvToCacData.getLTVMultiplier;
    getAvgLFDI = ltvToCacData.getAvgLFDI;
    // getPaybackPeriodMonths = ltvToCacData.getPaybackPeriodMonths;
    getMonthlyLtvCacChartData = ltvToCacData.getMonthlyLtvCacChartData;
    allLeadSourceCategories = ltvToCacData.allLeadSourceCategories;
  }

  let getMonthlyPercentageAutoInvesting = null;
  let getMonthlyPercentageReinvestingDividends = null;
  let getDaysToVerifiedAndFirstInvestmentChartData = null;
  if (marketingChartData) {
    getMonthlyPercentageAutoInvesting =
      marketingChartData.getMonthlyPercentageAutoInvesting;
    getMonthlyPercentageReinvestingDividends =
      marketingChartData.getMonthlyPercentageReinvestingDividends;
    getDaysToVerifiedAndFirstInvestmentChartData =
      marketingChartData.getDaysToVerifiedAndFirstInvestmentChartData;
  }

  const allInvestors = ltvByInitialInvestmentData || [];
  if (!(cmsDashboardData && permissions) || loading) return loadingJsx;
  const { roles } = permissions;

  const dataStructure = [
    // {
    //   attr: 'totalVisitors',
    //   prevAttr: 'prevTotalVisitors',
    //   label: 'Total Visitors',
    //   expandable: true,
    //   formatter: (val) => numeral(val).format('0,0'),
    // },
    // {
    //   attr: 'totalNewVisitors',
    //   prevAttr: 'prevTotalNewVisitors',
    //   label: 'New Visitors',
    //   expandable: true,
    //   formatter: (val) => numeral(val).format('0,0'),
    // },
    {
      attr: 'newUsers',
      prevAttr: 'prevNewUsers',
      label: 'New Users',
      expandable: true,
      width: 4,
      formatter: (val) => numeral(val).format('0,0'),
    },
    {
      attr: 'newInvestors',
      prevAttr: 'prevNewInvestors',
      label: 'New Investors',
      width: 4,
      expandable: true,
      formatter: (val) => numeral(val).format('0,0'),
    },
    {
      attr: 'marketingSpend',
      prevAttr: 'prevMarketingSpend',
      label: 'Marketing Spend',
      width: 4,
      expandable: true,
      formatter: (val) => numeral(val).format('$0,0'),
    },
    {
      attr: 'newUserInvestmentTotal',
      prevAttr: 'prevNewUserInvestmentTotal',
      label: 'New Investor Investments',
      width: 4,
      expandable: true,
      formatter: (val) => numeral(val).format('$0,0'),
    },
    {
      attr: 'existingUserInvestmentTotal',
      prevAttr: 'prevExistingUserInvestmentTotal',
      label: 'Existing Investor Investments',
      width: 4,

      expandable: true,
      formatter: (val) => numeral(val).format('$0,0'),
    },
    {
      attr: 'investmentTotal',
      prevAttr: 'prevInvestmentTotal',
      label: 'Total Investments',
      width: 4,

      expandable: true,
      formatter: (val) => numeral(val).format('$0,0'),
    },
  ];
  // BudgetMonths belows excludes the months at end of chart before marketing spend is uploaded. Commented out because previous month spend -> next months investments
  // const platformBudgetMonths = getMonthlyLtvCacChartData.map((cur) =>
  //   cur.marketingSpendTotal ? cur.month : null
  // );
  const platformTotalFirstInvestments = getMonthlyLtvCacChartData?.reduce(
    (sum, cur) => {
      // if (platformBudgetMonths.indexOf(cur.month) > -1) {
      return sum + cur.firstInvestmentTotal;
      // }
      // return sum;
    },
    0
  );
  const platformTotalMarketingSpend = getMonthlyLtvCacChartData?.reduce(
    (sum, cur) => sum + cur.marketingSpendTotal,
    0
  );

  const totalFirstInvestments = selectedMonthlyLtvCacChartData?.reduce(
    (sum, cur) => {
      // if (platformBudgetMonths.indexOf(cur.month) > -1) {
      return sum + cur.firstInvestmentTotal;
      // }
      // return sum;
    },
    0
  );
  const totalMarketingSpend = selectedMonthlyLtvCacChartData?.reduce(
    (sum, cur) => sum + cur.marketingSpendTotal,
    0
  );

  const initialToTotalInvestedData = allInvestors
    .filter((user) => user.id !== 76)
    .map((user) => ({
      user,
      x: user.initialInvestmentValue,
      y: user.investmentSum,
    }));

  const ltvMultiplierBuckets = {
    '0-499': { init: 0, total: 0, ltv: [], count: 0 },
    '500-999': { init: 0, total: 0, ltv: [], count: 0 },
    '1,000-2,499': { init: 0, total: 0, ltv: [], count: 0 },
    '2,500-4,999': { init: 0, total: 0, ltv: [], count: 0 },
    '5,000-9,999': { init: 0, total: 0, ltv: [], count: 0 },
    '10,000-24,999': { init: 0, total: 0, ltv: [], count: 0 },
    '25,000-49,999': { init: 0, total: 0, ltv: [], count: 0 },
    '50,000-99,999': { init: 0, total: 0, ltv: [], count: 0 },
    '100,000+': { init: 0, total: 0, ltv: [], count: 0 },
  };
  initialToTotalInvestedData.forEach((userPoint) => {
    if (userPoint.x < 500) {
      ltvMultiplierBuckets['0-499'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['0-499'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['0-499'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['0-499'].count += 1;
    } else if (userPoint.x < 1000) {
      ltvMultiplierBuckets['500-999'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['500-999'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['500-999'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['500-999'].count += 1;
    } else if (userPoint.x < 2500) {
      ltvMultiplierBuckets['1,000-2,499'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['1,000-2,499'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['1,000-2,499'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['1,000-2,499'].count += 1;
    } else if (userPoint.x < 5000) {
      ltvMultiplierBuckets['2,500-4,999'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['2,500-4,999'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['2,500-4,999'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['2,500-4,999'].count += 1;
    } else if (userPoint.x < 10000) {
      ltvMultiplierBuckets['5,000-9,999'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['5,000-9,999'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['5,000-9,999'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['5,000-9,999'].count += 1;
    } else if (userPoint.x < 25000) {
      ltvMultiplierBuckets['10,000-24,999'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['10,000-24,999'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['10,000-24,999'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['10,000-24,999'].count += 1;
    } else if (userPoint.x < 50000) {
      ltvMultiplierBuckets['25,000-49,999'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['25,000-49,999'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['25,000-49,999'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['25,000-49,999'].count += 1;
    } else if (userPoint.x < 100000) {
      ltvMultiplierBuckets['50,000-99,999'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['50,000-99,999'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['50,000-99,999'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['50,000-99,999'].count += 1;
    } else {
      ltvMultiplierBuckets['100,000+'].init += parseFloat(userPoint.x);
      ltvMultiplierBuckets['100,000+'].total += parseFloat(userPoint.y);
      ltvMultiplierBuckets['100,000+'].ltv.push(
        parseFloat(userPoint.y / userPoint.x)
      );
      ltvMultiplierBuckets['100,000+'].count += 1;
    }
  });

  const getMedian = (values) => {
    if (values.length === 0) return 0;
    values.sort((a, b) => a - b);
    var half = Math.floor(values.length / 2);
    if (values.length % 2) return values[parseInt(half, 10)];
    return (values[half - 1] + values[parseInt(half, 10)]) / 2.0;
  };

  const ltvMultiplierChartData = Object.keys(ltvMultiplierBuckets).map(
    (range) => ({
      x: range,
      y: getMedian(ltvMultiplierBuckets[String(range)].ltv),
    })
  );

  const platformDataPoints = [
    {
      label: 'Users',
      value: numeral(getUserCount).format('0,0'),
    },
    {
      label: 'Investors',
      value: numeral(getInvestorCount).format('0,0'),
    },
    {
      label: 'CAC (User)',
      value: numeral(platformTotalMarketingSpend / getUserCount).format('$0,0'),
    },
    {
      label: 'CAC (Investor)',
      value: numeral(platformTotalMarketingSpend / getInvestorCount).format(
        '$0,0'
      ),
    },
    {
      label: 'Avg. Invested',
      value: numeral(
        (platformTotalFirstInvestments * getLTVMultiplier) / getInvestorCount
      ).format('$0,0'),
    },
    {
      label: 'Avg. First Investment',
      value: numeral(platformTotalFirstInvestments / getInvestorCount).format(
        '$0,0'
      ),
    },
    {
      label: 'User -> Investor Conv. Ratio',
      value: numeral(getInvestorCount / getUserCount).format('0[.]0%'),
    },
  ];

  return (
    <Card>
      <Title title="Marketing Dashboard" />
      <CardContent>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="h4">Marketing</Typography>
          </Grid>
          <Grid item>
            <Select
              label="filter"
              value={presetTimeFrame}
              onChange={(event) => {
                const val = event.target.value;
                if (presetTimeFrame !== val && val !== 'custom') {
                  setLoading(true);
                  fetchDashboardData({ presetTimeFrame: val });
                }
                setPresetTimeFrame(val);
              }}
              style={{ width: '200px' }}
            >
              <MenuItem value="last30">Last 30 days</MenuItem>
              <MenuItem value="thisMonth">This month</MenuItem>
              <MenuItem value="lastMonth">Last month</MenuItem>
              <MenuItem value="thisYear">This year</MenuItem>
              <MenuItem value="lastYear">Last year</MenuItem>
              <Divider />
              <MenuItem
                value="custom"
                onClick={() => setOpenCustomDateDialog(true)}
              >
                Custom
              </MenuItem>
            </Select>
            <Dialog open={openCustomDateDialog} style={{ margin: '2rem' }}>
              <DialogTitle>
                <Typography variant="h5">Date Range Selector:</Typography>
              </DialogTitle>
              <DialogContent>
                <Grid container>
                  <Grid
                    container
                    item
                    alignItems="center"
                    justifyContent="space-between"
                    style={{ marginBottom: '2rem' }}
                  >
                    <Grid item>Current Period:</Grid>
                    <Grid item>
                      <TextField
                        id="fromDate"
                        label="From"
                        type="date"
                        value={fromDt}
                        onChange={(event) => {
                          const date = event.target.value;
                          setFromDt(date);
                          if (autoSelectPreviousDates && toDt) {
                            const daysBetween = moment(toDt).diff(
                              moment(date),
                              'days'
                            );
                            setPrevFromDt(
                              moment(date)
                                .subtract(daysBetween + 1, 'days')
                                .format('yyyy-MM-DD')
                            );
                            setPrevToDt(
                              moment(date)
                                .subtract(1, 'days')
                                .format('yyyy-MM-DD')
                            );
                          }
                        }}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        error={toDt && fromDt && toDt <= fromDt}
                        helperText={
                          toDt && fromDt && toDt <= fromDt
                            ? "'From' date must precede 'To' date"
                            : null
                        }
                      />
                    </Grid>
                    <Grid item>
                      <TextField
                        id="toDate"
                        label="To"
                        type="date"
                        value={toDt}
                        onChange={(event) => {
                          const date = event.target.value;
                          setToDt(date);
                          if (autoSelectPreviousDates && fromDt) {
                            const daysBetween = moment(date).diff(
                              moment(fromDt),
                              'days'
                            );
                            setPrevFromDt(
                              moment(fromDt)
                                .subtract(daysBetween + 1, 'days')
                                .format('yyyy-MM-DD')
                            );
                            setPrevToDt(
                              moment(fromDt)
                                .subtract(1, 'days')
                                .format('yyyy-MM-DD')
                            );
                          }
                        }}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        error={toDt && fromDt && toDt <= fromDt}
                        helperText={
                          toDt && fromDt && toDt <= fromDt
                            ? "'From' date must precede 'To' date"
                            : null
                        }
                      />
                    </Grid>
                  </Grid>
                  <Grid
                    container
                    item
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <Grid item>
                      <Grid container direction="column">
                        <Grid item>Previous Period:</Grid>
                        <Grid item>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={autoSelectPreviousDates}
                                onChange={(event) => {
                                  const { checked } = event.target;
                                  setAutoSelectPreviousDates(
                                    event.target.checked
                                  );
                                  if (checked && toDt && fromDt) {
                                    const daysBetween = moment(toDt).diff(
                                      moment(fromDt),
                                      'days'
                                    );
                                    setPrevFromDt(
                                      moment(fromDt)
                                        .subtract(daysBetween + 1, 'days')
                                        .format('yyyy-MM-DD')
                                    );
                                    setPrevToDt(
                                      moment(fromDt)
                                        .subtract(1, 'days')
                                        .format('yyyy-MM-DD')
                                    );
                                  }
                                }}
                              />
                            }
                            label={
                              <Typography variant="body2">
                                Auto-select
                              </Typography>
                            }
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item>
                      <TextField
                        id="fromDate"
                        label="From"
                        type="date"
                        value={prevFromDt}
                        onChange={(event) => setPrevFromDt(event.target.value)}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        disabled={autoSelectPreviousDates}
                        error={prevToDt && prevFromDt && prevToDt <= prevFromDt}
                        helperText={
                          prevToDt && prevFromDt && prevToDt <= prevFromDt
                            ? "'From' date must precede 'To' date"
                            : null
                        }
                      />
                    </Grid>
                    <Grid item>
                      <TextField
                        id="toDate"
                        label="To"
                        type="date"
                        value={prevToDt}
                        onChange={(event) => setPrevToDt(event.target.value)}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        disabled={autoSelectPreviousDates}
                        error={prevToDt && prevFromDt && prevToDt <= prevFromDt}
                        helperText={
                          prevToDt && prevFromDt && prevToDt <= prevFromDt
                            ? "'From' date must precede 'To' date"
                            : null
                        }
                      />
                    </Grid>
                  </Grid>
                </Grid>
                {fromDt &&
                toDt &&
                prevFromDt &&
                prevToDt &&
                prevToDt >= fromDt ? (
                  <Alert severity="warning" style={{ marginTop: '1rem' }}>
                    The previous period's 'To' date must precede the current
                    period's 'From' date.
                  </Alert>
                ) : null}
              </DialogContent>
              <DialogActions>
                <Button
                  onClick={() => setOpenCustomDateDialog(false)}
                  color="primary"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    fetchDashboardData();
                    setLoading(true);
                    setOpenCustomDateDialog(false);
                  }}
                  color="primary"
                  disabled={
                    !toDt ||
                    !fromDt ||
                    !prevToDt ||
                    !prevFromDt ||
                    fromDt >= toDt ||
                    prevFromDt >= prevToDt ||
                    prevToDt >= fromDt
                  }
                >
                  Go
                </Button>
              </DialogActions>
            </Dialog>
          </Grid>
        </Grid>
        <Divider
          style={{ width: '100%', marginTop: '2em', marginBottom: '2em' }}
        />
        <Grid
          container
          style={{ width: '100%' }}
          justifyContent="space-evenly"
          spacing={1}
        >
          {/* <Grid style={{ textAlign: 'center' }} item xs={12}>
            <Typography
              style={{ color: theme.palette.green.main }}
              variant="h2"
            >
              <b>
                <CountUp
                  useEasing={false}
                  decimals={0}
                  // delay={1}
                  duration={2}
                  end={100000}
                  formattingFn={(num) => `${numeral(num).format('0,0')}`}
                />
              </b>
            </Typography>
            <Typography
              style={{
                fontWeight: 'bold',
                color: 'rgba(0,0,0,.6)',
              }}
              variant="body1"
            >
              Site Visits
            </Typography>
          </Grid> */}
          {cmsDashboardData && !loading ? (
            <Grid style={{ textAlign: 'center' }} item xs={12}>
              <Typography
                variant="h5"
                style={{ color: theme.palette.secondary.main }}
              >
                {presetTimeFrame === 'thisYear' ||
                presetTimeFrame === 'lastYear'
                  ? `${moment(cmsDashboardData.fromDt).format(
                      'MMMM Do YYYY'
                    )} - ${moment(cmsDashboardData.toDt).format(
                      'MMMM Do YYYY'
                    )}`
                  : `${moment(cmsDashboardData.fromDt).format(
                      'MMMM Do'
                    )} - ${moment(cmsDashboardData.toDt).format('MMMM Do')}`}
              </Typography>
              <Typography
                style={{
                  color: 'rgba(0,0,0,.6)',
                }}
                variant="body2"
              >
                vs
              </Typography>
              <Typography
                style={{
                  color: 'rgba(0,0,0,.6)',
                  fontWeight: 'bold',
                }}
                variant="body1"
              >
                {presetTimeFrame === 'thisYear' ||
                presetTimeFrame === 'lastYear'
                  ? `${moment(cmsDashboardData.prevFromDt).format(
                      'MMMM Do YYYY'
                    )} - ${moment(cmsDashboardData.prevToDt).format(
                      'MMMM Do YYYY'
                    )}`
                  : `${moment(cmsDashboardData.prevFromDt).format(
                      'MMMM Do'
                    )} - ${moment(cmsDashboardData.prevToDt).format(
                      'MMMM Do'
                    )}`}
              </Typography>
            </Grid>
          ) : (
            <CircularProgress style={{ padding: '21px' }} />
          )}
          <Divider
            style={{ width: '100%', marginTop: '2em', marginBottom: '2em' }}
          />
          {cmsDashboardData ? (
            dataStructure.map((dataEl, index) => {
              const selected = false; // dataEl.label === visibleChart;
              const currentValue = cmsDashboardData[dataEl.attr];
              const previousValue = cmsDashboardData[dataEl.prevAttr];
              const change = (currentValue - previousValue) / previousValue;
              let icon;
              let color;
              if (change > 0) {
                color = dataEl.downGood ? 'red' : theme.palette.green.main;
                icon = (
                  <ArrowDropUp
                    key={`kpi-container-arrow-1-${dataEl.label}-${index}`}
                  />
                );
              } else if (change < 0) {
                color = dataEl.downGood ? theme.palette.green.main : 'red';
                icon = (
                  <ArrowDropDown
                    key={`kpi-container-arrow-2-${dataEl.label}-${index}`}
                  />
                );
              } else {
                color = '#666';
                icon = (
                  <ArrowDropUp
                    key={`kpi-container-arrow-3-${dataEl.label}-${index}`}
                  />
                );
              }
              return (
                <Grid
                  key={`kpi-container-${dataEl.label}-${index}`}
                  item
                  // lg={dataEl.expandable ? 3 : 4}
                  lg={dataEl.width || 4}
                  sm={6}
                  xs={12}
                >
                  <Card
                    elevation={selected ? 2 : 0}
                    style={{
                      height: '14rem',
                      boxSizing: 'border-box',
                      backgroundColor: selected ? '#fafafa' : null,
                    }}
                    classes={{ root: args.classes.root }}
                  >
                    <CardActionArea
                      // onClick={() =>
                      //   setVisibleChart(
                      //     dataEl.label === visibleChart ? null : dataEl.label
                      //   )
                      // }
                      aria-label={`${dataEl.label} chart`}
                      disabled
                      // disabled={!dataEl.expandable}
                      style={{
                        cursor: 'pointer',
                        height: '100%',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="center"
                          alignItems="center"
                          item
                          xs={12}
                        >
                          <Grid
                            style={{
                              marginTop: '1rem',
                            }}
                            item
                          >
                            <Grid
                              container
                              alignItems="center"
                              style={{ position: 'relative' }}
                            >
                              <Grid item>
                                <Typography
                                  style={{
                                    fontWeight: 'bold',
                                    textAlign: 'center',
                                    color: theme.palette.green.main,
                                  }}
                                  variant="h3"
                                >
                                  {dataEl.formatter(currentValue)}
                                </Typography>
                              </Grid>
                              <Grid
                                item
                                container
                                alignItems="center"
                                style={{
                                  position: 'absolute',
                                  right: '-7.7rem',
                                  width: '7.5rem',
                                  color,
                                }}
                              >
                                <Grid item style={{ marginRight: '-3px' }}>
                                  <Typography variant="body2">
                                    <span style={{ fontWeight: 'bold', color }}>
                                      {!isFinite(change) ? (
                                        <span>&infin;</span>
                                      ) : (
                                        numeral(change).format('0,0.0%')
                                      )}
                                    </span>
                                  </Typography>
                                </Grid>
                                <Grid item>{icon}</Grid>
                              </Grid>
                            </Grid>
                          </Grid>

                          <Grid item>
                            <Typography
                              style={{
                                color: 'rgba(0,0,0,.6)',
                              }}
                              variant="body1"
                            >
                              Previously {dataEl.formatter(previousValue)}
                            </Typography>
                          </Grid>
                          <Grid item>
                            <Typography
                              style={{
                                color: theme.palette.primary.main,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                minHeight: '2.667em',
                              }}
                              variant="h5"
                            >
                              {dataEl.label}
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              );
            })
          ) : (
            <CircularProgress style={{ padding: '21px' }} />
          )}
        </Grid>
        {/* <Grid container style={{ width: '100%' }}>
          {cmsDashboardData
            ? dataStructure.map((dataEl) => {
                if (!dataEl.dailyAttr && !dataEl.prevDailyAttr) {
                  return null;
                }
                const currentDailyValues = cmsDashboardData[dataEl.dailyAttr];
                const previousDailyValues =
                  cmsDashboardData[dataEl.prevDailyAttr];
                return (
                  <Grid item xs={12} key={`current-values-${dataEl.label}`}>
                    <Collapse in={visibleChart === dataEl.label}>
                      <Typography gutterBottom variant="h6">
                        {dataEl.label} :{' '}
                      </Typography>
                      <Grid item xs={12}>
                        <Line
                          height={300}
                          data={{
                            labels: currentDailyValues.map((day) => day.date),
                            datasets: [
                              {
                                label: 'Current Period',
                                data: currentDailyValues.map(
                                  (day) => day.value
                                ),
                                backgroundColor: 'rgba(21, 48, 76, 0.3)',
                                borderColor: theme.palette.primary.main,
                                fill: true,
                                tension: 0.5,
                              },
                              {
                                label: 'Previous Period',
                                data: previousDailyValues.map(
                                  (day) => day.value
                                ),
                                borderColor: theme.palette.green.main,
                                tension: 0.5,
                              },
                            ],
                          }}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                  title: (tooltipItem) => {
                                    const dataIndex = tooltipItem[0].dataIndex;
                                    const currLabelDt = new Date(
                                      tooltipItem[0].label
                                    );
                                    const currLabel = currLabelDt
                                      .toUTCString()
                                      .substr(5, 11);
                                    if (
                                      previousDailyValues[
                                        parseInt(dataIndex, 10)
                                      ]
                                    ) {
                                      const prevLabelDt = new Date(
                                        previousDailyValues[
                                          parseInt(tooltipItem[0].dataIndex, 10)
                                        ].date
                                      );
                                      const prevLabel = prevLabelDt
                                        .toUTCString()
                                        .substr(5, 11);
                                      return `${prevLabel} vs. ${currLabel}`;
                                    }
                                    return currLabel;
                                  },
                                  label: (tooltipItem) => {
                                    if (tooltipItem.datasetIndex === 1)
                                      return null;

                                    const label = tooltipItem.dataset.label;
                                    const currValue =
                                      tooltipItem.chart.data.datasets[0].data[
                                        tooltipItem.dataIndex
                                      ];
                                    const prevValue =
                                      tooltipItem.chart.data.datasets[1].data[
                                        tooltipItem.dataIndex
                                      ];
                                    const percentChg =
                                      (currValue - prevValue) / prevValue;
                                    const percentChgStr = isFinite(percentChg)
                                      ? numeral(percentChg).format('0,0.0% ')
                                      : '-%';
                                    return `${label}: ${dataEl.formatter(
                                      currValue
                                    )} (${percentChgStr} ${
                                      isFinite(percentChg) && percentChg < 0
                                        ? 'down'
                                        : 'up'
                                    } from ${dataEl.formatter(prevValue)})`;
                                  },
                                },
                              },
                            },
                            scales: {
                              y: {
                                beginAtZero: true,
                                ticks: {
                                  callback: (value) => dataEl.formatter(value),
                                },
                              },
                            },
                          }}
                        />
                      </Grid>
                      {currentDailyValues.length !==
                      previousDailyValues.length ? (
                        <Alert severity="warning" style={{ margin: '1em 0' }}>
                          When the two time periods being compared on the graph
                          above don't have the same number of days, some points
                          on the graph may be missing towards the end.
                        </Alert>
                      ) : null}
                    </Collapse>
                  </Grid>
                );
              })
            : null}
        </Grid> */}
        <Divider style={{ width: '100%', margin: '2rem 0' }} />
        {ltvToCacData ? (
          <Grid
            container
            style={{ width: '100%' }}
            justifyContent="space-evenly"
            spacing={1}
          >
            <Grid item xs={12}>
              <Typography variant="h5">LTV / CAC</Typography>
            </Grid>
            <Grid item xs={12}>
              {selectedMonthlyLtvCacChartData ? (
                <Line
                  height={300}
                  data={{
                    labels: selectedMonthlyLtvCacChartData
                      // .filter(point => point.marketingSpendTotal > 0)
                      .map((point) => point.month),
                    datasets: [
                      {
                        label: 'LTV / CAC',
                        data: selectedMonthlyLtvCacChartData.map((point) =>
                          point.marketingSpendRunningTotal === 0
                            ? null
                            : (point.avgLFDI *
                                point.ltvMultiplier *
                                point.firstInvestmentRunningTotal) /
                              point.marketingSpendRunningTotal
                        ),
                        borderColor: theme.palette.primary.main,
                        // tension: 0.5,
                        pointRadius: 0,
                        borderWidth: 5,
                        yAxisID: 'yLtvCac',
                      },
                      {
                        label: 'Avg LFDI',
                        data: selectedMonthlyLtvCacChartData.map(
                          (point) => point.avgLFDI
                        ),
                        borderColor: 'gray',
                        // tension: 0.5,
                        pointRadius: 0,
                        yAxisID: 'yLFDI',
                      },
                      {
                        label: 'LTV Multiplier',
                        data: selectedMonthlyLtvCacChartData.map(
                          (point) => point.ltvMultiplier
                        ),
                        borderColor: 'black',
                        // tension: 0.5,
                        pointRadius: 0,
                        yAxisID: 'yLTVMult',
                      },
                      {
                        label: 'CAC',
                        data: selectedMonthlyLtvCacChartData.map(
                          (point) => point.marketingSpendRunningTotal
                        ),
                        borderColor: 'orange',
                        // tension: 0.5,
                        pointRadius: 0,
                        hidden: true,
                      },
                      {
                        label: 'LTV',
                        data: selectedMonthlyLtvCacChartData
                          // .filter(point => point.marketingSpendTotal > 0)
                          .map(
                            (point) =>
                              point.avgLFDI *
                              point.ltvMultiplier *
                              point.firstInvestmentRunningTotal
                          ),
                        backgroundColor: 'rgba(21, 48, 76, 0.3)',
                        borderColor: theme.palette.green.main,
                        // tension: 0.5,
                        pointRadius: 0,
                        hidden: true,
                      },
                      {
                        label: 'LTV / CAC (Monthly)',
                        data: selectedMonthlyLtvCacChartData.map((point) =>
                          point.marketingSpendTotal === 0
                            ? null
                            : (point.avgLFDI *
                                point.ltvMultiplier *
                                point.firstInvestmentTotal) /
                              point.marketingSpendTotal
                        ),
                        borderColor: theme.palette.primary.main,
                        // tension: 0.5,
                        pointRadius: 0,
                        yAxisID: 'yLtvCac',
                        hidden: true,
                      },
                      {
                        label: 'LTV (Monthly)',
                        data: selectedMonthlyLtvCacChartData
                          // .filter(point => point.marketingSpendTotal > 0)
                          .map(
                            (point) =>
                              point.avgLFDI *
                              point.ltvMultiplier *
                              point.firstInvestmentTotal
                          ),
                        backgroundColor: 'rgba(21, 48, 76, 0.3)',
                        borderColor: theme.palette.green.main,
                        // tension: 0.5,
                        pointRadius: 0,
                        hidden: true,
                      },
                      {
                        label: 'CAC (Monthly)',
                        data: selectedMonthlyLtvCacChartData
                          // .filter(point => point.marketingSpendTotal > 0)
                          .map((point) => point.marketingSpendTotal),
                        borderColor: 'orange',
                        // tension: 0.5,
                        pointRadius: 0,
                        hidden: true,
                      },
                    ],
                  }}
                  options={{
                    maintainAspectRatio: false,
                    plugins: {
                      tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                          label: (tooltipItem) => {
                            let formatterStr = '$0,0';
                            if (
                              tooltipItem.dataset.label.includes('LTV / CAC')
                            ) {
                              formatterStr = '0,0.000';
                            } else if (
                              tooltipItem.dataset.label.includes('Avg LFDI')
                            ) {
                              formatterStr = '$0,0.000';
                            } else if (
                              tooltipItem.dataset.label.includes('LTV Mult')
                            ) {
                              formatterStr = '0,0.000';
                            }
                            return `${tooltipItem.dataset.label}: ${numeral(
                              tooltipItem.formattedValue
                            ).format(formatterStr)}`;
                          },
                        },
                      },
                    },
                    scales: {
                      x: {
                        type: 'time',
                        time: {
                          tooltipFormat: 'MMM YYYY',
                          unit: 'month',
                        },
                      },
                      y: {
                        beginAtZero: true,
                        position: 'left',
                        ticks: {
                          callback: (value) => numeral(value).format('$0,0'),
                        },
                      },
                      yLtvCac: {
                        title: {
                          display: true,
                          text: 'LTV / CAC',
                        },
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                          display: false,
                        },
                      },
                      yLFDI: {
                        title: {
                          display: true,
                          text: 'Avg LFDI',
                        },
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                          display: false,
                        },
                        ticks: {
                          callback: (value) =>
                            numeral(value).format('$0,0.[000]'),
                        },
                      },
                      yLTVMult: {
                        title: {
                          display: true,
                          text: 'LTV Multiplier',
                        },
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                          display: false,
                        },
                        ticks: {
                          callback: (value) =>
                            numeral(value).format('0,0.[000]'),
                        },
                      },
                    },
                  }}
                />
              ) : null}
            </Grid>
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>Glossary</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>LTV / CAC :</b> Ratio of estimated lifetime value of an
                    investor compared to the cost of acquiring them. This number
                    takes into account all prior investments and marketing
                    spend.
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>Avg LFDI :</b> Lifetime Fees per Dollar Invested. This
                    number is the net present value of all future fees generated
                    by a dollar invested using a 9% discount rate. The dollar
                    invested is spread across all portfolios (excluding Project
                    1) at the current actual ratios.
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>LTV Multiplier :</b> The amount investors have multiplied
                    their initial investment on average for the life of their
                    investment. This number only considers actual investments,
                    and assumes no future investments will be made by existing
                    investors.
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>CAC :</b> The cumulative amount spent on marketing.
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>LTV :</b> The cumulative amount invested multiplied by
                    the average LFDI.
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>LTV / CAC (Monthly) :</b> Ratio of estimated lifetime
                    value of a dollar invested compared to the cost of acquiring
                    the dollar. This number takes into account only initial
                    investments and marketing spend as well as the running LFDI
                    and LTV multiplier for a given month.
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>LTV (Monthly) :</b> (Total initial investments for the
                    month) X (average ltv multiplier up until that month) X (avg
                    lifetime fees per dollar invested of the month)
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="body2"
                    style={{ fontStyle: 'italic' }}
                  >
                    <b>CAC (Monthly) :</b> The cumulative amount spent on
                    marketing in a given month.
                  </Typography>
                </AccordionDetails>
              </Accordion>
            </Grid>
            <Grid item xs={12}>
              <Typography style={{ marginTop: '1rem' }} variant="h5">
                Avg. LTV / CAC ={' '}
                <span
                  style={{
                    color: getLTVColor(
                      (selectedAvgLFDI *
                        selectedLTVMultiplier *
                        totalFirstInvestments) /
                        totalMarketingSpend
                    ),
                  }}
                >
                  {numeral(
                    (selectedAvgLFDI *
                      selectedLTVMultiplier *
                      totalFirstInvestments) /
                      totalMarketingSpend
                  ).format('0,0.000')}
                </span>
              </Typography>
              <Typography variant="caption">
                Using data from{' '}
                <b>{moment('5/1/2021').format('MMMM D, YYYY')}</b> to{' '}
                <b>
                  {moment(latestBudgetLineItemDate)
                    .endOf('month')
                    .format('MMMM D, YYYY')}
                </b>
                .
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableContainer>
                <Table aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell>Marketing Spend</TableCell>
                      <TableCell align="right">Initial Investments</TableCell>
                      <TableCell align="right">LTV Multiplier</TableCell>
                      <TableCell align="right">Total Raised</TableCell>
                      <TableCell align="right">Avg LFDI</TableCell>
                      <TableCell align="right">LTV</TableCell>
                      <TableCell align="right">LTV / CAC</TableCell>
                      {/* <TableCell align="right">
                        Payback Period (Years)
                      </TableCell> */}
                      <TableCell align="right">Stats</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow
                      onClick={() => {
                        setSelectedMonthlyLtvCacChartData(
                          getMonthlyLtvCacChartData
                        );
                        setSelectedLTVMultiplier(getLTVMultiplier);
                        setSelectedAvgLFDI(getAvgLFDI);
                      }}
                      selected={
                        selectedMonthlyLtvCacChartData ===
                        getMonthlyLtvCacChartData
                      }
                      hover
                    >
                      <TableCell component="th" scope="row">
                        Platform
                      </TableCell>
                      <TableCell component="th" scope="row">
                        {numeral(platformTotalMarketingSpend).format('$0,0.00')}
                      </TableCell>
                      <TableCell align="right">
                        {numeral(platformTotalFirstInvestments).format(
                          '$0,0.00'
                        )}
                      </TableCell>
                      <TableCell align="right">
                        {numeral(getLTVMultiplier).format('0,0.000')}
                      </TableCell>
                      <TableCell align="right">
                        {numeral(
                          platformTotalFirstInvestments * getLTVMultiplier
                        ).format('$0,0.00')}
                      </TableCell>
                      <TableCell align="right">
                        {numeral(getAvgLFDI).format('0,0.000')}
                      </TableCell>
                      <TableCell align="right">
                        {numeral(
                          getAvgLFDI *
                            getLTVMultiplier *
                            platformTotalFirstInvestments
                        ).format('$0,0.00')}
                      </TableCell>
                      <TableCell align="right">
                        {numeral(
                          (getAvgLFDI *
                            getLTVMultiplier *
                            platformTotalFirstInvestments) /
                            platformTotalMarketingSpend
                        ).format('0,0.000')}
                      </TableCell>
                      {/* <TableCell align="right">
                        {numeral(getPaybackPeriodMonths / 12).format('0,0[.]0')}
                      </TableCell> */}
                      <TableCell align="right" style={{ padding: 0 }}>
                        <Tooltip
                          arrow
                          title={
                            <TableContainer>
                              <Table size="small" aria-label="a dense table">
                                <TableBody>
                                  {platformDataPoints.map((dataPoint) => (
                                    <TableRow
                                      key={`platform-data-point-key-${dataPoint.label}`}
                                    >
                                      <TableCell
                                        component="th"
                                        scope="row"
                                        style={{ width: '400px' }}
                                      >
                                        <Typography
                                          variant="body2"
                                          style={{ color: '#fff' }}
                                        >
                                          {dataPoint.label}
                                        </Typography>
                                      </TableCell>
                                      <TableCell
                                        component="th"
                                        scope="row"
                                        align="right"
                                      >
                                        <Typography
                                          variant="body2"
                                          style={{ color: '#fff' }}
                                        >
                                          {dataPoint.value}
                                        </Typography>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          }
                        >
                          <IconButton size="large">
                            <Subject />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                    {allLeadSourceCategories
                      .sort((a, b) => {
                        const aTotalFirstInvestments =
                          a.ltvToCacData.monthlyLtvCacChartData?.reduce(
                            (sum, cur) => sum + cur.firstInvestmentTotal,
                            0
                          );
                        const bTotalFirstInvestments =
                          b.ltvToCacData.monthlyLtvCacChartData?.reduce(
                            (sum, cur) => sum + cur.firstInvestmentTotal,
                            0
                          );
                        const aTotalRaised =
                          aTotalFirstInvestments * a.ltvToCacData.ltvMultiplier;
                        const bTotalRaised =
                          bTotalFirstInvestments * b.ltvToCacData.ltvMultiplier;
                        return aTotalRaised < bTotalRaised ? 1 : -1;
                      })
                      .map((category) => {
                        const lsTotalFirstInvestments =
                          category.ltvToCacData.monthlyLtvCacChartData?.reduce(
                            (sum, cur) => sum + cur.firstInvestmentTotal,
                            0
                          );
                        const lsTotalMarketingSpend =
                          category.ltvToCacData.monthlyLtvCacChartData?.reduce(
                            (sum, cur) => sum + cur.marketingSpendTotal,
                            0
                          );

                        const dataPoints = [
                          {
                            label: 'Users',
                            value: numeral(category.userCount).format('0,0'),
                          },
                          {
                            label: 'Investors',
                            value: numeral(category.investorCount).format(
                              '0,0'
                            ),
                          },
                          {
                            label: 'CAC (User)',
                            value: numeral(
                              lsTotalMarketingSpend / category.userCount
                            ).format('$0,0'),
                          },
                          {
                            label: 'CAC (Investor)',
                            value: numeral(
                              lsTotalMarketingSpend / category.investorCount
                            ).format('$0,0'),
                          },
                          {
                            label: 'Avg. Invested',
                            value: numeral(
                              (lsTotalFirstInvestments *
                                category.ltvToCacData.ltvMultiplier) /
                                category.investorCount
                            ).format('$0,0'),
                          },
                          {
                            label: 'Avg. First Investment',
                            value: numeral(
                              lsTotalFirstInvestments / category.investorCount
                            ).format('$0,0'),
                          },
                          {
                            label: 'Percentage of Total Raised',
                            value: numeral(
                              (lsTotalFirstInvestments *
                                category.ltvToCacData.ltvMultiplier) /
                                (platformTotalFirstInvestments *
                                  getLTVMultiplier)
                            ).format('0[.]0%'),
                          },
                          {
                            label: 'User -> Investor Conv. Ratio',
                            value: numeral(
                              category.investorCount / category.userCount
                            ).format('0[.]0%'),
                          },
                        ];

                        const ltv =
                          category.ltvToCacData.avgLFDI *
                          lsTotalFirstInvestments *
                          category.ltvToCacData.ltvMultiplier;

                        return (
                          <TableRow
                            onClick={() => {
                              setSelectedMonthlyLtvCacChartData(
                                category.ltvToCacData.monthlyLtvCacChartData
                              );
                              setSelectedLTVMultiplier(
                                category.ltvToCacData.ltvMultiplier
                              );
                              setSelectedAvgLFDI(category.ltvToCacData.avgLFDI);
                            }}
                            selected={
                              selectedMonthlyLtvCacChartData ===
                              category.ltvToCacData.monthlyLtvCacChartData
                            }
                            key={`lead-source-category-row-${category.id}`}
                            hover
                          >
                            <TableCell component="th" scope="row">
                              {category.name}
                            </TableCell>
                            <TableCell component="th" scope="row">
                              {numeral(lsTotalMarketingSpend).format('$0,0.00')}
                            </TableCell>
                            <TableCell align="right">
                              {numeral(lsTotalFirstInvestments).format(
                                '$0,0.00'
                              )}
                            </TableCell>
                            <TableCell align="right">
                              {numeral(
                                category.ltvToCacData.ltvMultiplier
                              ).format('0,0.000')}
                            </TableCell>
                            <TableCell align="right">
                              {numeral(
                                lsTotalFirstInvestments *
                                  category.ltvToCacData.ltvMultiplier
                              ).format('$0,0.00')}
                            </TableCell>
                            <TableCell align="right">
                              {numeral(category.ltvToCacData.avgLFDI).format(
                                '0,0.000'
                              )}
                            </TableCell>
                            <TableCell align="right">
                              {numeral(ltv).format('$0,0.00')}
                            </TableCell>
                            <TableCell align="right">
                              {!lsTotalMarketingSpend && ltv ? (
                                <span>&infin;</span>
                              ) : (
                                <span
                                  style={{
                                    color: getLTVColor(
                                      ltv / lsTotalMarketingSpend
                                    ),
                                    fontWeight: 'bold',
                                  }}
                                >
                                  {numeral(ltv / lsTotalMarketingSpend).format(
                                    '0,0.00'
                                  )}
                                </span>
                              )}
                            </TableCell>
                            {/* <TableCell align="right">
                              {lsTotalMarketingSpend === 0
                                ? null
                                : category.ltvToCacData.paybackPeriodMonths > 0
                                ? numeral(
                                    category.ltvToCacData.paybackPeriodMonths /
                                      12
                                  ).format('0,0[.]0')
                                : '25+'}
                            </TableCell> */}
                            <TableCell align="right" style={{ padding: 0 }}>
                              <Tooltip
                                arrow
                                title={
                                  <TableContainer>
                                    <Table
                                      size="small"
                                      aria-label="a dense table"
                                    >
                                      <TableBody>
                                        {dataPoints.map((dataPoint) => (
                                          <TableRow
                                            key={`lead-source-${category.id}-data-point-key-${dataPoint.label}`}
                                          >
                                            <TableCell
                                              component="th"
                                              scope="row"
                                              style={{ width: '400px' }}
                                            >
                                              <Typography
                                                variant="body2"
                                                style={{ color: '#fff' }}
                                              >
                                                {dataPoint.label}
                                              </Typography>
                                            </TableCell>
                                            <TableCell
                                              component="th"
                                              scope="row"
                                              align="right"
                                            >
                                              <Typography
                                                variant="body2"
                                                style={{ color: '#fff' }}
                                              >
                                                {dataPoint.value}
                                              </Typography>
                                            </TableCell>
                                          </TableRow>
                                        ))}
                                      </TableBody>
                                    </Table>
                                  </TableContainer>
                                }
                              >
                                <IconButton size="large">
                                  <Subject />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </Grid>
        ) : (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Skeleton
                animation="wave"
                variant="rectangular"
                height="2em"
                width="6rem"
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton animation="wave" variant="rectangular" height="16em" />
            </Grid>
            <Grid item xs={12}>
              <Skeleton
                animation="wave"
                variant="rectangular"
                height="2em"
                width="10rem"
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton animation="wave" variant="rectangular" height="40em" />
            </Grid>
          </Grid>
        )}
        <Grid
          container
          style={{ width: '100%' }}
          justifyContent="space-evenly"
          spacing={1}
        >
          <Divider
            style={{
              width: '100%',
              marginTop: '2em',
              marginBottom: '2em',
            }}
          />
          <Grid item xs={12}>
            <Typography variant="h5">Auto-Investing</Typography>
          </Grid>
          <Grid xs={12}>
            <Typography
              variant="h6"
              style={{ fontWeight: 'bold', marginTop: '1rem' }}
            >
              Scheduled Auto Investments (for the next month)
            </Typography>
            {upcomingInvestmentData ? (
              <Table aria-label="upcoming investment table" size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Portfolio</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Amount</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {upcomingInvestmentData.allPortfolios
                    .filter(
                      (portfolio) =>
                        portfolio.autoInvestSubscriptionMonthlyTotal > 0
                    )
                    .map((portfolio) => {
                      return (
                        <TableRow key={`portfolio-auto-invest-${portfolio.id}`}>
                          <TableCell component="th" scope="row">
                            <Typography
                              variant="body2"
                              component={Link}
                              to={`/Portfolio/${portfolio.id}`}
                            >
                              {portfolio.name}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            {numeral(
                              portfolio.autoInvestSubscriptionMonthlyTotal
                            ).format('$0,0.00')}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  <TableRow>
                    <TableCell component="th" scope="row">
                      <b>Total Upcoming Auto Investments</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>
                        {numeral(
                          upcomingInvestmentData.allPortfolios.reduce(
                            (a, b) =>
                              a +
                              (parseFloat(
                                b.autoInvestSubscriptionMonthlyTotal
                              ) || 0),
                            0
                          )
                        ).format('$0,0.00')}
                      </b>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            ) : (
              <CircularProgress />
            )}
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant="h6"
              style={{ fontWeight: 'bold', marginTop: '1rem' }}
            >
              Percentage of Investors Auto-Investing
            </Typography>
          </Grid>
          <Grid item xs={12}>
            {getMonthlyPercentageAutoInvesting ? (
              <Line
                height={300}
                data={{
                  labels: getMonthlyPercentageAutoInvesting.map((point) =>
                    moment(point.month, 'MMM YYYY')
                  ),
                  datasets: [
                    {
                      label: '% investors auto-investing',
                      data: getMonthlyPercentageAutoInvesting.map(
                        (point) => point.percentOfInvestors * 100
                      ),
                      borderColor: theme.palette.primary.main,
                      pointRadius: 0,
                      borderWidth: 3,
                      tension: 0.5,
                      fill: true,
                      backgroundColor: 'rgba(21, 48, 76, 0.3)',
                    },
                    {
                      type: 'bar',
                      label: 'Total auto-invested',
                      data: getMonthlyPercentageAutoInvesting.map(
                        (point) => point.totalAutoInvested
                      ),
                      borderColor: theme.palette.green.main,
                      pointRadius: 0,
                      borderWidth: 3,
                      tension: 0.5,
                      fill: true,
                      yAxisID: 'yTotals',
                      backgroundColor: 'rgba(129, 190, 55, 0.3)',
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        label: (tooltipItem) =>
                          `${tooltipItem.dataset.label}: ${tooltipItem.formattedValue}`,
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MMM YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0[.]0')}%`,
                      },
                    },
                    yTotals: {
                      position: 'right',
                      ticks: {
                        callback: (value) => numeral(value).format('$0,0[.]00'),
                      },
                      grid: {
                        display: false,
                      },
                      title: {
                        display: true,
                        text: 'Total Auto-Invested',
                      },
                    },
                  },
                }}
              />
            ) : (
              <CircularProgress />
            )}
          </Grid>
        </Grid>
        <Grid
          container
          style={{ width: '100%' }}
          justifyContent="space-evenly"
          spacing={1}
        >
          <Divider
            style={{
              width: '100%',
              marginTop: '2em',
              marginBottom: '2em',
            }}
          />
          <Grid item xs={12}>
            <Typography variant="h5">Dividend Reinvesting</Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant="h6"
              style={{ fontWeight: 'bold', marginTop: '1rem' }}
            >
              Projected Dividend Reinvestments (for the next month)
            </Typography>
            {upcomingInvestmentData ? (
              <Table
                aria-label="projected dividend reinvestment table"
                size="small"
              >
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Portfolio</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Est. Next Date</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Flagged to Reinvest</b>
                    </TableCell>
                    <TableCell align="right">
                      <b>Amount</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {upcomingInvestmentData.allPortfolios
                    .filter(
                      (portfolio) =>
                        portfolio
                          ?.latestMonthlyPortfolioFinancialProjectionForThisMonth
                          ?.grossCafd
                    )
                    .map((portfolio) => {
                      const percentageFlaggedToReinvest =
                        portfolio.sharesFlaggedForAutoReinvest /
                        portfolio.currentEquityData.ownedShares;
                      return (
                        <TableRow
                          key={`portfolio-auto-reinvestment-${portfolio.id}`}
                        >
                          <TableCell component="th" scope="row">
                            <Typography
                              variant="body2"
                              component={Link}
                              to={`/Portfolio/${portfolio.id}`}
                            >
                              {portfolio.name}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            {new Date(
                              portfolio?.latestMonthlyPortfolioFinancialProjectionForThisMonth?.effectiveDt
                            ).toLocaleDateString()}
                          </TableCell>
                          <TableCell align="right">
                            {numeral(percentageFlaggedToReinvest).format(
                              '%0.00'
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {numeral(
                              percentageFlaggedToReinvest *
                                portfolio
                                  ?.latestMonthlyPortfolioFinancialProjectionForThisMonth
                                  ?.grossCafd || 0
                            ).format('$0,0.00')}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  <TableRow>
                    <TableCell component="th" scope="row">
                      <b>Total Projected Dividend Reinvestments</b>
                    </TableCell>
                    <TableCell />
                    <TableCell />
                    <TableCell align="right">
                      <b>
                        {numeral(
                          upcomingInvestmentData.allPortfolios.reduce(
                            (a, b) => {
                              const percentageFlaggedToReinvest =
                                b.sharesFlaggedForAutoReinvest /
                                b.currentEquityData.ownedShares;
                              return (
                                a +
                                (percentageFlaggedToReinvest *
                                  b
                                    ?.latestMonthlyPortfolioFinancialProjectionForThisMonth
                                    ?.grossCafd || 0)
                              );
                            },
                            0
                          )
                        ).format('$0,0.00')}
                      </b>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            ) : (
              <CircularProgress />
            )}
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant="h6"
              style={{ fontWeight: 'bold', marginTop: '1rem' }}
            >
              Percentage of Investors Reinvesting Dividends
            </Typography>
          </Grid>
          <Grid item xs={12}>
            {getMonthlyPercentageReinvestingDividends ? (
              <Line
                height={300}
                data={{
                  labels: getMonthlyPercentageReinvestingDividends.map(
                    (point) => moment(point.month, 'MMM YYYY')
                  ),
                  datasets: [
                    {
                      label: '% investors reinvesting',
                      data: getMonthlyPercentageReinvestingDividends.map(
                        (point) => point.percentOfInvestors * 100
                      ),
                      borderColor: theme.palette.primary.main,
                      pointRadius: 0,
                      borderWidth: 3,
                      tension: 0.5,
                      fill: true,
                      backgroundColor: 'rgba(21, 48, 76, 0.3)',
                    },
                    {
                      type: 'bar',
                      label: 'Total reinvested',
                      data: getMonthlyPercentageReinvestingDividends.map(
                        (point) =>
                          point.totalReinvestedPerPortfolio?.reduce(
                            (a, b) => a + b.totalReinvested,
                            0
                          ) || 0
                      ),
                      borderColor: theme.palette.green.main,
                      pointRadius: 0,
                      borderWidth: 3,
                      tension: 0.5,
                      fill: true,
                      yAxisID: 'yTotals',
                      backgroundColor: 'rgba(129, 190, 55, 0.3)',
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        label: (tooltipItem) => {
                          if (tooltipItem?.dataset?.type === 'bar') {
                            return [
                              `${tooltipItem.dataset.label}: ${numeral(
                                tooltipItem.raw
                              ).format('$0,0[.]00')}`,
                              ...(getMonthlyPercentageReinvestingDividends[
                                parseInt(tooltipItem.dataIndex)
                              ]?.totalReinvestedPerPortfolio?.map(
                                (portfolioTotal) =>
                                  `${portfolioTotal.portfolioName}: ${numeral(
                                    portfolioTotal.totalReinvested
                                  ).format('$0,0[.]00')}`
                              ) || []),
                            ];
                          }
                          return `${tooltipItem.dataset.label}: ${numeral(
                            tooltipItem.formattedValue
                          ).format('0[.]0')}%`;
                        },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MMM YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      ticks: {
                        callback: (value) =>
                          `${numeral(value).format('0[.]0')}%`,
                      },
                    },
                    yTotals: {
                      position: 'right',
                      ticks: {
                        callback: (value) => numeral(value).format('$0,0[.]00'),
                      },
                      grid: {
                        display: false,
                      },
                      title: {
                        display: true,
                        text: 'Total Reinvested',
                      },
                    },
                  },
                }}
              />
            ) : (
              <CircularProgress />
            )}
          </Grid>
        </Grid>
        <Grid
          container
          style={{ width: '100%' }}
          justifyContent="space-evenly"
          spacing={1}
        >
          <Divider
            style={{
              width: '100%',
              marginTop: '2em',
              marginBottom: '2em',
            }}
          />
          <Grid item xs={12}>
            <Typography variant="h5">Daily Active Users</Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant="body2"
              style={{ fontStyle: 'italic' }}
              gutterBottom
            >
              This is calculated by counting the unique users who log in to
              energea.com each day
            </Typography>
          </Grid>
          <Grid item xs={12}>
            {dailyActiveUserData ? (
              <Bar
                height={300}
                data={{
                  labels: dailyActiveUserData.map((point) => point.date),
                  datasets: [
                    {
                      label: 'Daily Active Users',
                      data: dailyActiveUserData.map((point) => point.count),
                      borderColor: theme.palette.green.main,
                      pointRadius: 0,
                      borderWidth: 3,
                      tension: 0.5,
                      fill: true,
                      backgroundColor: 'rgba(129, 190, 55, 0.3)',
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        // label: (tooltipItem) => {
                        //   if (tooltipItem?.dataset?.type === 'bar') {
                        //     return [
                        //       `${tooltipItem.dataset.label}: ${numeral(
                        //         tooltipItem.raw
                        //       ).format('$0,0[.]00')}`,
                        //       ...getMonthlyPercentageReinvestingDividends[
                        //         parseInt(tooltipItem.dataIndex)
                        //       ]?.totalReinvestedPerPortfolio.map(
                        //         (portfolioTotal) =>
                        //           `${portfolioTotal.portfolioName}: ${numeral(
                        //             portfolioTotal.totalReinvested
                        //           ).format('$0,0[.]00')}`
                        //       ),
                        //     ];
                        //   }
                        //   return `${tooltipItem.dataset.label}: ${numeral(
                        //     tooltipItem.formattedValue
                        //   ).format('0[.]0')}%`;
                        // },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'ddd. MMM D, YYYY',
                        unit: 'day',
                      },
                    },
                    y: {
                      ticks: {
                        callback: (value) => numeral(value).format('0,0'),
                      },
                    },
                  },
                }}
              />
            ) : (
              <CircularProgress />
            )}
          </Grid>
        </Grid>
        <Grid>
          <Divider
            style={{
              width: '100%',
              marginTop: '2em',
              marginBottom: '2em',
            }}
          />
          <Grid
            container
            justifyContent="space-between"
            style={{ width: '100%', marginTop: '2rem' }}
          >
            <Grid item xs={12}>
              <Typography gutterBottom variant="h5">
                User Demographics
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Card>
                <Grid container style={{ padding: '1rem' }} spacing={2}>
                  <Grid item>
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="investor-status-select-label">
                        Users or Investors
                      </InputLabel>
                      <Select
                        label="investor-only-filter"
                        value={userDemographicsInvestorsOnly}
                        onChange={(event) => {
                          const val = event.target.value;
                          if (userDemographicsInvestorsOnly !== val) {
                            fetchDemographicsData({
                              investorsOnly: val,
                            });
                          }
                          setUserDemographicsInvestorsOnly(val);
                        }}
                        style={{ width: '200px' }}
                      >
                        <MenuItem value={true}>Investors Only</MenuItem>
                        <MenuItem value={false}>All Users</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item>
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="accredited-status-select-label">
                        Accredited Status
                      </InputLabel>
                      <Select
                        label="accredited-only-filter"
                        value={userDemographicsAccreditedFilter}
                        onChange={(event) => {
                          const val = event.target.value;
                          if (userDemographicsAccreditedFilter !== val) {
                            fetchDemographicsData({
                              accreditedFilter: val,
                            });
                          }
                          setUserDemographicsAccreditedFilter(val);
                        }}
                        style={{ width: '200px' }}
                      >
                        <MenuItem value="allInvestors">Any</MenuItem>
                        <MenuItem value="accreditedOnly">
                          Accredited Only
                        </MenuItem>
                        <MenuItem value="unaccreditedOnly">
                          Unaccredited Only
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item>
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="accredited-status-select-label">
                        Env. vs. Returns Focused
                      </InputLabel>
                      <Select
                        label="environmentally-or-returns-focused-filter"
                        value={userDemographicsEnvironmentallyFocusedFilter}
                        onChange={(event) => {
                          const val = event.target.value;
                          if (
                            userDemographicsEnvironmentallyFocusedFilter !== val
                          ) {
                            fetchDemographicsData({
                              environmentallyFocusedFilter: val,
                            });
                          }
                          setUserDemographicsEnvironmentallyFocusedFilter(val);
                        }}
                        style={{ width: '200px' }}
                      >
                        <MenuItem value="all">Any</MenuItem>
                        <MenuItem value="environment">
                          Environmentally Focused
                        </MenuItem>
                        <MenuItem value="returns">Returns Focused</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item>
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="total-invested-range-min-select-label">
                        Min Invested
                      </InputLabel>
                      <Select
                        label="total-invested-range-min-filter"
                        value={userDemographicsTotalInvestedRangeMin}
                        onChange={(event) => {
                          const val = event.target.value;
                          if (userDemographicsTotalInvestedRangeMin !== val) {
                            fetchDemographicsData({
                              totalInvestedMin: val,
                            });
                          }
                          setUserDemographicsTotalInvestedRangeMin(val);
                        }}
                        style={{ width: '200px' }}
                      >
                        <MenuItem value={0}>No Min</MenuItem>
                        <MenuItem
                          value={100}
                          disabled={userDemographicsTotalInvestedRangeMax < 100}
                        >
                          100
                        </MenuItem>
                        <MenuItem
                          value={1000}
                          disabled={
                            userDemographicsTotalInvestedRangeMax < 1000
                          }
                        >
                          1,000
                        </MenuItem>
                        <MenuItem
                          value={5000}
                          disabled={
                            userDemographicsTotalInvestedRangeMax < 5000
                          }
                        >
                          5,000
                        </MenuItem>
                        <MenuItem
                          value={10000}
                          disabled={
                            userDemographicsTotalInvestedRangeMax < 10000
                          }
                        >
                          10,000
                        </MenuItem>
                        <MenuItem
                          value={25000}
                          disabled={
                            userDemographicsTotalInvestedRangeMax < 25000
                          }
                        >
                          25,000
                        </MenuItem>
                        <MenuItem
                          value={50000}
                          disabled={
                            userDemographicsTotalInvestedRangeMax < 50000
                          }
                        >
                          50,000
                        </MenuItem>
                        <MenuItem
                          value={100000}
                          disabled={
                            userDemographicsTotalInvestedRangeMax < 100000
                          }
                        >
                          100,000
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item>
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="total-invested-range-max-select-label">
                        Max Invested
                      </InputLabel>
                      <Select
                        label="total-invested-range-max-filter"
                        value={userDemographicsTotalInvestedRangeMax}
                        onChange={(event) => {
                          const val = event.target.value;
                          if (userDemographicsTotalInvestedRangeMax !== val) {
                            fetchDemographicsData({
                              totalInvestedMax: val,
                            });
                          }
                          setUserDemographicsTotalInvestedRangeMax(val);
                        }}
                        style={{ width: '200px' }}
                      >
                        <MenuItem
                          value={100}
                          disabled={userDemographicsTotalInvestedRangeMin > 100}
                        >
                          100
                        </MenuItem>
                        <MenuItem
                          value={1000}
                          disabled={
                            userDemographicsTotalInvestedRangeMin > 1000
                          }
                        >
                          1,000
                        </MenuItem>
                        <MenuItem
                          value={5000}
                          disabled={
                            userDemographicsTotalInvestedRangeMin > 5000
                          }
                        >
                          5,000
                        </MenuItem>
                        <MenuItem
                          value={10000}
                          disabled={
                            userDemographicsTotalInvestedRangeMin > 10000
                          }
                        >
                          10,000
                        </MenuItem>
                        <MenuItem
                          value={25000}
                          disabled={
                            userDemographicsTotalInvestedRangeMin > 25000
                          }
                        >
                          25,000
                        </MenuItem>
                        <MenuItem
                          value={50000}
                          disabled={
                            userDemographicsTotalInvestedRangeMin > 50000
                          }
                        >
                          50,000
                        </MenuItem>
                        <MenuItem
                          value={100000}
                          disabled={
                            userDemographicsTotalInvestedRangeMin > 100000
                          }
                        >
                          100,000
                        </MenuItem>
                        <MenuItem
                          value={demographicsMapUserInvestedMaxDefaultValue}
                        >
                          No Max
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                <UserDemographicsMap
                  users={demographicsData?.users}
                  loading={demographicsLoading}
                />
                <Grid item xs={12}>
                  {demographicsLoading || !demographicsData?.users ? (
                    <Grid
                      container
                      alignItems="center"
                      justifyContent="center"
                      style={{ minHeight: '400px' }}
                    >
                      <CircularProgress />
                    </Grid>
                  ) : (
                    <>
                      <Grid item>
                        <Scatter
                          height={300}
                          data={{
                            datasets: [
                              {
                                backgroundColor: 'rgba(21, 48, 76, 0.5)',
                                data: demographicsData.users.map((u) => {
                                  let x = 0;
                                  if (userDemographicsScatterXAxis === 'age') {
                                    x = moment().diff(
                                      moment(u.dateOfBirth),
                                      'years'
                                    );
                                  } else if (
                                    userDemographicsScatterXAxis ===
                                    'environmentVsReturns'
                                  ) {
                                    x = u.environmentalImpactVsReturns;
                                  }
                                  let y = 0;
                                  if (
                                    userDemographicsScatterYAxis ===
                                    'totalInvested'
                                  ) {
                                    y = u.investmentSum;
                                  } else if (
                                    userDemographicsScatterYAxis ===
                                    'remainingAnnualInvestment'
                                  ) {
                                    y = u.currentYearRemainingInvestmentMax;
                                  }
                                  return {
                                    x,
                                    y,
                                    user: u.fullName,
                                  };
                                }),
                              },
                            ],
                          }}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { display: false },
                              tooltip: {
                                mode: 'nearest',
                                intersect: false,
                                callbacks: {
                                  label: (tooltipItem) => {
                                    const xLabel =
                                      userDemographicsScatterXAxis === 'age'
                                        ? 'Age'
                                        : 'Environmental Impact vs Returns';
                                    const yLabel =
                                      userDemographicsScatterYAxis ===
                                      'totalInvested'
                                        ? 'Total Invested'
                                        : ' Max Remaining Annual Investment';
                                    return [
                                      `User: ${tooltipItem.raw.user}`,
                                      `${xLabel}: ${tooltipItem.parsed.x}`,
                                      `${yLabel}: ${numeral(
                                        tooltipItem.parsed.y
                                      ).format('$0,0.00')}`,
                                    ];
                                  },
                                },
                              },
                            },
                            scales: {
                              y: {
                                type: 'logarithmic',
                              },
                            },
                          }}
                        />
                      </Grid>
                      <Grid
                        container
                        justifyContent="center"
                        alignItems="end"
                        style={{ marginBottom: '1rem' }}
                      >
                        <Grid item>
                          <FormControl variant="standard" fullWidth>
                            <InputLabel id="scatter-y-axis-select-label">
                              Y-Axis
                            </InputLabel>
                            <Select
                              label="scatter-y-axis-data"
                              value={userDemographicsScatterYAxis}
                              onChange={(event) => {
                                const val = event.target.value;
                                setUserDemographicsScatterYAxis(val);
                              }}
                              style={{ width: '200px' }}
                            >
                              <MenuItem value="totalInvested">
                                Total Invested
                              </MenuItem>
                              <MenuItem value="remainingAnnualInvestment">
                                Max Remaining Annual Investment (unaccredited
                                only)
                              </MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item style={{ padding: '0 1rem' }}>
                          by
                        </Grid>
                        <Grid item>
                          <FormControl variant="standard" fullWidth>
                            <InputLabel id="scatter-x-axis-select-label">
                              X-Axis
                            </InputLabel>
                            <Select
                              label="scatter-x-axis-data"
                              value={userDemographicsScatterXAxis}
                              onChange={(event) => {
                                const val = event.target.value;
                                setUserDemographicsScatterXAxis(val);
                              }}
                              style={{ width: '200px' }}
                            >
                              <MenuItem value="age">Investor Age</MenuItem>
                              <MenuItem value="environmentVsReturns">
                                Environmental (0) vs Returns (100)
                              </MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </>
                  )}
                </Grid>
                <Grid container alignContent="flex-end">
                  <Grid item lg={3} md={4} sm={6} xs={12}>
                    <Card
                      elevation={0}
                      style={{
                        height: '14rem',
                        boxSizing: 'border-box',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="center"
                          alignItems="center"
                          item
                          xs={12}
                        >
                          <Grid item>
                            <Typography
                              style={{
                                color: 'rgba(0,0,0,.6)',
                              }}
                              variant="body1"
                            >
                              Environmental Impact (Green) vs Returns (Red)
                            </Typography>
                          </Grid>
                          <Grid
                            style={{
                              marginTop: '1rem',
                            }}
                            item
                          >
                            <Grid
                              container
                              alignItems="center"
                              style={{ position: 'relative' }}
                            >
                              {demographicsLoading ? (
                                <CircularProgress />
                              ) : (
                                <GaugeChart
                                  id="gauge-chart2"
                                  animate={false}
                                  needleColor={theme.palette.appSecondary.main}
                                  nrOfLevels={20}
                                  textColor={theme.palette.primary.main}
                                  percent={
                                    (demographicsData?.avgEnvironmentalScore ||
                                      -1) / 100
                                  }
                                />
                              )}
                            </Grid>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item lg={3} md={4} sm={6} xs={12}>
                    <Card
                      elevation={0}
                      style={{
                        height: '14rem',
                        boxSizing: 'border-box',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="flex-end"
                          alignItems="center"
                          item
                          xs={12}
                        >
                          <Grid item>
                            <Typography
                              style={{
                                color: 'rgba(0,0,0,.6)',
                              }}
                              variant="body1"
                            >
                              Average Age
                            </Typography>
                          </Grid>
                          <Grid
                            style={{
                              marginTop: '1rem',
                            }}
                            item
                          >
                            <Grid
                              container
                              alignItems="center"
                              style={{ position: 'relative' }}
                            >
                              <Grid item>
                                {demographicsLoading ? (
                                  <CircularProgress />
                                ) : (
                                  <Typography
                                    style={{
                                      fontWeight: 'bold',
                                      textAlign: 'center',
                                      color: theme.palette.green.main,
                                    }}
                                    variant="h1"
                                  >
                                    {Math.round(demographicsData?.avgAge || 0)}
                                  </Typography>
                                )}
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item lg={3} md={4} sm={6} xs={12}>
                    <Card
                      elevation={0}
                      style={{
                        height: '14rem',
                        boxSizing: 'border-box',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="flex-end"
                          alignItems="center"
                          item
                          xs={12}
                        >
                          <Grid item>
                            <Typography
                              style={{
                                color: 'rgba(0,0,0,.6)',
                              }}
                              variant="body1"
                            >
                              Top States (
                              {userDemographicsInvestorsOnly
                                ? 'Investors'
                                : 'Users'}
                              )
                            </Typography>
                          </Grid>
                          <Grid
                            style={{
                              marginTop: '1rem',
                            }}
                            item
                          >
                            <Grid
                              container
                              alignItems="center"
                              style={{ position: 'relative' }}
                            >
                              {demographicsLoading ? (
                                <CircularProgress />
                              ) : (
                                <TableContainer>
                                  <Table
                                    padding="none"
                                    size="small"
                                    aria-label="simple table"
                                  >
                                    <TableBody>
                                      {demographicsData?.topStates?.map(
                                        (row) => (
                                          <TableRow
                                            key={row.state}
                                            sx={{
                                              '&:last-child td, &:last-child th':
                                                {
                                                  border: 0,
                                                },
                                            }}
                                          >
                                            <TableCell
                                              style={{ padding: '.1rem 1rem' }}
                                              component="th"
                                              scope="row"
                                            >
                                              <b>{row.state}</b>
                                            </TableCell>
                                            <TableCell
                                              style={{ padding: '.1rem 1rem' }}
                                              align="right"
                                            >
                                              <b>
                                                {numeral(row.userCount).format(
                                                  '0,0'
                                                )}
                                              </b>
                                            </TableCell>
                                            <TableCell
                                              style={{ padding: '.1rem 1rem' }}
                                              align="right"
                                            >
                                              <b>
                                                {numeral(
                                                  row.totalInvested
                                                ).format('$0,0')}
                                              </b>
                                            </TableCell>
                                          </TableRow>
                                        )
                                      )}
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              )}
                            </Grid>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item lg={3} md={4} sm={6} xs={12}>
                    <Card
                      elevation={0}
                      style={{
                        height: '14rem',
                        boxSizing: 'border-box',
                      }}
                    >
                      <CardContent>
                        <Grid
                          container
                          direction="column"
                          justifyContent="flex-end"
                          alignItems="center"
                          item
                          xs={12}
                        >
                          <Grid item>
                            <Typography
                              style={{
                                color: 'rgba(0,0,0,.6)',
                              }}
                              variant="body1"
                            >
                              Accredited vs Unaccredited
                            </Typography>
                          </Grid>
                          <Grid
                            style={{
                              marginTop: '1rem',
                            }}
                            item
                          >
                            <Grid
                              container
                              alignItems="center"
                              style={{ position: 'relative' }}
                            >
                              {demographicsLoading ? (
                                <CircularProgress />
                              ) : (
                                <TableContainer>
                                  <Table
                                    padding="none"
                                    size="small"
                                    aria-label="simple table"
                                  >
                                    <TableBody>
                                      <TableRow
                                        key="accredited-investor-count"
                                        sx={{
                                          '&:last-child td, &:last-child th': {
                                            border: 0,
                                          },
                                        }}
                                      >
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          component="th"
                                          scope="row"
                                        >
                                          <b>
                                            Accredited{' '}
                                            {userDemographicsInvestorsOnly
                                              ? 'Investors'
                                              : 'Users'}
                                          </b>
                                        </TableCell>
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          align="right"
                                        >
                                          <b>
                                            {
                                              demographicsData?.accreditedInvestorCount
                                            }
                                          </b>
                                        </TableCell>
                                      </TableRow>
                                      <TableRow
                                        key="accredited-total-invested"
                                        sx={{
                                          '&:last-child td, &:last-child th': {
                                            border: 0,
                                          },
                                        }}
                                      >
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          component="th"
                                          scope="row"
                                        >
                                          <b>Accredited Total Invested</b>
                                        </TableCell>
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          align="right"
                                        >
                                          <b>
                                            {numeral(
                                              demographicsData?.accreditedTotalInvested
                                            ).format('$0,0')}
                                          </b>
                                        </TableCell>
                                      </TableRow>
                                      <TableRow
                                        key="unaccredited-investor-count"
                                        sx={{
                                          '&:last-child td, &:last-child th': {
                                            border: 0,
                                          },
                                        }}
                                      >
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          component="th"
                                          scope="row"
                                        >
                                          <b>
                                            Unaccredited{' '}
                                            {userDemographicsInvestorsOnly
                                              ? 'Investors'
                                              : 'Users'}
                                          </b>
                                        </TableCell>
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          align="right"
                                        >
                                          <b>
                                            {
                                              demographicsData?.unaccreditedInvestorCount
                                            }
                                          </b>
                                        </TableCell>
                                      </TableRow>
                                      <TableRow
                                        key="unaccredited-total-invested"
                                        sx={{
                                          '&:last-child td, &:last-child th': {
                                            border: 0,
                                          },
                                        }}
                                      >
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          component="th"
                                          scope="row"
                                        >
                                          <b>Unaccredited Total Invested</b>
                                        </TableCell>
                                        <TableCell
                                          style={{ padding: '.1rem 1rem' }}
                                          align="right"
                                        >
                                          <b>
                                            {numeral(
                                              demographicsData?.unaccreditedTotalInvested
                                            ).format('$0,0')}
                                          </b>
                                        </TableCell>
                                      </TableRow>
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              )}
                            </Grid>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Card>
            </Grid>
          </Grid>
        </Grid>
        <Divider style={{ width: '100%', margin: '2rem 0' }} />
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
          style={{ marginTop: '1rem' }}
        >
          <Typography gutterBottom variant="h6">
            Median LTV Multiplier by User Initial Investment Range
          </Typography>
          <Grid item>
            <ToggleButtonGroup
              value={ltvMultiplierShowData}
              exclusive
              onChange={(event) => {
                const { value } = event.currentTarget;
                setLtvMultiplierShowData(value);
              }}
              aria-label="show user ltv data"
            >
              <ToggleButton value="ltvMultiplier" aria-label="ltv multipliers">
                LTV Multipliers
              </ToggleButton>
              <ToggleButton
                value="userData"
                aria-label="show user investment totals"
              >
                Individual Users
              </ToggleButton>
            </ToggleButtonGroup>
          </Grid>
        </Grid>
        <Grid item xs={12}>
          {ltvMultiplierShowData === 'userData' ? (
            <Scatter
              height={300}
              data={{
                datasets: [
                  {
                    backgroundColor: 'rgba(21, 48, 76, 0.5)',
                    data: initialToTotalInvestedData,
                  },
                ],
              }}
              options={{
                maintainAspectRatio: false,
                plugins: {
                  legend: { display: false },
                  tooltip: {
                    mode: 'nearest',
                    intersect: false,
                    callbacks: {
                      label: (tooltipItem) => {
                        return [
                          `User: ${
                            initialToTotalInvestedData[tooltipItem.dataIndex]
                              .user.fullName
                          }`,
                          `First Investment: ${numeral(
                            tooltipItem.parsed.x
                          ).format('$0,0.00')}`,
                          `Total Invested: ${numeral(
                            tooltipItem.parsed.y
                          ).format('$0,0.00')}`,
                          `LTV Multiplier: ${numeral(
                            tooltipItem.parsed.y / tooltipItem.parsed.x
                          ).format('0,0.0')}`,
                        ];
                      },
                    },
                  },
                },
                scales: {
                  x: {
                    display: true,
                    type: 'logarithmic',
                    title: {
                      display: true,
                      text: 'First Investment Amount',
                    },
                  },
                  y: {
                    display: true,
                    type: 'logarithmic',
                    title: {
                      display: true,
                      text: 'Total Invested (including reinvestments)',
                    },
                  },
                },
              }}
            />
          ) : (
            <Bar
              height={300}
              data={{
                datasets: [
                  {
                    backgroundColor: theme.palette.primary.main,
                    data: ltvMultiplierChartData,
                  },
                ],
              }}
              options={{
                maintainAspectRatio: false,
                plugins: {
                  legend: { display: false },
                  tooltip: {
                    mode: 'nearest',
                    intersect: false,
                    callbacks: {
                      label: (tooltipItem) => {
                        return [
                          `Median LTV Multiplier: ${numeral(
                            tooltipItem.formattedValue
                          ).format('0,0.00')}`,
                          `Initial Investment Sum: ${numeral(
                            ltvMultiplierBuckets[tooltipItem.label].init
                          ).format('$0,0')}`,
                          `Total Investment Sum: ${numeral(
                            ltvMultiplierBuckets[tooltipItem.label].total
                          ).format('$0,0')}`,
                          `Investors: ${numeral(
                            ltvMultiplierBuckets[tooltipItem.label].count
                          ).format('0,0')}`,
                        ];
                      },
                    },
                  },
                },
                scales: {
                  x: {
                    title: {
                      display: true,
                      text: 'Initial Investment Range',
                    },
                  },
                  y: {
                    title: {
                      display: true,
                      text: 'LTV Multiplier',
                    },
                  },
                },
              }}
            />
          )}
        </Grid>
        <Grid
          container
          style={{ width: '100%', paddingTop: '2rem' }}
          justifyContent="space-evenly"
          spacing={1}
        >
          <Grid item xs={12}>
            <Grid container>
              <Grid item>
                <Typography variant="h5">
                  Median Days through Investor Funnel
                </Typography>
              </Grid>
            </Grid>
          </Grid>
          {getDaysToVerifiedAndFirstInvestmentChartData ? (
            <Grid item xs={12}>
              <Line
                height={300}
                data={{
                  labels: getDaysToVerifiedAndFirstInvestmentChartData.map(
                    (point) => moment(point.date, 'MM-DD-YYYY')
                  ),
                  datasets: [
                    {
                      label: 'Avg. Hrs to Verified',
                      data: getDaysToVerifiedAndFirstInvestmentChartData.map(
                        (point) => point.avgDaysToVerified * 24
                      ),
                      borderColor: theme.palette.primary.main,
                      pointRadius: 0,
                      tension: 1,
                    },
                    {
                      label: 'Avg. Hrs Verified to First Investment',
                      data: getDaysToVerifiedAndFirstInvestmentChartData.map(
                        (point) => point.avgDaysVerifiedToFirstInvestment * 24
                      ),
                      borderColor: theme.palette.green.main,
                      pointRadius: 0,
                      tension: 1,
                    },
                    {
                      label: 'Avg. Hrs to First Investment',
                      data: getDaysToVerifiedAndFirstInvestmentChartData.map(
                        (point) => point.avgDaysToFirstInvestment * 24
                      ),
                      borderColor: theme.palette.warning.main,
                      pointRadius: 0,
                      tension: 1,
                    },
                    {
                      label: '# Verified Users',
                      data: getDaysToVerifiedAndFirstInvestmentChartData.map(
                        (point) => point.verifiedUserCount
                      ),
                      borderRadius: 0,
                      pointRadius: 0,
                      tension: 1,
                      fill: true,
                      yAxisID: 'yCounts',
                    },
                    {
                      label: '# Invested Users',
                      data: getDaysToVerifiedAndFirstInvestmentChartData.map(
                        (point) => point.investedUserCount
                      ),
                      borderRadius: 0,
                      pointRadius: 0,
                      tension: 1,
                      fill: true,
                      yAxisID: 'yCounts',
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        label: (tooltipItem) =>
                          `${tooltipItem.dataset.label}: ${tooltipItem.formattedValue}`,
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MMM D, YYYY',
                        unit: 'month',
                      },
                    },
                    y: {
                      title: {
                        display: true,
                        text: 'Hours',
                      },
                    },
                    yCounts: {
                      position: 'right',
                      ticks: {
                        callback: (value) => numeral(value).format('0,0[.]0'),
                      },
                      grid: {
                        display: false,
                      },
                      title: {
                        display: true,
                        text: 'Users',
                      },
                    },
                  },
                }}
              />
            </Grid>
          ) : null}
        </Grid>
      </CardContent>
    </Card>
  );
});
