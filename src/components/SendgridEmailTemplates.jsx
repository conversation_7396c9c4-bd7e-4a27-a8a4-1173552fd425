import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  Edit,
  List,
  SelectArrayInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Event Email Templates';

// const instanceTitle = ({ instance }) => (
//   <span>
//     {entityName} - {instance ? `"${instance.fullName}"` : ''}
//   </span>
// );

export const SendgridEmailTemplateList = () => {
  const { permissions } = usePermissions();
  return (
    <List perPage={25} title={entityName} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" sortable={false} />
        <TextField source="sendgridTemplateId" sortable={false} />
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

const attributeChoices = [
  { name: 'firstName' },
  { name: 'lastName' },
  { name: 'email' },
  { name: 'eventBannerImageUrl' },
  { name: 'title' },
  { name: 'summary' },
  { name: 'description' },
  { name: 'portfolioUrl' },
  { name: 'taxYear' },
];
export const SendgridEmailTemplateEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput multiline source="description" fullWidth />
            <TextInput source="sendgridTemplateId" fullWidth />
            <SelectArrayInput
              source="requiredAttributes"
              fullWidth
              choices={attributeChoices}
              optionText="name"
              optionValue="name"
              options={{ fullWidth: true }}
            />
          </Grid>
        </Grid>
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </SimpleForm>
    </Edit>
  );
};

export const SendgridEmailTemplateCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
