import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Alert } from '@mui/lab';
import moment from 'moment';
import {
  <PERSON><PERSON>anField,
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  Filter,
  FunctionField,
  List,
  NumberField,
  Pagination,
  SimpleForm,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  usePermissions,
  useNotify,
  useResourceDefinition,
  useListContext,
  BooleanInput,
  useRefresh,
} from 'react-admin';
import {
  Button,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';

import {
  CheckCircle,
  CloudDownload,
  CloudUpload,
  Error,
  GetApp,
  OpenInNew,
} from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import ExcelReader from './ExcelReader';
import { CustomNumberInput, DetailField } from './CustomFields';
import ProgressTracker from './ProgressTrackers';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Potential Corporate Investor';

export const PotentialCorporateInvestorEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const refresh = useRefresh();
  const notify = useNotify();
  const [loading, setLoading] = useState(false);

  const uploadToS3 = (event) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('PDF must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `CorporateInvestors/InvestmentAgreements/${
          file.name
        }_${moment().valueOf()}`
      );
      setLoading(true);
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          dataProvider
            .update('PotentialCorporateInvestor', {
              data: {
                id: parseInt(id, 10),
                investmentAgreementAwsObjectKey: awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Document uploaded', { type: 'success' });
                setLoading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading document', { type: 'error' });
                setLoading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading document to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="firstName" fullWidth />
            <TextInput source="lastName" fullWidth />
            <TextInput source="email" fullWidth />
            <CustomNumberInput source="commitmentAmount" fullWidth />
            <TextInput source="notes" fullWidth multiline />
            <DateInput source="transferReceivedDt" fullWidth />
            <Card
              style={{
                margin: '1rem 0',
                padding: '1rem',
                backgroundColor: '#f3f3f3',
              }}
            >
              <Typography>
                <b>Investment Agreement</b>
              </Typography>
              <DateInput source="investmentAgreementSentDt" fullWidth />
              <DateInput source="investmentAgreementSignedDt" fullWidth />
              <FunctionField
                label="Investment Agreement"
                render={(record) => {
                  return (
                    <Grid container spacing={2}>
                      <Grid item>
                        <Button
                          disabled={!record.investmentAgreementDownloadUrl}
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            window.location.assign(
                              record.investmentAgreementDownloadUrl
                            );
                          }}
                        >
                          Download
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          color="primary"
                          variant="contained"
                          component="label" // https://stackoverflow.com/a/54043619
                          startIcon={<CloudUpload />}
                          style={{ textTransform: 'none' }}
                          disabled={loading}
                        >
                          {loading ? (
                            <CircularProgress
                              style={{ position: 'absolute' }}
                            />
                          ) : null}
                          {record.investmentAgreementDownloadUrl
                            ? 'Overwrite Document'
                            : 'Upload Document'}
                          <input
                            type="file"
                            hidden
                            onChange={(event) => uploadToS3(event)}
                            accept="application/pdf"
                          />
                        </Button>
                      </Grid>
                    </Grid>
                  );
                }}
              />
            </Card>
          </Grid>
        </Grid>
        {/* <DateField source="createdAt" /> */}
      </SimpleForm>
    </Edit>
  );
};

const ContactsPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);
const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      <ExportButton maxResults={100000} />
    </TopToolbar>
  );
};

const styleRow = (record) => {
  const errorStyle = {
    backgroundColor: 'rgba(255, 0, 0, 0.2)',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  const successStyle = {
    backgroundColor: '#d6ffbd',
  };
  if (
    record.transferReceivedDt &&
    record.hasInvestmentAgreement &&
    record.commitmentAmount &&
    record.investmentAgreementSignedDt &&
    record.investmentAgreementSentDt
  ) {
    return successStyle;
  }
  if (
    record.investmentAgreementSentDt &&
    (!record.investmentAgreementSignedDt ||
      !record.transferReceivedDt ||
      !record.hasInvestmentAgreement)
  ) {
    return warningStyle;
  }
  if (
    record?.user?.dwollaId &&
    !record?.user?.verifiedDt &&
    !record?.user?.investmentSum
  ) {
    return errorStyle;
  }
  return {};
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name, Last Name, or Email"
      source="q"
      alwaysOn
    />
    <BooleanInput source="ndaSigned" label="NDA Signed" defaultValue={false} />
    <BooleanInput
      source="hasContributed"
      label="Has Committed"
      defaultValue={false}
    />
  </Filter>
);

export const PotentialCorporateInvestorList = () => {
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const [raiseStatusData, setRaiseStatusData] = useState(null);

  if (!raiseStatusData) {
    dataProvider.getOne('RaiseStatus', {}).then((res) => {
      setRaiseStatusData(res.data);
    });
  }

  return (
    <>
      <Card style={{ marginTop: '2rem' }}>
        <CardContent>
          <Typography variant="h5" paragraph>
            2024 Corporate Equity Raise
          </Typography>
          <Typography>
            NDAs Signed : <b>{raiseStatusData?.getSignedNDACount || 0}</b>
          </Typography>
          <Typography>Progress to Goal:</Typography>
          <ProgressTracker
            destination={5_000_000}
            current={raiseStatusData?.getTotalRaiseCommitments || 0}
          />
          <Typography>Commitments Received:</Typography>
          <ProgressTracker
            destination={raiseStatusData?.getTotalRaiseCommitments || 0}
            current={raiseStatusData?.getTotalRaiseCommitmentsReceived || 0}
          />
        </CardContent>
      </Card>
      <List
        title="Potential Corporate Investment"
        perPage={25}
        pagination={<ContactsPagination />}
        actions={<ListActions />}
        sort={{ field: 'firstName', order: 'ASC' }}
        filters={<CustomFilter />}
      >
        <Datagrid
          rowStyle={styleRow}
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="firstName" />
          <TextField source="lastName" />
          <TextField source="email" />
          <FunctionField
            label="Is a User?"
            sortable={false}
            render={(record) =>
              !!record.user?.id ? (
                <CheckCircle style={{ color: 'green' }} />
              ) : (
                <Error style={{ color: 'red' }} />
              )
            }
          />
          <NumberField
            source="user.investmentSum"
            options={{ style: 'currency', currency: 'USD' }}
            sortable={false}
          />
          <BooleanField source="user.isAccredited" sortable={false} />
          <TextField source="user.state" sortable={false} />
          <BooleanField source="ndaSigned" sortable={false} />
          <NumberField
            source="commitmentAmount"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField source="shares" />
          {/* <TextField source="user.dwollaId" sortable={false} /> */}
          <FunctionField
            label="HubSpot Profile"
            textAlign="center"
            render={({ user }) => {
              if (!user?.hubSpotContactUrl) return null;
              return (
                <a
                  target="_blank"
                  onClick={(e) => e.stopPropagation()}
                  href={user.hubSpotContactUrl}
                >
                  <OpenInNew />
                </a>
              );
            }}
          />
          <DateField source="investmentAgreementSentDt" sortable={false} />
          <DateField source="investmentAgreementSignedDt" sortable={false} />
          <DateField source="transferReceivedDt" sortable={false} />
          <BooleanField
            source="hasInvestmentAgreement"
            label="Investment Agreement Uploaded"
            sortable={false}
          />
          <DetailField source="notes" label="Notes" />
          <DateField source="user.verifiedDt" sortable={false} />
          <DateField source="user.createdAt" sortable={false} />
        </Datagrid>
      </List>
    </>
  );
};

const attrs = [
  {
    name: 'firstName',
    format: (val) => val || '',
    label: 'First Name',
    align: 'left',
  },
  {
    name: 'lastName',
    format: (val) => val || '',
    label: 'Last Name',
    align: 'left',
  },
  {
    name: 'email',
    format: (val) => (val ? val.toLowerCase() : ''),
    label: 'Email',
    align: 'left',
  },
];

export const PotentialCorporateInvestorCreate = (props) => {
  const [data, setData] = useState(null);
  const [reviewed, setReviewed] = useState(false);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  const save = () => {
    setLoading(true);
    dataProvider
      .create('PotentialCorporateInvestorUpload', {
        data,
      })
      .then(
        (resp) => {
          notify('Data saved successfully', { type: 'success' });
          window.location.href = '/PotentialCorporateInvestor';
          setLoading(false);
        },
        (e) => {
          console.log('ERROR', e, e?.errors, e.message, e[0]);
          setLoading(false);
          notify(e?.message, {
            type: 'error',
          });
        }
      );
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell align={attr.align || 'center'}>
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  const renderSubmit = () => {
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!reviewed}
                onChange={() => setReviewed(!reviewed)}
              />
            }
            label="I have checked and the list looks good (this will only create new records and will not effect existing records.)"
          />
          <Button
            onClick={save}
            disabled={!reviewed || loading}
            variant="contained"
            size="large"
            color="secondary"
          >
            {loading ? <CircularProgress /> : 'Save'}
          </Button>
        </Alert>
      </>
    );
  };
  return (
    <Create
      title={`Create ${entityName}`}
      helperText="This can be edited at any time so no need to be perfect."
    >
      <Grid container>
        <Grid xs={12} item style={{ padding: '1em' }}>
          <Button
            component="a"
            variant="contained"
            href="/csv-templates/PotentialCorporateInvestors.xlsx"
            download
          >
            <GetApp />
            Click to download the csv template
          </Button>
        </Grid>
        <ExcelReader handleData={(resp) => setData(resp)} />
        <Grid item style={{ margin: 'auto' }}>
          {data ? renderSubmit() : null}
        </Grid>
        <Grid item xs={12}>
          {data ? renderData() : null}
        </Grid>
      </Grid>
    </Create>
  );
};
