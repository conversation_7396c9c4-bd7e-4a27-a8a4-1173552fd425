import React, { PureComponent } from 'react';
import moment from 'moment-timezone';
import 'chart.js/auto';

import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-moment';

import theme from '../theme';

class MiniProductionLineChart extends PureComponent {
  render() {
    const { portfolio, projection, lineColor } = this.props;
    const datasets = [
      {
        label: 'projection',
        data: [
          { date: moment().add(-30, 'days').format('MM-DD-YYYY'), value: 0 },
          {
            date: moment().add(-1, 'day').format('MM-DD-YYYY'), // Remove today since it may not have generation yet or will be incomplete
            value: projection,
          },
        ],
        pointRadius: 0,
      },
    ];
    const actualData = [
      { date: moment().add(-30, 'days').format('MM-DD-YYYY'), value: 0 },
    ];
    if (
      portfolio.last30EnergyProductionData &&
      portfolio.last30EnergyProductionData.length > 0
    ) {
      // Remove today since it may not have generation yet or will be incomplete
      const filteredProductionData =
        portfolio.last30EnergyProductionData.filter((day) =>
          moment(day.date, 'MM-DD-YYYY')
            .startOf('day')
            .isBefore(moment().startOf('day'))
        );
      actualData.push(...filteredProductionData);
    } else {
      actualData.push({
        date: moment().add(-1, 'day').format('MM-DD-YYYY'),
        value: 0,
      });
    }
    const filterLib = [];
    const lintedActualData = actualData.filter((el) => {
      if (filterLib.indexOf(el.date) > -1) {
        return false;
      }
      filterLib.push(el.date);
      return true;
    });
    datasets.push({
      label: 'live',
      data: lintedActualData,
      pointRadius: 0,
      borderColor: lineColor,
    });
    return (
      <Line
        height="102"
        data={{
          datasets: datasets,
        }}
        options={{
          layout: {
            padding: {
              top: 1.5,
              bottom: 1.5,
              left: 1.5,
              right: 1.5,
            },
          },
          maintainAspectRatio: false,
          parsing: {
            xAxisKey: 'date',
            yAxisKey: 'value',
          },
          plugins: {
            legend: { display: false },
            tooltip: { enabled: false },
          },
          scales: {
            x: {
              type: 'time',
              grid: {
                display: false,
              },
              display: false,
            },
            y: {
              beginAtZero: true,
              grid: {
                display: false,
              },
              display: false,
            },
          },
        }}
      />
    );
  }
}

export default MiniProductionLineChart;
