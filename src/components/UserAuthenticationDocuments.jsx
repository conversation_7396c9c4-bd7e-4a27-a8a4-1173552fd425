import React, { useState } from 'react';
import moment from 'moment';
import { useDataProvider } from 'react-admin';
import { useParams } from 'react-router-dom';
import { Alert } from '@mui/lab';

import {
  CircularProgress,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { CloudDownload } from '@mui/icons-material';

export const UserAuthenticationDocuments = () => {
  const [userAuthDocuments, setUserAuthDocuments] = useState(null);
  const [loading, setLoading] = useState(true);
  const [
    userBeneficialOwnerAuthDocuments,
    setUserBeneficialOwnerAuthDocuments,
  ] = useState(null);
  const [
    userMTCVerificationDocuments,
    setUserMTCVerificationDocuments,
  ] = useState(null);
  const dataProvider = useDataProvider();

  const { id } = useParams();
  if (!userAuthDocuments) {
    dataProvider
      .getOne('UserWithAuthenticationDocuments', {
        id: parseInt(id, 10),
      })
      .then(
        (resp) => {
          setLoading(false);
          setUserAuthDocuments(resp.data.dwollaDocuments);
          setUserBeneficialOwnerAuthDocuments(
            resp.data.beneficialOwnerDwollaDocuments
          );
          setUserMTCVerificationDocuments(
            resp.data.millenniumTrustVerificationDocuments
          );
        },
        (e) => {
          setLoading(false);
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  }

  const jsx = [];

  if (loading) {
    return <CircularProgress key="auth-doc-progress" />;
  }

  if (userAuthDocuments) {
    if (userAuthDocuments.length === 0) {
      jsx.push(
        <Alert key="no-auth-doc-error" severity="info">
          No authentication documents have been uploaded to Dwolla
        </Alert>
      );
    } else {
      jsx.push(
        <Grid key="customer-dwolla-docs-container" item xs={12} md={6}>
          <Typography variant="h6">
            Customer Dwolla Documents ({userAuthDocuments.length})
          </Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Image</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Comment</TableCell>
                <TableCell>All Reasons</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Download</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {userAuthDocuments.map((document) => {
                return (
                  <TableRow key={`auth-doc-${document.id}`}>
                    <TableCell>
                      {document.downloadUrl ? (
                        <img src={document.downloadUrl} height="75px" />
                      ) : null}
                    </TableCell>
                    <TableCell>{document.status}</TableCell>
                    <TableCell>{document.type}</TableCell>
                    <TableCell>{document.failureReason}</TableCell>
                    <TableCell>
                      {document.allFailureReasons &&
                        document.allFailureReasons
                          .map(
                            (reason) =>
                              `${reason.reason} - ${reason.description}`
                          )
                          .join('\n')}
                    </TableCell>
                    <TableCell>
                      {moment(document.created).format('MMM D, YYYY HH:mm:ss')}
                    </TableCell>
                    <TableCell>
                      {document.downloadUrl ? (
                        <IconButton
                          onClick={() =>
                            window.location.assign(document.downloadUrl)
                          }
                          size="large"
                        >
                          <CloudDownload />
                        </IconButton>
                      ) : null}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </Grid>
      );
    }
  }
  if (userBeneficialOwnerAuthDocuments) {
    if (userBeneficialOwnerAuthDocuments.length > 0) {
      userBeneficialOwnerAuthDocuments.sort((a, b) =>
        a.beneficialOwnerId < b.beneficialOwnerId ? -1 : 1
      );
      jsx.push(
        <Grid item xs={12} md={6} style={{ marginTop: '3rem' }}>
          <Typography variant="h6">
            Beneficial Owner Dwolla Documents (
            {userBeneficialOwnerAuthDocuments.length})
          </Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Image</TableCell>
                <TableCell>Beneficial Owner</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Comment</TableCell>
                <TableCell>All Reasons</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Download</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {userBeneficialOwnerAuthDocuments.map((document) => {
                return (
                  <TableRow key={`ben-owner-auth-doc-${document.id}`}>
                    <TableCell>
                      {document.downloadUrl ? (
                        <img src={document.downloadUrl} height="75px" />
                      ) : null}
                    </TableCell>
                    <TableCell>{document.beneficialOwnerId}</TableCell>
                    <TableCell>{document.status}</TableCell>
                    <TableCell>{document.type}</TableCell>
                    <TableCell>{document.failureReason}</TableCell>
                    <TableCell>
                      {document.allFailureReasons &&
                        document.allFailureReasons
                          .map(
                            (reason) =>
                              `${reason.reason} - ${reason.description}`
                          )
                          .join('\n')}
                    </TableCell>
                    <TableCell>
                      {moment(document.created).format('MMM D, YYYY HH:mm:ss')}
                    </TableCell>
                    <TableCell>
                      {document.downloadUrl ? (
                        <IconButton
                          onClick={() =>
                            window.location.assign(document.downloadUrl)
                          }
                          size="large"
                        >
                          <CloudDownload />
                        </IconButton>
                      ) : null}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </Grid>
      );
    }
  }
  if (userMTCVerificationDocuments) {
    if (userMTCVerificationDocuments.length === 0) {
      jsx.push(
        <Alert
          key="no-mtc-auth-doc-error"
          severity="info"
          style={{ marginTop: '1rem' }}
        >
          No authentication documents have been uploaded to MTC
        </Alert>
      );
    } else {
      jsx.push(
        <Grid
          key="mtc-verification-docs-container"
          item
          xs={12}
          md={6}
          style={{ marginTop: '2rem' }}
        >
          <Typography variant="h6">
            MTC Verification Documents ({userMTCVerificationDocuments.length})
          </Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Image</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Download</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {userMTCVerificationDocuments.map((document) => {
                return (
                  <TableRow key={`auth-doc-${document.id}`}>
                    <TableCell>
                      {document.awsObjectUrl ? (
                        <img src={document.awsObjectUrl} height="75px" />
                      ) : null}
                    </TableCell>
                    <TableCell>{document.documentType}</TableCell>
                    <TableCell>
                      {moment(document.createdAt).format(
                        'MMM D, YYYY HH:mm:ss'
                      )}
                    </TableCell>
                    <TableCell>
                      {document.awsObjectUrl ? (
                        <IconButton
                          onClick={() =>
                            window.location.assign(document.awsObjectUrl)
                          }
                          size="large"
                        >
                          <CloudDownload />
                        </IconButton>
                      ) : null}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </Grid>
      );
    }
  }
  return jsx;
};
