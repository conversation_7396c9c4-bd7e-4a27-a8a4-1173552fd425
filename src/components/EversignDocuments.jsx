import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Eversign Document';

export const EversignDocumentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextInput source="eversignDocumentId" />
      </SimpleForm>
    </Edit>
  );
};

export const EversignDocumentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="eversignDocumentId" />
      </Datagrid>
    </List>
  );
};

export const EversignDocumentCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <TextInput source="label" />
    </SimpleForm>
  </Create>
);
