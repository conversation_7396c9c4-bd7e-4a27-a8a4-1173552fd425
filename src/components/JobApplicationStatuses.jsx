import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Job Application';

export const JobApplicationStatusEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="title" required fullWidth />
            <TextInput source="description" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const JobApplicationStatusList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="title" />
        <TextField source="description" />
      </Datagrid>
    </List>
  );
};

export const JobApplicationStatusCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="title" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
