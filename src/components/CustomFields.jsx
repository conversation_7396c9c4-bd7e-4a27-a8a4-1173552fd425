import React from 'react';
import { <PERSON> } from 'react-router-dom';

import {
  <PERSON>,
  Tooltip,
  IconButton,
  LinearProgress,
  Typography,
  InputAdornment,
} from '@mui/material';
import {
  BooleanField,
  NumberInput,
  usePermissions,
  useRecordContext,
} from 'react-admin';
import { Subject, Remove } from '@mui/icons-material';
import { roleMatrix } from '../utils/applyRoleAuth';

const LinkField = ({ linkSource, reference, labelSource }) => {
  const { permissions } = usePermissions();
  const record = useRecordContext();
  const roles = permissions.roles?.map((role) => role.name) || [];

  if (!record) return null;

  const { readAccess: readAccessRoles, writeAccess: writeAccessRoles } =
    roleMatrix[String(reference)];
  let hasReadPermission = false;
  for (
    let readRoleIndex = 0;
    readRoleIndex < readAccessRoles.length;
    readRoleIndex++
  ) {
    const readRole = readAccessRoles[parseInt(readRoleIndex, 10)];
    if (roles.indexOf(readRole) > -1) {
      hasReadPermission = true;
      break;
    }
  }

  let tempLabelRecord = record;
  let label;
  const aLabelPath = labelSource ? labelSource.split('.') : null;
  aLabelPath.forEach((path, index) => {
    if (!tempLabelRecord) return; // protects from missing record error that can arise from conflicting caches
    if (aLabelPath?.length === index + 1) {
      label = tempLabelRecord[String(path)];
    } else {
      tempLabelRecord = tempLabelRecord[String(path)];
    }
  });

  let tempIdRecord = record;
  let resourceId;
  const aIdPath = linkSource.split('.');
  aIdPath.forEach((path, index) => {
    if (!tempIdRecord) return; // protects from missing record error that can arise from conflicting caches
    if (aIdPath.length === index + 1) {
      resourceId = tempIdRecord[String(path)];
    } else {
      tempIdRecord = tempIdRecord[String(path)];
    }
  });

  return (
    <Typography
      variant="body2"
      component={hasReadPermission ? Link : null}
      to={hasReadPermission ? `/${reference}/${resourceId}` : null}
      // style={{ margin: '4px' }}
      onClick={(event) => {
        if (hasReadPermission) {
          event.stopPropagation();
        }
      }}
    >
      {label}
    </Typography>
  );
};

const CustomReferenceField = ({ color, source, linkOverride, disableLink }) => {
  const record = useRecordContext();

  if (!record) return <LinearProgress />;

  let label;
  let tempRecord = record;

  if (typeof source === 'function') {
    label = source(record);
  } else {
    const aPath = source.split('.');
    aPath.forEach((path, index) => {
      if (!tempRecord) return; // protects from missing record error that can arise from conflicting caches
      if (aPath.length === index + 1) {
        label = tempRecord[String(path)];
      } else {
        tempRecord = tempRecord[String(path)];
      }
    });
  }

  const chipColor = color ? color(record) : 'default';
  if (disableLink) {
    return (
      <Chip
        color={chipColor}
        label={label}
        clickable={false}
        style={{
          margin: '4px',
          color: chipColor === 'default' ? null : 'white',
        }}
      />
    );
  }

  return (
    <Chip
      color={chipColor}
      clickable
      component={Link}
      to={
        linkOverride
          ? linkOverride(record)
          : `/${record.__typename}/${record.id}`
      }
      label={label}
      style={{ margin: '4px', color: chipColor === 'default' ? null : 'white' }}
    />
  );
};

const DetailField = ({ source, richText }) => {
  const record = useRecordContext();
  if (!record) return null;
  if (record[String(source)] && record[String(source)] !== '') {
    let title;
    if (richText) {
      title = (
        <div dangerouslySetInnerHTML={{ __html: record[String(source)] }} />
      );
    } else {
      title = record[String(source)];
    }
    return (
      <>
        <Tooltip title={title}>
          <IconButton aria-label={record[String(source)]} size="large">
            <Subject />
          </IconButton>
        </Tooltip>
      </>
    );
  }
  return (
    <IconButton disabled aria-label={record[String(source)]} size="large">
      <Remove />
    </IconButton>
  );
};

const CustomBooleanField = ({ source, hideOnFalse }) => {
  const record = useRecordContext();
  const value = record && record[String(source)];

  let color;
  if (value) {
    color = 'green';
  } else if (hideOnFalse) {
    color = 'rgba(0,0,0,0)';
  } else {
    color = 'red';
  }
  return <BooleanField style={{ color }} sortable={false} source={source} />;
};

const CustomNumberInput = (props) => {
  return <NumberInput {...props} onWheel={(e) => e.target.blur()} />;
};

const PercentageInput = ({ source, label, ...rest }) => {
  return (
    <CustomNumberInput
      source={source}
      label={label}
      parse={(value) => parseFloat(value) / 100}
      format={(value) => (value ? `${parseFloat(value) * 100}` : '')}
      step="0.01"
      {...rest}
      InputProps={{
        endAdornment: <InputAdornment position="end">%</InputAdornment>,
      }}
    />
  );
};

export {
  CustomReferenceField,
  DetailField,
  CustomBooleanField,
  CustomNumberInput,
  LinkField,
  PercentageInput,
};
