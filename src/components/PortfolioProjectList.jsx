import React, { PureComponent } from 'react';

import numeral from 'numeral';
import queryString from 'query-string';

import {
  Avatar,
  Badge,
  Chip,
  Dialog,
  DialogContent,
  Fab,
  Grid,
  Icon,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Typography,
} from '@mui/material';
import moment from 'moment';
import { withStyles } from '@mui/styles';

const styles = (theme) => ({
  backdropRoot: { backgroundColor: 'rgba(0, 0, 0, 0.8)' },
  badge: { background: theme.palette.yellow.main, fontWeight: 'bold' },
  projectSecondaryAction: {
    paddingRight: '128px',
    [theme.breakpoints.down('sm')]: {
      paddingRight: '106px',
    },
  },
  secondaryAction: {
    paddingRight: '88px',
    color: 'rgba(0, 0, 0, 0.54)',
    '&:hover': {
      color: theme.palette.secondary.main,
    },
  },
  video: {
    width: '100%',
  },
});

const findProjectVideo = (projectId, projects) => {
  const selectedProjects = projects.filter(
    (proj) => proj.id === parseInt(projectId, 10) && proj.primaryVideo
  );
  return selectedProjects[0] && selectedProjects[0].primaryVideo;
};

const getProjectTitle = (project) => {
  const sysSizeText =
    project.ownedSystemSizeDC < 1
      ? `${numeral(project.ownedSystemSizeDC * 1000).format('0,0')} kW`
      : `${numeral(project.ownedSystemSizeDC).format('0.[0]')} MW`;
  return `${project.name} (${sysSizeText})`;
};

class ProjectList extends PureComponent {
  constructor(props) {
    super(props);

    const { autoPlayProjectVideo } = queryString.parse(window.location.search);
    this.state = {
      projectVideoDialogOpen: false,
      selectedProjectVideo: null,
      selectedProjectVideoOverride: autoPlayProjectVideo,
      showPlayBtn: autoPlayProjectVideo,
    };
  }

  handleProjectClick(project) {
    const { parent } = this.props;
    return () => {
      //   parent.setState({ projectPopperOpen: true, clickedProject: project });
    };
  }

  getBackgroundColor(investmentStatusId) {
    const { theme } = this.props;
    let color;
    switch (investmentStatusId) {
      // investmentStatusId 1, 2, 3, 5, 7 should no longer be in use
      case 3:
      case 7:
      case 2:
        color = theme.palette.yellow.main;
        break;
      case 5:
        color = theme.palette.secondary.main;
        break;
      case 8: // Cash Flowing
        color = theme.palette.green.main;
        break;
      case 9: // Construction
        color = theme.palette.primary.main;
        break;
      case 10: // Development
        color = theme.palette.secondary.main;
        break;
      case 6: // Sold
        color = 'gray'; // theme.palette.primary.main;
        break;
      default:
        break;
    }
    return color;
  }

  renderProjectList() {
    const { classes, fullScreen, projects } = this.props;
    return (
      <List style={{ width: '100%' }}>
        {projects.map((project) => (
          <ListItem
            dense={projects.length > 7 && !fullScreen}
            button
            onClick={this.handleProjectClick(project)}
            // onFocus={this.handleProjectHover(project)}
            // onBlur={this.handleProjectHoverExit()}
            // style={{ cursor: 'pointer' }}
            key={`desktop-project-list-item-${project.id}`}
            divider
            disableGutters={fullScreen}
            style={{ width: '100%' }}
            classes={{ secondaryAction: classes.projectSecondaryAction }}
          >
            <Badge
              style={{
                position: 'absolute',
                top: fullScreen ? '4px' : '6px',
                left: fullScreen ? '15px' : '0',
                display: project.newFlg ? 'inherit' : 'none',
              }}
              badgeContent="NEW!"
              color="error"
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
              classes={{ badge: classes.badge }}
            />
            <ListItemText
              primaryTypographyProps={{ style: { fontWeight: 'bold' } }}
              primary={getProjectTitle(project)}
            />
            {project.projectInvestmentStatus ? (
              <ListItemSecondaryAction style={{ right: fullScreen ? 0 : null }}>
                <Grid container spacing={1} alignItems="center">
                  <Grid item>
                    <Chip
                      color="primary"
                      style={{
                        background: this.getBackgroundColor(
                          project.projectInvestmentStatus.id
                        ),
                      }}
                      avatar={
                        fullScreen ? null : (
                          <Avatar style={{ background: 'rgba(0,0,0,.3)' }}>
                            <Icon
                              style={{
                                fontSize: '.9rem',
                                marginTop: '.05rem',
                                textAlign: 'center',
                                width: '2em',
                              }}
                              className={
                                project.projectInvestmentStatus.iconClass
                              }
                            />
                          </Avatar>
                        )
                      }
                      label={project.projectInvestmentStatus.name}
                    />
                  </Grid>
                  <Grid item>
                    <Typography
                      variant="body2"
                      style={{ minWidth: '110px', textAlign: 'right' }}
                    >
                      {project.actualCOD ? (
                        moment(project.actualCOD).format('ll')
                      ) : (
                        <i style={{ color: '#999' }}>
                          {moment(project.projectedCOD).format('ll')}
                        </i>
                      )}
                    </Typography>
                  </Grid>
                </Grid>
              </ListItemSecondaryAction>
            ) : null}
          </ListItem>
        ))}
      </List>
    );
  }

  render() {
    const { classes, fullScreen, projects } = this.props;

    const {
      projectVideoDialogOpen,
      selectedProjectVideoOverride,
      selectedProjectVideo,
      showPlayBtn,
    } = this.state;

    if (selectedProjectVideoOverride && !selectedProjectVideo) {
      const projectVideo = findProjectVideo(
        selectedProjectVideoOverride,
        projects
      );
      const tempProjectVideoDialogOpen = !!projectVideo;
      this.setState({
        selectedProjectVideoOverride: null,
        selectedProjectVideo: projectVideo,
        projectVideoDialogOpen: tempProjectVideoDialogOpen,
      });
    }

    if (projects.length <= 0) {
      return null;
    }
    const lintedShowPlayBtn = showPlayBtn && !fullScreen;
    return this.renderProjectList();
  }
}

export default withStyles(styles, { withTheme: true })(ProjectList);
