import React from 'react';
import numeral from 'numeral';
import {
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  Fab,
  Grid,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import cloudinary from 'cloudinary-core';

import { withStyles } from '@mui/styles';

import Config from '../config/config';

// Note: At the time this was made, this PortfolioTileSmall only differs from the PortfolioTileSmall
// in energea-app in a couple minor places. Styling of text and fab, setting the width of it to 75%,
// and changing portfolio.activeProjects.length to portfolio.activeProjectCount

const cl = new cloudinary.Cloudinary({
  cloud_name: Config.cloud_name,
  secure: true,
});
const styles = (theme) => ({
  isAcceptingInvestmentsColor: {
    backgroundColor: theme.palette.green.main,
    '&:hover': {
      backgroundColor: theme.palette.green.dark,
    },
  },
});
const PortfolioTileSmall = (props) => {
  const { portfolio, fullyFunded, theme, classes } = props;
  const fullScreen = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  let lintedUrl;
  if (portfolio.bannerImage && portfolio.bannerImage.public_id) {
    lintedUrl = cl.url(portfolio.bannerImage.public_id, {
      width: fullScreen ? '720' : '1280',
      crop: 'scale',
    });
  } else {
    lintedUrl = cl.url('site/missing_image', {
      width: fullScreen ? '720' : '1280',
      crop: 'scale',
    });
  }
  return (
    <Grid
      direction={fullScreen ? 'row-reverse' : 'row'}
      key={`portfolio-section-${portfolio.id}`}
      item
      container
      spacing={fullScreen ? 5 : 8}
      style={{ width: '75%' }}
    >
      <Grid item md={6} xs={12}>
        <Card raised>
          <CardActionArea>
            <CardMedia
              style={{ height: '14rem' }}
              image={lintedUrl}
              title={portfolio.subtitle}
              subheader={portfolio.summary}
            >
              <Grid
                container
                justifyContent="center"
                alignItems="center"
                style={{ width: '100%', height: '100%' }}
              >
                <Grid item>
                  <Fab
                    classes={{
                      colorInherit:
                        portfolio.isAcceptingInvestments && !fullyFunded
                          ? classes.isAcceptingInvestmentsColor
                          : null,
                    }}
                    style={{
                      margin: 'auto',
                      color: '#fff',
                      marginBottom: '.5rem',
                      backgroundColor:
                        portfolio.isAcceptingInvestments && !fullyFunded
                          ? theme.palette.green.main
                          : '#5292c3',
                    }}
                    size="large"
                    variant="extended"
                    color={
                      portfolio.isAcceptingInvestments && !fullyFunded
                        ? 'inherit'
                        : 'secondary'
                    }
                  >
                    <Typography
                      style={{
                        padding: '1rem',
                        fontSize: '1.25em',
                        fontWeight: 'bold',
                        textTransform: 'none',
                      }}
                      variant="h6"
                    >
                      {portfolio.isAcceptingInvestments && !fullyFunded
                        ? 'Invest Now'
                        : 'Learn More'}
                    </Typography>
                  </Fab>
                </Grid>
              </Grid>
            </CardMedia>
            <CardContent style={{ background: theme.palette.primary.dark }}>
              <Grid item container xs={12}>
                <Grid container item xs={4} justifyContent="center">
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <Typography
                      color="primary"
                      style={{ fontWeight: 'bold', color: '#fff' }}
                      variant="h4"
                    >
                      {portfolio.activeProjectCount || '-'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <Typography variant="body2" style={{ color: '#fff' }}>
                      {portfolio.activeProjectCount === 1 ? 'Asset' : 'Assets'}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container item xs={4} justifyContent="center">
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <Typography
                      style={{ fontWeight: 'bold', color: '#fff' }}
                      variant="h4"
                    >
                      {portfolio.systemSizeSumDC ? (
                        <>
                          {numeral(portfolio.systemSizeSumDC).format('0,0.0')}
                          <span
                            style={{ fontSize: '.6em', fontWeight: 'normal' }}
                          >
                            MW
                          </span>
                        </>
                      ) : (
                        '-'
                      )}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <Typography style={{ color: '#fff' }} variant="body2">
                      Total Size
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container item xs={4} justifyContent="center">
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <Typography
                      style={{ fontWeight: 'bold', color: '#fff' }}
                      variant="h4"
                    >{`${portfolio.projectedCOCYield}`}</Typography>
                  </Grid>
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <Typography style={{ color: '#fff' }} variant="body2">
                      Estimated IRR
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </CardActionArea>
        </Card>
      </Grid>
      <Grid item md={6} xs={12} style={{ textAlign: 'left' }}>
        <Typography
          style={{ display: 'block', fontWeight: 'bold' }}
          gutterBottom
          variant="h5"
        >
          {portfolio.subtitle}
        </Typography>
        <Grid item>
          <Typography
            style={{
              minHeight: '8rem',
              color: 'rgba(0, 0, 0, 0.7)',
              fontSize: '1.1rem',
            }}
          >
            {portfolio.description ? portfolio.description : 'No description'}
          </Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};
export default withStyles(styles, { withTheme: true })(PortfolioTileSmall);
