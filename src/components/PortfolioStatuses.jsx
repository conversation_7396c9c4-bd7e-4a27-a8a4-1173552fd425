import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Portfolio Status';

export const PortfolioStatusEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const PortfolioStatusList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <NumberField source="id" />
        <TextField source="name" />
        <TextField source="description" />
      </Datagrid>
    </List>
  );
};

export const PortfolioStatusCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="description" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
