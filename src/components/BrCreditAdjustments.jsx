import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Credit Adjustment';

export const BrCreditAdjustmentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="brBillingCycle.id"
              reference="BrBillingCycleLite"
              perPage={10_000}
              sort={{ field: 'billingMonth', order: 'DESC' }}
            >
              <SelectInput
                optionText="label"
                label="Billing Cycle"
                fullWidth
                required
              />
            </ReferenceInput>
            <CustomNumberInput source="creditAdjustment" fullWidth required />
            <TextInput multiline source="notes" fullWidth required />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const styleRow = (record, index) => {
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  if (!record.brBillingCycle) {
    return errorStyle;
  }
  return {};
};

export const BrCreditAdjustmentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrBillingCycle"
          linkSource="brBillingCycle.id"
          labelSource="brBillingCycle.label"
          label="Billing cycle"
        />
        <NumberField source="creditAdjustment" />
        <TextField source="notes" />
      </Datagrid>
    </List>
  );
};

export const BrCreditAdjustmentCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="brBillingCycle.id"
            reference="BrBillingCycleLite"
            perPage={10_000}
            sort={{ field: 'billingMonth', order: 'DESC' }}
          >
            <SelectInput
              optionText="label"
              label="Billing Cycle"
              fullWidth
              required
            />
          </ReferenceInput>
          <CustomNumberInput source="creditAdjustment" fullWidth required />
          <TextInput multiline source="notes" fullWidth required />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
