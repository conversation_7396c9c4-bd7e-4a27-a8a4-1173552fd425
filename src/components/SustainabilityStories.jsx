import React from 'react';

import {
  Create,
  Datagrid,
  Edit,
  Show,
  FunctionField,
  List,
  NumberField,
  NumberInput,
  BooleanField,
  BooleanInput,
  DateField,
  RichTextField,
  SimpleForm,
  SimpleShowLayout,
  TextField,
  TextInput,
  UrlField,
  useDataProvider,
  useNotify,
  useRefresh,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { RichTextInput } from 'ra-input-rich-text';
import { useParams } from 'react-router-dom';
import { Button, Grid } from '@mui/material';
import { Delete } from '@mui/icons-material';

import { Video, Image, Transformation } from 'cloudinary-react';
import { getEditable } from '../utils/applyRoleAuth';

import { openUploadWidget } from '../utils/CloudinaryService';

import Config from '../config/config';
import { CustomNumberInput, DetailField } from './CustomFields';

const entityName = 'Sustainabilty Story';

export const SustainabilityStoryEdit = () => {
  const dataProvider = useDataProvider();
  const refresh = useRefresh();
  const notify = useNotify();
  const { id } = useParams();

  const onPhotoUploaded = (photo) => {
    dataProvider
      .update('SustainabilityStory', {
        data: {
          id: parseInt(id, 10),
          bannerImageCloudinaryPublicId: photo.public_id,
          bannerImageCloudinaryUrl: photo.url,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading image', { type: 'error' });
      })
      .then(() => {
        notify('Image successfully uploaded');
        refresh();
      });
  };

  const onVideoUploaded = (video) => {
    dataProvider
      .update('SustainabilityStory', {
        data: {
          id: parseInt(id, 10),
          videoCloudinaryPublicId: video.public_id,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading video', { type: 'error' });
      })
      .then(() => {
        notify('Video successfully uploaded');
        refresh();
      });
  };

  const handleAddImage = () => {
    const uploadOptions = {
      tags: ['sustainability-stories-banner-image'],
      multiple: false,
      resourceType: 'image',
      showPoweredBy: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.sustainability_stories_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotoUploaded(resp.info);
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.sustainability_stories_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideoUploaded(resp.info);
      }
    });
  };

  const handleRemoveVideo = () => {
    return () => {
      dataProvider
        .update('SustainabilityStory', {
          data: {
            id: parseInt(id, 10),
            videoCloudinaryPublicId: null,
          },
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing video', { type: 'error' });
        })
        .then(() => {
          notify('Video successfully removed');
          refresh();
        });
    };
  };

  const handleRemoveImage = (imageId) => {
    return () => {
      dataProvider
        .update('SustainabilityStory', {
          data: {
            id: parseInt(id, 10),
            videoCloudinaryPublicId: null,
          },
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing image', { type: 'error' });
        })
        .then(() => {
          notify('Image successfully removed');
          refresh();
        });
    };
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="title"
              helperText="Title text of post"
              fullWidth
            />
            <TextInput
              source="storyTellerFirstName"
              helperText="Story Teller First Name"
              fullWidth
            />
            <TextInput
              source="storyTellerLastName"
              helperText="Story Teller Last Name"
              fullWidth
            />
            <RichTextInput
              multiline
              fullWidth
              source="summary"
              helperText="This will show if the user clicks on the tile."
            />
            <CustomNumberInput source="orderNo" fullWidth step={1} />
            <BooleanInput
              label="Coming Soon"
              source="comingSoonFlg"
              fullWidth
            />
            <BooleanInput source="inactive" fullWidth />
          </Grid>
        </Grid>
        <UrlField
          target="_blank"
          source="emailLinkUrl"
          label="Email Link"
          fullWidth
        />
        <TextField source="bannerImageCloudinaryPublicId" fullWidth />
        <UrlField target="_blank" source="bannerImageCloudinaryUrl" fullWidth />
        <TextField source="videoCloudinaryPublicId" fullWidth />
        <UrlField target="_blank" source="videoCloudinaryUrl" fullWidth />
        <FunctionField
          align="center"
          label="Banner Image"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <>
                <Image
                  style={{}}
                  cloud_name={Config.cloud_name}
                  publicId={record.bannerImage && record.bannerImage.public_id}
                >
                  <Transformation width="200" crop="scale" />
                </Image>
                {record.bannerImage ? (
                  <Button onClick={handleRemoveImage(record.bannerImage.id)}>
                    <Delete /> Delete
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={handleAddImage}
                  >
                    Add Image
                  </Button>
                )}
              </>
            );
          }}
        />
        <FunctionField
          align="center"
          label="Video"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record.video || !record.video.public_id) return null;
            return (
              <>
                <Video
                  cloud_name={Config.cloud_name}
                  publicId={record.video.public_id}
                  muted
                  width="200"
                  crop="scale"
                  sourceTypes={['mp4']}
                  controls
                />
                <Button
                  style={{ float: 'right' }}
                  onClick={handleRemoveVideo(record)}
                >
                  <Delete />
                </Button>
              </>
            );
          }}
        />
        <div className="actions">
          <Button
            variant="contained"
            color="primary"
            onClick={uploadVideoWithCloudinary}
          >
            Add video
          </Button>
        </div>
      </SimpleForm>
    </Edit>
  );
};

export const SustainabilityStoryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="title" />
        <TextField source="storyTellerFirstName" />
        <TextField source="storyTellerLastName" />
        <DetailField source="summary" sortable={false} />
        <NumberField source="orderNo" />
        <BooleanField label="Coming Soon" source="comingSoonFlg" />
        <BooleanField source="inactive" />
        <UrlField
          target="_blank"
          source="emailLinkUrl"
          label="Email Link"
          sortable={false}
          fullWidth
        />
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const SustainabilityStoryCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="title"
            helperText="Title text of post. This can be updated at any time."
            fullWidth
          />
          <TextInput source="storyTellerFirstName" fullWidth />
          <TextInput source="storyTellerLastName" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);

export const SustainabilityStoryShow = () => (
  <Show>
    <SimpleShowLayout>
      <TextField source="id" />
      <TextField source="title" />
      <TextField source="storyTellerFirstName" />
      <TextField source="storyTellerLastName" />
      <RichTextField source="summary" />
      <DateField source="updatedAt" />
      <DateField source="createdAt" />
    </SimpleShowLayout>
  </Show>
);
