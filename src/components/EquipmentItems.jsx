import React from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  NumberInput,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Chip, Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'Equipment Item';

export const EquipmentItemEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }} spacing={5}>
          <Grid item xs={12} md={6}>
            <Typography variant="h5" gutterBottom>
              General Data
            </Typography>
            <TextInput source="model" fullWidth />
            <TextInput source="manufacturer" fullWidth />
            <ReferenceInput
              source="equipmentType.id"
              reference="EquipmentType"
              sort={{ field: 'name', order: 'DESC' }}
            >
              <SelectInput
                label="Type"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
          <FormDataConsumer>
            {({ formData, ...rest }) =>
              formData?.equipmentType?.id === 1 && (
                <Grid item xs={12} md={6}>
                  <Typography variant="h5" gutterBottom>
                    Module Data
                  </Typography>
                  <NumberInput
                    source="noct"
                    label="NOCT"
                    helperText="The nominal operating cell temperature of the modules. (ex: 45)"
                    fullWidth
                  />
                  <NumberInput
                    source="tempCoeffPmax"
                    label="Temp. Coefficient (PMax)"
                    helperText="The temperature coefficient of Pmax for the site\'s modules. this should be a loss coefficient per degrees celsius. (ex: 0.0034)"
                    fullWidth
                  />
                </Grid>
              )
            }
          </FormDataConsumer>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px', marginBottom: '4px' }}
      label="Search for a type, manufacturer or model"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      source="project.id"
      reference="Project"
      label="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Location" optionText="name" />
    </ReferenceInput>
    <BooleanInput
      source="onlyProjectsWithSpares"
      label="Show only projects with spares"
      alwaysOn
    />
  </Filter>
);

export const EquipmentItemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} filters={<CustomFilter />}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <LinkField
          reference="EquipmentType"
          linkSource="equipmentType.id"
          labelSource="equipmentType.name"
          label="Type"
        />
        <TextField source="manufacturer" />
        <TextField source="model" />
        <FunctionField
          label="Inventory"
          render={(record) => (
            <Grid container spacing={1}>
              {record.inventory.map((item) => (
                <Grid item>
                  <Chip
                    onClick={(event) => {
                      event.stopPropagation();
                      event.preventDefault();
                      window.location.href = `/Project/${item.projectId}/Inventory`;
                    }}
                    color="primary"
                    label={item.label}
                  />
                </Grid>
              ))}
            </Grid>
          )}
        />
        <BooleanField source="dataCompleteFlg" label="Data Complete" />
      </Datagrid>
    </List>
  );
};

export const EquipmentItemCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="model" required fullWidth />
          <TextInput source="manufacturer" required fullWidth />
          <ReferenceInput
            source="equipmentType.id"
            reference="EquipmentType"
            sort={{ field: 'name', order: 'ASC' }}
            helperText={
              <span>
                If you don't see the equipment type you need, create it{' '}
                <a href="/EquipmentType/create">here</a>.
              </span>
            }
          >
            <SelectInput
              label="Equipment Type"
              fullWidth
              required
              optionText="name"
              helperText={
                <span>
                  If you don't see the equipment type you need, create it{' '}
                  <a href="/EquipmentType/create">here</a>.
                </span>
              }
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
