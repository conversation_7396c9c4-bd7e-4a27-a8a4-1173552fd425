import React, { useState } from 'react';
import { Alert } from '@mui/lab';

import { useDataProvider, useNotify, useRedirect } from 'react-admin';
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

import { GetApp } from '@mui/icons-material';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';

export const SensorDataPeriodsUpload = (props) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [reviewed, setReviewed] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const redirect = useRedirect();

  const lintData = (data, attrs) => {
    return data
      .map((entry) => {
        const returnObj = {};
        Object.keys(entry).forEach((entryAttr) => {
          const entryAttrData = findWithAttr(attrs, 'name', entryAttr);
          if (!entryAttrData) {
            console.log('Missing attr detected', entryAttr);
          } else if (entryAttrData.dataFormat) {
            returnObj[String(entryAttr)] = entryAttrData.dataFormat(
              entry[String(entryAttr)]
            );
          } else {
            returnObj[String(entryAttr)] = entry[String(entryAttr)];
          }
        });
        return returnObj;
      })
      .filter((el) => !!el);
  };
  const handleData = (data) => {
    const { attrs } = props;
    const lintedData = lintData(data, attrs);
    setData(lintedData);
  };

  const save = () => {
    setLoading(true);
    dataProvider
      .create(`UploadSensorDataPeriod`, {
        data,
      })
      .catch((e) => {
        notify('Error adding sensor data periods', { type: 'error' });
        setLoading(false);
        console.error('ERROR', e);
      })
      .then((resp) => {
        setLoading(false);
        notify(
          `Successfully saved ${resp?.data?.length} new sensor data periods.`,
          { type: 'success' }
        );
        redirect('/SensorDataPeriod');
      });
  };
  const renderData = () => {
    const { attrs } = props;
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            {attrs.map((attr) => (
              <TableCell
                key={`sensorDataPeriods-header-cell-${attr.name}`}
                align={attr.align || 'center'}
              >
                {attr.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell
                  key={`sensorDataPeriods-val-cell-${attr.name}`}
                  align={attr.align || 'center'}
                >
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                key={`sensorDataPeriodRow-${row.name}-${index}`}
              >
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  const renderSubmit = () => {
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!reviewed}
                onChange={() => setReviewed(!reviewed)}
              />
            }
            label="I have checked the below numbers."
          />
          <Button
            onClick={save}
            disabled={!reviewed || loading}
            variant="contained"
            size="large"
            color="secondary"
          >
            {loading ? <CircularProgress /> : 'Save'}
          </Button>
        </Alert>
      </>
    );
  };

  return (
    <Grid container>
      <Grid xs={12} item style={{ padding: '1em' }}>
        <Button
          component="a"
          variant="contained"
          href={`/csv-templates/sensorDataPeriods.xlsx`}
          download
        >
          <GetApp />
          Click to download the csv template
        </Button>
      </Grid>
      <ExcelReader handleData={handleData} />
      <Grid item xs={12} style={{ margin: '1em' }}>
        {data ? renderSubmit() : null}
      </Grid>
      <Grid item xs={12}>
        {data ? renderData() : null}
      </Grid>
    </Grid>
  );
};
