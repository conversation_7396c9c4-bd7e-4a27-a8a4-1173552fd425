import React, { Fragment, useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Backdrop,
  // Badge,
  Button,
  Card,
  CardActionArea,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Icon,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Paper,
  Popover,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import 'flag-icons/css/flag-icons.min.css';
import { CloudDownload } from '@mui/icons-material';
import { Alert } from '@mui/lab';
import { Image, Transformation } from 'cloudinary-react';

import Config from '../config/config';

import queryString from 'query-string';

import { useDataProvider, usePermissions, useNotify } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment-timezone';

import 'chartjs-adapter-moment';
import annotationPlugin from 'chartjs-plugin-annotation';
import { Chart } from 'chart.js';

import { withStyles } from '@mui/styles';

import theme from '../theme';
import MiniProductionLineChart from './MiniProductionLineChart';
import PortfolioGenerationChart from './PortfolioGenerationChart';
import { AssetMgmtProjectExpansionPanel } from './AssetMgmtProjectExpansionPanel';
import ProjectMonitoringPopover from './ProjectMonitoringPopover';
import PartnerMonitoringHeader from './PartnerMonitoringHeader';
import Map from './Map';

const styles = (theme) => ({});

const getDiffColor = (diff) => {
  if (diff >= -0.05) return theme.palette.green.dark;
  // if (diff >= -0.05) return '#666';
  if (diff >= -0.15) return theme.palette.warning.main;
  if (diff >= -0.3) return theme.palette.orange.main;
  return theme.palette.error.main;
};

const getLossColor = (val) => {
  // if (val < 0.1) return theme.palette.green.dark;
  if (val < 0.1) return null;
  if (val < 0.2) return theme.palette.orange.main;
  if (val < 0.3) return theme.palette.warning.main;
  return theme.palette.error.main;
};

export default withStyles(styles)(() => {
  const { permissions } = usePermissions();
  const fullScreen = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const isPartner =
    [
      'VictoryHill',
      'Lattice',
      'Laerskool',
      'Connaught',
      'Fresno',
      'Eventide',
    ].filter((el) => permissions?.roles.map((role) => role.name).includes(el))
      .length > 0;
  const [selectedPortfolio, setSelectedPortfolio] = useState(null);
  const [selectedPortfolioProjectList, setSelectedPortfolioProjectList] =
    useState(null);
  const [selectedProject, setSelectedProject] = useState(undefined);
  const [portfolios, setPortfolios] = useState(null);
  const [portfolioGenerationChartOpen, setPortfolioGenerationChartOpen] =
    useState(false);
  const [portfoliosLoading, setPortfoliosLoading] = useState(false);
  const [portfolioProjectListLoading, setPortfolioProjectListLoading] =
    useState(false);

  const [portfolioDataError, setPortfolioDataError] = useState(null);
  const [projectSummary, setProjectSummary] = useState(null);
  const [projectDataError, setProjectDataError] = useState(null);

  const [viewType, setViewType] = useState('list');
  const [projectPopoverOpen, setProjectPopoverOpen] = useState(false);
  const [projectPopoverAnchorEl, setProjectPopoverAnchorEl] = useState(null);
  const [showWelcome, setShowWelcome] = useState(
    sessionStorage.getItem('hide-starlight-welcome') !== 'true'
  );
  const [monthlyReportLoading, setMonthlyReportLoading] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();

  useEffect(() => {
    Chart.register(annotationPlugin);
  });

  const fetchProjectSummary = () => {
    dataProvider.getOne('CMSProjectSummary', {}).then(
      (res) => {
        setProjectSummary(res.data);
        setProjectDataError(null);
      },
      (e) => {
        setProjectDataError(e);
        console.error('Error retrieving portfolios', e);
      }
    );
  };

  const fetchPortfolios = () => {
    dataProvider.getList('CMSMonitoringDashboardPortfolios', {}).then(
      (res) => {
        const { portfolio } = queryString.parse(window.location.search);
        if (!selectedPortfolio && portfolio) {
          const queryStringPortfolio = res.data.filter(
            (el) => String(el.id) === portfolio
          );
          setSelectedPortfolio(queryStringPortfolio[0] || res.data[0]);
        } else if (!selectedPortfolio) {
          setSelectedPortfolio(res.data[0]);
        }
        setPortfolios(res.data);
        setPortfoliosLoading(false);
        setPortfolioDataError(null);
        setTimeout(() => {
          setShowWelcome(false);
          sessionStorage.setItem('hide-starlight-welcome', 'true');
        });
      },
      (e) => {
        console.error('Error retrieving portfolios', e);
        setPortfoliosLoading(false);
        setPortfolioDataError(e);
        setTimeout(() => {
          setShowWelcome(false);
          sessionStorage.setItem(`hide-starlight-welcome`, 'true');
        });
      }
    );
  };
  const getFilteredActiveProjectList = (activeProjects) => {
    if (!activeProjects) {
      return [];
    }
    const laerskoolFilter =
      permissions?.roles &&
      permissions?.roles?.map((role) => role.name).indexOf('Laerskool') > -1 &&
      permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
    const eventideFilter =
      permissions?.roles &&
      permissions?.roles?.map((role) => role.name).indexOf('Eventide') > -1 &&
      permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
    const connaughtFilter =
      permissions?.roles &&
      permissions?.roles?.map((role) => role.name).indexOf('Connaught') > -1 &&
      permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
    const fresnoFilter =
      permissions?.roles &&
      permissions?.roles?.map((role) => role.name).indexOf('Fresno') > -1 &&
      permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
    if (activeProjects) {
      return activeProjects.filter((el) => {
        if (laerskoolFilter) {
          return el.id === 348;
        }
        if (eventideFilter) {
          return el.id === 354;
        }
        if (connaughtFilter) {
          return el.id === 266;
        }
        if (fresnoFilter) {
          return el.id === 189;
        }
        return true;
      });
    }
    return [];
  };

  const fetchPortfolioProjectList = () => {
    dataProvider
      .getOne('CMSMonitoringDashboardPortfolioProjectList', {
        id: selectedPortfolio.id,
      })
      .then(
        (res) => {
          setSelectedPortfolioProjectList(res.data);
          setPortfolioProjectListLoading(false);
        },
        (e) => {
          // setPortfolioProjectListLoading(false);
          console.error('Error retrieving portfolio project list', e);
        }
      );
  };

  if (!portfolios && !portfoliosLoading && !portfolioDataError) {
    setPortfoliosLoading(true);
    fetchPortfolios();
  }
  if (!projectSummary && !projectDataError) {
    fetchProjectSummary();
  }

  if (
    selectedPortfolio &&
    ((!portfolioProjectListLoading && !selectedPortfolioProjectList) ||
      (!portfolioProjectListLoading &&
        selectedPortfolioProjectList?.id !== selectedPortfolio?.id))
  ) {
    setPortfolioProjectListLoading(true);
    fetchPortfolioProjectList();
  }

  const renderMapView = () => {
    const onMarkerClick = (project) => {
      return (event) => {
        setSelectedProject(project);
        setProjectPopoverOpen(true);
        setProjectPopoverAnchorEl(event.currentTarget);
      };
    };

    const handleClose = () => {
      setProjectPopoverOpen(false);
      setProjectPopoverAnchorEl(null);
    };

    const { timezone, sunrise, sunset } =
      selectedPortfolioProjectList.activeProjects.length &&
      selectedPortfolioProjectList.activeProjects[0];
    const localTime = timezone ? moment().tz(timezone) : moment();
    const nightTime = localTime < moment(sunrise) || localTime > moment(sunset);
    if (projectDataError || portfolioDataError) {
      return (
        <Alert>
          There was an error fetching data and IT has been alerted. Please try
          again in a few minutes.
        </Alert>
      );
    }
    return (
      <>
        <Grid
          item
          onClick={(e) => e.preventDefault()}
          style={{
            overflow: 'hidden',
            borderRadius: theme.shape.borderRadius,
          }}
        >
          <Map
            portfolioId={selectedPortfolioProjectList.id}
            nightTime={nightTime}
            markers={getFilteredActiveProjectList(
              selectedPortfolioProjectList.activeProjects
            )?.map((project) => ({
              onMarkerClick: onMarkerClick(project),
              latitude: project.latitude,
              longitude: project.longitude,
              color: project.projectMonitoringStatus?.color || 'gray',
            }))}
          />
        </Grid>
        {projectPopoverOpen ? (
          <Popover
            open={projectPopoverOpen}
            anchorEl={projectPopoverAnchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
          >
            <ProjectMonitoringPopover
              onClick={handleClose}
              data={selectedProject}
            />
          </Popover>
        ) : null}
      </>
    );
  };

  const renderPortfolioProjects = () => {
    const cellBackgroundStyle = { backgroundColor: 'rgba(0,0,0,.03' };
    const titleCellBackgroundStyle = { backgroundColor: 'rgba(0,0,0,.08' };
    // NOTE: only Ryan from marketing gets the different view. In the future this could be dynamic.
    const showExpected =
      !permissions?.email ||
      !(['<EMAIL>'].indexOf(permissions?.email) > -1);
    // TODO: if fullScreen render a list element that works on mobile instead of table
    return (
      <TableContainer
        component={Paper}
        style={{ borderRadius: theme.shape.borderRadius }}
      >
        <Table
          size="small"
          style={{
            overflow: 'hidden',
            borderRadius: theme.shape.borderRadius,
          }}
        >
          <TableHead>
            <TableRow>
              <TableCell colSpan={4} />
              {showExpected && (
                <TableCell
                  align="center"
                  colSpan={showExpected ? 3 : 2}
                  style={{ ...cellBackgroundStyle }}
                >
                  <b>Last 30 Days</b>
                </TableCell>
              )}
              {showExpected && (
                <TableCell align="center" colSpan={showExpected ? 3 : 2}>
                  <b>Last 90 Days</b>
                </TableCell>
              )}
              <TableCell
                align="center"
                colSpan={showExpected ? 3 : 2}
                style={{ ...cellBackgroundStyle }}
              >
                <b>Last 365 Days</b>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell
                style={{ ...titleCellBackgroundStyle, padding: '0px' }}
              />
              <TableCell
                style={{ ...titleCellBackgroundStyle, paddingLeft: '0px' }}
              >
                <b>Project</b>
              </TableCell>
              <TableCell
                style={{ ...titleCellBackgroundStyle, padding: '8px' }}
              >
                <Grid
                  container
                  direction="column"
                  alignItems="left"
                  textAlign="left"
                >
                  <Grid item>
                    <b>Monitoring Start Dt</b>
                  </Grid>
                  <Grid item>
                    <Typography
                      variant="body2"
                      fontStyle="italic"
                      textAlign="left"
                    >
                      (COD)
                    </Typography>
                  </Grid>
                </Grid>
              </TableCell>
              <TableCell
                style={{
                  borderRight: '2px solid rgb(224,224,224)',
                  ...titleCellBackgroundStyle,
                  padding: '8px',
                  textAlign: 'center',
                }}
              >
                <b>Sys. Size (DC)</b>
              </TableCell>
              {showExpected && (
                <>
                  <TableCell align="center" style={{ ...cellBackgroundStyle }}>
                    <b>Actual</b>
                  </TableCell>
                  <TableCell align="center" style={{ ...cellBackgroundStyle }}>
                    <b>Projected</b>
                  </TableCell>
                  {showExpected && (
                    <TableCell
                      align="center"
                      style={{ ...cellBackgroundStyle }}
                    >
                      <b>Expected</b>
                    </TableCell>
                  )}
                </>
              )}
              {showExpected && (
                <>
                  <TableCell align="center">
                    <b>Actual</b>
                  </TableCell>
                  <TableCell align="center">
                    <b>Projected</b>
                  </TableCell>
                  {showExpected && (
                    <TableCell align="center">
                      <b>Expected</b>
                    </TableCell>
                  )}
                </>
              )}
              <TableCell align="center" style={{ ...cellBackgroundStyle }}>
                <b>Actual</b>
              </TableCell>
              <TableCell align="center" style={{ ...cellBackgroundStyle }}>
                <b>Projected</b>
              </TableCell>
              {showExpected && (
                <TableCell align="center" style={{ ...cellBackgroundStyle }}>
                  <b>Expected</b>
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {getFilteredActiveProjectList(
              selectedPortfolioProjectList.activeProjects
            )
              .sort((a, b) => {
                // If either project lacks monitoring status or orderNo, put them at the end
                if (
                  !a.projectMonitoringStatus ||
                  !a.projectMonitoringStatus.orderNo
                )
                  return 1;
                if (
                  !b.projectMonitoringStatus ||
                  !b.projectMonitoringStatus.orderNo
                )
                  return -1;

                // Compare orderNo
                if (
                  a.projectMonitoringStatus.orderNo <
                  b.projectMonitoringStatus.orderNo
                )
                  return -1;
                if (
                  a.projectMonitoringStatus.orderNo >
                  b.projectMonitoringStatus.orderNo
                )
                  return 1;

                // If orderNo is equal, use name as tiebreaker
                return a.name.localeCompare(b.name);
              })
              .map((project) => {
                const projectedDiff30 =
                  (project.last30DayActual - project.last30DayProjection) /
                    project.last30DayProjection || 0;
                const projectedDiff90 =
                  (project.last90DayActual - project.last90DayProjection) /
                    project.last90DayProjection || 0;
                const projectedDiff365 =
                  (project.last365DayActual - project.last365DayProjection) /
                    project.last365DayProjection || 0;
                const expectedDiff30 =
                  (project.last30DayActual - project.last30DayExpected) /
                    project.last30DayExpected || 0;
                const expectedDiff90 =
                  (project.last90DayActual - project.last90DayExpected) /
                    project.last90DayExpected || 0;
                const expectedDiff365 =
                  (project.last365DayActual - project.last365DayExpected) /
                    project.last365DayExpected || 0;
                let rowSelected;
                if (selectedProject) {
                  rowSelected = selectedProject.id === project.id;
                } else if (selectedProject === null) {
                  rowSelected = false;
                } else {
                  rowSelected =
                    getFilteredActiveProjectList(
                      selectedPortfolioProjectList.activeProjects
                    )?.length === 1;
                }

                return (
                  <Fragment
                    key={`selected-portfolio-project-list-${project.id}`}
                  >
                    <TableRow
                      hover
                      onClick={() =>
                        setSelectedProject(rowSelected ? null : project)
                      }
                      style={{ cursor: 'pointer' }}
                      selected={rowSelected}
                      key={`table-row-project-${project.id}`}
                    >
                      <TableCell style={{ ...titleCellBackgroundStyle }}>
                        {project.projectMonitoringStatus ? (
                          <Tooltip
                            title={
                              <>
                                <Typography
                                  variant="body2"
                                  style={{ fontWeight: 'bold' }}
                                >
                                  Monitoring Status
                                </Typography>
                                <Typography variant="body2">
                                  {project.projectMonitoringStatus.name}
                                  {project.monitoringNotes
                                    ? ` - ${project.monitoringNotes}`
                                    : ''}
                                </Typography>
                              </>
                            }
                            arrow
                          >
                            <span>
                              <Icon
                                style={{
                                  color: project.projectMonitoringStatus.color,
                                }}
                                className={
                                  project.projectMonitoringStatus.iconClassName
                                }
                              />
                            </span>
                          </Tooltip>
                        ) : null}
                      </TableCell>
                      <TableCell
                        style={{
                          ...titleCellBackgroundStyle,
                          paddingLeft: '0px',
                        }}
                      >
                        {/* <Badge
                          badgeContent={
                            !showExpected
                              ? null
                              : project.openMonitoringAlarmsCount > 0 ||
                                project.openOMTicketsCount > 0
                              ? '!'
                              : 0
                          }
                          color="error"
                          overlap="rectangular"
                        > */}
                        <Grid container direction="column">
                          <Grid item>
                            <Typography
                              variant="body2"
                              style={{ fontWeight: 'bold' }}
                              component={Link}
                              to={`/Project/${project.id}/monitoring`}
                            >
                              {project.name}
                            </Typography>
                          </Grid>
                          <Grid item>
                            <Typography
                              variant="body2"
                              style={{ color: 'rgba(0,0,0,.5)' }}
                            >
                              {project.projectInvestmentStatus &&
                                project.projectInvestmentStatus.name}{' '}
                            </Typography>
                          </Grid>
                        </Grid>
                        {/* </Badge> */}
                      </TableCell>
                      <TableCell
                        style={{
                          ...titleCellBackgroundStyle,
                          width: '100px',
                          padding: '8px',
                          textAlign: 'center',
                        }}
                      >
                        <Grid container direction="column">
                          <Grid item>
                            <Typography variant="body2" textAlign="left">
                              {project.monitoringStartDt ? (
                                moment(project.monitoringStartDt).format(
                                  'MMM D, YYYY'
                                )
                              ) : (
                                <br />
                              )}
                            </Typography>
                          </Grid>
                          <Grid item>
                            {project.actualCOD ? (
                              <Typography
                                variant="body2"
                                fontStyle="italic"
                                textAlign="left"
                              >
                                {`(${moment(project.actualCOD).format(
                                  "MMM D, 'YY"
                                )})`}
                              </Typography>
                            ) : (
                              <Typography
                                variant="body2"
                                fontStyle="italic"
                                style={{ color: '#999' }}
                                textAlign="left"
                              >
                                {`(${moment(project.projectedCOD).format(
                                  "MMM D, 'YY"
                                )})`}
                              </Typography>
                            )}
                          </Grid>
                        </Grid>
                      </TableCell>
                      <TableCell
                        style={{
                          borderRight: '2px solid rgb(224,224,224)',
                          ...titleCellBackgroundStyle,
                          padding: '8px',
                          textAlign: 'center',
                        }}
                      >
                        {project.systemSizeDC || '-'} MW
                      </TableCell>
                      {showExpected && (
                        <>
                          <TableCell
                            align="center"
                            style={{ ...cellBackgroundStyle }}
                          >{`${numeral(project.last30DayActual / 1000).format(
                            '0,0[.]0'
                          )} MWh`}</TableCell>
                          <TableCell
                            align="center"
                            style={{ ...cellBackgroundStyle }}
                          >
                            <Grid container direction="column">
                              <Grid item>
                                {`${numeral(
                                  project.last30DayProjection / 1000
                                ).format('0,0[.]0')} MWh`}
                              </Grid>
                              <Grid item>
                                <Typography
                                  variant="body2"
                                  style={{
                                    color: getDiffColor(projectedDiff30),
                                    fontWeight: 'bold',
                                  }}
                                >
                                  {projectedDiff30 > 0 ? '+' : ''}
                                  {projectedDiff30 === Infinity ? (
                                    <span>&infin;</span>
                                  ) : (
                                    projectedDiff30 &&
                                    numeral(projectedDiff30).format('%0,0[.]00')
                                  )}
                                </Typography>
                              </Grid>
                            </Grid>
                          </TableCell>
                          {showExpected && (
                            <TableCell
                              align="center"
                              style={{ ...cellBackgroundStyle }}
                            >
                              {project.last30DayExpected !== null ? (
                                <Grid container direction="column">
                                  <Grid item>
                                    {`${numeral(
                                      project.last30DayExpected / 1000
                                    ).format('0,0[.]0')} MWh`}
                                  </Grid>
                                  <Grid item>
                                    <Typography
                                      variant="body2"
                                      style={{
                                        color: getDiffColor(expectedDiff30),
                                        fontWeight: 'bold',
                                      }}
                                    >
                                      {expectedDiff30 > 0 ? '+' : ''}
                                      {expectedDiff30 === Infinity ? (
                                        <span>&infin;</span>
                                      ) : (
                                        expectedDiff30 &&
                                        numeral(expectedDiff30).format(
                                          '%0,0[.]00'
                                        )
                                      )}
                                    </Typography>
                                  </Grid>
                                </Grid>
                              ) : (
                                '--'
                              )}
                            </TableCell>
                          )}
                        </>
                      )}

                      {showExpected && (
                        <>
                          <TableCell align="center">{`${numeral(
                            project.last90DayActual / 1000
                          ).format('0,0[.]0')} MWh`}</TableCell>
                          <TableCell align="center">
                            <Grid container direction="column">
                              <Grid item>
                                {`${numeral(
                                  project.last90DayProjection / 1000
                                ).format('0,0[.]0')} MWh`}
                              </Grid>
                              <Grid item>
                                <Typography
                                  variant="body2"
                                  style={{
                                    color: getDiffColor(projectedDiff90),
                                    fontWeight: 'bold',
                                  }}
                                >
                                  {projectedDiff90 > 0 ? '+' : ''}
                                  {projectedDiff90 === Infinity ? (
                                    <span>&infin;</span>
                                  ) : (
                                    projectedDiff90 &&
                                    numeral(projectedDiff90).format('%0,0[.]00')
                                  )}
                                </Typography>
                              </Grid>
                            </Grid>
                          </TableCell>
                          {showExpected && (
                            <TableCell align="center">
                              {project.last90DayExpected !== null ? (
                                <Grid container direction="column">
                                  <Grid item>
                                    {`${numeral(
                                      project.last90DayExpected / 1000
                                    ).format('0,0[.]0')} MWh`}
                                  </Grid>
                                  <Grid item>
                                    <Typography
                                      variant="body2"
                                      style={{
                                        color: getDiffColor(expectedDiff90),
                                        fontWeight: 'bold',
                                      }}
                                    >
                                      {expectedDiff90 > 0 ? '+' : ''}
                                      {expectedDiff90 === Infinity ? (
                                        <span>&infin;</span>
                                      ) : (
                                        expectedDiff90 &&
                                        numeral(expectedDiff90).format(
                                          '%0,0[.]00'
                                        )
                                      )}
                                    </Typography>
                                  </Grid>
                                </Grid>
                              ) : (
                                '--'
                              )}
                            </TableCell>
                          )}
                        </>
                      )}

                      <TableCell
                        align="center"
                        style={{ ...cellBackgroundStyle }}
                      >{`${numeral(project.last365DayActual / 1000).format(
                        '0,0[.]0'
                      )} MWh`}</TableCell>
                      <TableCell
                        align="center"
                        style={{ ...cellBackgroundStyle }}
                      >
                        <Grid container direction="column">
                          <Grid item>
                            {`${numeral(
                              project.last365DayProjection / 1000
                            ).format('0,0[.]0')} MWh`}
                          </Grid>
                          <Grid item>
                            <Typography
                              variant="body2"
                              style={{
                                color: getDiffColor(projectedDiff365),
                                fontWeight: 'bold',
                              }}
                            >
                              {projectedDiff365 > 0 ? '+' : ''}
                              {projectedDiff365 === Infinity ? (
                                <span>&infin;</span>
                              ) : (
                                projectedDiff365 &&
                                numeral(projectedDiff365).format('%0,0[.]00')
                              )}
                            </Typography>
                          </Grid>
                        </Grid>
                      </TableCell>
                      {showExpected && (
                        <TableCell
                          align="center"
                          style={{ ...cellBackgroundStyle }}
                        >
                          {project.last365DayExpected !== null ? (
                            <Grid container direction="column">
                              <Grid item>
                                {`${numeral(
                                  project.last365DayExpected / 1000
                                ).format('0,0[.]0')} MWh`}
                              </Grid>
                              <Grid item>
                                <Typography
                                  variant="body2"
                                  style={{
                                    color: getDiffColor(expectedDiff365),
                                    fontWeight: 'bold',
                                  }}
                                >
                                  {expectedDiff365 > 0 ? '+' : ''}
                                  {expectedDiff365 === Infinity ? (
                                    <span>&infin;</span>
                                  ) : (
                                    expectedDiff365 &&
                                    numeral(expectedDiff365).format('%0,0[.]00')
                                  )}
                                </Typography>
                              </Grid>
                            </Grid>
                          ) : (
                            '--'
                          )}
                        </TableCell>
                      )}
                    </TableRow>
                    <AssetMgmtProjectExpansionPanel
                      open={rowSelected}
                      project={project}
                      refetchPortfolioData={fetchPortfolios}
                      contextUser={{
                        id: permissions?.id,
                        employee: permissions?.employee,
                        fullName: `${permissions?.firstName} ${permissions?.lastName}`,
                        roles: permissions?.roles,
                      }}
                    />
                  </Fragment>
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  let portfolioTilesJsx = null;

  const getProjectCount = () => {
    if (!selectedPortfolio) return null;
    return getFilteredActiveProjectList(
      selectedPortfolio.activeProjects
    ).reduce((count, project) => {
      return project?.projectInvestmentStatus?.id === 8 ? count + 1 : count;
    }, 0);
  };

  const getTotalSysSize = () => {
    if (!selectedPortfolio) return null;
    return getFilteredActiveProjectList(
      selectedPortfolio.activeProjects
    ).reduce((acc, v) => {
      return v?.projectInvestmentStatus?.id === 8 ? acc + v.systemSizeDC : acc;
    }, 0);
  };

  if (portfolios) {
    portfolioTilesJsx = portfolios.map((portfolio) => {
      const last30DaysPortfolioProjection =
        getFilteredActiveProjectList(portfolio.activeProjects)?.reduce(
          (acc, v) => {
            if (
              v.projectMonitoringStatus &&
              v.projectMonitoringStatus.name === 'Active'
            ) {
              return acc + v.last30DayProjection;
            }
            return acc;
          },
          0
        ) / 1000;
      const last30DaysPortfolioActual =
        (portfolio?.last30EnergyProductionData?.[0] &&
          portfolio.last30EnergyProductionData[
            portfolio.last30EnergyProductionData.length - 1
          ].value) ||
        0;
      const selected =
        portfolio.id === (selectedPortfolio && selectedPortfolio.id);
      // const projectsWithOpenAlerts = portfolio.activeProjects.filter(
      //   (project) =>
      //     project.openMonitoringAlarmsCount > 0 ||
      //     project.openOMTicketsCount > 0
      // );
      const portfolioLast30GenerationDiff =
        (last30DaysPortfolioActual - last30DaysPortfolioProjection) /
        last30DaysPortfolioProjection;

      return (
        <Grid
          key={`portfolio-container-${portfolio.id}`}
          item
          // NOTE: If you change the breakpoints here, change them in the else clause also for the skeletons
          xxl={3}
          xl={4}
          sm={6}
          xs={12}
        >
          <Card
            elevation={selected ? 0 : 10}
            style={{
              height: '10.5rem',
              border: selected ? `solid ${theme.palette.primary.main} 3px` : '',
              backgroundColor: selected ? 'rgba(230, 230, 230, 0.3)' : '',
              boxSizing: 'border-box',
              borderRadius: theme.shape.borderRadius,
            }}
          >
            <CardActionArea
              onClick={() => {
                // TODO: set the query string
                if (history.pushState) {
                  var newurl =
                    window.location.protocol +
                    '//' +
                    window.location.host +
                    window.location.pathname +
                    `?portfolio=${portfolio.id}`;
                  window.history.pushState({ path: newurl }, '', newurl);
                }
                setSelectedPortfolio(portfolio);
                setSelectedProject(null);
              }}
              aria-label={`${portfolio.subtitle} chart`}
              style={{
                cursor: 'pointer',
                height: '100%',
                padding: selected ? null : '3px',
              }}
            >
              <CardContent>
                <Grid
                  container
                  justifyContent="center"
                  alignItems="center"
                  item
                  xs={12}
                >
                  <Grid item xs={12}>
                    <Typography variant="h5" style={{ fontWeight: 'normal' }}>
                      {portfolio.subtitle}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" style={{ fontStyle: 'italic' }}>
                      Last 30 days
                    </Typography>
                    {/* <Tooltip
                      arrow
                      title="Total generation over the last 30 days."
                    >
                      <Grid container alignItems="baseline">
                        <Grid item>
                          <Typography variant="h5">
                            {numeral(last30DaysPortfolioActual).format(
                              '0,0.[0]'
                            )}
                          </Typography>
                        </Grid>
                        <Grid item>
                          <Typography
                            variant="h6"
                            style={{ marginLeft: '3px' }}
                          >
                            MWh
                          </Typography>
                        </Grid>
                      </Grid>
                    </Tooltip> */}
                    <Tooltip
                      arrow
                      title="Percentage below P50 over the last 30 days."
                    >
                      <Typography
                        variant="h5"
                        style={{
                          color: getDiffColor(portfolioLast30GenerationDiff),
                          fontWeight: 'bold',
                        }}
                      >
                        {last30DaysPortfolioActual >
                        last30DaysPortfolioProjection
                          ? '+'
                          : ''}
                        {last30DaysPortfolioActual === 0
                          ? '--%'
                          : numeral(portfolioLast30GenerationDiff).format(
                              '0,0.[00]%'
                            )}
                      </Typography>
                    </Tooltip>
                    {/* <Typography
                      style={{
                        color: theme.palette.error.dark,
                        visibility:
                          projectsWithOpenAlerts.length === 0 ? 'hidden' : null,
                      }}
                      variant="body2"
                    >
                      {projectsWithOpenAlerts.length} project
                      {projectsWithOpenAlerts.length > 1 ? 's' : ''} with open
                      alerts
                    </Typography> */}
                    <Tooltip
                      arrow
                      title="Total generation currently being lost due to open O&M tickets."
                    >
                      <Typography
                        variant="body2"
                        style={{
                          color: getLossColor(
                            portfolio.percentGenerationImpactedByOpenOMTickets
                          ),
                          // fontWeight: 'bold',
                        }}
                      >
                        {numeral(
                          portfolio.percentGenerationImpactedByOpenOMTickets
                        ).format('%0[.]0')}{' '}
                        generation affected
                      </Typography>
                    </Tooltip>
                  </Grid>
                  <Grid item xs={6}>
                    <MiniProductionLineChart
                      portfolio={portfolio}
                      projection={last30DaysPortfolioProjection}
                      lineColor={getDiffColor(portfolioLast30GenerationDiff)}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </CardActionArea>
          </Card>
        </Grid>
      );
    });
  } else {
    portfolioTilesJsx = [1, 2, 3, 4, 5, 6].map((index) => (
      <Grid
        key={`portfolio-container-skeleton-${index}`}
        item
        // NOTE: If you change the breakpoints here, change them in the if clause also for the Cards
        xxl={3}
        xl={4}
        sm={6}
        xs={12}
      >
        <Skeleton
          animation="wave"
          variant="rectangular"
          style={{
            borderRadius: theme.shape.borderRadius,
          }}
          height="10.5rem"
        />
      </Grid>
    ));
  }

  const handleClickDownloadMonthlyProduction = (portfolioId) => {
    setMonthlyReportLoading(true);
    dataProvider
      .create('PortfolioMonthlyGenerationReport', {
        input: {
          portfolioId: parseInt(portfolioId, 10),
        },
      })
      .then(
        (res) => {
          setMonthlyReportLoading(false);
          const link = document.createElement('a');
          link.href = res.data.downloadUrl;
          link.download = true;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (e) => {
          setMonthlyReportLoading(false);
          console.error('Error downloading monthly generation report', e);
          notify('Error downloading monthly generation report', {
            type: 'error',
          });
        }
      );
  };

  const last30GlobalDiff = projectSummary
    ? (projectSummary?.last30DayActualGeneration -
        projectSummary?.last30DayProjectedGeneration) /
      projectSummary?.last30DayProjectedGeneration
    : 0;

  return (
    <>
      <Backdrop
        open={!!showWelcome}
        style={{
          zIndex: 1800,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundColor: '#fff',
          // backgroundImage: `url(${mobileBackgroundImage})`,
        }}
        timeout={{
          // appear: 100,
          // enter: 100,
          exit: 500,
        }}
      >
        <Grid
          style={{
            // width: '600px',
            padding: '4rem',
            // height: '600px',
            textAlign: 'center',
            alignItems: 'center',
          }}
          container
          direction="column"
          justifyContent="center"
        >
          <Grid item lg={6} md={10} xs={12}>
            <Image
              cloud_name={Config.cloud_name}
              publicId="energea/cms-assets/STARLIGHT_LOGO"
            >
              <Transformation width="600" crop="scale" />
            </Image>
          </Grid>
          <CircularProgress />
        </Grid>
      </Backdrop>
      <Grid container style={{ padding: '1rem' }}>
        {isPartner ? (
          <PartnerMonitoringHeader
            portfolios={portfolios}
            getDiffColor={getDiffColor}
          />
        ) : (
          <>
            <Grid item xs={12} style={{ textAlign: 'right' }}>
              <Button
                component="a"
                download
                target="_blank"
                href="https://asset.cloudinary.com/energea/bf8d9f27e0a7fecef1187a1ea60e3a10"
                variant="contained"
                color="primary"
                onClick={() => {
                  alert(
                    `You will be redirected to a download link once you clock 'OK'. \n\nIf you do not have the desktop Intelbras app you can download it here (https://www.intelbras.com/en/monitoring-software-intelbras-sim-play). Then choose to 'import' this file and use the password 'energea'.`
                  );
                }}
                startIcon={<CloudDownload />}
                style={{ marginRight: '1rem' }}
              >
                Intelbras Camera Import File
              </Button>
              <Button
                component={Link}
                variant="contained"
                color="primary"
                to="/ProjectMonitoringRequirement"
                startIcon={<CloudDownload />}
              >
                Data Punchlist Summary
              </Button>
            </Grid>
            <Grid
              item
              xs={12}
              style={{ margin: '1rem 0 2rem', textAlign: 'right' }}
            >
              {projectSummary ? (
                <>
                  <Grid container justifyContent="space-evenly" spacing={10}>
                    <Grid item xs={4}>
                      <Grid
                        container
                        direction="column"
                        alignItems="center"
                        justifyContent="center"
                        style={{ minWidth: !fullScreen && '8rem' }}
                      >
                        <Grid
                          container
                          style={{ minHeight: '5rem' }}
                          item
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Grid
                            container
                            spacing={3}
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Grid item>
                              <Typography
                                style={{
                                  fontWeight: 'bold',
                                  textAlign: 'center',
                                  color: theme.palette.green.main,
                                }}
                                variant="h3"
                              >
                                {numeral(projectSummary.projectCount).format(
                                  '0,0'
                                )}{' '}
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Grid container direction="column" spacing={0}>
                                {projectSummary.projectCountByStatus
                                  .sort((a, b) => {
                                    if (a.status === 'Cash Flowing') return -1;
                                    if (a.status === 'Construction') return 0;
                                    if (a.status === 'Development') return 1;
                                  })
                                  .map((el, i) => (
                                    <Grid
                                      item
                                      key={`proj-count-summary-key-${i}`}
                                    >
                                      <Grid
                                        container
                                        justifyContent="space-between"
                                        spacing={2}
                                      >
                                        <Grid item>
                                          <Typography variant="body2">
                                            {el.status}
                                          </Typography>
                                        </Grid>
                                        <Grid item>
                                          <Typography variant="body2">
                                            {numeral(el.count).format('0,0')}{' '}
                                          </Typography>
                                        </Grid>
                                      </Grid>
                                    </Grid>
                                  ))}
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item>
                          <Typography>
                            <b>Projects</b>
                          </Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={4}>
                      <Grid
                        container
                        direction="column"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Grid
                          container
                          style={{ minHeight: '5rem' }}
                          item
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Grid
                            container
                            spacing={3}
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Grid item>
                              <Typography
                                style={{
                                  fontWeight: 'bold',
                                  textAlign: 'center',
                                  color: theme.palette.green.main,
                                }}
                                variant="h3"
                              >
                                {numeral(projectSummary.totalSystemSize).format(
                                  '0,0'
                                )}{' '}
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Grid container direction="column" spacing={0}>
                                {projectSummary.totalSystemSizeByStatus
                                  .sort((a, b) => {
                                    if (a.status === 'Cash Flowing') return -1;
                                    if (a.status === 'Construction') return 0;
                                    if (a.status === 'Development') return 1;
                                  })
                                  .map((el, i) => (
                                    <Grid
                                      item
                                      key={`proj-system-size-summary-key-${i}`}
                                    >
                                      <Grid
                                        container
                                        justifyContent="space-between"
                                        spacing={2}
                                      >
                                        <Grid item>
                                          <Typography variant="body2">
                                            {el.status}
                                          </Typography>
                                        </Grid>
                                        <Grid item>
                                          <Typography variant="body2">
                                            {numeral(el.systemSize).format(
                                              '0,0.0'
                                            )}{' '}
                                          </Typography>
                                        </Grid>
                                      </Grid>
                                    </Grid>
                                  ))}
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item>
                          <Typography>
                            <b>Installed Capacity (MWdc)</b>
                          </Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={4}>
                      <Grid
                        container
                        direction="column"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Grid
                          container
                          style={{ minHeight: '5rem' }}
                          item
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Grid
                            container
                            spacing={3}
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Grid item>
                              <Typography
                                style={{
                                  fontWeight: 'bold',
                                  textAlign: 'center',
                                  color: theme.palette.green.main,
                                }}
                                variant="h3"
                              >
                                {numeral(
                                  projectSummary.last30DayActualGeneration /
                                    1000
                                ).format('0,0[.]0a')}
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Grid container direction="column" spacing={0}>
                                <Grid item>
                                  <Grid
                                    container
                                    justifyContent="space-between"
                                    spacing={2}
                                  >
                                    <Grid item>
                                      <Typography variant="body2">
                                        Projected
                                      </Typography>
                                    </Grid>
                                    <Grid item>
                                      <Typography variant="body2">
                                        {numeral(
                                          projectSummary.last30DayProjectedGeneration /
                                            1000
                                        ).format('0,0[.]0a')}{' '}
                                        {/* (
                                        <b
                                          style={{
                                            color:
                                              getDiffColor(last30GlobalDiff),
                                          }}
                                        >
                                          {last30GlobalDiff > 0 ? '+' : ''}
                                          {numeral(last30GlobalDiff).format(
                                            '%0[.]0'
                                          )}
                                        </b>
                                        ) */}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                </Grid>
                                <Grid item>
                                  <Grid
                                    container
                                    justifyContent="space-between"
                                    spacing={2}
                                  >
                                    <Grid item>
                                      <Typography variant="body2">
                                        Diff
                                      </Typography>
                                    </Grid>
                                    <Grid item>
                                      <Typography
                                        variant="body2"
                                        style={{
                                          color: getDiffColor(last30GlobalDiff),
                                          fontWeight: 'bold',
                                        }}
                                      >
                                        {last30GlobalDiff > 0 ? '+' : ''}
                                        {numeral(last30GlobalDiff).format(
                                          '%0[.]0'
                                        )}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                </Grid>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item>
                          <Typography>
                            <b>Generation Last 30 Days (MWh)</b>
                          </Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12} style={{ textAlign: 'left' }}>
                    <Typography
                      variant="body2"
                      style={{ color: 'rgba(0,0,0,.6)', paddingLeft: '1rem' }}
                    >
                      <em>
                        The above numbers are 'public unsold projects' in order
                        to stay consistent with what is shown on energea.com.
                      </em>
                    </Typography>
                  </Grid>
                </>
              ) : (
                <Grid
                  container
                  style={{
                    width: '100%',
                    height: '124px',
                    // margin: '1rem 0 2rem',
                  }}
                  justifyContent="center"
                  alignItems="center"
                >
                  <Grid item>
                    <CircularProgress />
                  </Grid>
                </Grid>
              )}
            </Grid>
            <Grid container spacing={3}>
              {portfolioTilesJsx}
            </Grid>
            <Divider
              style={{
                width: '100%',
                marginTop: '2em',
                marginBottom: '2em',
              }}
            />
          </>
        )}
        <Grid container direction="column" style={{ width: '100%' }}>
          <Grid container item style={{ paddingBottom: '1rem' }}>
            <Grid
              container
              justifyContent="space-between"
              alignItems="center"
              style={{ paddingBottom: '1rem' }}
            >
              <Grid item>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar>
                      <Icon
                        style={{ height: '100%', width: '100%' }}
                        className={`fi fi-${
                          selectedPortfolio?.countryCode?.toLowerCase() || 'xx'
                        } fis`}
                      />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      !isPartner &&
                      selectedPortfolio && <b>{selectedPortfolio.subtitle}</b>
                    }
                    secondary={
                      selectedPortfolio && (
                        <>
                          <b>{numeral(getProjectCount()).format('0,0')}</b> Cash
                          Flowing Projects&nbsp;&nbsp;|&nbsp;&nbsp;
                          <b>{numeral(getTotalSysSize()).format('0,0.0')}</b> MW
                        </>
                      )
                    }
                  />
                </ListItem>
                {/* <Grid container alignItems="center">
                  <Grid item>
                    <Typography variant="h5">
                      {!isPartner &&
                        selectedPortfolio &&
                        selectedPortfolio.subtitle}
                    </Typography>
                    <Typography
                      variant="caption"
                      style={{ lineHeight: 0, marginTop: '-8px' }}
                    >
                      15 Projects | 535 MW
                    </Typography>
                  </Grid>
                </Grid> */}
              </Grid>
              <Grid item>
                <Grid container alignItems="center">
                  <Grid item style={{ marginRight: '1rem' }}>
                    <Button
                      onClick={() => setPortfolioGenerationChartOpen(true)}
                      variant="outlined"
                      size="medium"
                    >
                      View generation details
                    </Button>
                  </Grid>
                  {selectedPortfolio &&
                    selectedPortfolio.id === 13 &&
                    !isPartner && (
                      <Grid item style={{ marginRight: '1rem' }}>
                        <Button
                          onClick={() =>
                            handleClickDownloadMonthlyProduction(
                              selectedPortfolio.id
                            )
                          }
                          variant="outlined"
                          size="medium"
                          disabled={monthlyReportLoading}
                        >
                          {monthlyReportLoading ? (
                            <CircularProgress
                              style={{ position: 'absolute' }}
                            />
                          ) : null}{' '}
                          Download monthly generation
                        </Button>
                      </Grid>
                    )}
                  <Grid item>
                    <ToggleButtonGroup
                      size="small"
                      value={viewType}
                      exclusive
                      onChange={(event) => {
                        const { value } = event.currentTarget;
                        setViewType(value);
                      }}
                    >
                      <ToggleButton value={'map'}>Map View</ToggleButton>
                      <ToggleButton value={'list'}>List View</ToggleButton>
                    </ToggleButtonGroup>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          {selectedPortfolio &&
          selectedPortfolioProjectList &&
          !(viewType !== 'map' && portfolioProjectListLoading) ? (
            viewType === 'map' ? (
              renderMapView()
            ) : (
              renderPortfolioProjects()
            )
          ) : (
            <Grid container>
              <Grid item xs={12}>
                <Skeleton
                  animation="wave"
                  variant="rectangular"
                  height="40rem"
                  style={{
                    borderRadius: theme.shape.borderRadius,
                  }}
                />
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
      <PortfolioGenerationChart
        key={`portfolio-generation-chart-${selectedPortfolio?.id}`}
        portfolioName={selectedPortfolio?.subtitle}
        portfolioId={selectedPortfolio?.id}
        open={portfolioGenerationChartOpen}
        onClose={() => setPortfolioGenerationChartOpen(false)}
      />
    </>
  );
});
