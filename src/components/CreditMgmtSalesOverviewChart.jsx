import React, { useState } from 'react';
import { CircularProgress, Grid, Typography } from '@mui/material';
import { Alert } from '@mui/lab';
import { useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';
import 'chartjs-adapter-moment';
import { Bar } from 'react-chartjs-2';

import { interpolateColors } from '../utils/global';

export default (props) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(null);
  const [error, setError] = useState(null);

  const notify = useNotify();
  const dataProvider = useDataProvider();

  const getBorderColorsArray = (numColors) => {
    return interpolateColors(numColors, null, null, 1);
  };

  const getColorsArray = (numColors) => {
    return interpolateColors(numColors, null, null, 0.8);
  };

  const fetchData = () => {
    dataProvider.getOne('CreditMgmtSalesOverviewChartData', {}).then(
      (res) => {
        setLoading(false);
        setError(null);
        setData(res.data.monthlyBrSaleVolumeByPartner);
      },
      (err) => {
        console.error(err);
        setError(err);
        setLoading(false);
        notify('Failed to fetch list of projects', {
          type: 'error',
        });
      }
    );
  };

  if (!loading && !data && !error) {
    fetchData();
  }

  if (error) {
    return <Alert severity="error">Error loading data</Alert>;
  }

  if (!data || loading) {
    return (
      <Grid container style={{ width: '100%' }} justifyContent="center">
        <CircularProgress />
      </Grid>
    );
  }

  const sellerNames = [];
  data.forEach((month) => {
    month.sellers.forEach((seller) => {
      if (!sellerNames.includes(seller.name)) {
        sellerNames.push(seller.name);
      }
    });
  });
  const datasets = [];

  let borderColorsArray = getBorderColorsArray(sellerNames.length);
  let colorsArray = getColorsArray(sellerNames.length);

  sellerNames.forEach((seller) => {
    const dataValues = [];
    data.forEach((month) => {
      const sellerData = month.sellers.find((s) => s.name === seller);
      dataValues.push({
        x: month.month,
        y: sellerData ? sellerData.soldConsumptionKWh / 1000 : 0,
      });
    });
    datasets.push({
      label: seller,
      data: dataValues,
      borderColor: borderColorsArray.shift(),
      backgroundColor: colorsArray.shift(),
    });
  });

  return (
    <Bar
      height={400}
      data={{
        datasets,
      }}
      options={{
        maintainAspectRatio: false,
        borderRadius: 4,
        plugins: {
          legend: {
            onClick: (e, legendItem, legend) => {
              const chart = legend.chart;
              const datasetIndex = legendItem.datasetIndex;
              const meta = chart.getDatasetMeta(datasetIndex);

              const allHidden = chart.data.datasets.every((_, i) => {
                return chart.getDatasetMeta(i).hidden === true;
              });

              const onlyThisVisible = chart.data.datasets.every((_, i) => {
                const m = chart.getDatasetMeta(i);
                return i === datasetIndex ? !m.hidden : m.hidden === true;
              });

              if (onlyThisVisible) {
                // If only this one is visible, reset and show all
                chart.data.datasets.forEach((_, i) => {
                  chart.getDatasetMeta(i).hidden = false;
                });
              } else {
                // Otherwise, show only the clicked one
                chart.data.datasets.forEach((_, i) => {
                  chart.getDatasetMeta(i).hidden = i !== datasetIndex;
                });
              }

              chart.update();
            },
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            position: 'nearest',
            callbacks: {
              label: (tooltipItem) => {
                if (tooltipItem?.raw?.y === 0) {
                  return null;
                }
                return `${tooltipItem.dataset.label}: ${numeral(
                  tooltipItem.formattedValue
                ).format('0,0[.][00]')} MWh`;
              },
            },
          },
        },
        scales: {
          x: {
            stacked: true,
            type: 'time',
            time: {
              tooltipFormat: 'YYYY-MM',
              unit: 'month',
            },
          },
          y: {
            stacked: true,
            ticks: {
              // callback: (value) => numeral(value).format('$0,0.[0]a'),
            },
            title: {
              display: true,
              text: 'Avg Monthly Cons. Signed (MWh)',
            },
            beginAtZero: true,
          },
        },
      }}
    />
  );
};
