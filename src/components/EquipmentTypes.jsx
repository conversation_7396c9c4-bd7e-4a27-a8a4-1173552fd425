import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>yField,
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Icon } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, CustomReferenceField } from './CustomFields';

const entityName = 'Equipment Type';

export const EquipmentTypeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth step={1} />
            <TextInput source="iconClass" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const EquipmentTypeList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <FunctionField
          label="Icon"
          sortable={false}
          render={(record) => (
            <Icon
              style={{ width: '2rem', textAlign: 'center', color: '#666' }}
              className={record.iconClass}
            />
          )}
        />
        <TextField source="name" />
        <NumberField source="orderNo" />
        <ArrayField source="equipmentItems" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="model" />
          </SingleFieldList>
        </ArrayField>
      </Datagrid>
    </List>
  );
};

export const EquipmentTypeCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth required />
          <TextInput source="description" fullWidth />
          <CustomNumberInput
            source="orderNo"
            helperText="The order that equipment types show up in lists and selects"
            fullWidth
            step={1}
          />
          <TextInput
            source="iconClass"
            fullWidth
            helperText="Leave for IT to fill out"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
