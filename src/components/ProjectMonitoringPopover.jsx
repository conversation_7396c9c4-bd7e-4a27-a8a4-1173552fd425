import React, { Component, useState } from 'react';

import classnames from 'classnames';
import cloudinary from 'cloudinary-core';

// @material-ui
import { withStyles } from '@mui/styles';
import {
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  CircularProgress,
  Grid,
  Typography,
} from '@mui/material';
import { SimpleListLoading, useDataProvider, useNotify } from 'react-admin';

import numeral from 'numeral';
import moment from 'moment';

import Config from '../config/config';

const cl = new cloudinary.Cloudinary({
  cloud_name: Config.cloud_name,
  secure: true,
});

const styles = () => ({
  dataPoint: { fontSize: '1.7em' },
  dataDescription: { fontSize: '.75em' },
  dataIcon: { fontSize: '1.25em' },
});

const getFormattedSysSizeString = (sysSize) => {
  const sysSizeText =
    sysSize < 1
      ? `${numeral(sysSize * 1000).format('0,0')} kW`
      : `${numeral(sysSize).format('0.[0]')} MW`;
  return sysSizeText;
};

const ProjectMonitoringPopover = (props) => {
  const [projectMonitoringMapData, setProjectMonitoringMapData] =
    useState(null);
  const [loading, setLoading] = useState(false);

  const dataProvider = useDataProvider();
  const {
    classes,
    data,
    mediaHeight,
    hideDataIcons,
    onClick,
    // clickDestination
  } = props;
  const fetchProjectData = () => {
    dataProvider
      .getOne('ProjectMonitoringMapData', {
        id: data.id,
      })
      .then(
        (res) => {
          setLoading(false);
          setProjectMonitoringMapData(res.data);
        },
        (e) => {
          setLoading(false);
          console.error('Error retrieving project map data', e);
        }
      );
  };

  if (!data) return null;
  // let sClickDestination;
  // if (clickDestination === 'project') {
  //   sClickDestination = `/project/${data.id}`;
  // } else {
  //   sClickDestination = `/investment/${portfolioId ||
  //     (data.portfolio && data.portfolio.id)}`;
  // }

  if (!projectMonitoringMapData && data && !loading) {
    setLoading(true);
    fetchProjectData();
  }

  let formattedTimeSince = 'No data';
  if (projectMonitoringMapData?.mostRecentGenerationDataTimestamp) {
    const minsSinceLastPoint = moment().diff(
      moment(projectMonitoringMapData.mostRecentGenerationDataTimestamp),
      'minutes'
    );
    if (minsSinceLastPoint > 24 * 60) {
      formattedTimeSince = `${numeral(minsSinceLastPoint / (24 * 60)).format(
        '0,0[.]0'
      )} days ago`;
    } else if (minsSinceLastPoint > 60) {
      formattedTimeSince = `${numeral(minsSinceLastPoint / 60).format(
        '0,0[.]0'
      )} hrs ago`;
    } else {
      formattedTimeSince = `${numeral(minsSinceLastPoint).format(
        '0,0'
      )} mins ago`;
    }
  }

  return (
    <Grid style={{ maxWidth: '400px' }} item xs={12}>
      {/* <Link to={sClickDestination}> */}
      <Card className={classes.card}>
        <CardActionArea onClick={onClick || null}>
          <CardMedia
            component="img"
            alt={data.name}
            height={mediaHeight || '260'}
            src={
              'https://res.cloudinary.com/energea/image/upload/v1652373609/energea/icons/underconstruction.png'
            }
            image={cl.url(
              (data.primaryImage && data.primaryImage.public_id) ||
                'energea/icons/underconstruction',
              {
                width: '400',
                height: '260',
                crop: 'scale',
              }
            )}
            title={data.name}
          />
          <CardContent>
            <Typography gutterBottom variant="h5" component="h2">
              {data.name}
            </Typography>
            {/* <Typography variant="body2" color="textSecondary" component="p">
								{data.shortSummary} <br />
								<em>-{data.countryName}</em>
							</Typography> */}
            <Grid
              style={{
                fontSize: '1.25em',
                marginTop: '1em',
                color: '#999',
              }}
              container
            >
              <Grid
                item
                style={{
                  textAlign: 'center',
                  padding: '1em 1em 1em 0',
                  borderRight: 'solid 1px lightgrey',
                }}
                xs={6}
              >
                {hideDataIcons ? null : (
                  <i
                    className={classnames(
                      'fas',
                      'fa-bell-exclamation',
                      classes.dataIcon
                    )}
                  />
                )}
                <Typography className={classes.dataPoint} align="center">
                  {numeral(data.openMonitoringAlarmsCount).format('0,0')}
                </Typography>
                <Typography className={classes.dataDescription}>
                  Open Alarms
                </Typography>
              </Grid>
              <Grid
                style={{
                  textAlign: 'center',
                  padding: '1em 0 1em 1em',
                  marginRight: '-1em',
                  // borderRight: 'solid 1px lightgrey'
                }}
                item
                xs={6}
              >
                {hideDataIcons ? null : (
                  <i
                    className={classnames('fas', 'fa-clock', classes.dataIcon)}
                  />
                )}
                <Typography className={classes.dataPoint} align="center">
                  {loading ? <CircularProgress /> : formattedTimeSince}
                </Typography>
                <Typography className={classes.dataDescription}>
                  Time Since Last Data
                </Typography>
              </Grid>
              {data.systemSizeDC !== data.ownedSystemSizeDC ? (
                <Typography
                  variant="body2"
                  style={{ marginTop: '.5rem' }}
                  className={classes.dataDescription}
                >
                  <b>
                    *Energea investors own{' '}
                    {getFormattedSysSizeString(data.ownedSystemSizeDC)} of this
                    project.
                  </b>
                </Typography>
              ) : null}
            </Grid>
          </CardContent>
        </CardActionArea>
      </Card>
      {/* </Link> */}
    </Grid>
  );
};

export default withStyles(styles)(ProjectMonitoringPopover);
