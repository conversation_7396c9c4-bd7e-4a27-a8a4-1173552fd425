import React, { Component, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import classnames from 'classnames';
import cloudinary from 'cloudinary-core';

// @material-ui
import { withStyles } from '@mui/styles';
import {
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  CircularProgress,
  Grid,
  IconButton,
  Typography,
} from '@mui/material';
import { SimpleListLoading, useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment';

import Config from '../config/config';
import { Close } from '@mui/icons-material';

const styles = () => ({
  dataPoint: { fontSize: '1.7em' },
  dataDescription: { fontSize: '.75em' },
  dataIcon: { fontSize: '1.25em' },
});

const UserPopover = (props) => {
  const { classes, data, handleClose } = props;

  return (
    <Grid style={{ maxWidth: '400px' }} item xs={12}>
      <Card className={classes.card}>
        <CardContent>
          <Grid container>
            <Grid
              container
              item
              justifyContent="space-between"
              alignItems="center"
            >
              <Grid item>
                <Typography variant="h6">{data.fullName}</Typography>
              </Grid>
              <Grid item>
                <IconButton onClick={handleClose}>
                  <Close />
                </IconButton>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1">
                User since:{' '}
                <b>{moment(data.createdAt).format('MMM D, YYYY')}</b>
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1">
                Total invested:{' '}
                <b>{numeral(data.investmentSum || 0).format('$0,0[.]00')}</b>
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1">
                Investor age:{' '}
                <b>
                  {moment().diff(
                    moment(data.dateOfBirth, 'YYYY-MM-DD'),
                    'years'
                  )}
                </b>
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1">
                Is accredited: <b>{data.isAccredited ? 'Yes' : 'No'}</b>
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1">
                Environment (0) vs Returns (100):{' '}
                <b>{data.environmentalImpactVsReturns || 50}</b>
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body1" style={{ fontStyle: 'italic' }}>
                <a href={`/User/${data.id}`} target="_blank">
                  CMS
                </a>{' '}
                -{' '}
                <a href={data.hubSpotContactUrl} target="_blank">
                  HubSpot
                </a>
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Grid>
  );
};

export default withStyles(styles)(UserPopover);
