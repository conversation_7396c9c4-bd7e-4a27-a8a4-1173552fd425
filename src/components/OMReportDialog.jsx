import React, { useState } from 'react';
import { useDataProvider, useNotify, usePermissions } from 'react-admin';
import { openUploadWidget } from '../utils/CloudinaryService';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardActionArea,
  Checkbox,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import moment from 'moment';
import { CheckCircle, CloudUpload } from '@mui/icons-material';

import Config from '../config/config';
import theme from '../theme';

export const OMReportDialog = (props) => {
  const { fullScreen } = props;
  const [omReportTypeId, setOMReportTypeId] = useState(
    props.omReport?.omReportType?.id
  );
  const [diagnosticProcedureNotes, setDiagnosticProcedureNotes] = useState(
    props.omReport?.notes
  );
  const [ppeNotes, setPPENotes] = useState(props.omReport?.ppeNotes);
  const [rmaNotes, setRMANotes] = useState(props.omReport?.rmaNotes);
  const [partsUsedNotes, setPartsUsedNotes] = useState(
    props.omReport?.partsUsedNotes
  );
  const [partsOrderedNotes, setPartsOrderedNotes] = useState(
    props.omReport?.partsOrderedNotes
  );
  const [startDt, setStartDt] = useState(
    props.omReport?.startDt
      ? moment(props.omReport.startDt).format('yyyy-MM-DDTHH:mm')
      : null
  );
  const [endDt, setEndDt] = useState(
    props.omReport?.endDt
      ? moment(props.omReport.endDt).format('yyyy-MM-DDTHH:mm')
      : null
  );
  const [employeeIds, setEmployeeIds] = useState(
    props.omReport?.employees?.map((e) => e.id) || []
  );
  const [thirdPartyFlg, setThirdPartyFlg] = useState(
    props.omReport?.thirdPartyFlg
  );
  const [thirdPartyCompanyName, setThirdPartyCompanyName] = useState(
    props.omReport?.thirdPartyCompanyName
  );
  const [thirdPartyReportEnglishPublicId, setThirdPartyReportEnglishPublicId] =
    useState(props.omReport?.thirdPartyReportEnglishPublicId);
  const [
    thirdPartyReportPortuguesePublicId,
    setThirdPartyReportPortuguesePublicId,
  ] = useState(props.omReport?.thirdPartyReportPortuguesePublicId);
  const [resolvedFlg, setResolvedFlg] = useState(!!props.omReport?.resolvedFlg);
  const [flaggedForSubmission, setFlaggedForSubmission] = useState(
    !!props.omReport?.submittedDt || false
  );
  const [omReportTypesLoading, setOMReportTypesLoading] = useState(false);
  const [omEmployeesLoading, setOMEmployeesLoading] = useState(false);
  const [allOMReportTypes, setAllOMReportTypes] = useState(null);
  const [allOMEmployees, setAllOMEmployees] = useState(null);

  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  const fetchOMReportTypes = () => {
    setOMReportTypesLoading(true);
    dataProvider
      .getMany('OMReportType', {
        sort: { field: 'name', order: 'ASC' },
        filter: {},
      })
      .then((res) => {
        setAllOMReportTypes(res.data);
        setOMReportTypesLoading(false);
      });
  };

  const fetchOMEmployees = () => {
    setOMEmployeesLoading(true);
    dataProvider
      .getList('Employee', {
        sort: { field: 'lastName', order: 'ASC' },
        filter: {
          // TODO: Eventually we want to show the omTruck but also all om employees
          // but we dont yet have employee types created or assigned. Once we do, we
          // can have a hard coded set of ids in employeeFeed or a flag on the employee type table.
          // omEmployeesOnlyFlg: true
          omTruckId: props.omTruckId,
        },
        pagination: { page: 1, perPage: 10000 },
      })
      .then((res) => {
        setAllOMEmployees(res.data);
        setOMEmployeesLoading(false);
      });
  };

  if (!allOMReportTypes && !omReportTypesLoading) {
    fetchOMReportTypes();
  }
  if (!allOMEmployees && !omEmployeesLoading) {
    fetchOMEmployees();
  }

  const createReport = () => {
    setOMReportTypesLoading(true);
    const input = {
      employeeIds,
      notes: diagnosticProcedureNotes,
      ppeNotes,
      rmaNotes,
      partsUsedNotes,
      partsOrderedNotes,
      omTicketId: props.omTicketId,
      omReportTypeId,
      startDt: moment(startDt).toDate(),
      endDt: endDt ? moment(endDt).toDate() : null,
      resolvedFlg: resolvedFlg || false,
      thirdPartyFlg,
      thirdPartyCompanyName,
      thirdPartyReportEnglishPublicId,
      thirdPartyReportPortuguesePublicId,
    };
    if (flaggedForSubmission && !props.omReport?.submittedDt) {
      input.submittedDt = new Date();
      input.submittedByEmployeeId = permissions.employee?.id;
    }
    dataProvider
      .create('OMReport', {
        data: input,
      })
      .then(
        (res) => {
          notify('Report created', { type: 'success' });
          setOMReportTypesLoading(false);
          props.onClose();
        },
        (err) => {
          notify('Error creating report', { type: 'error' });
          setOMReportTypesLoading(false);
        }
      );
  };

  const updateReport = () => {
    if (!props.omReport?.id) {
      console.warn('No report id. Skipping update');
      return null;
    }
    setOMReportTypesLoading(true);
    const input = {
      id: parseFloat(props.omReport?.id, 10),
      notes: diagnosticProcedureNotes,
      employeeIds,
      ppeNotes,
      rmaNotes,
      partsUsedNotes,
      partsOrderedNotes,
      startDt: moment(startDt).toDate(),
      endDt: endDt ? moment(endDt).toDate() : null,
      resolvedFlg: resolvedFlg || false,
      omReportTypeId,
      thirdPartyCompanyName,
      thirdPartyFlg,
      thirdPartyReportEnglishPublicId,
      thirdPartyReportPortuguesePublicId,
    };
    if (flaggedForSubmission && !props.omReport?.submittedDt) {
      input.submittedDt = new Date();
      input.submittedByEmployeeId = permissions.employee?.id;
    }
    dataProvider
      .update('OMReport', {
        data: input,
      })
      .then(
        (res) => {
          notify('Report updated', { type: 'success' });
          setOMReportTypesLoading(false);
          props.onClose();
        },
        (err) => {
          notify('Error updating report', { type: 'error' });
          setOMReportTypesLoading(false);
        }
      );
  };

  const deleteReport = () => {
    setOMReportTypesLoading(true);
    dataProvider
      .delete('OMReport', {
        id: props.omReport?.id,
      })
      .then(
        () => {
          notify('Service report successfully deleted', { type: 'success' });
          setOMReportTypesLoading(false);
          props.onClose();
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error deleting service report', { type: 'error' });
          setOMReportTypesLoading(false);
        }
      );
  };

  const uploadThirdPartyReportWithCloudinary = (language) => {
    const uploadOptions = {
      tags: ['om-report'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.om_report_upload_preset,
      clientAllowedFormats: ['pdf'],
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        if (language === 'english') {
          setThirdPartyReportEnglishPublicId(resp.info?.public_id);
        } else if (language === 'portuguese') {
          setThirdPartyReportPortuguesePublicId(resp.info?.public_id);
        }
      }
    });
  };

  let dialogTitle = 'Create Report';
  let submit = () => {};
  if (props.action === 'create') {
    dialogTitle = 'Create Report';
    submit = createReport;
  } else if (props.action === 'update') {
    dialogTitle = 'Update Report';
    submit = updateReport;
  } else {
    console.error(`Unrecognized report action ${props.action}`);
    return null;
  }

  return (
    <Dialog open fullScreen={fullScreen}>
      <DialogTitle>{dialogTitle}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            {props.omReport?.submittedDt ? (
              <Alert severity="info">
                This service report was submitted by{' '}
                {props.omReport?.submittedByEmployee?.fullName} on{' '}
                {moment(props.omReport?.submittedDt).format(
                  'MMM D, YYYY HH:mm Z'
                )}
                .
              </Alert>
            ) : null}
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={thirdPartyFlg}
                  label="Dispatch by third party"
                  onChange={(event) => {
                    setThirdPartyFlg(!thirdPartyFlg);
                  }}
                  fullWidth
                />
              }
              label="Dispatch by third party"
            />
          </Grid>
          <Grid item xs={12} style={{ paddingTop: 0, paddingBottom: 0 }}>
            <Collapse in={thirdPartyFlg}>
              <Alert severity="info" icon={false}>
                <Grid container>
                  <Grid item xs={12}>
                    <TextField
                      label="Third Party Company Name"
                      value={thirdPartyCompanyName || ''}
                      multiline
                      required
                      onChange={(event) => {
                        setThirdPartyCompanyName(event.target.value);
                      }}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} style={{ paddingTop: '1rem' }}>
                    <Grid container spacing={2}>
                      <Grid item>
                        <Grid
                          container
                          direction="column"
                          style={{ cursor: 'pointer' }}
                          alignItems="center"
                          onClick={() =>
                            uploadThirdPartyReportWithCloudinary('english')
                          }
                        >
                          <Grid item>
                            {thirdPartyReportEnglishPublicId ? (
                              <CheckCircle
                                style={{
                                  fontSize: '4rem',
                                  color: theme.palette.success.main,
                                }}
                              />
                            ) : (
                              <CloudUpload
                                style={{ fontSize: '4rem', color: '#999' }}
                              />
                            )}
                          </Grid>
                          <Grid item>
                            <Typography variant="body2" align="center">
                              Upload Thirdparty Report
                              <br />
                              (English)
                            </Typography>
                          </Grid>
                        </Grid>
                      </Grid>
                      <Grid item>
                        <Grid
                          container
                          direction="column"
                          style={{ cursor: 'pointer' }}
                          alignItems="center"
                          onClick={() =>
                            uploadThirdPartyReportWithCloudinary('portuguese')
                          }
                        >
                          <Grid item>
                            {thirdPartyReportPortuguesePublicId ? (
                              <CheckCircle
                                style={{
                                  fontSize: '4rem',
                                  color: theme.palette.success.main,
                                }}
                              />
                            ) : (
                              <CloudUpload
                                style={{ fontSize: '4rem', color: '#999' }}
                              />
                            )}
                          </Grid>
                          <Grid item>
                            <Typography variant="body2" align="center">
                              Upload Third Party Report
                              <br />
                              (Portuguese)
                            </Typography>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Alert>
            </Collapse>
          </Grid>
          <Grid item xs={12}>
            <FormControl required fullWidth>
              <InputLabel id="om-report-type">Report Type</InputLabel>
              <Select
                labelId="om-report-type"
                placeholder="Report Type"
                value={omReportTypeId || ''}
                onChange={(event) => {
                  setOMReportTypeId(event.target.value);
                }}
                style={{ width: '100%' }}
                required
                input={<OutlinedInput label="Report Type" />}
              >
                {allOMReportTypes?.map((reportType) => (
                  <MenuItem
                    key={`report-type-${reportType.id}`}
                    value={reportType.id}
                  >
                    {reportType.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl required fullWidth>
              <InputLabel id="select-employees-label">
                Select Employees
              </InputLabel>
              <Select
                labelId="select-employees-label"
                id="select-multiple-employee"
                multiple
                value={employeeIds}
                onChange={(event) => {
                  const {
                    target: { value },
                  } = event;
                  setEmployeeIds(
                    // On autofill we get a stringified value.
                    typeof value === 'string' ? value.split(',') : value
                  );
                }}
                input={<OutlinedInput label="Select Employees" />}
              >
                {allOMEmployees &&
                  allOMEmployees.map((employee) => {
                    return (
                      <MenuItem
                        key={employee.id}
                        value={employee.id}
                        style={{
                          fontWeight:
                            employeeIds.indexOf(employee.id) > -1
                              ? 'bold'
                              : null,
                        }}
                      >
                        {employee.fullName}
                      </MenuItem>
                    );
                  })}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Diagnostic Procedure"
              value={diagnosticProcedureNotes || ''}
              multiline
              required
              onChange={(event) => {
                setDiagnosticProcedureNotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="PPE Level and Gear"
              value={ppeNotes || ''}
              multiline
              required={!thirdPartyFlg}
              onChange={(event) => {
                setPPENotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Parts Used"
              helperText="Put N/A if not applicable"
              value={partsUsedNotes || ''}
              multiline
              required={!thirdPartyFlg}
              onChange={(event) => {
                setPartsUsedNotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Parts Ordered"
              value={partsOrderedNotes || ''}
              multiline
              helperText="Put N/A if not applicable"
              required={!thirdPartyFlg}
              onChange={(event) => {
                setPartsOrderedNotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="RMA"
              value={rmaNotes || ''}
              multiline
              helperText="Put N/A if not applicable"
              required={!thirdPartyFlg}
              onChange={(event) => {
                setRMANotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                id="startDt"
                label="Start Date"
                type="datetime-local"
                required
                value={startDt || ''}
                onChange={(event) => {
                  setStartDt(
                    event.target.value
                      ? moment(event.target.value).format('yyyy-MM-DDTHH:mm')
                      : null
                  );
                }}
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  inputProps: {
                    max: moment().format('yyyy-MM-DDTHH:mm'),
                  },
                }}
                error={endDt && startDt && endDt <= startDt}
                helperText={
                  endDt && startDt && endDt <= startDt
                    ? 'Start date must precede end date'
                    : null
                }
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                id="endDt"
                label="End Date"
                type="datetime-local"
                required
                value={endDt || ''}
                onChange={(event) => {
                  setEndDt(
                    event.target.value
                      ? moment(event.target.value).format('yyyy-MM-DDTHH:mm')
                      : null
                  );
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                fullWidth
                error={endDt && startDt && endDt <= startDt}
                helperText={
                  endDt && startDt && endDt <= startDt
                    ? 'Start date must precede end date'
                    : null
                }
              />
            </FormControl>
          </Grid>
          <Grid xs={12}>
            <FormHelperText style={{ paddingLeft: '16px' }}>
              The start and end of the time spent on this ticket's issue during
              this field visit.
            </FormHelperText>
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={resolvedFlg}
                  label="This report resolves the ticket"
                  onChange={(event) => {
                    setResolvedFlg(!resolvedFlg);
                  }}
                  fullWidth
                />
              }
              label="This report resolves the ticket"
            />
          </Grid>
          <Grid item xs={12}>
            {props.omReport?.submittedDt ? null : (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={flaggedForSubmission}
                    label="Submit this service report"
                    onChange={(event) => {
                      setFlaggedForSubmission(!flaggedForSubmission);
                    }}
                    fullWidth
                  />
                }
                label={`By checking this box, I, ${permissions.firstName} ${permissions.lastName}, confirm that this report is filled out correctly to the best of my knowledge. Once this checkbox is checked and the report is saved, it will no longer be editable.`}
              />
            )}
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              color="error"
              onClick={() => deleteReport()}
            >
              Delete Service Report
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button disabled={omReportTypesLoading} onClick={() => props.onClose()}>
          Cancel
        </Button>
        <Button
          onClick={() => submit()}
          variant="contained"
          color="primary"
          disabled={
            omReportTypesLoading ||
            (thirdPartyFlg
              ? !thirdPartyCompanyName
              : !ppeNotes ||
                !partsUsedNotes ||
                !partsOrderedNotes ||
                !rmaNotes) ||
            !diagnosticProcedureNotes ||
            !startDt ||
            !endDt ||
            endDt < startDt ||
            !omReportTypeId
          }
        >
          Save
          {omReportTypesLoading ? (
            <CircularProgress style={{ position: 'absolute' }} />
          ) : null}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
