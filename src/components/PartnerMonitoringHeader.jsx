import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

import {
  Avatar,
  Button,
  Card,
  CardActionArea,
  Chip,
  CircularProgress,
  Grid,
  Icon,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { withStyles } from '@mui/styles';
import { useDataProvider, useNotify, usePermissions } from 'react-admin';
import numeral from 'numeral';
import 'chartjs-adapter-moment';
import annotationPlugin from 'chartjs-plugin-annotation';
import { Chart } from 'chart.js';
import classNames from 'classnames';

import theme from '../theme';
import MiniProductionLineChart from './MiniProductionLineChart';
import PortfolioGenerationChart from './PortfolioGenerationChart';

const styles = (theme) => ({});

const showDiff = false;

export default withStyles(styles)((props) => {
  const [quarterlyReportLoading, setQuarterlyReportLoading] = useState(null);
  const [monthlyReportLoading, setMonthlyReportLoading] = useState(null);
  const [portfolioGenerationChartOpen, setPortfolioGenerationChartOpen] =
    useState(false);
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  const notify = useNotify();

  useEffect(() => {
    Chart.register(annotationPlugin);
  });

  const { portfolios, getDiffColor } = props;
  const selectedPortfolio = portfolios?.[0];

  const handleClickDownloadQuarterlyProduction = (portfolioId) => {
    setQuarterlyReportLoading(true);
    dataProvider
      .create('PortfolioQuarterlyGenerationReport', {
        input: {
          portfolioId: parseInt(portfolioId, 10),
        },
      })
      .then(
        (res) => {
          setQuarterlyReportLoading(false);
          const link = document.createElement('a');
          link.href = res.data.downloadUrl;
          link.download = true;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (e) => {
          setQuarterlyReportLoading(false);
          console.error('Error downloading quarterly generation report', e);
          notify('Error downloading quarterly generation report', {
            type: 'error',
          });
        }
      );
  };

  const handleClickDownloadMonthlyProduction = (portfolioId) => {
    setMonthlyReportLoading(true);
    dataProvider
      .create('PortfolioMonthlyGenerationReport', {
        input: {
          portfolioId: parseInt(portfolioId, 10),
        },
      })
      .then(
        (res) => {
          setMonthlyReportLoading(false);
          const link = document.createElement('a');
          link.href = res.data.downloadUrl;
          link.download = true;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (e) => {
          setMonthlyReportLoading(false);
          console.error('Error downloading monthly generation report', e);
          notify('Error downloading monthly generation report', {
            type: 'error',
          });
        }
      );
  };
  const laerskoolFilter =
    permissions?.roles &&
    permissions?.roles?.map((role) => role.name).indexOf('Laerskool') > -1 &&
    permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
  const eventideFilter =
    permissions?.roles &&
    permissions?.roles?.map((role) => role.name).indexOf('Eventide') > -1 &&
    permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
  const connaughtFilter =
    permissions?.roles &&
    permissions?.roles?.map((role) => role.name).indexOf('Connaught') > -1 &&
    permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;
  const fresnoFilter =
    permissions?.roles &&
    permissions?.roles?.map((role) => role.name).indexOf('Fresno') > -1 &&
    permissions?.roles?.map((role) => role.name).indexOf('allUsers') === -1;

  const lintedActiveProjects = selectedPortfolio?.activeProjects?.filter(
    (el) => {
      if (laerskoolFilter) {
        return el.id === 348;
      } else if (eventideFilter) {
        return el.id === 354;
      } else if (connaughtFilter) {
        return el.id === 266;
      } else if (fresnoFilter) {
        return el.id === 189;
      } else {
        return true;
      }
    }
  );
  const operationalCount =
    lintedActiveProjects?.filter(
      (el) => el.projectInvestmentStatus && el.projectInvestmentStatus.id === 8
    ).length || 0;
  const operationalMWDC =
    lintedActiveProjects?.reduce((acc, cur) => {
      if (cur.projectInvestmentStatus && cur.projectInvestmentStatus.id === 8) {
        // "Cash Flowing"
        return acc + (cur.systemSizeDC || 0);
      }
      return acc;
    }, 0) || 0;
  const constructionMWDC =
    lintedActiveProjects?.reduce((acc, cur) => {
      if (cur.projectInvestmentStatus && cur.projectInvestmentStatus.id === 9) {
        // "Construction"
        return acc + (cur.systemSizeDC || 0);
      }
      return acc;
    }, 0) || 0;
  const constructionCount =
    lintedActiveProjects?.filter(
      (el) => el.projectInvestmentStatus && el.projectInvestmentStatus.id === 9
    ).length || 0;
  const developmentMWDC =
    lintedActiveProjects?.reduce((acc, cur) => {
      if (
        cur.projectInvestmentStatus &&
        cur.projectInvestmentStatus.id === 10
      ) {
        // "Development"
        return acc + (cur.systemSizeDC || 0);
      }
      return acc;
    }, 0) || 0;
  const developmentCount =
    lintedActiveProjects?.filter(
      (el) => el.projectInvestmentStatus && el.projectInvestmentStatus.id === 10
    ).length || 0;
  const totalMWDC = operationalMWDC + constructionMWDC + developmentMWDC;

  const last30DaysPortfolioProjection =
    lintedActiveProjects?.reduce((acc, v) => {
      if (
        v.projectMonitoringStatus &&
        v.projectMonitoringStatus.name === 'Active'
      ) {
        return acc + v.last30DayProjection;
      }
      return acc;
    }, 0) / 1000 || 0;
  const last30DaysPortfolioActual =
    selectedPortfolio?.last30EnergyProductionData?.[
      selectedPortfolio?.last30EnergyProductionData.length - 1
    ]?.value || 0;

  const portfolioLast30GenerationDiff =
    (last30DaysPortfolioActual - last30DaysPortfolioProjection) /
    last30DaysPortfolioProjection;

  const isLaerskool = permissions?.roles
    .map((role) => role.name)
    .includes('Laerskool');
  const isEventide = permissions?.roles
    .map((role) => role.name)
    .includes('Eventide');
  const isConnaught = permissions?.roles
    .map((role) => role.name)
    .includes('Connaught');
  const isFresno = permissions?.roles
    .map((role) => role.name)
    .includes('Fresno');

  const showGenerationReports =
    !isLaerskool && !isConnaught && !isFresno && !isEventide;

  let title = selectedPortfolio?.subtitle;
  if (isLaerskool) {
    title = 'Laerskool Havinga';
  } else if (isEventide) {
    title = 'CPOA - Eventide';
  } else if (isConnaught) {
    title = 'Connaught Park';
  } else if (isFresno) {
    title = 'Fresno Yosemite Airport';
  }

  return (
    <>
      <Grid container style={{ margin: '2rem 0' }}>
        <Grid container direction="column" style={{ width: '100%' }}>
          {portfolios ? (
            <Grid
              container
              component={Card}
              style={{
                padding: '2rem',
                borderRadius: theme.shape.borderRadius,
              }}
            >
              <Grid item xs={12} style={{ marginBottom: '1rem' }}>
                <Grid container justifyContent="space-between">
                  <Grid item>
                    <Grid container>
                      <Grid item>
                        <Typography variant="h4">{title}</Typography>
                      </Grid>
                      {selectedPortfolio &&
                      !isLaerskool &&
                      !isEventide &&
                      !isConnaught &&
                      !isFresno ? (
                        <Grid item>
                          <Typography
                            variant="h4"
                            style={{
                              fontWeight: 'normal',
                              paddingLeft: '.5rem',
                            }}
                          >
                            {`${numeral(totalMWDC).format('0,0[.]0')} MW (DC)`}
                          </Typography>
                        </Grid>
                      ) : null}
                    </Grid>
                  </Grid>
                  {showGenerationReports && (
                    <Grid item>
                      <Grid container direction="column">
                        <Grid item>
                          <Tooltip
                            title="Click here to download monthly generation report"
                            arrow
                          >
                            <Button
                              color="secondary"
                              variant="text"
                              startIcon={
                                <Icon
                                  color="primary"
                                  className={classNames(
                                    'fas',
                                    'fa-file-download'
                                  )}
                                />
                              }
                              onClick={() =>
                                handleClickDownloadMonthlyProduction(
                                  selectedPortfolio.id
                                )
                              }
                              disabled={monthlyReportLoading}
                              style={{ textTransform: 'none' }}
                            >
                              {monthlyReportLoading ? (
                                <CircularProgress
                                  style={{ position: 'absolute' }}
                                />
                              ) : null}{' '}
                              <Typography>
                                Download monthly generation
                              </Typography>
                            </Button>
                          </Tooltip>
                        </Grid>
                        <Grid item>
                          <Tooltip
                            title="Click here to download quarterly generation report"
                            arrow
                          >
                            <Button
                              color="secondary"
                              variant="text"
                              startIcon={
                                <Icon
                                  color="primary"
                                  className={classNames(
                                    'fas',
                                    'fa-file-download'
                                  )}
                                />
                              }
                              onClick={() =>
                                handleClickDownloadQuarterlyProduction(
                                  selectedPortfolio.id
                                )
                              }
                              disabled={quarterlyReportLoading}
                              style={{ textTransform: 'none' }}
                            >
                              {quarterlyReportLoading ? (
                                <CircularProgress
                                  style={{ position: 'absolute' }}
                                />
                              ) : null}{' '}
                              <Typography>
                                Download quarterly generation
                              </Typography>
                            </Button>
                          </Tooltip>
                        </Grid>
                      </Grid>
                    </Grid>
                  )}
                </Grid>
              </Grid>
              {selectedPortfolio ? (
                <Grid
                  container
                  item
                  xs={12}
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Grid item>
                    <Grid
                      container
                      direction="column"
                      spacing={1}
                      style={{ marginBottom: '1rem' }}
                    >
                      <Grid item>
                        <Chip
                          color="primary"
                          style={{ background: theme.palette.green.main }}
                          avatar={
                            <Avatar style={{ background: 'rgba(0,0,0,.3)' }}>
                              <Icon
                                style={{
                                  fontSize: '.9rem',
                                  marginTop: '.05rem',
                                  textAlign: 'center',
                                  width: '2em',
                                }}
                                className="fas fa-lightbulb"
                              />
                            </Avatar>
                          }
                          label={
                            <>
                              Operational | <b>{operationalCount} projects</b> |{' '}
                              <b>
                                {numeral(operationalMWDC).format('0,0[.]0')} MW
                              </b>{' '}
                              |{' '}
                              <b>
                                {numeral(operationalMWDC / totalMWDC).format(
                                  '%0'
                                )}
                              </b>
                            </>
                          }
                        />
                      </Grid>
                      <Grid item>
                        <Chip
                          color="primary"
                          style={{ background: theme.palette.warning.main }}
                          avatar={
                            <Avatar style={{ background: 'rgba(0,0,0,.3)' }}>
                              <Icon
                                style={{
                                  fontSize: '.9rem',
                                  marginTop: '.05rem',
                                  textAlign: 'center',
                                  width: '2em',
                                }}
                                className="fas fa-hard-hat"
                              />
                            </Avatar>
                          }
                          label={
                            <>
                              Under construction |{' '}
                              <b>{constructionCount} projects</b> |{' '}
                              <b>
                                {numeral(constructionMWDC).format('0,0[.]0')} MW
                              </b>{' '}
                              |{' '}
                              <b>
                                {numeral(constructionMWDC / totalMWDC).format(
                                  '%0'
                                )}
                              </b>
                            </>
                          }
                        />
                      </Grid>
                      <Grid item>
                        <Chip
                          color="primary"
                          style={{ background: theme.palette.primary.main }}
                          avatar={
                            <Avatar style={{ background: 'rgba(0,0,0,.3)' }}>
                              <Icon
                                style={{
                                  fontSize: '.9rem',
                                  marginTop: '.05rem',
                                  textAlign: 'center',
                                  width: '2em',
                                }}
                                className="fas fa-map-marked"
                              />
                            </Avatar>
                          }
                          label={
                            <>
                              In development (pending approval) |{' '}
                              <b>{developmentCount} projects</b> |{' '}
                              <b>
                                {numeral(developmentMWDC).format('0,0[.]0')} MW
                              </b>{' '}
                              |{' '}
                              <b>
                                {numeral(developmentMWDC / totalMWDC).format(
                                  '%0'
                                )}
                              </b>
                            </>
                          }
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item>
                    <Grid container>
                      <Grid item style={{ paddingRight: '2rem' }}>
                        <Table dense>
                          <TableHead>
                            <TableRow>
                              <TableCell colSpan={2} style={{ padding: '8px' }}>
                                <b>30-Day Production Snapshot</b>
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell style={{ padding: '8px' }}>
                                Projected
                              </TableCell>
                              <TableCell
                                align="right"
                                style={{ padding: '8px' }}
                              >
                                {numeral(last30DaysPortfolioProjection).format(
                                  '0,0.[0]'
                                )}{' '}
                                MWh
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell style={{ padding: '8px' }}>
                                Actual
                              </TableCell>
                              <TableCell
                                align="right"
                                style={{ padding: '8px' }}
                              >
                                {numeral(last30DaysPortfolioActual).format(
                                  '0,0.[0]'
                                )}{' '}
                                MWh
                              </TableCell>
                            </TableRow>
                            {showDiff ? (
                              <TableRow>
                                <TableCell
                                  style={{
                                    borderBottom: 'none',
                                    padding: '8px',
                                  }}
                                >
                                  Delta
                                </TableCell>
                                <TableCell
                                  align="right"
                                  style={{
                                    borderBottom: 'none',
                                    padding: '8px',
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    style={{
                                      color: getDiffColor(
                                        portfolioLast30GenerationDiff
                                      ),
                                    }}
                                  >
                                    {last30DaysPortfolioActual >
                                    last30DaysPortfolioProjection
                                      ? '+'
                                      : ''}
                                    {last30DaysPortfolioActual === 0
                                      ? '--%'
                                      : numeral(
                                          portfolioLast30GenerationDiff
                                        ).format('0,0.[00]%')}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ) : null}
                          </TableBody>
                        </Table>
                      </Grid>
                      <Tooltip
                        title="This shows the entire portfolio's generation over the last 30 days in comparison to the p50 projections. Click for more detail."
                        arrow
                      >
                        <Grid item component={Card} elevation={0}>
                          <CardActionArea
                            onClick={() =>
                              setPortfolioGenerationChartOpen(true)
                            }
                          >
                            <MiniProductionLineChart
                              portfolio={selectedPortfolio}
                              projection={last30DaysPortfolioProjection}
                              lineColor={getDiffColor(
                                portfolioLast30GenerationDiff
                              )}
                            />
                          </CardActionArea>
                        </Grid>
                      </Tooltip>
                    </Grid>
                  </Grid>
                </Grid>
              ) : null}
            </Grid>
          ) : (
            <Grid container style={{ marginBottom: '2rem' }}>
              <Grid item xs={12}>
                <Skeleton
                  style={{
                    borderRadius: theme.shape.borderRadius,
                  }}
                  animation="wave"
                  variant="rectangular"
                  height="17rem"
                />
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
      <PortfolioGenerationChart
        portfolioName={selectedPortfolio?.subtitle}
        portfolioId={selectedPortfolio?.id}
        open={portfolioGenerationChartOpen}
        onClose={() => setPortfolioGenerationChartOpen(false)}
      />
    </>
  );
});
