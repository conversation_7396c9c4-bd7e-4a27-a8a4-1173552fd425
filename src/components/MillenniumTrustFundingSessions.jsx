import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  DateTimeInput,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Button,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
} from '@mui/material';
import MuiTextField from '@mui/material/TextField';
import { Alert } from '@mui/lab';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Millennium Trust Funding Transfers';

export const MillenniumTrustFundingSessionEdit = () => {
  const [reissueAcatValue, setReissueAcatValue] = useState(null);
  const [reissueAcatDelivererName, setReissueAcatDelivererName] =
    useState(null);
  const [reissueAcatDelivererDTC, setReissueAcatDelivererDTC] = useState(null);
  const [reissueAcatDialogOpen, setReissueAcatDialogOpen] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const { id } = useParams();

  const handleReissueAcat = () => {
    dataProvider
      .update('MillenniumTrustFundingSession', {
        data: {
          id: parseInt(id, 10),
          amount: parseFloat(reissueAcatValue),
          delivererName: reissueAcatDelivererName || null,
          delivererDTC: reissueAcatDelivererDTC || null,
          cancelAndReissueACAT: true,
        },
      })
      .then(
        (res) => {
          refresh();
          notify('ACAT Transfer resubmitted', { type: 'success' });
        },
        (err) => {
          console.error(err);
          refresh();
          notify('Error resubmitting ACAT Transfer', { type: 'error' });
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput source="subAccount.user.id" reference="UserLite">
              <SelectInput
                label="User"
                fullWidth
                allowEmpty
                disabled
                optionText="fullName"
              />
            </ReferenceInput>
            <ReferenceInput source="subAccount.id" reference="SubAccountLite">
              <SelectInput
                label="SubAccount"
                fullWidth
                allowEmpty
                disabled
                optionText="name"
              />
            </ReferenceInput>
            <TextInput
              source="fundingType"
              disabled
              fullWidth
              helperText="'transfer' means its an ACAT"
            />
            <FunctionField
              label="Transfer Form"
              render={(record) => {
                if (record.offlineTransferDocumentStatusUrl) {
                  return (
                    <Grid item>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() =>
                          window.location.assign(
                            record.offlineTransferDocumentStatusUrl
                          )
                        }
                      >
                        View Offline Transfer/Rollover Form
                      </Button>
                    </Grid>
                  );
                }
                return null;
              }}
            />
            <TextInput
              source="transfer.status"
              label="Status"
              disabled
              fullWidth
            />
            <FunctionField
              label="Amount"
              render={(record) => {
                if (record.transfer) {
                  // This transfer is fetchable from the Inspira API and editing in our db won't impact the real transfer at Inspira.
                  return (
                    <CustomNumberInput
                      disabled
                      label="Amount"
                      source="transfer.amount"
                      fullWidth
                      helperText="This amount is not editable because it is referencing a transfer at Inspira placed through the API. Editing this value will not impact the actual transfer."
                    />
                  );
                }
                return (
                  <CustomNumberInput
                    label="Amount"
                    source="amount"
                    fullWidth
                    helperText="This amount is editable for offline transfers that were manually input by Energea, not transfers submitted via the API."
                  />
                );
              }}
            />
            <DateTimeInput
              disabled
              fullWidth
              label="Created Dt"
              source="createdAt"
            />
            <DateTimeInput
              // disabled
              fullWidth
              label="Completed Dt"
              source="toMillenniumTransferCompletedDt"
              helperText="For transfers initiated through the API, this will be updated automatically"
            />
            <DateTimeInput
              // disabled
              fullWidth
              label="Cancelled Dt"
              source="toMillenniumTransferCancelledDt"
              helperText="For transfers initiated through the API, this will be updated automatically"
            />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (formData.fundingType === 'contribution') {
                  return (
                    <TextInput
                      source="transfer.contributionTaxYear"
                      label="Contribution tax year"
                      disabled
                      fullWidth
                    />
                  );
                } else if (formData.fundingType === 'transfer') {
                  return (
                    <>
                      <TextInput
                        source="transfer.acatDelivererName"
                        label="ACAT deliverer name"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatDtccParticipantNumber"
                        label="ACAT DTCC #"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatDelivererAccountNumber"
                        label="ACAT deliverer account #"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatDelivererAccountType"
                        label="ACAT deliverer account type"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatStatus"
                        label="ACAT status"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatRejectCode"
                        label="ACAT reject code"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatRejectReason"
                        label="ACAT reject reason"
                        disabled
                        fullWidth
                      />
                      <TextInput
                        source="transfer.acatComments"
                        label="ACAT comments"
                        disabled
                        fullWidth
                      />
                    </>
                  );
                }
                return null;
              }}
            </FormDataConsumer>
            <TextInput
              source="offlineTransferDocumentEversignId"
              fullWidth
              helperText="This will only be used for transfers that were initiated offline with documents signed via Eversign"
            />
            <TextInput source="ipAddress" fullWidth />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (formData.fundingType !== 'transfer') return null;
                return (
                  <>
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => setReissueAcatDialogOpen(true)}
                      disabled={!formData.toMillenniumTransferCancelledDt}
                    >
                      Re-issue ACAT Transfer
                    </Button>
                    {!formData.toMillenniumTransferCancelledDt ? null : (
                      <Dialog open={reissueAcatDialogOpen} fullWidth>
                        <DialogTitle>Re-issue ACAT Transfer</DialogTitle>
                        <DialogContent>
                          <MuiTextField
                            label="Transfer value"
                            value={reissueAcatValue || ''}
                            onChange={(event) => {
                              setReissueAcatValue(event.target.value);
                            }}
                            fullWidth
                            type="number"
                            required
                            style={{ margin: '.5rem 0' }}
                          />
                          <MuiTextField
                            label="Deliverer Name"
                            value={reissueAcatDelivererName || ''}
                            onChange={(event) => {
                              setReissueAcatDelivererName(event.target.value);
                            }}
                            fullWidth
                            style={{ margin: '.5rem 0' }}
                          />
                          <MuiTextField
                            label="Deliverer DTC #"
                            value={reissueAcatDelivererDTC || ''}
                            onChange={(event) => {
                              setReissueAcatDelivererDTC(event.target.value);
                            }}
                            fullWidth
                            style={{ margin: '.5rem 0' }}
                          />
                        </DialogContent>
                        <DialogActions>
                          <Button
                            onClick={() => setReissueAcatDialogOpen(false)}
                            color="primary"
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleReissueAcat}
                            color="primary"
                            variant="contained"
                            disabled={!reissueAcatValue}
                          >
                            Submit
                          </Button>
                        </DialogActions>
                      </Dialog>
                    )}
                  </>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const FundingSessionFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Investor Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const MillenniumTrustFundingSessionList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<FundingSessionFilter />}
      sort={{ field: 'id', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="subAccount.user.id"
          labelSource="subAccount.user.fullName"
          label="User"
        />
        <LinkField
          label="SubAccount"
          linkSource="subAccount.id"
          labelSource="subAccount.name"
          reference="SubAccount"
        />
        <TextField source="fundingType" />
        <TextField source="contributionYear" label="Contribution tax year" />
        <TextField source="transferStatus" label="Status" />
        <NumberField
          source="amount"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <DateField
          label="Completed Dt"
          source="toMillenniumTransferCompletedDt"
          showTime={true}
        />
        <DateField
          label="Cancelled Dt"
          source="toMillenniumTransferCancelledDt"
          showTime={true}
        />
        <DateField source="createdAt" showTime={true} />
        <DateField source="updatedAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

export const MillenniumTrustFundingSessionCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <Alert severity="info">
            This is used to create DB records for offline transfers only. This
            will not create a funding transfer in Millennium Trusts system.
          </Alert>
          <ReferenceInput
            perPage={100000}
            source="user.id"
            sortable={false}
            reference="UserLite"
          >
            <AutocompleteInput
              label="User (Investor)"
              required
              fullWidth
              allowEmpty
              optionText="fullName"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData.user) {
                return null;
              }
              return (
                <Collapse in={formData.user && formData.user.id}>
                  <ReferenceInput
                    source="subAccount.id"
                    reference="SubAccountLite"
                    sort={{ field: 'id', order: 'ASC' }}
                    perPage={10000}
                    filter={{ userId: formData.user.id }}
                  >
                    <SelectInput
                      label="SubAccount"
                      fullWidth
                      optionText="name"
                    />
                  </ReferenceInput>
                </Collapse>
              );
            }}
          </FormDataConsumer>
          <TextInput
            source="fundingType"
            fullWidth
            helperText="Offline options: 'qualified retirement plan rollover' or 'offline ira to ira transfer'"
          />
          <TextInput
            source="transferStatus"
            fullWidth
            defaultValue="Offline"
            helperText="Use 'Offline' when its an offline transfer"
          />
          <CustomNumberInput source="amount" fullWidth />
          <DateInput
            source="toMillenniumTransferCompletedDt"
            fullWidth
            label="Transfer completed dt"
          />
          <TextInput
            source="offlineTransferDocumentEversignId"
            label="Offline transfer form Eversign ID"
            fullWidth
            helperText="This will only be used for transfers that were initiated offline with documents signed via Eversign"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
