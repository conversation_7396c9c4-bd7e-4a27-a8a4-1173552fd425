import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Weather Adj. Expected Daily Generation';

export const ExpectedProductionPeriodEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                label="Project"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <TextInput
              source="periodStartDt"
              fullWidth
              helperText="YYYY-MM-DD"
            />
            <CustomNumberInput source="production" fullWidth />
            <CustomNumberInput source="globEffIrradiance" fullWidth />
            <CustomNumberInput source="irradianceDataCoverage" fullWidth />
            <NumberField source="inverterAvailability" />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const ExpectedProductionPeriodList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="periodStartDt" />
        <NumberField source="production" />
        <NumberField source="globEffIrradiance" />
        <NumberField source="irradianceDataCoverage" />
        <NumberField source="avgModuleTemperature" />
        <NumberField source="inverterAvailability" />
      </Datagrid>
    </List>
  );
};

export const ExpectedProductionPeriodCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="periodStartDt"
            required
            fullWidth
            helperText="YYYY-MM-DD"
          />
          <CustomNumberInput source="production" fullWidth />
          <ReferenceInput
            source="project.id"
            reference="Project"
            perPage={10000}
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput
              label="Project"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
