import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  Filter,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Alert, Grid, Typography } from '@mui/material';

import { CustomNumberInput, DetailField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Portfolio Risk';

export const PortfolioRiskEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="title" fullWidth />
            <CustomNumberInput
              source="orderNo"
              step={1}
              helperText="The order these show up on the portfolio page 'Risks' section"
            />
            <TextInput source="description" multiline fullWidth />
            <TextInput source="mitigation" multiline fullWidth />
            <ReferenceInput source="portfolio.id" reference="PortfolioLite">
              <SelectInput fullWidth label="Portfolio" optionText="name" />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const PortfolioRisksPagination = () => (
  <Pagination rowsPerPageOptions={[10, 25, 100]} />
);
const PortfolioRiskFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const PortfolioRiskList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <Alert severity="info" style={{ marginTop: '1rem' }}>
        These are the risks that are displayed on the portfolio page. These
        should be consistent with the portfolio Offering Circulars, and are
        currently maintained by Kathy (<EMAIL>).
      </Alert>
      <List
        sort={{ field: 'id', order: 'DESC' }}
        title={entityName}
        perPage={100}
        pagination={<PortfolioRisksPagination />}
        filters={<PortfolioRiskFilter />}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="title" />
          <NumberField source="orderNo" />
          <DetailField source="description" sortable={false} />
          <DetailField source="mitigation" sortable={false} />
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />
        </Datagrid>
      </List>
    </>
  );
};

export const PortfolioRiskCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="title"
            required
            fullWidth
            helperText="This is the title to help stay organized...may or may not be used on frontend"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
