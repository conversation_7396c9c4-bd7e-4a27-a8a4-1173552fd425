import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import {
  Alert,
  Button,
  Card,
  Chip,
  CircularProgress,
  Dialog,
  Grid,
  IconButton,
  ListItem,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import moment from 'moment';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import {
  AddCircle,
  CloudDownload,
  CloudUpload,
  Edit as EditIcon,
} from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import theme from '../theme';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Project Insurance Policy';
const formatUTCDate = (date) =>
  date ? new Date(date).toISOString().slice(0, 10) : null;

export const ProjectInsurancePolicyEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [invoiceUploading, setInvoiceUploading] = useState(false);

  const uploadPolicyToS3 = (event) => {
    setInvoiceUploading(true);
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Document must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `ProjectInsurancePolicies/${moment().valueOf()}_${file.name}`
      );
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          dataProvider
            .update('ProjectInsurancePolicy', {
              data: { id: parseInt(id, 10), awsObjectKey },
            })
            .then(
              () => {
                notify('Policy document uploaded', { type: 'success' });
                setInvoiceUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading policy', { type: 'error' });
                setInvoiceUploading(false);
              }
            );
        },
        (e) => {
          console.error(e);
          notify('Error uploading policy', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <FormDataConsumer>
          {({ formData, ...rest }) => {
            const signedStatusSelected =
              formData.insurancePolicyStatus?.id === 3;
            return (
              <Grid container style={{ width: '100%' }}>
                <Grid item xs={12} md={6}>
                  <ReferenceInput
                    source="project.id"
                    reference="Project"
                    sort={{ field: 'name', order: 'ASC' }}
                    perPage={10000}
                  >
                    <SelectInput
                      label="Project"
                      fullWidth
                      required
                      optionText="name"
                    />
                  </ReferenceInput>
                  <ReferenceInput
                    source="insuranceCarrier.id"
                    reference="InsuranceCarrier"
                    sort={{ field: 'name', order: 'ASC' }}
                    perPage={10000}
                  >
                    <SelectInput
                      label="Carrier"
                      fullWidth
                      allowEmpty
                      optionText="name"
                      helperText={
                        <Typography variant="caption">
                          To add a new insurance carrier,{' '}
                          <a href="/InsuranceCarrier/create">click here.</a>
                        </Typography>
                      }
                      required={signedStatusSelected}
                    />
                  </ReferenceInput>
                  <ReferenceInput
                    source="insurancePolicyStatus.id"
                    reference="InsurancePolicyStatus"
                    sort={{ field: 'name', order: 'ASC' }}
                    perPage={10000}
                  >
                    <SelectInput
                      label="Policy Status"
                      fullWidth
                      required
                      optionText={(value) => (
                        <ListItem>
                          <ListItemText
                            primary={value.name}
                            secondary={value.description}
                          />
                        </ListItem>
                      )}
                      helperText={
                        <Typography variant="caption">
                          To add a new policy status,{' '}
                          <a href="/InsurancePolicyStatus/create">
                            click here.
                          </a>
                        </Typography>
                      }
                    />
                  </ReferenceInput>
                  <ReferenceInput
                    source="insurancePolicyType.id"
                    reference="InsurancePolicyType"
                  >
                    <SelectInput
                      label="Insurance Policy Type"
                      fullWidth
                      required
                      optionText={(value) => (
                        <ListItem>
                          <ListItemText
                            primary={value.name}
                            secondary={value.description}
                          />
                        </ListItem>
                      )}
                      helperText={
                        <Typography variant="caption">
                          To add a new insurance policy type,{' '}
                          <a href="/InsurancePolicyType/create">click here.</a>
                        </Typography>
                      }
                    />
                  </ReferenceInput>
                  <TextInput
                    source="policyNumber"
                    fullWidth
                    required={signedStatusSelected}
                  />
                  <DateInput
                    source="effectiveDt"
                    fullWidth
                    options={{ timeZone: 'UTC' }}
                    format={formatUTCDate}
                    required={signedStatusSelected}
                  />
                  <DateInput
                    source="expirationDt"
                    fullWidth
                    options={{ timeZone: 'UTC' }}
                    format={formatUTCDate}
                    required={signedStatusSelected}
                  />
                  <CustomNumberInput
                    source="premium"
                    fullWidth
                    required={signedStatusSelected}
                  />
                  <CustomNumberInput
                    source="coverageLimit"
                    fullWidth
                    required={signedStatusSelected}
                  />
                  <FunctionField
                    label="Policy Document"
                    render={(record) => {
                      return (
                        <Grid container spacing={2}>
                          <Grid item>
                            <Button
                              disabled={!record.downloadUrl}
                              variant="contained"
                              startIcon={<CloudDownload />}
                              style={{ textTransform: 'none' }}
                              onClick={() =>
                                window.location.assign(record.downloadUrl)
                              }
                            >
                              Download Policy
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              color="primary"
                              variant="contained"
                              component="label" // https://stackoverflow.com/a/54043619
                              startIcon={<CloudUpload />}
                              style={{ textTransform: 'none' }}
                              disabled={invoiceUploading}
                            >
                              {invoiceUploading ? (
                                <CircularProgress
                                  style={{ position: 'absolute' }}
                                />
                              ) : null}
                              {record.downloadUrl
                                ? 'Overwrite Policy Document'
                                : 'Upload Policy Document'}
                              <input
                                type="file"
                                hidden
                                onChange={(event) => uploadPolicyToS3(event)}
                                accept="application/pdf"
                              />
                            </Button>
                            <Typography variant="body2">
                              This should be a PDF
                            </Typography>
                          </Grid>
                        </Grid>
                      );
                    }}
                  />
                </Grid>
              </Grid>
            );
          }}
        </FormDataConsumer>
      </SimpleForm>
    </Edit>
  );
};

const styleRow = (record, index) => {
  // const { expirationDt } = record;
  // const expiredStyle = {
  //   backgroundColor: 'red',
  //   color: '#fff',
  // };
  // const oneWeekStyle = {
  //   backgroundColor: 'orange',
  // };
  // const oneMonthStyle = {
  //   backgroundColor: 'yellow',
  // };
  // const today = new Date();
  // const daysBetween =
  //   (new Date(expirationDt).getTime() - today.getTime()) /
  //   (1000 * 60 * 60 * 24);
  // if (daysBetween <= 0) {
  //   return expiredStyle;
  // } else if (daysBetween <= 7) {
  //   return oneWeekStyle;
  // } else if (daysBetween <= 30) {
  //   return oneMonthStyle;
  // }
  return {};
};

export const InsurancePolicyPanel = ({ id, record, resource }) => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const redirect = useRedirect();
  const refresh = useRefresh();

  return (
    <Grid
      container
      style={{
        padding: '1rem',
        margin: '0.5rem 0',
        backgroundColor: '#fafafa',
      }}
      elevation={6}
      component={Card}
    >
      <Grid item container justifyContent="space-between" alignItems="center">
        <Grid item>
          <Typography variant="h6">Insurance Policy Details</Typography>
        </Grid>
        <Grid item>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setCreateDialogOpen(true);
            }}
            startIcon={<AddCircle />}
          >
            Add Insurance Policy
          </Button>
          <Dialog
            open={createDialogOpen}
            onClose={() => setCreateDialogOpen(false)}
          >
            <ProjectInsurancePolicyCreate
              projectId={record.id}
              closeDialog={() => {
                setCreateDialogOpen(false);
                refresh();
              }}
            />
          </Dialog>
        </Grid>
      </Grid>
      {record.projectInsurancePolicies.length > 0 ? (
        <Grid item xs={12}>
          <Grid container>
            <Grid item xs={12}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Policy Status
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Policy Number
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Policy Type
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Policy Carrier
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Effective Dt
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Expiration Dt
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Premium
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Coverage Limit
                    </TableCell>
                    <TableCell style={{ fontWeight: 'bold' }}>
                      Actions
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {record.projectInsurancePolicies.map((policy, index) => {
                    const hideBottomBorder =
                      index === record.projectInsurancePolicies.length - 1;
                    const policyIsSigned =
                      policy.insurancePolicyStatus?.id === 3;
                    const missingJsx = policyIsSigned ? (
                      <i style={{ color: 'red' }}>Missing</i>
                    ) : null;
                    return (
                      <TableRow key={policy.id}>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.insurancePolicyStatus?.name}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.policyNumber || missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.insurancePolicyType?.name || missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.insuranceCarrier?.name || missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.effectiveDt
                            ? moment(policy.effectiveDt).format('MMM D, YYYY')
                            : missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.expirationDt
                            ? moment(policy.expirationDt).format('MMM D, YYYY')
                            : missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.premium || missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          {policy.coverageLimit || missingJsx}
                        </TableCell>
                        <TableCell
                          style={{
                            borderBottom: hideBottomBorder ? 'none' : null,
                          }}
                        >
                          <Tooltip title="Download policy document" arrow>
                            <IconButton
                              onClick={() => {
                                window.location.assign(policy.downloadUrl);
                              }}
                              disabled={!policy.downloadUrl}
                              style={{
                                color:
                                  !policy.downloadUrl && policyIsSigned
                                    ? 'red'
                                    : null,
                              }}
                            >
                              <CloudDownload />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit policy" arrow>
                            <IconButton
                              onClick={() => {
                                redirect(
                                  `/ProjectInsurancePolicy/${policy.id}`
                                );
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Grid>
          </Grid>
        </Grid>
      ) : (
        <Alert severity="info">
          No insurance policies have been added to this project.
        </Alert>
      )}
    </Grid>
  );
};

const ProjectFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Project Name"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="Portfolio"
      source="portfolioId"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
      allowEmpty={false}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const ProjectInsurancePolicyList = () => {
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'actualCOD', order: 'ASC' }}
      filters={<ProjectFilter />}
    >
      <Datagrid
        expand={<InsurancePolicyPanel />}
        bulkActionButtons={false}
        rowStyle={styleRow}
      >
        <LinkField
          reference="Project"
          linkSource="id"
          labelSource="name"
          label="Project"
        />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <FunctionField
          label="Notes"
          render={(record) => {
            return (
              <Chip
                style={{
                  backgroundColor: record.insuranceStatus?.sufficientCoverage
                    ? theme.palette.green.dark
                    : record.insuranceStatus?.warningOnly
                    ? theme.palette.warning.dark
                    : theme.palette.error.dark,
                  color: 'white',
                }}
                label={record.insuranceStatus?.message || 'Sufficient Coverage'}
              />
            );
          }}
        />
        <DateField source="actualCOD" label="Actual COD" />
        <DateField source="projectedCOD" label="Projected COD" />
        <DateField
          source="actualConstructionStartDt"
          label="Actual Construction Start Dt"
        />
        <DateField
          source="projectedConstructionStartDt"
          label="Projected Construction Start Dt"
        />
      </Datagrid>
    </List>
  );
};

const ProjectInsurancePolicyCreate = (props) => {
  const [awsObjectKey, setAwsObjectKey] = useState(null);
  const notify = useNotify();

  const MyCreateButton = () => {
    const dataProvider = useDataProvider();
    const { getValues } = useFormContext();

    const { id, ...data } = getValues();

    const signedStatusSelected = data.insurancePolicyStatus?.id === 3;

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      const { id, ...data } = getValues();
      data.awsObjectKey = awsObjectKey;
      dataProvider.create('ProjectInsurancePolicy', { id, data }).then(
        () => {
          notify('Insurance policy created');
          props.closeDialog();
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    const getDisabled = () => {
      if (!signedStatusSelected) {
        return (
          !data.project?.id ||
          !data.insurancePolicyStatus?.id ||
          !data.insurancePolicyType?.id
        );
      }
      return (
        !data.project?.id ||
        !data.insurancePolicyStatus?.id ||
        !data.insurancePolicyType?.id ||
        !data.policyNumber ||
        !data.effectiveDt ||
        !data.expirationDt ||
        (!data.premium && data.premium !== 0) ||
        (!data.coverageLimit && data.coverageLimit !== 0) ||
        !awsObjectKey
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={getDisabled()}
        />
      </Toolbar>
    );
  };

  const uploadPolicyToS3 = (event) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `ProjectInsurancePolicies/${moment().valueOf()}_${file.name}`
      );
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          setAwsObjectKey(awsObjectKey);
        },
        (e) => {
          notify('Error uploading policy', { type: 'error' });
        }
      );
    }
  };

  return (
    <Create undoable={false}>
      <SimpleForm toolbar={<MyCreateButton />}>
        <FormDataConsumer>
          {({ formData, ...rest }) => {
            const signedStatusSelected =
              formData.insurancePolicyStatus?.id === 3;
            return (
              <Grid container style={{ width: '100%' }}>
                <Typography variant="h5">Create {entityName}</Typography>
                <Grid item xs={12}>
                  <ReferenceInput
                    source="project.id"
                    reference="Project"
                    sort={{ field: 'name', order: 'ASC' }}
                    perPage={10000}
                    filter={{ id: props.projectId }}
                  >
                    <SelectInput
                      label="Project"
                      fullWidth
                      required
                      optionText="name"
                    />
                  </ReferenceInput>
                  <ReferenceInput
                    source="insuranceCarrier.id"
                    reference="InsuranceCarrier"
                    sort={{ field: 'name', order: 'ASC' }}
                    perPage={10000}
                  >
                    <SelectInput
                      label="Carrier"
                      fullWidth
                      required={signedStatusSelected}
                      optionText="name"
                      helperText={
                        <Typography variant="caption">
                          To add a new insurance carrier,{' '}
                          <a href="/InsuranceCarrier/create">click here.</a>
                        </Typography>
                      }
                    />
                  </ReferenceInput>
                  <ReferenceInput
                    source="insurancePolicyStatus.id"
                    reference="InsurancePolicyStatus"
                    sort={{ field: 'name', order: 'ASC' }}
                    perPage={10000}
                  >
                    <SelectInput
                      label="Policy Status"
                      fullWidth
                      required
                      helperText={
                        <Typography variant="caption">
                          To add a new policy status,{' '}
                          <a href="/InsurancePolicyStatus/create">
                            click here.
                          </a>
                        </Typography>
                      }
                      optionText={(value) => (
                        <ListItem>
                          <ListItemText
                            primary={value.name}
                            secondary={value.description}
                          />
                        </ListItem>
                      )}
                    />
                  </ReferenceInput>
                  <ReferenceInput
                    source="insurancePolicyType.id"
                    reference="InsurancePolicyType"
                  >
                    <SelectInput
                      label="Insurance Policy Type"
                      fullWidth
                      required
                      optionText={(value) => (
                        <ListItem>
                          <ListItemText
                            primary={value.name}
                            secondary={value.description}
                          />
                        </ListItem>
                      )}
                      helperText={
                        <Typography variant="caption">
                          To add a new insurance policy type,{' '}
                          <a href="/InsurancePolicyType/create">click here.</a>
                        </Typography>
                      }
                    />
                  </ReferenceInput>
                  <TextInput
                    source="policyNumber"
                    fullWidth
                    required={signedStatusSelected}
                  />
                  <DateInput
                    source="effectiveDt"
                    fullWidth
                    options={{ timeZone: 'UTC' }}
                    format={formatUTCDate}
                    required={signedStatusSelected}
                  />
                  <DateInput
                    source="expirationDt"
                    fullWidth
                    options={{ timeZone: 'UTC' }}
                    format={formatUTCDate}
                    required={signedStatusSelected}
                  />
                  <CustomNumberInput
                    source="premium"
                    fullWidth
                    required={signedStatusSelected}
                  />
                  <CustomNumberInput
                    source="coverageLimit"
                    fullWidth
                    required={signedStatusSelected}
                  />
                  <Button
                    color="primary"
                    disabled={awsObjectKey}
                    variant="contained"
                    component="label"
                  >
                    Upload Policy
                    <input
                      type="file"
                      hidden
                      onChange={(event) => uploadPolicyToS3(event)}
                      accept="application/pdf"
                    />
                  </Button>
                </Grid>
              </Grid>
            );
          }}
        </FormDataConsumer>
      </SimpleForm>
    </Create>
  );
};
