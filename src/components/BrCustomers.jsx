import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';
import numeral from 'numeral';
import {
  ArrayField,
  ArrayInput,
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  List,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  SimpleFormIterator,
  SingleFieldList,
  TabbedForm,
  TextField,
  TextInput,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRecordContext,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Alert,
  AlertTitle,
  Backdrop,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  CheckCircle,
  Edit as EditI<PERSON>,
  <PERSON>rror,
  GetApp,
} from '@mui/icons-material';
import moment from 'moment';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomReferenceField, LinkField } from './CustomFields';
import { BrContactCreate } from './BrContacts';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';
import theme from '../theme';

const entityName = 'Customer';

const definitionJsx = (
  <Alert severity="info" style={{ marginBottom: '1rem' }}>
    <Typography variant="body2">
      A 'Customer' is an business or account that may have one or multiple store
      fronts or locations associated with it which we call 'Consumer Units'. A
      'Customer' should have at least one 'Contact' associated with it that is
      responsible for the account and to whom communications and invoices will
      be sent to. The contacts will also be the people who can log in to manage
      the accounts they are associated with.
    </Typography>
    <Divider style={{ margin: '1rem 0' }} />
    <Typography variant="body2">
      Um 'Cliente' é um negócio ou conta que pode ter uma ou várias frentes de
      loja ou locais associados a ele, os quais chamamos de 'Unidades
      Consumidoras'. Um 'Cliente' deve ter pelo menos um 'Contato' associado a
      ele que é responsável pela conta e para quem as comunicações e faturas
      serão enviadas. Os contatos também serão as pessoas que podem fazer login
      para gerenciar as contas com as quais estão associados.
    </Typography>
  </Alert>
);

const attrs = [
  {
    name: 'installationCode',
    format: (val) => String(val),
    label: 'Código instalação',
    align: 'left',
    validate: (val) => !!val,
  },
  {
    name: 'utilityCustomerCode',
    format: (val) => String(val),
    label: 'Código do cliente',
    align: 'left',
    // validate: (val) => !!val,
  },
  {
    name: 'brConsumerUnitStage',
    format: (val) => val,
    label: 'Status',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'tariffClass',
    format: (val) => val,
    label: 'Classe tarifária',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'brVoltagePhase',
    format: (val) => val,
    label: 'Conexão',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'utilityCompany',
    format: (val) => val,
    label: 'Distribuidora',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'discountRate',
    format: (val) => val,
    label: 'Desconto',
    align: 'right',
    validate: (val) => !!val || val === 0,
  },
  {
    name: 'address1',
    format: (val) => val,
    label: 'Endereço',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'address2',
    format: (val) => val,
    label: 'Complemento unidade',
    align: 'right',
    validate: (val) => true,
  },
  {
    name: 'postalCode',
    format: (val) => val,
    label: 'CEP unidade',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'district',
    format: (val) => val,
    label: 'Bairro unidade',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'city',
    format: (val) => val,
    label: 'Cidade unidade',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'state',
    format: (val) => val,
    label: 'Estado unidade',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'customerName',
    format: (val) => val,
    label: 'Nome do cliente',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'type',
    format: (val) => val,
    label: 'Tipo',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'cnpj',
    format: (val) => val,
    label: 'CNPJ',
    align: 'right',
    validate: (val, row) => {
      if (row.type?.toLowerCase() === 'cnpj') {
        return !!val;
      }
      return true;
    },
  },
  {
    name: 'cpf',
    format: (val) => val,
    label: 'CPF',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'nire',
    format: (val) => val,
    label: 'NIRE',
    align: 'right',
    validate: (val, row) => {
      if (row.type?.toLowerCase() === 'cnpj') {
        return !!val;
      }
      return true;
    },
  },
  {
    name: 'primaryContactFirstName',
    format: (val) => val,
    label: 'Primeiro nome do contato principal',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'primaryContactLastName',
    format: (val) => val,
    label: 'Sobrenome do contato principal',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'primaryContactEmail',
    format: (val) => val,
    label: 'Email do contato principal',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'primaryContactPhoneNumber',
    format: (val) => val,
    label: 'Número de telefone do contato principal',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'janConsumption',
    format: (val) => val,
    label: 'Janeiro consumo (kWh)',
    align: 'right',
  },
  {
    name: 'febConsumption',
    format: (val) => val,
    label: 'Fevereiro consumo (kWh)',
    align: 'right',
  },
  {
    name: 'marConsumption',
    format: (val) => val,
    label: 'Marco consumo (kWh)',
    align: 'right',
  },
  {
    name: 'aprConsumption',
    format: (val) => val,
    label: 'Abril consumo (kWh)',
    align: 'right',
  },
  {
    name: 'mayConsumption',
    format: (val) => val,
    label: 'Maio consumo (kWh)',
    align: 'right',
  },
  {
    name: 'junConsumption',
    format: (val) => val,
    label: 'Junho consumo (kWh)',
    align: 'right',
  },
  {
    name: 'julConsumption',
    format: (val) => val,
    label: 'Julho consumo (kWh)',
    align: 'right',
  },
  {
    name: 'augConsumption',
    format: (val) => val,
    label: 'Agosto consumo (kWh)',
    align: 'right',
  },
  {
    name: 'sepConsumption',
    format: (val) => val,
    label: 'Setembro consumo (kWh)',
    align: 'right',
  },
  {
    name: 'octConsumption',
    format: (val) => val,
    label: 'Outubro consumo (kWh)',
    align: 'right',
  },
  {
    name: 'novConsumption',
    format: (val) => val,
    label: 'Novembro consumo (kWh)',
    align: 'right',
  },
  {
    name: 'decConsumption',
    format: (val) => val,
    label: 'Dezembro consumo (kWh)',
    align: 'right',
  },
  {
    name: 'termsOfAdhesionStartDt',
    format: (val) => (val ? moment(val).format('YYYY-MM-DD') : ''),
    label: 'Termo de adesão data de início',
    align: 'right',
    validate: (val) => !!val,
  },
  {
    name: 'termsOfAdhesionSignatureDt',
    format: (val) => (val ? moment(val).format('YYYY-MM-DD') : ''),
    label: 'Termo de adesão data da assinatura',
    align: 'right',
    validate: (val) => !!val,
  },
];

const CreateStripeCustomerButton = () => {
  const record = useRecordContext();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const createStripeCustomer = () => {
    dataProvider
      .update('BrCustomer', {
        data: { id: parseInt(record.id, 10), createStripeCustomer: true },
      })
      .then(
        () => {
          notify('Stripe Customer created', { type: 'success' });
          refresh();
        },
        () => notify(`Error creating Stripe Customer`, { type: 'error' })
      );
  };

  return (
    <Grid item>
      <Button
        color="primary"
        variant="contained"
        disabled={!record || !!record.stripeCustomerId}
        onClick={(event) => {
          event.preventDefault();
          event.stopPropagation();
          createStripeCustomer();
        }}
      >
        Create Stripe Customer
      </Button>
    </Grid>
  );
};

export const BrCustomerEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [contactCreateDialogOpen, setContactCreateDialogOpen] = useState(false);
  const [eversignDocumentOpen, setEversignDocumentOpen] = useState(false);
  const [eversignDocumentLoaded, setEversignDocumentLoaded] = useState(false);
  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  const createOktaUser = (brContactId) => {
    dataProvider
      .update('BrContact', {
        data: { id: parseInt(brContactId, 10), createOktaUser: true },
      })
      .then(
        () => {
          notify(
            'Okta User created and brContact has been emailed with instructions on how to set password.',
            { type: 'success' }
          );
          refresh();
        },
        () => notify(`Error creating Okta User`, { type: 'error' })
      );
  };

  return (
    <>
      <Edit title={`${entityName} #${id}`} undoable={false}>
        <TabbedForm>
          <FormTab label="Summary">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                {definitionJsx}
              </Grid>
              <Grid item xs={12} md={6}>
                <TextInput source="name" fullWidth label="Name / Nome" />
                <FunctionField
                  label="Consortium / Consórcio"
                  render={(record) => (
                    <ReferenceInput
                      source="brConsortium.id"
                      reference="BrConsortium"
                      perPage={10000}
                      sort={{ field: 'id', order: 'ASC' }}
                    >
                      <SelectInput
                        optionText="internalName"
                        label="Consortium / Consórcio"
                        fullWidth
                        allowEmpty
                        disabled={
                          record.brConsortium && record.stripeCustomerId
                        }
                      />
                    </ReferenceInput>
                  )}
                />
                <SelectInput
                  source="type"
                  choices={[
                    { id: 'cnpj', name: 'CNPJ' },
                    { id: 'cpf', name: 'CPF' },
                  ]}
                  fullWidth
                  label="Type / Tipo"
                  helperText="Choose whether this customer is a business or an individual. (Escolha se este cliente é uma empresa ou um indivíduo.)"
                />
                <TextInput source="cnpj" fullWidth label="CNPJ" />
                <TextInput source="cpf" fullWidth label="CPF" />
                <TextInput source="nire" fullWidth label="NIRE" />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Contacts" path="contacts">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Contacts
                </Typography>
                <ArrayInput
                  source="brContactsBrCustomers"
                  fullWidth
                  label="Contact Relationships / Selecione os contatos associados a este cliente."
                >
                  <SimpleFormIterator
                    fullWidth
                    TransitionProps={{ enter: false, exit: false }}
                  >
                    <FormDataConsumer>
                      {({ scopedFormData }) => {
                        if (!scopedFormData?.brContact) return null;
                        return (
                          <Grid
                            container
                            style={{
                              width: '100%',
                              margin: '1rem 0',
                              padding: '1rem',
                              backgroundColor: '#eee',
                              borderRadius: theme.shape.borderRadius,
                            }}
                            direction="column"
                          >
                            <Grid item>
                              <Typography>
                                <b>Contact Information:</b>
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Typography>
                                Name: <i>{scopedFormData.brContact.fullName}</i>
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Typography>
                                Email:{' '}
                                <a
                                  href={`mailto:${scopedFormData.brContact.email}`}
                                >
                                  <i>{scopedFormData.brContact.email}</i>
                                </a>
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Typography>
                                Phone:{' '}
                                <a
                                  href={
                                    scopedFormData.brContact.whatsAppPhoneLink
                                  }
                                  target="_blank"
                                >
                                  {scopedFormData.brContact.phone}
                                </a>
                              </Typography>
                            </Grid>
                          </Grid>
                        );
                      }}
                    </FormDataConsumer>
                    <ReferenceInput
                      perPage={10000}
                      source="brContact.id"
                      reference="BrContact"
                      sort={{ field: 'firstName', order: 'ASC' }}
                    >
                      <AutocompleteInput
                        allowEmpty={true}
                        optionText="fullName"
                        label="Contact"
                        required
                        fullWidth
                        helperText={
                          <Typography variant="caption">
                            To add a new contact,{' '}
                            <a
                              onClick={() => {
                                setContactCreateDialogOpen(true);
                              }}
                              style={{ cursor: 'pointer' }}
                            >
                              click here.
                            </a>{' '}
                            Para adicionar um novo contato,{' '}
                            <a
                              onClick={() => {
                                setContactCreateDialogOpen(true);
                              }}
                              style={{ cursor: 'pointer' }}
                            >
                              clique aqui.
                            </a>
                          </Typography>
                        }
                      />
                    </ReferenceInput>
                    <SelectInput
                      source="role"
                      fullWidth
                      choices={[
                        {
                          name: 'Owner',
                          value: 'owner',
                        },
                        {
                          name: 'Admin',

                          value: 'admin',
                        },
                        {
                          name: 'Member',

                          value: 'member',
                        },
                        {
                          name: 'Salesperson',
                          value: 'salesPerson',
                        },
                      ]}
                      label="Role / Função"
                    />
                    <BooleanInput
                      source="primaryContactFlg"
                      label="Primary Contact"
                      helperText="Turn this on if this contact is the primary contact. This is currently not used a lot but is important for Consumer Units who can or will access energea.com.br."
                    />
                    <BooleanInput
                      source="sendInvoiceFlg"
                      label="Send Invoice"
                      helperText="Turn this on if this contact will receive the invoice emails for this customer."
                    />
                    <FormDataConsumer>
                      {({ scopedFormData }) => {
                        const hasLoginAccess =
                          !!scopedFormData?.brContact?.auth0Id;
                        return (
                          <Grid container direction="column">
                            <Grid item>
                              <Button
                                onClick={() =>
                                  createOktaUser(scopedFormData.brContact.id)
                                }
                                variant="contained"
                                color="primary"
                                disabled={
                                  hasLoginAccess ||
                                  !scopedFormData?.brContact?.email
                                }
                                style={{ marginTop: '1rem' }}
                              >
                                {hasLoginAccess
                                  ? 'Contact Has Login Access'
                                  : 'Create Login Access'}
                              </Button>
                            </Grid>
                            <Grid item>
                              <Typography
                                variant="caption"
                                style={{ paddingLeft: '1rem' }}
                              >
                                This will send this contact an email with a
                                temporary password to log in to energea.com.br
                                with.
                              </Typography>
                            </Grid>
                          </Grid>
                        );
                      }}
                    </FormDataConsumer>
                  </SimpleFormIterator>
                </ArrayInput>
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Consumer Units">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Consumer Units
                </Typography>
                <FunctionField
                  label="Consumer Units"
                  render={(record) => {
                    return (
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>
                              <b>Name</b>
                            </TableCell>
                            <TableCell>
                              <b>Onboarding Status</b>
                            </TableCell>
                            <TableCell>
                              <b>Current Discount Rate</b>
                            </TableCell>
                            <TableCell>
                              <b>Terms of Adhesions</b>
                            </TableCell>
                            <TableCell>
                              <b>Link</b>
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {record?.brConsumerUnits?.map((cu, index) => (
                            <TableRow>
                              <TableCell>{cu.name}</TableCell>
                              <Tooltip
                                title={`Requirements: ${cu.onboardingStatus?.requirementsEnglish}`}
                                arrow
                              >
                                <TableCell style={{ cursor: 'pointer' }}>
                                  {cu.onboardingStatus?.status}
                                </TableCell>
                              </Tooltip>
                              <TableCell>
                                {numeral(cu.currentDiscountRate).format(
                                  '0[.]00%'
                                )}
                              </TableCell>
                              <TableCell>
                                {cu?.brTermsOfAdhesions?.map((toa) => (
                                  <Chip
                                    label={toa.label}
                                    component={Link}
                                    to={`/BrTermsOfAdhesion/${toa.id}`}
                                    style={{ pointer: 'cursor' }}
                                    color={
                                      toa.signatureDt ? 'primary' : 'default'
                                    }
                                  />
                                ))}
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  component={Link}
                                  to={`/BrConsumerUnit/${cu.id}`}
                                >
                                  <EditIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          {isIT && (
            <FormTab label="IT Utilities">
              <Grid container style={{ width: '100%' }}>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    IT Utilities
                  </Typography>
                  <TextInput
                    source="hubSpotCompanyId"
                    fullWidth
                    label="HubSpot Company ID"
                  />
                  <TextInput
                    source="stripeCustomerId"
                    fullWidth
                    label="Stripe Customer ID"
                  />
                  <Grid container spacing={2}>
                    <CreateStripeCustomerButton />
                  </Grid>
                </Grid>
              </Grid>
            </FormTab>
          )}
        </TabbedForm>
      </Edit>
      <Dialog
        open={contactCreateDialogOpen}
        onClose={() => setContactCreateDialogOpen(false)}
      >
        <BrContactCreate
          withinDialog={true}
          onSuccess={() => {
            setContactCreateDialogOpen(false);
            refresh();
          }}
        />
      </Dialog>
      <Backdrop
        style={{
          zIndex: 1700,
          background: 'rgba(255,255,255,1)',
        }}
        open={!!(eversignDocumentOpen && !eversignDocumentLoaded)}
      >
        <Grid
          style={{
            padding: '4rem',
            textAlign: 'center',
            alignItems: 'center',
          }}
          container
          direction="column"
          justifyContent="center"
        >
          <Grid item lg={6} md={10} xs={12}>
            <Alert variant="outlined" severity="info">
              <AlertTitle gutterBottom variant="h5">
                Drafting Terms of Adhesion Form
              </AlertTitle>
              <Typography>Please wait while we draft the form.</Typography>
            </Alert>
            <CircularProgress style={{ marginTop: '2em' }} color="primary" />
          </Grid>
        </Grid>
      </Backdrop>
    </>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Name, CNPJ, or CPF"
      source="q"
      alwaysOn
    />
    <SelectInput
      source="type"
      choices={[
        { id: 'cnpj', name: 'CNPJ' },
        { id: 'cpf', name: 'CPF' },
      ]}
    />
  </Filter>
);

const styleRow = (record, index) => {
  const { hasPrimaryContact } = record;
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  if (!hasPrimaryContact) {
    return errorStyle;
  }
  return {};
};

export const BrCustomerList = () => {
  const { permissions } = usePermissions();
  const isIT = permissions?.roles?.map((el) => el.name).indexOf('ITWrite') > -1;

  return (
    <>
      <Grid container style={{ marginTop: '1rem' }}>
        {definitionJsx}
      </Grid>
      <List
        title={entityName}
        perPage={25}
        filters={<CustomFilter />}
        sort={{ field: 'id', order: 'DESC' }}
      >
        <Datagrid
          rowStyle={styleRow}
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="name" label="Name / nome" />
          <TextField source="type" label="Type / Tipo" />
          <TextField source="cnpj" label="CNPJ" />
          <TextField source="cpf" label="CPF" />
          <TextField source="nire" label="NIRE" />
          <LinkField
            reference="BrConsortium"
            linkSource="brConsortium.id"
            labelSource="brConsortium.internalName"
            label="Consortium / Consórcio"
            sortable={false}
          />
          <BooleanField
            source="hasPrimaryContact"
            label="Has Primary Contact"
            sortable={false}
          />
          <ArrayField source="brContactsBrCustomers" label="Contacts">
            <SingleFieldList>
              <CustomReferenceField
                source="brContactLabel"
                linkOverride={(record) => `/BrContact/${record.brContact.id}`}
              />
            </SingleFieldList>
          </ArrayField>
          <ArrayField source="brConsumerUnits" label="Consumer Units">
            <SingleFieldList>
              <CustomReferenceField
                source="name"
                linkOverride={(record) => `/BrConsumerUnit/${record.id}`}
              />
            </SingleFieldList>
          </ArrayField>
          <FunctionField
            label="Stripe Customer"
            render={(record) => {
              if (record.stripeCustomerId) {
                return record.stripeCustomerId;
              }
              return <CreateStripeCustomerButton />;
            }}
          />
          <TextField source="hubSpotCompanyId" label="HubSpot Company ID" />
        </Datagrid>
      </List>
    </>
  );
};

export const BrCustomerCreate = (props) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const redirect = useRedirect();
  const refresh = useRefresh();
  const [contactCreateDialogOpen, setContactCreateDialogOpen] = useState(false);
  const [data, setData] = useState(null);
  const [reviewed, setReviewed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploadErrors, setUploadErrors] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState(null);
  const [selectedSalesPersonId, setSelectedSalesPersonId] = useState(null);
  const [onContactCreateSuccessHandler, setOnContactCreateSuccessHandler] =
    useState(null);

  const isComponentWithinDialog = !!props?.withinDialog;

  const MyCreateButton = () => {
    const { getValues } = useFormContext();
    const { id, ...data } = getValues();

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      dataProvider.create('BrCustomer', { data }).then(
        (res) => {
          notify('Element created');
          if (props?.onSuccess) {
            props.onSuccess(res?.data?.id);
          } else {
            redirect('/BrCustomer');
          }
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={
            !data.name ||
            !data.type ||
            (!data.cnpj && data.type === 'cnpj') ||
            (!data.cpf && data.type === 'cpf')
          }
        />
      </Toolbar>
    );
  };

  const save = () => {
    setLoading(true);
    dataProvider
      .create('BrCustomerUpload', {
        data: {
          brSalesPersonId: selectedSalesPersonId,
          salesforceProjectId: selectedProjectId,
          data,
        },
      })
      .then(
        (res) => {
          setLoading(false);
          notify('Customers uploaded.', { type: 'success' });
          setData(null);
          setReviewed(false);
          redirect('/BrCustomer');
        },
        (e) => {
          setLoading(false);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
          refresh();
        }
      );
  };

  const handleData = (data) => {
    setUploadErrors(false);
    const stringFields = [
      'utilityCustomerCode',
      'installationCode',
      'cpf',
      'cnpj',
      'nire',
      'primaryContactFirstName',
      'primaryContactLastName',
      'primaryContactEmail',
      'primaryContactPhoneNumber',
      'type',
    ];

    const lintedData = data.map((row) => {
      const lintedRow = {};
      Object.keys(row).forEach((key) => {
        const entryAttrData = findWithAttr(attrs, 'label', key);
        if (!entryAttrData) {
          console.log('Missing attr detected', key);
        } else {
          let lintedValue = row[String(key)];
          if (stringFields.indexOf(entryAttrData.name) > -1) {
            lintedValue = String(lintedValue);
          }
          lintedRow[entryAttrData.name] = lintedValue;
        }
      });
      return lintedRow;
    });
    setData(lintedData);
  };

  const renderSubmit = () => {
    return (
      <Alert severity={!reviewed ? 'warning' : 'success'}>
        <FormControlLabel
          control={
            <Checkbox
              checked={!!reviewed}
              onChange={() => setReviewed(!reviewed)}
              disabled={
                uploadErrors || !selectedProjectId || !selectedSalesPersonId
              }
            />
          }
          label="I have checked the list and all dates and values look good (this will override the existing records!)"
        />
        <Button
          onClick={save}
          disabled={
            !reviewed ||
            loading ||
            uploadErrors ||
            !selectedProjectId ||
            !selectedSalesPersonId
          }
          variant="contained"
          size="large"
          color="secondary"
        >
          {loading ? <CircularProgress /> : 'Save'}
        </Button>
      </Alert>
    );
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate ? attr.validate(val, row) : true;
              if (!errors && !validated) {
                errors = true;
                if (!uploadErrors) {
                  setUploadErrors(true);
                }
              }
              const lintedVal = attr.format ? attr.format(val) : val;
              return (
                <TableCell
                  align={attr.align || 'center'}
                  style={{ backgroundColor: validated ? null : 'red' }}
                >
                  {lintedVal}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  return (
    <>
      <Create title={`Create ${entityName}`} undoable={false}>
        <SimpleForm toolbar={<MyCreateButton />}>
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={isComponentWithinDialog ? 12 : 6}>
              {definitionJsx}
              <Typography variant="h6" gutterBottom>
                Create Single Customer
              </Typography>
              <TextInput source="name" fullWidth required label="Name / Nome" />
              <ReferenceInput
                source="brConsortium.id"
                reference="BrConsortium"
                perPage={10000}
                sort={{ field: 'id', order: 'ASC' }}
              >
                <SelectInput
                  optionText="internalName"
                  label="Consortium / Consórcio"
                  fullWidth
                  required
                />
              </ReferenceInput>
              <SelectInput
                source="type"
                choices={[
                  { id: 'cnpj', name: 'CNPJ' },
                  { id: 'cpf', name: 'CPF' },
                ]}
                fullWidth
                required
                label="Type / Tipo"
              />
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (formData.type === 'cnpj') {
                    return (
                      <TextInput
                        source="cnpj"
                        fullWidth
                        required
                        label="CNPJ"
                      />
                    );
                  } else if (formData.type === 'cpf') {
                    return (
                      <TextInput source="cpf" fullWidth required label="CPF" />
                    );
                  }
                  return (
                    <TextInput
                      source="placeholder"
                      label="CNPJ/CPF"
                      fullWidth
                      required
                      disabled
                    />
                  );
                }}
              </FormDataConsumer>
              <TextInput source="nire" fullWidth label="NIRE" />
              <ArrayInput
                source="brContactsBrCustomers"
                fullWidth
                label="Contact Relationships / Selecione os contatos associados a este cliente."
              >
                <SimpleFormIterator
                  fullWidth
                  TransitionProps={{ enter: false, exit: false }}
                >
                  <ReferenceInput
                    perPage={10000}
                    source="brContact.id"
                    reference="BrContact"
                    sort={{ field: 'firstName', order: 'ASC' }}
                  >
                    <AutocompleteInput
                      allowEmpty={true}
                      optionText="fullName"
                      label="Contact"
                      required
                      fullWidth
                      helperText={
                        <Typography variant="caption">
                          To add a new contact,{' '}
                          <a
                            onClick={() => {
                              setContactCreateDialogOpen(true);
                            }}
                            style={{ cursor: 'pointer' }}
                          >
                            click here.
                          </a>{' '}
                          Para adicionar um novo contato,{' '}
                          <a
                            onClick={() => {
                              setContactCreateDialogOpen(true);
                            }}
                            style={{ cursor: 'pointer' }}
                          >
                            clique aqui.
                          </a>
                        </Typography>
                      }
                    />
                  </ReferenceInput>
                  <SelectInput
                    source="role"
                    fullWidth
                    choices={[{ name: 'Admin', id: 'Admin' }]}
                    label="Role / Função"
                  />
                </SimpleFormIterator>
              </ArrayInput>
              {isComponentWithinDialog ? null : (
                <>
                  <Divider style={{ margin: '2rem 0' }} />
                  <Typography variant="h6" gutterBottom>
                    Upload Customers from CSV
                  </Typography>
                  <Grid container item xs={12} direction="column">
                    <Grid item>
                      <ReferenceInput
                        perPage={10000}
                        source="brSalesPerson.id"
                        reference="BrSalesPerson"
                        sort={{ field: 'id', order: 'ASC' }}
                      >
                        <AutocompleteInput
                          label="Sales Partner"
                          onChange={(event) => {
                            const salesPersonId = event.target?.value || null;
                            setSelectedSalesPersonId(salesPersonId);
                          }}
                          optionText="brContact.fullName"
                          helperText="The name of the Sales Partner whose customers these are. This will dictate which Power Plans are available."
                        />
                      </ReferenceInput>
                    </Grid>
                    <Grid item>
                      <ReferenceInput
                        perPage={10000}
                        source="salesforceProject.id"
                        reference="SalesforceProject"
                        sort={{ field: 'name', order: 'DESC' }}
                      >
                        <SelectInput
                          label="Project"
                          onChange={(event) => {
                            const projectId = event.target?.value || null;
                            setSelectedProjectId(projectId);
                          }}
                          optionText="name"
                          helperText="The project these customers will be assigned to within the consortium."
                        />
                      </ReferenceInput>
                    </Grid>
                  </Grid>
                  <Grid xs={12} item style={{ padding: '1em' }}>
                    <Button
                      component="a"
                      variant="contained"
                      href="/csv-templates/consortiumCustomerListUpload.xlsx"
                      download
                    >
                      <GetApp />
                      Click to download the excel template
                    </Button>
                  </Grid>
                  <ExcelReader handleData={handleData} />
                  <Grid item style={{ margin: 'auto' }}>
                    {data ? renderSubmit() : null}
                  </Grid>
                  <Grid item xs={12}>
                    {data ? renderData() : null}
                  </Grid>
                </>
              )}
            </Grid>
          </Grid>
        </SimpleForm>
      </Create>
      <Dialog
        open={contactCreateDialogOpen}
        onClose={() => setContactCreateDialogOpen(false)}
      >
        <BrContactCreate
          withinDialog={true}
          onSuccess={(newContactId) => {
            // TODO: Set newContactId to form values. Joe spent 1-2 hours trying but couldn't get it to work. I think there are a couple of things potentially complicating this: 1) The reference field we want to assign to is within a nested ArrayField and SimpleFormIterator. 2) This dialog is being opened from another dialog (CustomerCreate)
            setContactCreateDialogOpen(false);
            refresh();
          }}
        />
      </Dialog>
    </>
  );
};
