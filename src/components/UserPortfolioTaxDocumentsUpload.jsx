import React, { useState, Component } from 'react';
import { Alert } from '@mui/lab';

import { useDataProvider, useNotify, useRefresh } from 'react-admin';
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

import { GetApp } from '@mui/icons-material';
import ExcelReader from './ExcelReader';
import { findWithAttr } from '../utils/global';

export const UserPortfolioTaxDocumentsUpload = (props) => {
  const [reviewed, setReviewed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [upload1099Type, setUpload1099Type] = useState(null);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const handleData = (data) => {
    const { attrs } = props;
    const lintedData = lintData(data, attrs);
    setData(lintedData);
  };

  const lintData = (data, attrs) => {
    return data
      .map((entry) => {
        const returnObj = {};
        Object.keys(entry).forEach((entryAttr) => {
          const entryAttrData = findWithAttr(attrs, 'name', entryAttr);
          if (entryAttr === 'portfolioUser') {
            returnObj.portfolioId = entry[String('portfolioUser')].split(
              '-'
            )[0];
            returnObj.userId = entry[String('portfolioUser')].split('-')[1];
          } else if (!entryAttrData) {
            console.log('Missing attr detected', entryAttr);
          } else if (entryAttrData.dataFormat) {
            returnObj[String(entryAttr)] = entryAttrData.dataFormat(
              entry[String(entryAttr)]
            );
          } else {
            returnObj[String(entryAttr)] = entry[String(entryAttr)];
          }
        });
        return returnObj;
      })
      .filter((el) => !!el);
  };

  const save = () => {
    setLoading(true);

    dataProvider
      .create(`UserPortfolioTaxDocument`, {
        data: [],
      })
      .then(
        (res) => {
          notify('Tax documents uploaded', { type: 'success' });
          setLoading(false);
          setData(null);
          setReviewed(false);
          setUpload1099Type(null);
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          setLoading(false);
          refresh();
        }
      );
  };

  const renderData = () => {
    const { attrs } = props;
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            {attrs.map((attr) => (
              <TableCell
                key={`userPortfolioTaxDocuments-header-cell-${attr.name}`}
                align={attr.align || 'center'}
              >
                {attr.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell
                  key={`userPortfolioTaxDocuments-val-cell-${attr.name}`}
                  align={attr.align || 'center'}
                >
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`userPortfolioTaxDocumentRow-${row.name}-${index}`}
              >
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  const renderSubmit = () => {
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <Grid container direction="column">
            <Grid item>
              <FormControlLabel
                // style={{ width: '100%' }}
                control={
                  <Checkbox
                    checked={!!reviewed}
                    onChange={() => setReviewed(!reviewed)}
                  />
                }
                label="I have checked and double checked the below numbers."
              />
            </Grid>
            <Grid item>
              <Select
                onChange={(event) => setUpload1099Type(event.target.value)}
                required
                fullWidth
                style={{ margin: '1rem' }}
              >
                <MenuItem value="1099-DIV">1099-DIV</MenuItem>
                <MenuItem value="1099-B">1099-B</MenuItem>
              </Select>
            </Grid>
            <Grid item>
              <Button
                onClick={save}
                disabled={!reviewed || loading || !upload1099Type}
                variant="contained"
                size="large"
                color="secondary"
              >
                {loading ? <CircularProgress /> : 'Save'}
              </Button>
            </Grid>
          </Grid>
        </Alert>
      </>
    );
  };

  return (
    <Grid container>
      <Grid xs={12} item style={{ padding: '1em' }}>
        <Button
          component="a"
          variant="contained"
          href={`/csv-templates/userPortfolioTaxDocumentUpload.csv`}
          download
        >
          <GetApp />
          Click to download the csv template
        </Button>
      </Grid>
      <Alert severity="warning">
        In order for this file to be read correctly, COPY AND PASTE THE BELOW
        TEXT without the quotations as the first line of the .txt file.
        <br />
        "ssnEinEnum;ssnEin;userName;portfolioUser"
      </Alert>
      <ExcelReader handleData={handleData} />
      <Grid item xs={12} style={{ margin: '1em' }}>
        {data ? renderSubmit() : null}
      </Grid>
      <Grid item xs={12}>
        {data ? renderData() : null}
      </Grid>
    </Grid>
  );
};
