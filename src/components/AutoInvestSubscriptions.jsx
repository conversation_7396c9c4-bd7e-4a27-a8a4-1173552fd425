import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateInput,
  DateField,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useDataProvider,
  useResourceDefinition,
} from 'react-admin';
import {
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Link,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import MuiList from '@mui/material/List';
import { Alert } from '@mui/lab';
import { CloudDownload } from '@mui/icons-material';
import { Bar } from 'react-chartjs-2';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import theme from '../theme';
import numeral from 'numeral';

const entityName = 'Auto Invest Subscription';

export const AutoInvestSubscriptionEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <BooleanInput source="isActive" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <CustomNumberInput source="value" disabled />
        <CustomNumberInput source="dayOfMonth" disabled step={1} />
        <DateInput source="archivedDt" />
        <FunctionField
          label="Agreement"
          render={(record) => {
            if (record.agreementDownloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={() =>
                    window.location.assign(record.agreementDownloadUrl)
                  }
                >
                  Download Agreement
                </Button>
              );
            }
            return null;
          }}
        />
        <FunctionField
          label="investments"
          render={(record) => {
            if (!record.investments || record.investments.length === 0) {
              return (
                <Alert severity="info">
                  There are no investments associated with this subscription
                </Alert>
              );
            }
            return (
              <>
                <MuiList>
                  {record.investments.map((investment) => {
                    return (
                      <>
                        <ListItem
                          key={`auto-reinvest-subscription-investment-list-item-${investment.id}`}
                        >
                          <Link href={`../../Investment/${investment.id}`}>
                            <ListItemText
                              primary={investment.label}
                              secondary={`${investment.portfolio.name} - ${investment.portfolio.subtitle}`}
                            />
                          </Link>
                        </ListItem>
                        <Divider style={{ width: '100%' }} />
                      </>
                    );
                  })}
                </MuiList>
              </>
            );
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

const AutoInvestSubscriptionFilter = (props) => {
  const dayOfMonthCheck = () => {
    return (value) => {
      if (!value) {
        return '';
      }
      const dayOfMonthInt = parseInt(value, 10);
      if (dayOfMonthInt < 1 || dayOfMonthInt > 31) {
        return `${dayOfMonthInt} is not a valid day of month.`;
      }
      return undefined;
    };
  };

  return (
    <Filter {...props}>
      <TextInput
        style={{ minWidth: '420px' }}
        label="Search by First Name or Last Name"
        source="q"
        alwaysOn
      />
      <ReferenceInput
        label="Portfolio"
        source="portfolio.id"
        reference="PortfolioLite"
        perPage={10_000}
        sort={{ field: 'name', order: 'ASC' }}
      >
        <SelectInput label="Portfolio" optionText="name" />
      </ReferenceInput>
      <CustomNumberInput
        label="Day of Month"
        source="dayOfMonth"
        validate={[dayOfMonthCheck()]}
        min={1}
        max={31}
      />
      <BooleanInput source="inactive" label="Inactive" defaultValue={false} />
    </Filter>
  );
};

const styleRow = (record, index) => {
  const { dwollaFundingSource, isActive } = record;
  const errorStyle = {
    backgroundColor: 'red',
    color: '#fff',
  };
  if (
    isActive &&
    (!dwollaFundingSource || dwollaFundingSource.status !== 'verified')
  ) {
    return errorStyle;
  }
  return {};
};

export const AutoInvestSubscriptionList = () => {
  const [dailyTotals, setDailyTotals] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  if (!loading && !dailyTotals) {
    setLoading(true);
    dataProvider.getOne('AutoInvestDailyTotals', {}).then((res) => {
      setLoading(false);
      setDailyTotals(res.data.resp);
    });
  }

  return (
    <>
      <Card style={{ marginTop: '2rem' }}>
        <CardContent>
          <Grid container>
            <Grid item>
              <Typography variant="h6">
                Scheduled Investments by Day of Month
              </Typography>
            </Grid>
            <Grid item xs={12}>
              {dailyTotals ? (
                <Bar
                  height={300}
                  data={{
                    labels: dailyTotals.map((day) => day.dayOfMonth),
                    datasets: [
                      {
                        data: dailyTotals.map((day) => day.value),
                        backgroundColor: theme.palette.primary.main,
                      },
                    ],
                  }}
                  options={{
                    maintainAspectRatio: false,
                    borderRadius: 4,
                    plugins: {
                      legend: { display: false },
                      tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                          label: (tooltipItem) =>
                            numeral(tooltipItem.raw).format('$0,0[.]00'),
                        },
                      },
                    },
                  }}
                />
              ) : (
                <CircularProgress />
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      <List
        title={entityName}
        sort={{ field: 'id', order: 'DESC' }}
        filters={<AutoInvestSubscriptionFilter />}
        perPage={25}
      >
        <Datagrid
          rowStyle={styleRow}
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <LinkField
            reference="User"
            linkSource="user.id"
            labelSource="user.fullName"
            label="User"
          />
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />
          <NumberField
            source="value"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField source="dayOfMonth" />
          <DateField source="nextInvestmentDt" />
          <FunctionField
            label="Dwolla Funding Source Status"
            render={(record) => (
              <Typography variant="body2">
                {record?.dwollaFundingSource?.status}
              </Typography>
            )}
          />
          <BooleanField source="isActive" />
          <FunctionField
            label="Agreement"
            render={(record) => {
              if (record.agreementDownloadUrl) {
                return (
                  <Button
                    variant="contained"
                    startIcon={<CloudDownload />}
                    style={{ textTransform: 'none' }}
                    onClick={() =>
                      window.location.assign(record.agreementDownloadUrl)
                    }
                  >
                    Download Agreement
                  </Button>
                );
              }
              return null;
            }}
          />
          <DateField source="archivedDt" />
          <DateField source="updatedAt" />
          <DateField source="createdAt" showTime={true} />
        </Datagrid>
      </List>
    </>
  );
};

export const AutoInvestSubscriptionCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <BooleanInput
            defaultValue={true}
            fullWidth
            source="isActive"
            helperText="Leave this on to enable the auto investment feature. Turn it off to disable auto investing."
          />
          <ReferenceInput
            perPage={10_000}
            source="user.id"
            reference="UserLite"
          >
            <AutocompleteInput
              allowEmpty={true}
              optionText="fullName"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
              label="User (Investor)"
              required
              fullWidth
            />
          </ReferenceInput>
          <ReferenceInput source="portfolio.id" reference="PortfolioLite">
            <SelectInput
              label="Portfolio"
              required
              fullWidth
              optionText="name"
            />
          </ReferenceInput>
          <CustomNumberInput source="dayOfMonth" fullWidth required step={1} />
          <CustomNumberInput source="value" fullWidth required />
          <TextInput source="dwollaFundingSourceId" fullWidth required />
          <BooleanInput
            source="createSignedSubscriptionAgreement"
            fullWidth
            defaultValue={false}
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
