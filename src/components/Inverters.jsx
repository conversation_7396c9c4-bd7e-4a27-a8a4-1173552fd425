import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  Filter,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Alert, Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';
import LegendItem from './LegendItem';

const entityName = 'Inverter';

export const InverterEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" required fullWidth />
            <CustomNumberInput
              source="capacity"
              label="Capacity (Inverter Input DC (Wp))"
              fullWidth
            />
            <TextInput source="serialNumber" fullWidth />
            <ReferenceInput source="equipmentItem.id" reference="EquipmentItem">
              <SelectInput
                label="Equipment Item"
                fullWidth
                allowEmpty
                optionText="label"
                helperText={
                  <span>
                    If you don't see the equipment type you need, create it{' '}
                    <a href="/EquipmentItem/create">here</a>.
                  </span>
                }
              />
            </ReferenceInput>
            <ReferenceInput source="solarEdgeSite.id" reference="SolarEdgeSite">
              <SelectInput
                label="SolarEdge Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="scadaSystem.id" reference="ScadaSystemLite">
              <SelectInput
                label="SCADA System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="sgdSystem.id" reference="SgdSystem">
              <SelectInput
                label="SGD System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="smaSite.id" reference="SmaSite">
              <SelectInput
                label="SMA Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="flexOmSite.id" reference="FlexOmSite">
              <SelectInput
                label="FlexOM Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="sungrowSystem.id" reference="SungrowSystem">
              <SelectInput
                label="Sungrow System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="solisSystem.id" reference="SolisSystem">
              <SelectInput
                label="Solis System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="sunExchangeSite.id"
              reference="SunExchangeSite"
            >
              <SelectInput
                label="AMMP Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="powerFactorSystem.id"
              reference="PowerFactorSystem"
            >
              <SelectInput
                label="PowerFactor System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            {isIT ? (
              <>
                <TextInput
                  source="dataKey"
                  required
                  fullWidth
                  helperText="This field is used to look up data in the responses from SolarEdge, SCADA, etc."
                />
                <Typography style={{ color: 'orange' }}>
                  Note: For SolarEdge inverters, these fields are pulled
                  directly from SolarEdge so be careful while editing.
                </Typography>
              </>
            ) : null}
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const InverterFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="project.id"
      reference="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
  </Filter>
);

const styleRow = (record) => {
  const { capacity, serialNumber, equipmentItem } = record;
  const errorStyle = {
    backgroundColor: 'rgba(255,0,0,.2)',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (!capacity || !serialNumber || !equipmentItem) {
    return {
      ...errorStyle,
    };
  }
};

export const InverterList = () => {
  const { permissions } = usePermissions();

  const isIT = permissions?.roles?.map((el) => el.name)?.indexOf('ITRead') > -1;

  return (
    <>
      <Alert severity="info">
        <Grid container>
          <Grid item xs={12} style={{ padding: '1rem 1rem 0rem 1rem' }}>
            <Typography gutterBottom>
              <b>Inverter Notes:</b>
            </Typography>
            <Typography>
              - <b>SolarEdge</b> inverters should be automatically created for
              you when the SolarEdge Site is created.
            </Typography>
            <Typography>
              - <b>SCADA</b> inverters should be automatically created for you
              when the SCADA System is created.
            </Typography>
            <Typography>
              - <b>AMMP</b> inverters should be automatically created for you
              when the system is created.
            </Typography>
            <Typography>
              - <b>PowerFactor</b> TODO:
            </Typography>
            <Typography>
              - <b>SGD</b> inverters should be automatically created for you
              when the system is created. Code change needed in sgd.js to hook
              up daily inverter generation and site generation fetching. Find
              the keys for 'Geração Diária' using SGD API.
            </Typography>
            <Typography>
              - <b>SMA</b> inverters should be automatically created for you
              when the system is created.
            </Typography>
            <Typography>
              - <b>FlexOM</b> No API exists for FlexOM yet. Manually create
              inverters if we have data to upload for them
            </Typography>
            <Typography>
              - <b>Sungrow</b> inverters should be automatically created for you
              when the system is created.
            </Typography>
            <Typography>
              - <b>Solis</b> inverters should be automatically created for you
              when the Solis System is created.
            </Typography>
            <Typography gutterBottom style={{ marginTop: '1rem' }}>
              <b>Table Key:</b>
            </Typography>

            <LegendItem
              label="Missing capacity, serial number, and/or equipment item"
              color="rgba(255,0,0,.2)"
            />
          </Grid>
          <Grid item xs={12}>
            {' '}
          </Grid>
        </Grid>
      </Alert>
      <List title={entityName} perPage={25} filters={<InverterFilter />}>
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
          rowStyle={styleRow}
        >
          <TextField source="id" />
          <TextField source="name" />
          <NumberField
            source="capacity"
            label="Capacity (Inverter Input DC (Wp))"
          />
          <TextField source="serialNumber" />
          <LinkField
            label="Equipment Item"
            linkSource="equipmentItem.id"
            labelSource="equipmentItem.label"
            reference="EquipmentItem"
          />
          <LinkField
            label="SolarEdge Site"
            linkSource="solarEdgeSite.id"
            labelSource="solarEdgeSite.name"
            reference="SolarEdgeSite"
          />
          <LinkField
            label="SCADA System"
            linkSource="scadaSystem.id"
            labelSource="scadaSystem.name"
            reference="ScadaSystem"
          />
          <LinkField
            label="SGD System"
            linkSource="sgdSystem.id"
            labelSource="sgdSystem.name"
            reference="SgdSystem"
          />
          <LinkField
            label="SMA Site"
            linkSource="smaSite.id"
            labelSource="smaSite.name"
            reference="SmaSite"
          />
          <LinkField
            label="FlexOM Site"
            linkSource="flexOmSite.id"
            labelSource="flexOmSite.name"
            reference="FlexOmSite"
          />
          <LinkField
            label="Sungrow System"
            linkSource="sungrowSystem.id"
            labelSource="sungrowSystem.name"
            reference="SungrowSystem"
          />
          <LinkField
            label="Solis System"
            linkSource="solisSystem.id"
            labelSource="solisSystem.name"
            reference="SolisSystem"
          />
          <LinkField
            label="AMMP Site"
            linkSource="sunExchangeSite.id"
            labelSource="sunExchangeSite.name"
            reference="SunExchangeSite"
          />
          <LinkField
            label="PowerFactor System"
            linkSource="powerFactorSystem.id"
            labelSource="powerFactorSystem.name"
            reference="PowerFactorSystem"
          />
          {isIT ? <TextField source="dataKey" /> : null}
        </Datagrid>
      </List>
    </>
  );
};

export const InverterCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="serialNumber" fullWidth />
          <ReferenceInput source="equipmentItem.id" reference="EquipmentItem">
            <SelectInput
              label="Equipment Item"
              fullWidth
              allowEmpty
              optionText="label"
              helperText={
                <span>
                  If you don't see the equipment type you need, create it{' '}
                  <a href="/EquipmentItem/create">here</a>.
                </span>
              }
            />
          </ReferenceInput>
          <ReferenceInput source="solarEdgeSite.id" reference="SolarEdgeSite">
            <SelectInput
              label="SolarEdge Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="scadaSystem.id" reference="ScadaSystemLite">
            <SelectInput
              label="SCADA System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="sgdSystem.id" reference="SgdSystem">
            <SelectInput
              label="SGD System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="smaSite.id" reference="SmaSite">
            <SelectInput
              label="SMA Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="flexOmSite.id" reference="FlexOmSite">
            <SelectInput
              label="FlexOM Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="sungrowSystem.id" reference="SungrowSystem">
            <SelectInput
              label="Sungrow System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="solisSystem.id" reference="SolisSystem">
            <SelectInput
              label="Solis System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput
            source="sunExchangeSite.id"
            reference="SunExchangeSite"
          >
            <SelectInput
              label="AMMP Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput
            source="powerFactorSystem.id"
            reference="PowerFactorSystem"
          >
            <SelectInput
              label="PowerFactor System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <TextInput
            source="dataKey"
            required
            fullWidth
            helperText="This field is used to look up data in the responses from SolarEdge, SCADA, etc."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
