import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import {
  CustomNumberInput,
  CustomReferenceField,
  DetailField,
} from './CustomFields';

const entityName = 'Br Faq Category';

export const BrFaqCategoryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth step={1} />
            <TextInput source="description" multiline fullWidth />
          </Grid>
        </Grid>
        <ArrayField sortable={false} source="entries">
          <SingleFieldList>
            <CustomReferenceField source="questionPT" />
          </SingleFieldList>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const BrFaqCategoryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <NumberField source="orderNo" />
        <ArrayField source="faqEntries" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="questionPt" />
          </SingleFieldList>
        </ArrayField>
        <DetailField source="description" sortable={false} />
      </Datagrid>
    </List>
  );
};

export const BrFaqCategoryCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
