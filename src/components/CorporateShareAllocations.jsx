import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Button,
  Card,
  CircularProgress,
  Grid,
  Typography,
} from '@mui/material';
import moment from 'moment';

import {
  CustomBooleanField,
  CustomNumberInput,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CloudDownload, CloudUpload } from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Corporate Share Allocation';

export const CorporateShareAllocationEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const refresh = useRefresh();
  const notify = useNotify();
  const [loading, setLoading] = useState(false);

  const uploadToS3 = (event) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('PDF must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `CorporateInvestors/InvestmentAgreements/${
          file.name
        }_${moment().valueOf()}`
      );
      setLoading(true);
      uploadObjectToS3(file, awsObjectKey).then(
        () => {
          dataProvider
            .update('CorporateShareAllocation', {
              data: {
                id: parseInt(id, 10),
                shareAllocationAgreementAwsObjectKey: awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Document uploaded', { type: 'success' });
                setLoading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading document', { type: 'error' });
                setLoading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading document to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="user.id"
              reference="UserLite"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
              required
            >
              <AutocompleteInput
                optionText="label"
                label="User"
                fullWidth
                required
              />
            </ReferenceInput>
            <DateInput source="startDt" fullWidth />
            <CustomNumberInput source="shares" fullWidth />
            <CustomNumberInput source="value" fullWidth />
            <ReferenceInput
              source="corporateShareClass.id"
              reference="CorporateShareClass"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Corporate Share Class"
                fullWidth
              />
            </ReferenceInput>
            <ReferenceInput
              source="corporateRound.id"
              reference="CorporateRound"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Corporate Round"
                fullWidth
              />
            </ReferenceInput>
            <BooleanInput source="optionFlg" fullWidth />
            <Card
              style={{
                margin: '1rem 0',
                padding: '1rem',
                backgroundColor: '#f3f3f3',
              }}
            >
              <Typography>
                <b>Share Allocation Agreement</b>
              </Typography>
              <FunctionField
                label="Share Allocation Agreement"
                render={(record) => {
                  return (
                    <Grid container spacing={2}>
                      <Grid item>
                        <Button
                          disabled={!record.shareAllocationAgreementDownloadUrl}
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            window.location.assign(
                              record.shareAllocationAgreementDownloadUrl
                            );
                          }}
                        >
                          Download
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          color="primary"
                          variant="contained"
                          component="label" // https://stackoverflow.com/a/54043619
                          startIcon={<CloudUpload />}
                          style={{ textTransform: 'none' }}
                          disabled={loading}
                        >
                          {loading ? (
                            <CircularProgress
                              style={{ position: 'absolute' }}
                            />
                          ) : null}
                          {record.shareAllocationAgreementDownloadUrl
                            ? 'Overwrite Document'
                            : 'Upload Document'}
                          <input
                            type="file"
                            hidden
                            onChange={(event) => uploadToS3(event)}
                            accept="application/pdf"
                          />
                        </Button>
                      </Grid>
                    </Grid>
                  );
                }}
              />
            </Card>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Investor First Name or Last Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const CorporateShareAllocationList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} filters={<CustomFilter />}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.label"
          label="User"
        />
        <DateField source="startDt" />
        <NumberField source="shares" />
        <NumberField source="value" />
        <NumberField source="realizedSharePrice" sortable={false} />
        <LinkField
          reference="CorporateShareClass"
          linkSource="corporateShareClass.id"
          labelSource="corporateShareClass.name"
          label="Corporate Share Class"
        />
        <LinkField
          reference="CorporateRound"
          linkSource="corporateRound.id"
          labelSource="corporateRound.name"
          label="Corporate Round"
        />
        <CustomBooleanField source="optionFlg" />
        <FunctionField
          label="Share allocation agreement"
          render={(record) => {
            if (record.shareAllocationAgreementDownloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.assign(
                      record.shareAllocationAgreementDownloadUrl
                    );
                  }}
                >
                  Download
                </Button>
              );
            }
            return null;
          }}
        />
      </Datagrid>
    </List>
  );
};

export const CorporateShareAllocationCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false} redirect="list">
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="user.id"
            reference="UserLite"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <AutocompleteInput
              optionText="fullName"
              label="User"
              fullWidth
              required
            />
          </ReferenceInput>
          <DateInput source="startDt" fullWidth />
          <CustomNumberInput source="shares" fullWidth />
          <CustomNumberInput source="value" fullWidth />
          <ReferenceInput
            source="corporateShareClass.id"
            reference="CorporateShareClass"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Corporate Share Class"
              fullWidth
            />
          </ReferenceInput>
          <ReferenceInput
            source="corporateRound.id"
            reference="CorporateRound"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput optionText="name" label="Corporate Round" fullWidth />
          </ReferenceInput>
          <BooleanInput source="optionFlg" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
