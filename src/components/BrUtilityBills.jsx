import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  FormDataConsumer,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import {
  Button,
  CircularProgress,
  Collapse,
  Grid,
  Typography,
} from '@mui/material';
import { CheckCircle, CloudDownload, CloudUpload } from '@mui/icons-material';
import moment from 'moment';
import { useFormContext } from 'react-hook-form';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { uploadObjectToS3 } from '../utils/aws';
import theme from '../theme';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Utility Bill';

export const BrUtilityBillEdit = () => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();

  const uploadFileToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      let fileExtension = '';
      if (file.type === 'image/png') {
        fileExtension = 'png';
      } else if (file.type === 'image/jpeg') {
        fileExtension = 'jpeg';
      } else if (file.type === 'image/jpg') {
        fileExtension = 'jpg';
      } else if (file.type === 'application/pdf') {
        fileExtension = 'pdf';
      } else {
        notify('Unknown file type. Contact IT for help.', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `UtilityBills/UtilityBill_${moment().valueOf()}.${fileExtension}`
      );
      setLoading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          dataProvider
            .update('BrUtilityBill', {
              data: {
                id: parseInt(id, 10),
                awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Utility bill uploaded', { type: 'success' });
                setLoading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading utility bill', { type: 'error' });
                setLoading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading utility bill to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              perPage={10_000}
              source="brConsumerUnit.id"
              sortable={false}
              reference="BrConsumerUnitLite"
              required
            >
              <AutocompleteInput
                label="Consumer Unit"
                required
                fullWidth
                optionText="name"
                shouldRenderSuggestions={(value) => true}
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Grid container spacing={2}>
                    <Grid item>
                      <Button
                        color="primary"
                        variant="contained"
                        component="label" // https://stackoverflow.com/a/54043619
                        startIcon={<CloudUpload />}
                        style={{ textTransform: 'none' }}
                        disabled={loading}
                      >
                        {loading ? (
                          <CircularProgress style={{ position: 'absolute' }} />
                        ) : null}
                        {formData.downloadUrl
                          ? 'Overwrite Utility Bill'
                          : 'Upload Utility Bill'}
                        <input
                          type="file"
                          hidden
                          onChange={(event) => uploadFileToS3(event, formData)}
                          accept="image/png, image/jpeg, image/jpg, application/pdf"
                        />
                      </Button>
                    </Grid>
                    <Grid item>
                      <Button
                        disabled={!formData.downloadUrl}
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{ textTransform: 'none' }}
                        onClick={() =>
                          window.location.assign(formData.downloadUrl)
                        }
                      >
                        Download Utility Bill
                      </Button>
                    </Grid>
                  </Grid>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrUtilityBillList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="BrConsumerUnit"
          linkSource="brConsumerUnit.id"
          labelSource="brConsumerUnit.name"
          label="Consumer Unit"
        />
        <FunctionField
          label="Utility Bill"
          render={(record) => {
            if (record.downloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.assign(record.downloadUrl);
                  }}
                >
                  Download
                </Button>
              );
            }
            return null;
          }}
        />
      </Datagrid>
    </List>
  );
};

export const BrUtilityBillCreate = () => {
  const [billAwsObjectKey, setBillAwsObjectKey] = useState(null);
  const notify = useNotify();

  const MyCreateButton = () => {
    const dataProvider = useDataProvider();
    const { getValues } = useFormContext();
    const redirect = useRedirect();

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      const { id, ...data } = getValues();
      data.awsObjectKey = billAwsObjectKey;
      dataProvider.create('BrUtilityBill', { id, data }).then(
        () => {
          notify('Element created');
          redirect('/BrUtilityBill');
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={!billAwsObjectKey}
        />
      </Toolbar>
    );
  };

  const uploadFileToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      let fileExtension = '';
      if (file.type === 'image/png') {
        fileExtension = 'png';
      } else if (file.type === 'image/jpeg') {
        fileExtension = 'jpeg';
      } else if (file.type === 'image/jpg') {
        fileExtension = 'jpg';
      } else if (file.type === 'application/pdf') {
        fileExtension = 'pdf';
      } else {
        notify('Unknown file type. Contact IT for help.', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `UtilityBills/UtilityBill_${moment().valueOf()}.${fileExtension}`
      );
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          setBillAwsObjectKey(awsObjectKey);
        },
        (e) => {
          notify('Error uploading file to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm toolbar={<MyCreateButton />}>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              perPage={10_000}
              source="brConsumerUnit.id"
              sortable={false}
              reference="BrConsumerUnitLite"
              required
            >
              <AutocompleteInput
                label="Consumer Unit"
                required
                fullWidth
                optionText="name"
                shouldRenderSuggestions={(value) => true}
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Collapse in={formData.brConsumerUnit?.id}>
                    {billAwsObjectKey ? (
                      <Grid container alignItems="center">
                        <Grid item>
                          <CheckCircle
                            style={{ color: theme.palette.success.main }}
                          />
                        </Grid>
                        <Grid item>
                          <Typography
                            style={{
                              color: theme.palette.success.main,
                              paddingLeft: '.5rem',
                            }}
                          >
                            Bill ready to upload. Click 'Save' below to complete
                            upload.
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <Button
                        color="primary"
                        disabled={billAwsObjectKey}
                        variant="contained"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        Upload Utility Bill
                        <input
                          type="file"
                          hidden
                          onChange={(event) => uploadFileToS3(event, formData)}
                          accept="application/pdf"
                        />
                      </Button>
                    )}
                  </Collapse>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
