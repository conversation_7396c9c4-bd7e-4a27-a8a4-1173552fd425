import React, { useState } from 'react';
import { useDataProvider, useNotify, usePermissions } from 'react-admin';
import {
  Alert,
  Avatar,
  Button,
  Card,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  Icon,
  IconButton,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  Typography,
  useMediaQuery,
} from '@mui/material';
import moment from 'moment';
import {
  Add,
  Cancel,
  CheckCircle,
  Close,
  Delete,
  Edit,
} from '@mui/icons-material';
import { Image, Transformation } from 'cloudinary-react';

import Config from '../config/config';
import theme from '../theme';

import { openUploadWidget } from '../utils/CloudinaryService';
import cloudinary from 'cloudinary-core';

const cl = new cloudinary.Cloudinary({
  cloud_name: Config.cloud_name,
  secure: true,
});

export const OMReportImageDialog = (props) => {
  const {
    reportId,
    handleRefreshParent,
    open,
    setOpen,
    fullScreen,
    isTicketEditable,
  } = props;
  const [loading, setLoading] = useState(false);
  const [report, setReport] = useState(null);
  const [editImageDialogOpen, setEditImageDialogOpen] = useState(false);
  const [selectedImageId, setSelectedImageId] = useState(null);
  const [selectedImageUrl, setSelectedImageUrl] = useState(null);
  const [imgDescription, setImgDescription] = useState(null);
  const [includeInReportFlg, setIncludeInReportFlg] = useState(null);
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  const isXS = useMediaQuery((theme) => theme.breakpoints.only('xs'));
  const isSM = useMediaQuery((theme) => theme.breakpoints.only('sm'));
  const isMD = useMediaQuery((theme) => theme.breakpoints.only('md'));
  const isLG = useMediaQuery((theme) => theme.breakpoints.only('lg'));
  const isXL = useMediaQuery((theme) => theme.breakpoints.only('xl'));
  const getImageListCols = () => {
    if (isSM || isXS) return 1;
    if (isMD) return 2;
    if (isLG) return 3;
    if (isXL) return 4;
  };

  const fetchOMReport = () => {
    setLoading(true);
    dataProvider.getOne('OMReport', { id: reportId }).then((res) => {
      setReport(res.data);
      setLoading(false);
    });
  };

  if (
    (!report && !loading) ||
    (report?.id && !loading && report?.id !== reportId)
  ) {
    fetchOMReport();
  }

  const onPhotosUploaded = (aPhotos) => {
    setLoading(true);
    const photos = aPhotos.map((photo) => {
      return {
        public_id: photo.public_id,
        description: null,
        omReportId: report.id,
      };
    });
    photos.forEach((photo) => {
      dataProvider
        .create('OMReportImage', {
          data: photo,
        })
        .then(
          () => {
            notify('Image successfully added', { type: 'success' });
            fetchOMReport();
            handleRefreshParent();
            setLoading(false);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error uploading image', { type: 'error' });
            setLoading(false);
          }
        );
    });
  };

  const updateOMReportImage = () => {
    dataProvider
      .update('OMReportImage', {
        data: {
          id: selectedImageId,
          description: imgDescription,
          includeInReportFlg: !!includeInReportFlg,
        },
      })
      .then(
        () => {
          notify('Image successfully updated', { type: 'success' });
          fetchOMReport();
          handleRefreshParent();
          setLoading(false);
          setEditImageDialogOpen(false);
          setImgDescription(null);
          setIncludeInReportFlg(null);
          setSelectedImageId(null);
          setSelectedImageUrl(null);
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error updating image', { type: 'error' });
          setLoading(false);
        }
      );
  };

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['om-report'],
      showPoweredBy: false,
      multiple: true,
      cloudName: Config.cloud_name,
      uploadPreset: Config.om_report_upload_preset,
      clientAllowedFormats: ['jpg', 'jpeg', 'png'],
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };

  const handleRemovePhoto = () => {
    setLoading(true);
    dataProvider
      .delete('OMReportImage', {
        id: selectedImageId,
      })
      .then(
        () => {
          notify('Image successfully removed', { type: 'success' });
          setLoading(false);
          fetchOMReport();
          handleRefreshParent();
          setEditImageDialogOpen(false);
          setImgDescription(null);
          setIncludeInReportFlg(null);
          setSelectedImageId(null);
          setSelectedImageUrl(null);
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error removing image', { type: 'error' });
          setLoading(false);
        }
      );
  };

  return (
    <Dialog
      open={open}
      maxWidth="lg"
      fullScreen={fullScreen}
      onClose={() => setOpen(false)}
    >
      <DialogTitle>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            {fullScreen ? 'Images' : 'Service Report Images'}{' '}
            {isTicketEditable && (
              <Button
                onClick={uploadImageWithCloudinary}
                variant="contained"
                style={{ marginLeft: '1rem' }}
                endIcon={<Add />}
              >
                {fullScreen ? 'Upload' : 'Upload Photos'}
              </Button>
            )}
          </Grid>
          <Grid item>
            <IconButton onClick={() => setOpen(false)}>
              <Close />
            </IconButton>
          </Grid>
        </Grid>
      </DialogTitle>
      <DialogContent>
        {!report ? (
          <CircularProgress />
        ) : (
          <>
            <Grid container>
              <Grid item xs={12}>
                {report.omReportImages?.length > 0 ? (
                  <ImageList cols={getImageListCols()} gap={6}>
                    {report.omReportImages ? (
                      report.omReportImages.map((img) => {
                        const clUrl =
                          img.public_id &&
                          cl.url(img.public_id, {
                            width: '500',
                            height: '500',
                            crop: 'fill',
                          });
                        const highResClUrl = cl.url(img.public_id);
                        return (
                          <ImageListItem key={`om-report-image-${img.id}`}>
                            <img
                              src={clUrl}
                              style={{
                                cursor: isTicketEditable ? 'pointer' : null,
                              }}
                              loading="lazy"
                              onClick={() => {
                                if (!isTicketEditable) return;
                                setImgDescription(img.description);
                                setIncludeInReportFlg(!!img.includeInReportFlg);
                                setSelectedImageUrl(highResClUrl);
                                setSelectedImageId(img.id);
                                setEditImageDialogOpen(true);
                              }}
                            />
                            <ImageListItemBar
                              title={
                                <Typography
                                  variant="body2"
                                  paragraph
                                  style={{
                                    textOverflow: 'ellipsis',
                                    overflow: 'auto',
                                  }}
                                >
                                  {img.description || <i>No description</i>}
                                </Typography>
                              }
                              subtitle={
                                <Grid
                                  container
                                  alignItems="center"
                                  style={{ height: '100%' }}
                                >
                                  {img.includeInReportFlg ? (
                                    <Chip
                                      color="primary"
                                      style={{
                                        background: theme.palette.green.dark,
                                      }}
                                      size="small"
                                      icon={<CheckCircle />}
                                      label="Included in report"
                                    />
                                  ) : (
                                    <Chip
                                      color="primary"
                                      style={{
                                        background: '#777',
                                      }}
                                      size="small"
                                      icon={<Cancel />}
                                      label="Excluded from report"
                                    />
                                  )}
                                </Grid>
                              }
                              actionIcon={
                                isTicketEditable && (
                                  <IconButton
                                    sx={{
                                      color: 'white',
                                    }}
                                    onClick={() => {
                                      setImgDescription(img.description);
                                      setIncludeInReportFlg(
                                        !!img.includeInReportFlg
                                      );
                                      setSelectedImageUrl(highResClUrl);
                                      setSelectedImageId(img.id);
                                      setEditImageDialogOpen(true);
                                    }}
                                  >
                                    <Edit />
                                  </IconButton>
                                )
                              }
                            />
                          </ImageListItem>
                        );
                      })
                    ) : (
                      <CircularProgress />
                    )}
                  </ImageList>
                ) : (
                  <Alert severity="info">No images have been uploaded</Alert>
                )}
              </Grid>
            </Grid>
            <Dialog
              open={editImageDialogOpen}
              onClose={() => {
                setEditImageDialogOpen(false);
                setImgDescription(null);
                setIncludeInReportFlg(null);
                setSelectedImageId(null);
                setSelectedImageUrl(null);
              }}
              fullScreen={fullScreen}
            >
              <DialogTitle>
                <Grid container justifyContent="flex-end" alignItems="center">
                  <Grid item>
                    <IconButton
                      onClick={() => {
                        setEditImageDialogOpen(false);
                        setImgDescription(null);
                        setIncludeInReportFlg(null);
                        setSelectedImageId(null);
                        setSelectedImageUrl(null);
                      }}
                    >
                      <Close />
                    </IconButton>
                  </Grid>
                </Grid>
              </DialogTitle>
              <DialogContent>
                <Grid container>
                  <Grid item xs={12}>
                    {selectedImageUrl && (
                      <img width="100%" src={selectedImageUrl} />
                    )}
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      value={imgDescription || ''}
                      multiline
                      required
                      onChange={(event) => {
                        setImgDescription(event.target.value);
                      }}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeInReportFlg}
                          label="Include image in report"
                          onChange={(event) => {
                            setIncludeInReportFlg(!includeInReportFlg);
                          }}
                          fullWidth
                        />
                      }
                      label="Include image in report"
                    />
                  </Grid>
                  <Grid item xs={12} style={{ marginTop: '1rem' }}>
                    <Button
                      variant="contained"
                      onClick={() => handleRemovePhoto()}
                      color="error"
                    >
                      Delete Image
                    </Button>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button
                  onClick={() => {
                    setEditImageDialogOpen(false);
                    setImgDescription(null);
                    setIncludeInReportFlg(null);
                    setSelectedImageId(null);
                    setSelectedImageUrl(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    updateOMReportImage();
                  }}
                  variant="contained"
                  color="primary"
                >
                  Save
                </Button>
              </DialogActions>
            </Dialog>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
