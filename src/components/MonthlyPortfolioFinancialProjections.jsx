import React, { useState } from 'react';
import moment from 'moment';
import numeral from 'numeral';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useRefresh,
  usePermissions,
  useDataProvider,
  useResourceDefinition,
} from 'react-admin';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
} from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { MonthlyPortfolioFinancialsUpload } from './MonthlyPortfolioFinancialsUpload';
import { CustomNumberInput, DetailField, LinkField } from './CustomFields';

const entityName = 'Monthly Financial Projection';

export const MonthlyPortfolioFinancialProjectionEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextField source="id" />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <DateInput source="effectiveDt" fullWidth />
            <CustomNumberInput source="totalShares" fullWidth />
            <CustomNumberInput source="fees" fullWidth />
            <CustomNumberInput source="carry" fullWidth />
            <CustomNumberInput source="totalEquity" fullWidth />
            <CustomNumberInput source="grossCafd" fullWidth />
            <CustomNumberInput source="production" fullWidth />
            <CustomNumberInput source="sharePrice" fullWidth />
            <ReferenceInput source="portfolio.id" reference="PortfolioLite">
              <SelectInput label="Portfolio" fullWidth optionText="name" />
            </ReferenceInput>
            <TextInput source="notes" multiline fullWidth />
            {/* <DateInput source="createdAt" /> */}
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const ProjectionsFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="createdAtReference.id"
      label="Version"
      reference="UniqueMonthlyPortfolioFinancialProjectionCreatedAtDates"
    >
      <SelectInput
        label="Versions"
        optionText={(value) => moment(value.date).format('MM/DD/YYYY LT')}
      />
    </ReferenceInput>
  </Filter>
);
const ProjectionsPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

export const MonthlyPortfolioFinancialProjectionList = () => {
  const refresh = useRefresh();
  const [openDialog, setOpenDialog] = useState(false);
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();

  return (
    <>
      <Grid container justifyContent="flex-end">
        <Grid item>
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setOpenDialog(true)}
          >
            Undo latest upload
          </Button>
          <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
            <DialogTitle>Undo latest upload?</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to undo the latest upload? This cannot be
                undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenDialog(false)} color="primary">
                Cancel
              </Button>
              <Button
                onClick={() => {
                  dataProvider
                    .delete('MonthlyPortfolioFinancialProjection', {
                      id: 0, // zero id indicates that you are deleting all most recently uploaded entries
                    })
                    .then(() => {
                      setOpenDialog(false);
                      refresh();
                    });
                }}
                color="primary"
                autoFocus
              >
                Undo
              </Button>
            </DialogActions>
          </Dialog>
        </Grid>
      </Grid>
      <List
        title="Monthly Financial Projections"
        perPage={25}
        pagination={<ProjectionsPagination />}
        filters={<ProjectionsFilter />}
        sort={{ field: 'id', order: 'DESC' }}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <DateField source="effectiveDt" />
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />
          <NumberField source="totalShares" />
          <NumberField
            source="fees"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="carry"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="totalEquity"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField
            source="grossCafd"
            options={{ style: 'currency', currency: 'USD' }}
          />
          <NumberField source="production" label="Production (MWh)" />
          <NumberField
            source="sharePrice"
            options={{
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 6,
            }}
          />
          <DetailField source="notes" />
          <DateField source="createdAt" />
        </Datagrid>
      </List>
    </>
  );
};

export const MonthlyPortfolioFinancialProjectionCreate = () => {
  const dataProvider = useDataProvider();

  const attrs = [
    { name: 'portfolioId', label: 'Portfolio ID', align: 'center' },
    {
      name: 'effectiveDt',
      format: (val) => moment(val).format('MM/DD/YYYY'),
      label: 'Effective Date',
      align: 'center',
    },
    {
      name: 'totalShares',
      dataFormat: (val) => Math.round(val),
      format: (val) => numeral(val).format('0,0'),
      label: 'Total Share Count',
      align: 'center',
    },
    {
      name: 'totalEquity',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Total Equity',
      align: 'center',
    },
    {
      name: 'netDistribution',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Net Distribution',
      align: 'center',
    },
    {
      name: 'fees',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Fees',
      align: 'center',
    },
    {
      name: 'carry',
      dataFormat: (val) => Math.round(val * 100) / 100,
      format: (val) => numeral(val).format('$0,0.00'),
      label: 'Carry',
      align: 'center',
    },
    {
      name: 'production',
      dataFormat: (val) => Math.round(val * 10) / 10,
      format: (val) => numeral(val).format('0,0.0'),
      label: 'Production (MWh)',
      align: 'center',
    },
    {
      name: 'sharePrice',
      dataFormat: (val) => Math.round(val * 1000000) / 1000000,
      format: (val) => numeral(val).format('$0,0.000'),
      label: 'Share Price',
      align: 'center',
    },
    {
      name: 'notes',
      label: 'Notes',
      align: 'center',
    },
  ];

  return (
    <Create title={`Create ${entityName}`}>
      <MonthlyPortfolioFinancialsUpload
        dataProvider={dataProvider}
        attrs={attrs}
        type="Projection"
      />
    </Create>
  );
};
