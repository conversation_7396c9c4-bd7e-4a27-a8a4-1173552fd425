import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDataProvider, useNotify, useRefresh } from 'react-admin';

import {
  Alert,
  Badge,
  Button,
  CircularProgress,
  Divider,
  Fab,
  Grid,
  IconButton,
  TextField as MuiTextField,
  Typography,
} from '@mui/material';

import { Cancel, Delete, CloudUpload, ArrowForward } from '@mui/icons-material';

import { Image, Video, Transformation } from 'cloudinary-react';

import Config from '../config/config';
import theme from '../theme';

import { openUploadWidget } from '../utils/CloudinaryService';

const PortfolioMedia = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();
  const [images, setImages] = useState(null);
  const [videos, setVideos] = useState(null);
  const [loading, setLoading] = useState(false);
  const [imageTitles, setImageTitles] = useState({});

  const mediaQueryParams = {
    filter: {
      portfolioId: parseInt(id, 10),
    },
    sort: {
      field: 'id',
      order: 'ASC',
    },
    pagination: {
      page: 1,
      perPage: 10000,
    },
  };

  const fetchImages = () => {
    dataProvider
      .getList('PortfolioImage', mediaQueryParams)
      .then((images) => setImages(images.data));
  };

  if (!images) {
    fetchImages();
  }

  if (!videos) {
    dataProvider
      .getList('PortfolioVideo', mediaQueryParams)
      .then((videos) => setVideos(videos.data));
  }

  const generateSignature = (callback, props2) => {
    return dataProvider
      .getOne('VideoSignature', {
        data: JSON.stringify(props2),
      })
      .then(
        (resp) => {
          return callback(resp.data.signature);
        },
        (e) => {
          console.error(e);
        }
      );
  };

  const updateSelectedImageState = (flg, publicId) => {
    const updatedImages = [...images];
    updatedImages.forEach((image) => {
      image[String(flg)] = image.public_id === publicId;
    });
    setImages(updatedImages);
  };

  const updateSelectedVideoState = (publicId) => {
    const updatedVideos = [...videos];
    updatedVideos.forEach((video) => {
      video.primaryFlg = video.public_id === publicId;
    });
    setVideos(updatedVideos);
  };

  const onPhotosUploaded = (aPhotos) => {
    setLoading(true);
    const photos = aPhotos.map((photo) => {
      return {
        public_id: photo.public_id,
        portfolioId: parseInt(id, 10),
        bannerFlg: false,
        primaryFlg: false,
        mobileFlg: false,
      };
    });
    dataProvider
      .create('PortfolioImage', {
        input: photos,
      })
      .then(
        () => {
          notify('Image successfully added', { type: 'success' });
          refresh();
          setLoading(false);
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error uploading image', { type: 'error' });
          setLoading(false);
        }
      );
  };

  const onVideosUploaded = (aVideos) => {
    setLoading(true);
    const newVideos = aVideos.map((video) => {
      return {
        public_id: video.public_id,
        portfolioId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('PortfolioVideo', {
        input: newVideos,
      })
      .then(
        () => {
          notify('Video successfully added', { type: 'success' });
          refresh();
          setLoading(false);
        },
        (e) => {
          console.error(e);
          notify('Error uploading videos.', { type: 'error' });
          setLoading(false);
        }
      );
  };

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['portfolios'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.portfolio_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.portfolio_video_upload_preset,
      uploadSignature: generateSignature,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideosUploaded([resp.info]);
      }
    });
  };

  const handleUpdatePhotoTitle = (imageId, title) => {
    const updatedRecord = {
      id: parseInt(imageId, 10),
      title: title,
    };
    dataProvider
      .update('PortfolioImage', {
        data: updatedRecord,
      })
      .then(
        () => {
          notify('Title successfully updated', { type: 'success' });
          const updatedImageTitles = { ...imageTitles };
          delete updatedImageTitles[parseInt(imageId, 10)];
          setImageTitles(updatedImageTitles);
          fetchImages();
          // updateSelectedImageState('primaryFlg', resource.public_id);
        },
        (e) => {
          console.error('ERROR', e);
          notify('Error updating image', { type: 'error' });
        }
      );
  };

  const handleMakePhotoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryImage: resource.id,
      };
      dataProvider
        .update('Portfolio', {
          data: updatedRecord,
        })
        .then(
          () => {
            notify('Primary image successfully updated', { type: 'success' });
            updateSelectedImageState('primaryFlg', resource.public_id);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error updating images', { type: 'error' });
          }
        );
    };
  };

  const handleMakePhotoBanner = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        bannerImage: resource.id,
      };
      dataProvider
        .update('Portfolio', {
          data: updatedRecord,
        })
        .then(
          () => {
            notify('Banner image successfully updated', { type: 'success' });
            updateSelectedImageState('bannerFlg', resource.public_id);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error updating images', { type: 'error' });
          }
        );
    };
  };

  const handleMakePhotoMobile = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        mobileImage: resource.id,
      };
      dataProvider
        .update('Portfolio', {
          data: updatedRecord,
        })
        .then(
          () => {
            notify('Mobile image successfully updated', { type: 'success' });
            updateSelectedImageState('mobileFlg', resource.public_id);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error updating images', { type: 'error' });
          }
        );
    };
  };

  const handleMakeVideoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryVideo: resource.id,
      };
      dataProvider
        .update('Portfolio', {
          data: updatedRecord,
        })
        .then(
          () => {
            notify('Successfully updated videos', { type: 'success' });
            updateSelectedVideoState(resource.public_id);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error updating videos', { type: 'error' });
          }
        );
    };
  };

  const handleRemovePhoto = (resource) => {
    return () => {
      setLoading(true);
      dataProvider
        .delete('PortfolioImage', {
          portfolioId: parseInt(id, 10),
          id: resource.id,
        })
        .then(
          () => {
            notify('Image successfully removed', { type: 'success' });
            refresh();
            setLoading(false);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error removing image', { type: 'error' });
            setLoading(false);
          }
        );
    };
  };

  const handleRemoveVideo = (resource) => {
    return () => {
      setLoading(true);
      dataProvider
        .delete('PortfolioVideo', {
          portfolioId: parseInt(id, 10),
          id: resource.id,
        })
        .then(
          () => {
            notify('Video successfully removed', { type: 'success' });
            refresh();
            setLoading(false);
          },
          (e) => {
            console.error('ERROR', e);
            notify('Error removing video', { type: 'error' });
            setLoading(false);
          }
        );
    };
  };

  return (
    <>
      <Grid container justifyContent="space-between">
        <Grid item>
          <Typography gutterBottom variant="h4">
            Images :
          </Typography>
        </Grid>
      </Grid>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} component={Alert} severity="info">
          <Typography>
            Desktop Banner Image : 1920 X 1276 (landscape)
          </Typography>
          <Typography>Mobile Banner Image : 1080 X 1920 (portrait)</Typography>
          <Typography>Tile Image : 1600 X 1200 (landscape)</Typography>
          <Button
            variant="outlined"
            style={{ marginTop: '1em' }}
            disabled={loading}
            onClick={uploadImageWithCloudinary}
            startIcon={<CloudUpload />}
          >
            {loading ? <CircularProgress /> : 'Upload Photo'}
          </Button>
        </Grid>
      </Grid>
      <Grid container spacing={4} style={{ marginTop: '1em' }}>
        {images
          ? images.map((image) => {
              const saveImageTitleDisabled =
                !imageTitles[image.id] || imageTitles[image.id] === image.title;
              console.log(imageTitles);
              return (
                <Grid
                  key={`portfolio-image-grid-${image.id}`}
                  // style={{ width: '100%' }}
                  item
                  xs={12}
                  md={4}
                  xl={3}
                >
                  {loading ? (
                    <CircularProgress />
                  ) : (
                    <div style={{ position: 'relative' }}>
                      <span>image id: {image.id}</span>
                      <Image
                        style={{ width: '100%' }}
                        cloud_name={Config.cloud_name}
                        publicId={image.public_id}
                      >
                        <Transformation width="380" crop="scale" />
                      </Image>
                      <Fab
                        size="small"
                        color="secondary"
                        style={{
                          position: 'absolute',
                          top: '-1rem',
                          right: '-1rem',
                          backgroundColor: theme.palette.error.main,
                        }}
                      >
                        <Delete onClick={handleRemovePhoto(image)} />
                      </Fab>
                    </div>
                  )}
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      <MuiTextField
                        label="Image title"
                        value={imageTitles[image.id] || image.title || ''}
                        onChange={(event) => {
                          const title = event.target.value;
                          const updatedImageTitles = { ...imageTitles };
                          updatedImageTitles[image.id] = title;
                          setImageTitles(updatedImageTitles);
                        }}
                        on
                        fullWidth
                        InputProps={{
                          endAdornment: (
                            <IconButton
                              onClick={() => {
                                handleUpdatePhotoTitle(
                                  image.id,
                                  imageTitles[image.id]
                                );
                              }}
                              disabled={saveImageTitleDisabled}
                            >
                              <ArrowForward />
                            </IconButton>
                          ),
                        }}
                      />
                    </Grid>
                    <Grid item>
                      <Button
                        color="primary"
                        style={
                          image.primaryFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        size="small"
                        disabled={image.primaryFlg || loading}
                        variant={image.primaryFlg ? 'contained' : 'outlined'}
                        onClick={handleMakePhotoPrimary(image)}
                      >
                        {loading ? <CircularProgress /> : 'Mobile Tile'}
                      </Button>
                    </Grid>
                    <Grid item>
                      <Button
                        color="primary"
                        style={
                          image.bannerFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        size="small"
                        disabled={image.bannerFlg || loading}
                        variant={image.bannerFlg ? 'contained' : 'outlined'}
                        onClick={handleMakePhotoBanner(image)}
                      >
                        {loading ? <CircularProgress /> : 'Desktop Banner'}
                      </Button>
                    </Grid>
                    <Grid item>
                      <Button
                        color="primary"
                        style={
                          image.mobileFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        size="small"
                        disabled={image.mobileFlg || loading}
                        variant={image.mobileFlg ? 'contained' : 'outlined'}
                        onClick={handleMakePhotoMobile(image)}
                      >
                        {loading ? <CircularProgress /> : 'Mobile Banner'}
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              );
            })
          : null}
      </Grid>
      <Divider
        style={{
          width: '100%',
          marginTop: '2em',
          marginBottom: '2em',
        }}
      />
      <Grid container style={{ width: '90%' }} justifyContent="space-between">
        <Grid item>
          <Typography gutterBottom variant="h4">
            Videos :
          </Typography>
        </Grid>
        <Grid item>
          <div className="actions">
            <Button
              variant="outlined"
              style={{ marginTop: '1em' }}
              disabled={loading}
              onClick={uploadVideoWithCloudinary}
              startIcon={<CloudUpload />}
            >
              {loading ? <CircularProgress /> : 'Upload Video'}
            </Button>
          </div>
        </Grid>
      </Grid>
      <Grid container spacing={4} style={{ width: '100%' }}>
        {videos
          ? videos.map((video) => {
              return (
                <Grid
                  key={`portfolio-video-${video.id}`}
                  item
                  xs={12}
                  md={4}
                  xl={3}
                  style={{ width: '100%' }}
                >
                  {loading ? (
                    <CircularProgress />
                  ) : (
                    <div style={{ position: 'relative' }}>
                      <Fab
                        size="small"
                        color="secondary"
                        style={{
                          position: 'absolute',
                          top: '-1rem',
                          right: '-1rem',
                          backgroundColor: theme.palette.error.main,
                        }}
                      >
                        <Delete onClick={handleRemoveVideo(video)} />
                      </Fab>
                      <Video
                        width="200"
                        cloud_name={Config.cloud_name}
                        style={{ width: '100%' }}
                        publicId={video.public_id}
                        muted
                        sourceTypes={['mp4']}
                        controls
                      >
                        <Transformation width="200" crop="scale" />
                      </Video>
                    </div>
                  )}
                  <Grid item>
                    <Button
                      color="primary"
                      size="small"
                      style={
                        video.primaryFlg
                          ? {
                              backgroundColor: 'green',
                              color: 'white',
                              marginRight: '1em',
                            }
                          : { marginRight: '1em' }
                      }
                      disabled={video.primaryFlg || loading}
                      variant={video.primaryFlg ? 'contained' : 'outlined'}
                      onClick={handleMakeVideoPrimary(video)}
                    >
                      {loading ? <CircularProgress /> : 'Primary'}
                    </Button>
                  </Grid>
                </Grid>
              );
            })
          : null}
      </Grid>
    </>
  );
};

export default PortfolioMedia;
