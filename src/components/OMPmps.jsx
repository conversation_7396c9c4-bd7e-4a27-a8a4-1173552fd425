import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import moment from 'moment';
import numeral from 'numeral';
import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import xl from 'excel4node';

import {
  Button,
  Chip,
  CircularProgress,
  Collapse,
  Divider,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import {
  AddCircle,
  ArrowForward,
  CloudDownload,
  KeyboardArrowDown,
  KeyboardArrowUp,
} from '@mui/icons-material';
import theme from '../theme';
import { downloadWorkbook, writeToCell } from '../utils/excel';

const entityName = 'PMP Dashboard';

const OMPmpChecklistItemTicketLink = ({ omPmpChecklistItemId }) => {
  const dataProvider = useDataProvider();
  const refresh = useRefresh();
  const notify = useNotify();
  const [ticketData, setTicketData] = useState(null);
  const [loading, setLoading] = useState(false);

  const linkOMTicketToChecklistItem = (omTicketId, omPmpChecklistItemId) => {
    dataProvider
      .update('OMTicket', {
        data: {
          id: omTicketId,
          omPmpChecklistItemId,
        },
      })
      .then(
        () => {
          notify('Successfully linked ticket to checklist item', {
            type: 'success',
          });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error linking ticket to checklist item', { type: 'error' });
        }
      );
  };

  if (!ticketData && !loading) {
    setLoading(true);
    dataProvider
      .getOne('OMPmpChecklistItemTickets', {
        id: omPmpChecklistItemId,
      })
      .then(
        (res) => {
          setTicketData(res.data);
          setLoading(false);
        },
        (err) => {
          console.error(err);
          setLoading(false);
        }
      );
  }

  return (
    <Grid
      container
      style={{ width: '100%', padding: '1rem' }}
      direction="column"
    >
      <Grid item>
        <Typography variant="h6">Existing tickets</Typography>
        <Typography variant="body2" gutterBottom>
          Below are all of the existing tickets that belong to this project and
          have the same code. These tickets may not belong to this checklist
          item if the ticket is for a different device. Only link the tickets to
          this checklist item if there is no device or the device matches.
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <b>Ticket #</b>
              </TableCell>
              <TableCell>
                <b>Title</b>
              </TableCell>
              <TableCell>
                <b>Device Name</b>
              </TableCell>
              <TableCell>
                <b>Start Date</b>
              </TableCell>
              <TableCell>
                <b>Link</b>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {!ticketData || loading ? (
              <CircularProgress />
            ) : ticketData.potentialOMTickets?.length > 0 ? (
              ticketData.potentialOMTickets.map((potentialOMTicket) => (
                <TableRow>
                  <TableCell>{potentialOMTicket.ticketNumber}</TableCell>
                  <TableCell>{potentialOMTicket.title}</TableCell>
                  <TableCell>{potentialOMTicket.deviceName}</TableCell>
                  <TableCell>{potentialOMTicket.startDt}</TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={!!potentialOMTicket.omPmpChecklistItemId}
                      style={{
                        textTransform: 'none',
                      }}
                      onClick={() =>
                        linkOMTicketToChecklistItem(
                          potentialOMTicket.id,
                          data.id
                        )
                      }
                    >
                      {!!potentialOMTicket.omPmpChecklistItemId
                        ? 'Linked'
                        : 'Link ticket'}
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5}>
                  <i>No existing tickets with a matching code and project</i>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Grid>
    </Grid>
  );
};

export const OMPmpEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();
  const [matchPotentialTicketsOpenId, setMatchPotentialTicketsOpenId] =
    useState(null);

  const roleNames = permissions?.roles?.map((r) => r.name) || [];
  const isThirdParty =
    roleNames.includes('OMPartnerI9') ||
    roleNames.includes('OMPartnerRun') ||
    roleNames.includes('OMPartnerSolRen');

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <>
          <Grid container style={{ width: '100%' }}>
            <FunctionField
              label="PMP"
              render={(record) => {
                const weeks = [];
                const periodicities = [];
                let oldestScheduleItemDt = moment().utc().startOf('day');
                let mostFutureScheduleItemDt = moment().add(1, 'month');
                record.omPmpChecklistItems?.forEach((cli) => {
                  if (
                    !periodicities.find((p) => p.id === cli.omPmpPeriodicity.id)
                  ) {
                    periodicities.push(cli.omPmpPeriodicity);
                  }
                  // TODO: Use this periodicities list to show legend
                  cli.schedule.forEach((scheduleItem) => {
                    if (
                      moment(scheduleItem.scheduledDt).isBefore(
                        oldestScheduleItemDt
                      )
                    ) {
                      oldestScheduleItemDt = moment(
                        scheduleItem.scheduledDt
                      ).startOf('day');
                    }
                    if (
                      moment(scheduleItem.scheduledDt).isAfter(
                        mostFutureScheduleItemDt
                      )
                    ) {
                      mostFutureScheduleItemDt = moment(
                        scheduleItem.scheduledDt
                      ).startOf('day');
                    }
                  });
                });
                const thisWeek = moment().startOf('day').isoWeekday(1);
                const currentDt = moment(oldestScheduleItemDt).isoWeekday(1);
                while (currentDt.isSameOrBefore(mostFutureScheduleItemDt)) {
                  weeks.push({
                    weekStartDt: moment(currentDt),
                    weekEndDt: moment(currentDt).add(1, 'week'),
                    monthStr: moment(currentDt).format('MMM YYYY'),
                  });
                  currentDt.add(1, 'week');
                }

                const getStickyStyle = (left) => ({
                  position: 'sticky',
                  left: left,
                  background: '#fff',
                  zIndex: 3,
                  whiteSpace: 'nowrap',
                  fontWeight: 'bold',
                });

                const getWeekCellStyling = (weekStartDt) => {
                  if (weekStartDt.isBefore(thisWeek)) {
                    return {
                      backgroundColor: 'lightyellow',
                    };
                  }
                  if (weekStartDt.isSame(thisWeek)) {
                    return {
                      backgroundColor: 'yellow',
                    };
                  }
                  return {};
                };

                const headerCellStyle = {
                  backgroundColor: '#f5f5f5',
                };

                const weekFormat = 'D/M/YY';
                const labelLeftPlacement = 0;
                const kpisLeftPlacement = labelLeftPlacement + 675;
                const startDtLeftPlacement = kpisLeftPlacement + 150;
                const periodicityLeftPlacement = startDtLeftPlacement + 170;

                const getDescriptionValue = (cli) => cli.label;
                const getRealizacaoValue = (cli) =>
                  `(${cli.currentPeriodCompletedTicketCount} / ${cli.currentPeriodExpectedTicketCount})`;
                const getPeriodicityValue = (cli) => cli.omPmpPeriodicity.name;
                const getStartDtValue = (cli) =>
                  moment(cli.startDt).format(weekFormat);

                const getScheduleItemForWeek = (cli, week) =>
                  cli.schedule.filter((scheduleItem) =>
                    moment(scheduleItem.scheduledDt).isBetween(
                      week.weekStartDt,
                      week.weekEndDt,
                      null,
                      '[)' // includes starting date but not end
                    )
                  )?.[0];

                const exportPMPToExcel = () => {
                  const wb = new xl.Workbook();
                  const ws = wb.addWorksheet();

                  const filename = `${record.label} PMP`;
                  const title = filename;
                  writeToCell({
                    ws,
                    rowIndex: 1,
                    columnIndex: 1,
                    val: title,
                    style: { bold: true, fontSize: 20 },
                  });

                  const headerRowIndex = 3;
                  writeToCell({
                    ws,
                    rowIndex: headerRowIndex,
                    columnIndex: 1,
                    val: 'Descrição',
                    style: { bold: true },
                  });
                  writeToCell({
                    ws,
                    rowIndex: headerRowIndex,
                    columnIndex: 2,
                    val: 'Realização',
                    style: { bold: true },
                  });
                  writeToCell({
                    ws,
                    rowIndex: headerRowIndex,
                    columnIndex: 3,
                    val: 'Data Inicio planejada',
                    style: { bold: true },
                  });
                  writeToCell({
                    ws,
                    rowIndex: headerRowIndex,
                    columnIndex: 4,
                    val: 'Periodicidade',
                    style: { bold: true },
                  });
                  weeks.forEach((w, index) => {
                    writeToCell({
                      ws,
                      rowIndex: headerRowIndex,
                      columnIndex: 5 + index,
                      val: w.weekStartDt.toDate(),
                      style: { bold: true },
                    });
                  });

                  const bodyTopRowIndex = headerRowIndex + 1;
                  record.omPmpChecklistItems.map((cli, cliIndex) => {
                    writeToCell({
                      ws,
                      rowIndex: bodyTopRowIndex + cliIndex,
                      columnIndex: 1,
                      val: getDescriptionValue(cli),
                      style: { bold: true },
                    });
                    writeToCell({
                      ws,
                      rowIndex: bodyTopRowIndex + cliIndex,
                      columnIndex: 2,
                      val: getRealizacaoValue(cli),
                      style: { bold: true },
                    });
                    writeToCell({
                      ws,
                      rowIndex: bodyTopRowIndex + cliIndex,
                      columnIndex: 3,
                      val: moment(getStartDtValue(cli), weekFormat).toDate(),
                      style: { bold: true },
                    });
                    writeToCell({
                      ws,
                      rowIndex: bodyTopRowIndex + cliIndex,
                      columnIndex: 4,
                      val: getPeriodicityValue(cli),
                      style: { bold: true },
                    });
                    weeks.forEach((week, weekIndex) => {
                      const scheduleItemForWeek = getScheduleItemForWeek(
                        cli,
                        week
                      );
                      if (!scheduleItemForWeek) {
                        // Do nothing
                      } else if (scheduleItemForWeek.completedDt) {
                        writeToCell({
                          ws,
                          rowIndex: bodyTopRowIndex + cliIndex,
                          columnIndex: weekIndex + 5,
                          val: '✓',
                          style: {
                            fillColor: theme.palette.success.main.replace(
                              /^#/,
                              ''
                            ),
                          },
                        });
                      } else if (
                        !scheduleItemForWeek.completedDt &&
                        week.weekStartDt.isBefore(thisWeek)
                      ) {
                        writeToCell({
                          ws,
                          rowIndex: bodyTopRowIndex + cliIndex,
                          columnIndex: weekIndex + 5,
                          val: '!',
                          style: {
                            fillColor: theme.palette.error.main.replace(
                              /^#/,
                              ''
                            ),
                          },
                        });
                      } else {
                        writeToCell({
                          ws,
                          rowIndex: bodyTopRowIndex + cliIndex,
                          columnIndex: weekIndex + 5,
                          val: '',
                          style: { fillColor: cli.omPmpPeriodicity.color },
                        });
                      }
                    });
                  });

                  downloadWorkbook(wb, `${filename}.xlsx`.normalize());
                };

                return (
                  <Grid container direction="column">
                    <Grid item style={{ marginBottom: '1rem' }}>
                      <Typography variant="h4">{record.label}</Typography>
                      <Typography variant="h6">
                        Preventative Maintenance Plan
                      </Typography>
                      <Button
                        style={{ textTransform: 'none' }}
                        onClick={() => {
                          exportPMPToExcel();
                        }}
                        startIcon={<CloudDownload />}
                      >
                        Download as Excel
                      </Button>
                    </Grid>
                    <Grid item style={{ marginBottom: '1rem' }}>
                      <Typography variant="h5">
                        {`Realização: ${numeral(
                          record.currentPeriodCompletedTicketCount /
                            (record.currentPeriodExpectedTicketCount || 1) // avoid divide by 0
                        ).format('0[.]0%')} (${
                          record.currentPeriodCompletedTicketCount
                        }/${record.currentPeriodExpectedTicketCount})`}
                      </Typography>
                    </Grid>
                    <Grid item style={{ marginBottom: '1rem' }}>
                      <Typography variant="h5">
                        {`Backlog: ${numeral(
                          (record.currentPeriodTicketCount -
                            record.currentPeriodCompletedTicketCount) /
                            (record.currentPeriodTicketCount || 1) // avoid divide by 0
                        ).format('0[.]0%')} (${
                          record.currentPeriodTicketCount -
                          record.currentPeriodCompletedTicketCount
                        }/${record.currentPeriodTicketCount})`}
                      </Typography>
                    </Grid>
                    <Grid item>
                      {periodicities.map((p) => (
                        <Chip
                          label={
                            <Typography
                              variant="body2"
                              style={{
                                color: '#fff',
                              }}
                            >
                              {p.name} - {p.description}
                            </Typography>
                          }
                          variant="contained"
                          style={{
                            margin: '0.25em',
                            backgroundColor: p.color,
                            padding: '4px',
                          }}
                        />
                      ))}
                    </Grid>
                    <Grid item>
                      <TableContainer
                        component={Paper}
                        style={{ maxWidth: '2500px', overflowX: 'auto' }}
                        elevation={0}
                      >
                        <Table stickyHeader size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell
                                colSpan={4}
                                style={{
                                  ...getStickyStyle(labelLeftPlacement),
                                  ...headerCellStyle,
                                  borderRight: '2px solid lightgray',
                                }}
                              />
                              {weeks.map((w, index) => {
                                if (
                                  index === 0 ||
                                  weeks[index - 1].monthStr !== w.monthStr
                                ) {
                                  return (
                                    <TableCell style={{ ...headerCellStyle }}>
                                      <b>{w.monthStr}</b>
                                    </TableCell>
                                  );
                                }
                                return (
                                  <TableCell style={{ ...headerCellStyle }} />
                                );
                              })}
                            </TableRow>
                          </TableHead>
                          <TableHead>
                            <TableRow>
                              <TableCell
                                style={{
                                  ...getStickyStyle(labelLeftPlacement),
                                  ...headerCellStyle,
                                  minWidth: kpisLeftPlacement,
                                }}
                                align="left"
                              >
                                <b>Descrição</b>
                              </TableCell>
                              <TableCell
                                style={{
                                  ...getStickyStyle(kpisLeftPlacement),
                                  ...headerCellStyle,
                                  minWidth:
                                    startDtLeftPlacement - kpisLeftPlacement,
                                }}
                                align="right"
                              >
                                <b>Realização</b>
                              </TableCell>
                              <TableCell
                                style={{
                                  ...getStickyStyle(startDtLeftPlacement),
                                  ...headerCellStyle,
                                  minWidth:
                                    periodicityLeftPlacement -
                                    startDtLeftPlacement,
                                }}
                                align="right"
                              >
                                <b>Data Inicio planejada</b>
                              </TableCell>
                              <TableCell
                                style={{
                                  ...getStickyStyle(periodicityLeftPlacement),
                                  ...headerCellStyle,
                                  minWidth: 100,
                                  borderRight: '2px solid lightgray',
                                }}
                                align="right"
                              >
                                <b>Periodicidade</b>
                              </TableCell>
                              {weeks.map((w) => {
                                return (
                                  <TableCell
                                    key={`pmp-header-week-${w.weekStartDt.format(
                                      weekFormat
                                    )}`}
                                    style={{
                                      minWidth: '50px',
                                      ...headerCellStyle,
                                    }}
                                    align="center"
                                  >
                                    <b>{w.weekStartDt.format('D/M')}</b>
                                  </TableCell>
                                );
                              })}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {record.omPmpChecklistItems?.map((cli) => {
                              return (
                                <>
                                  <TableRow
                                    key={`pmp-checklist-item-${cli.id}`}
                                    hover={!isThirdParty}
                                    style={{ cursor: 'pointer' }}
                                    component={Link}
                                    to={
                                      isThirdParty
                                        ? ''
                                        : `/OMPmpChecklistItem/${cli.id}`
                                    }
                                  >
                                    <TableCell
                                      style={{
                                        ...getStickyStyle(labelLeftPlacement),
                                        ...headerCellStyle,
                                        minWidth: kpisLeftPlacement,
                                      }}
                                      align="left"
                                    >
                                      {isThirdParty ? null : (
                                        <IconButton
                                          size="small"
                                          onClick={(event) => {
                                            event.stopPropagation();
                                            event.preventDefault();
                                            setMatchPotentialTicketsOpenId(
                                              matchPotentialTicketsOpenId ===
                                                cli.id
                                                ? null
                                                : cli.id
                                            );
                                          }}
                                        >
                                          {cli.id ===
                                          matchPotentialTicketsOpenId ? (
                                            <KeyboardArrowUp />
                                          ) : (
                                            <KeyboardArrowDown />
                                          )}
                                        </IconButton>
                                      )}
                                      {getDescriptionValue(cli)}
                                    </TableCell>
                                    <TableCell
                                      style={{
                                        ...getStickyStyle(kpisLeftPlacement),
                                        ...headerCellStyle,
                                        minWidth:
                                          startDtLeftPlacement -
                                          kpisLeftPlacement,
                                      }}
                                      align="right"
                                    >
                                      {getRealizacaoValue(cli)}
                                    </TableCell>
                                    <TableCell
                                      style={{
                                        ...getStickyStyle(startDtLeftPlacement),
                                        ...headerCellStyle,
                                        minWidth:
                                          periodicityLeftPlacement -
                                          startDtLeftPlacement,
                                      }}
                                      align="right"
                                    >
                                      {getStartDtValue(cli)}
                                    </TableCell>
                                    <TableCell
                                      style={{
                                        ...getStickyStyle(
                                          periodicityLeftPlacement
                                        ),
                                        ...headerCellStyle,
                                        minWidth: 100,
                                        borderRight: '2px solid lightgray',
                                      }}
                                      align="right"
                                    >
                                      {getPeriodicityValue(cli)}
                                    </TableCell>
                                    {weeks.map((week) => {
                                      const scheduleItemForWeek =
                                        getScheduleItemForWeek(cli, week);
                                      const jsx = [];
                                      if (!scheduleItemForWeek) {
                                        // Do nothing
                                      } else if (
                                        scheduleItemForWeek.completedDt
                                      ) {
                                        jsx.push(
                                          <Chip
                                            label={
                                              <i class="fa-solid fa-check" />
                                            }
                                            variant="outlined"
                                            style={{
                                              color: theme.palette.success.main,
                                              borderColor:
                                                theme.palette.success.main,
                                              borderWidth: '2px',
                                              width: '100%',
                                            }}
                                          />
                                        );
                                      } else if (
                                        !scheduleItemForWeek.completedDt &&
                                        week.weekStartDt.isBefore(thisWeek)
                                      ) {
                                        jsx.push(
                                          <Chip
                                            label={
                                              <i class="fa-solid fa-exclamation" />
                                            }
                                            variant="outlined"
                                            style={{
                                              color: theme.palette.error.main,
                                              borderColor:
                                                theme.palette.error.main,
                                              borderWidth: '2px',
                                              width: '100%',
                                            }}
                                          />
                                        );
                                      } else {
                                        jsx.push(
                                          <Chip
                                            style={{
                                              backgroundColor:
                                                cli.omPmpPeriodicity.color,
                                              width: '100%',
                                            }}
                                          />
                                        );
                                      }
                                      return (
                                        <TableCell
                                          key={`pmp-checklist-item-${
                                            cli.id
                                          }-week-${week.weekStartDt.format(
                                            weekFormat
                                          )}`}
                                          style={{
                                            padding: '4px',
                                            ...getWeekCellStyling(
                                              week.weekStartDt
                                            ),
                                          }}
                                          align="center"
                                        >
                                          {jsx}
                                        </TableCell>
                                      );
                                    })}
                                  </TableRow>
                                  <Collapse
                                    in={matchPotentialTicketsOpenId === cli.id}
                                    style={{ width: '100%' }}
                                  >
                                    {matchPotentialTicketsOpenId === cli.id && (
                                      <OMPmpChecklistItemTicketLink
                                        omPmpChecklistItemId={cli.id}
                                      />
                                    )}
                                  </Collapse>
                                </>
                              );
                            })}
                            {isThirdParty ? null : (
                              <TableRow>
                                <TableCell
                                  colSpan={4}
                                  style={{
                                    ...getStickyStyle(0),
                                    ...headerCellStyle,
                                    borderRight: '2px solid lightgray',
                                  }}
                                >
                                  <Button
                                    startIcon={<AddCircle />}
                                    component={Link}
                                    to={`/OMPmpChecklistItem/create`}
                                    variant="outlined"
                                    color="primary"
                                    style={{ textTransform: 'none' }}
                                  >
                                    Add new checklist item
                                  </Button>
                                </TableCell>
                                <TableCell
                                  colSpan={weeks.length}
                                  style={{ ...headerCellStyle }}
                                />
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
          {isThirdParty ? null : (
            <>
              <Divider style={{ width: '100%', margin: '3rem 1rem' }} />
              <Grid container style={{ width: '100%' }}>
                <Grid item xs={12}>
                  <Typography variant="h6">Edit PMP details:</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <ReferenceInput
                    source="project.id"
                    reference="Project"
                    perPage={10_000}
                    sort={{ field: 'id', order: 'ASC' }}
                    // label="Project"
                    required
                  >
                    <AutocompleteInput
                      optionText="name"
                      label="Project"
                      fullWidth
                      required
                    />
                  </ReferenceInput>
                  <DateInput source="startDt" fullWidth />
                  <DateInput source="endDt" fullWidth />
                </Grid>
              </Grid>
            </>
          )}
        </>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Project"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="O&M Truck"
      source="omTruck.id"
      reference="OMTruck"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <SelectInput label="O&M Truck" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const OMPmpList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<CustomFilter />}
      exporter={false}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
        bulkActionButtons={false}
      >
        <FunctionField
          label="Preventative maintenance plan"
          render={(record) => <Typography>{record.label}</Typography>}
        />
        <NumberField
          source="omPmpChecklistItemCount"
          label="Number of checklist items"
          sortable={false}
        />
        <FunctionField
          label="Realização"
          render={(record) => (
            <Typography>{`${numeral(
              record.currentPeriodCompletedTicketCount /
                (record.currentPeriodExpectedTicketCount || 1) // avoid divide by 0
            ).format('0[.]0%')} (${record.currentPeriodCompletedTicketCount}/${
              record.currentPeriodExpectedTicketCount
            })`}</Typography>
          )}
        />
        <FunctionField
          label="Backlog"
          render={(record) => (
            <Typography>{`${numeral(
              (record.currentPeriodTicketCount -
                record.currentPeriodCompletedTicketCount) /
                (record.currentPeriodTicketCount || 1) // avoid divide by 0
            ).format('0[.]0%')} (${
              record.currentPeriodTicketCount -
              record.currentPeriodCompletedTicketCount
            }/${record.currentPeriodTicketCount})`}</Typography>
          )}
        />
        <FunctionField
          label="Schedule"
          render={(record) => (
            <Button
              endIcon={<ArrowForward />}
              component={Link}
              to={`/OMPmp/${record.id}`}
              variant="contained"
              color="primary"
              style={{ textTransform: 'none' }}
            >
              View PMP Schedule
            </Button>
          )}
        />
      </Datagrid>
    </List>
  );
};

export const OMPmpCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <AutocompleteInput
              optionText="name"
              label="Project"
              fullWidth
              required
            />
          </ReferenceInput>
          <DateInput source="startDt" fullWidth />
          <DateInput source="endDt" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
