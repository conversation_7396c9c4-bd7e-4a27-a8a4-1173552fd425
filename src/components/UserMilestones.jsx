import React from 'react';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>grid,
  Edit,
  FunctionField,
  ImageField,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';
import { useParams } from 'react-router-dom';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'User Milestone';

export const UserMilestoneEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextField source="id" />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput fullWidth source="title" />
            <TextInput fullWidth source="description" />
            <TextInput fullWidth source="iconClass" />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const User<PERSON>ilestoneList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="title" />
        <TextField source="description" />
        <FunctionField
          label="Icon"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <span>
                <i
                  style={{ marginRight: '6px' }}
                  className={record.iconClass}
                />
                {record.iconClass}
              </span>
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const UserMilestoneCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="title" fullWidth required />
          <TextInput source="description" fullWidth required />
          <TextInput source="iconClass" fullWidth required />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
