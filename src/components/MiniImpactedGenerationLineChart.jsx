import React from 'react';
import moment from 'moment-timezone';

import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-moment';
import 'chart.js/auto';

import theme from '../theme';

const MiniImpactedGenerationLineChart = (props) => {
  let width, height, gradient;
  function getGradient(ctx, chartArea) {
    const chartWidth = chartArea.right - chartArea.left;
    const chartHeight = chartArea.bottom - chartArea.top;
    if (!gradient || width !== chartWidth || height !== chartHeight) {
      // Create the gradient because this is either the first render
      // or the size of the chart has changed
      width = chartWidth;
      height = chartHeight;
      gradient = ctx.createLinearGradient(
        0,
        chartArea.top,
        0,
        chartArea.bottom
      );
      gradient.addColorStop(0, theme.palette.green.dark);
      gradient.addColorStop(0.08, theme.palette.green.dark);
      gradient.addColorStop(0.17, theme.palette.warning.main);
      gradient.addColorStop(0.23, theme.palette.warning.main);
      gradient.addColorStop(0.32, theme.palette.orange.main);
      gradient.addColorStop(0.38, theme.palette.orange.main);
      gradient.addColorStop(0.47, theme.palette.error.main);
      gradient.addColorStop(1, theme.palette.error.main);
    }

    return gradient;
  }

  const { truck } = props;

  const datasets = [
    {
      label: 'fully functioning',
      data: [
        { date: moment().add(-30, 'days').format('MM-DD-YYYY'), value: 0 },
        {
          date: moment().add(-1, 'day').format('MM-DD-YYYY'), // Remove today since it may not have generation yet or will be incomplete
          value: 0,
        },
      ],
      pointRadius: 0,
    },
  ];
  const actualData = [];
  if (
    truck.last30DayPercentGenerationImpactedByOpenOMTickets &&
    truck.last30DayPercentGenerationImpactedByOpenOMTickets.length > 0
  ) {
    // Remove today since it may not have generation yet or will be incomplete
    const filteredProductionData =
      truck.last30DayPercentGenerationImpactedByOpenOMTickets.filter((day) =>
        moment(day.date, 'MM-DD-YYYY')
          .startOf('day')
          .isBefore(moment().startOf('day'))
      );
    actualData.push(...filteredProductionData);
  } else {
    actualData.push({
      date: moment().add(-1, 'day').format('MM-DD-YYYY'),
      value: 0,
    });
  }
  const filterLib = [];
  const lintedActualData = actualData.filter((el) => {
    if (filterLib.indexOf(el.date) > -1) {
      return false;
    }
    filterLib.push(el.date);
    return true;
  });
  datasets.push({
    label: 'actual impacted',
    data: lintedActualData,
    pointRadius: 0,
    borderColor: function (context) {
      const chart = context.chart;
      const { ctx, chartArea } = chart;

      if (!chartArea) {
        // This case happens on initial chart load
        return;
      }
      return getGradient(ctx, chartArea);
    },
  });

  return (
    <Line
      height="102"
      data={{
        datasets: datasets,
      }}
      options={{
        layout: {
          padding: {
            top: 1.5,
            bottom: 1.5,
            left: 1.5,
            right: 1.5,
          },
        },
        maintainAspectRatio: false,
        parsing: {
          xAxisKey: 'date',
          yAxisKey: 'value',
        },
        plugins: {
          legend: { display: false },
          tooltip: { enabled: false },
        },
        scales: {
          x: {
            type: 'time',
            grid: {
              display: false,
            },
            display: false,
          },
          y: {
            beginAtZero: true,
            grid: {
              display: false,
            },
            reverse: true,
            max: 1, // This is important because without a full 0 -> 1 spread, the gradient will not properly align to values but rather to percent of the way down the chart. This also is important to provide context when there is no y-axis label
            display: false,
          },
        },
      }}
    />
  );
};

export default MiniImpactedGenerationLineChart;
