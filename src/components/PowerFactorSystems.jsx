import React, { useState } from 'react';
import moment from 'moment';
import numeral from 'numeral';

import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FormDataConsumer,
  FormTab,
  List,
  NumberField,
  NumberInput,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import {
  Button,
  Checkbox,
  CircularProgress,
  Divider,
  FormControlLabel,
  FormHelperText,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { CheckCircle, Error, GetApp } from '@mui/icons-material';
import { Alert } from '@mui/lab';

import { getEditable } from '../utils/applyRoleAuth';
import ExcelReader from './ExcelReader';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Power Factor System';

const attrs = [
  {
    name: 'periodStartDt',
    format: (val) => val,
    label: 'Time',
    align: 'left',
  },
  {
    name: 'production',
    format: (val) => (val ? numeral(val).format('0,0') : ''),
    label: 'Production (kWh)',
    align: 'left',
  },
  {
    name: 'unit',
    format: (val) => val,
    label: 'Unit',
    align: 'right',
  },
  {
    name: 'timeUnit',
    format: (val) => val,
    label: 'Time Unit',
    align: 'right',
  },
];

export const PowerFactorSystemEdit = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [reviewed, setReviewed] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const { id } = useParams();

  const handleData = (data) => {
    const lintedData = data
      .filter((point) => Object.keys(point).length === 2)
      .map((point) => {
        const keys = Object.keys(point);
        const periodStartDt = moment
          .utc(point.Time)
          .format('YYYY-MM-DD HH:mm:ss');
        const production = keys.length > 1 ? point[keys[1]] : 0;
        return {
          powerFactorSystemId: parseInt(id, 10),
          periodStartDt,
          production,
          unit: 'Wh',
          timeUnit: 'DAY',
        };
      });
    setData(lintedData);
  };

  const save = () => {
    setLoading(true);
    dataProvider
      .create('UploadProductionPeriod', {
        data: {
          powerFactorSystemId: parseInt(id, 10),
          data,
        },
      })
      .catch((e) => {
        setLoading(false);
        notify(
          `Error uploading PowerFactor production data. Error: ${e}`,
          'error'
        );
        refresh();
      })
      .then(() => {
        setLoading(false);
        notify('PowerFactor Production data uploaded.');
        refresh();
      });
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell align={attr.align || 'center'}>
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  const renderSubmit = () => {
    return (
      <Alert severity={!reviewed ? 'warning' : 'success'}>
        <FormControlLabel
          control={
            <Checkbox
              checked={!!reviewed}
              onChange={() => setReviewed(!reviewed)}
            />
          }
          label="I have checked and the list looks good (this will override the existing records!)"
        />
        <Button
          onClick={save}
          disabled={!reviewed || loading}
          variant="contained"
          size="large"
          color="secondary"
        >
          {loading ? <CircularProgress /> : 'Save'}
        </Button>
      </Alert>
    );
  };

  const backfillHistoryFromPowerFactor = (startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ProductionPeriod', {
        input: {
          backfillFromAPI: true,
          powerFactorSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('PowerFactor site production history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling production history', { type: 'error' });
      });
  };

  const backfillSiteExpectedGeneration = (formData) => {
    const {
      backfillExpectedStartDt,
      backfillExpectedEndDt,
      project: { id: projectId },
    } = formData;
    if (backfillExpectedStartDt > backfillExpectedEndDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ExpectedProductionPeriod', {
        input: {
          backfillFromAPI: true,
          projectId: parseInt(projectId, 10),
          startDt: backfillExpectedStartDt,
          endDt: backfillExpectedEndDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('Project expected generation successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error saving project expected generation', { type: 'error' });
      });
  };

  const backfillSiteSensorHistory = (sensorId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('SensorDataPeriod', {
        input: {
          backfillFromAPI: true,
          sensorId,
          powerFactorSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('PowerFactor site sensor history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling site sensor history', { type: 'error' });
      });
  };

  const backfillInverterPower = (inverterId, startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('InverterProductionPeriod', {
        input: {
          backfillFromAPI: true,
          inverterId,
          powerFactorSystemId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('AMMP inverter power history backfill initiated');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error initiating inverter power history backfill', 'error');
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm redirect={false}>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="name" fullWidth />
              <TextInput source="systemId" fullWidth />
              <ReferenceInput
                source="project.id"
                reference="Project"
                sort={{ field: 'name', order: 'ASC' }}
                perPage={10000}
              >
                <SelectInput
                  required
                  label="Project"
                  fullWidth
                  helperText="Select the project associated with this PowerFactor site."
                  optionText="name"
                />
              </ReferenceInput>
              <BooleanInput source="isLivePollingEnabled" fullWidth />
              <TextInput source="timezone" fullWidth />
              <CustomNumberInput
                source="noCommsLimitMins"
                fullWidth
                label="No Comms Alert Limit (minutes)"
                helperText="If you want to disable the 'No Comms' alert for this site, set to null."
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Backfill">
          <Grid item xs={12}>
            <Typography variant="h5" gutterBottom>
              Backfill Expected Generation Data
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <TextInput
              label="From"
              source="backfillExpectedStartDt"
              type="datetime-local"
              helperText="Enter date in the timezone of the site"
              style={{ marginRight: '2rem' }}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <TextInput
              label="To"
              source="backfillExpectedEndDt"
              type="datetime-local"
              helperText="Enter date in the timezone of the site"
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <FormDataConsumer>
              {({ formData, ...rest }) => (
                <>
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={
                      loading ||
                      !(
                        formData.backfillExpectedStartDt &&
                        formData.backfillExpectedEndDt
                      )
                    }
                    onClick={() => {
                      setLoading(true);
                      backfillSiteExpectedGeneration(formData);
                    }}
                  >
                    {loading ? (
                      <CircularProgress />
                    ) : (
                      'Backfill Expected Generation Data'
                    )}
                  </Button>
                  <FormHelperText style={{ marginLeft: '14px' }}>
                    Irradiance, inverter power, and module temperature backfills
                    need to be complete before expected generation backfills can
                    be calculated.
                  </FormHelperText>
                </>
              )}
            </FormDataConsumer>
          </Grid>
          <Divider style={{ width: '100%', margin: '2em 0' }} />

          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="backfillStartDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="backfillEndDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(formData.backfillStartDt && formData.backfillEndDt)
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillHistoryFromPowerFactor(
                          formData.backfillStartDt,
                          formData.backfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Historical Data'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Site Sensor Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="siteBackfillSensorStartDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="siteBackfillSensorEndDt"
                type="datetime-local"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="sensor.id"
                reference="Sensor"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ powerFactorSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Sensor"
                  helperText="Select the sensor you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.siteBackfillSensorStartDt &&
                          formData.siteBackfillSensorEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillSiteSensorHistory(
                          formData.sensor && formData.sensor.id,
                          formData.siteBackfillSensorStartDt,
                          formData.siteBackfillSensorEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Sensor History'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Divider style={{ width: '100%', margin: '2em 0' }} />
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Inverter Power Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="inverterBackfillStartDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="inverterBackfillEndDt"
                type="datetime-local"
                helperText="Enter time in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <ReferenceInput
                source="inverter.id"
                reference="Inverter"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
                filter={{ powerFactorSystemId: parseInt(id, 10) }}
              >
                <SelectInput
                  label="Inverter"
                  helperText="Select the inverter you want to backfill. If none are selected, all will be backfilled"
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.inverterBackfillStartDt &&
                          formData.inverterBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillInverterPower(
                          formData.inverter && formData.inverter.id,
                          formData.inverterBackfillStartDt,
                          formData.inverterBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Inverter History'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Production Upload">
          <Grid container style={{ minWidth: '450px' }}>
            <Typography gutterBottom>
              To upload data from CSV,{' '}
              <a href="/ProductionPeriod/create">click here</a>.
            </Typography>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Typography>or</Typography>
            </Grid>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Alert severity="info">
                Make sure the excel document you are uploading matches the
                (date, value, column, etc.) formatting of the downloadable excel
                below.
              </Alert>
            </Grid>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Button
                component="a"
                variant="contained"
                href="/csv-templates/PowerFactorProductionData.xlsx"
                download
              >
                <GetApp />
                Click to download the csv template
              </Button>
            </Grid>
            <ExcelReader handleData={handleData} />
            <Grid item style={{ margin: 'auto' }}>
              {data ? renderSubmit() : null}
            </Grid>
            <Grid item xs={12}>
              {data ? renderData() : null}
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

export const PowerFactorSystemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="name" />
        <TextField source="systemId" />
        <BooleanField source="isLivePollingEnabled" />
        <TextField source="timezone" />
        <NumberField source="noCommsLimitMins" />
      </Datagrid>
    </List>
  );
};

export const PowerFactorSystemCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="systemId"
            label="System ID (Ex: '1032.CA.10003')"
            required
            fullWidth
            helperText="Query fetchAllSystemsFromPowerFactors at localhost:5000/graphql and check console logs for ids of all projects"
          />
          <ReferenceInput
            source="project.id"
            reference="Project"
            sort={{ field: 'name', order: 'ASC' }}
            perPage={10000}
          >
            <SelectInput
              required
              label="Project"
              fullWidth
              helperText="Select the project associated with this PowerFactor system."
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
