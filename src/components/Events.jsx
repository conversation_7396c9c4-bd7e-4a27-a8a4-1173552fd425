import React, { useState } from 'react';
import theme from '../theme';
import { withStyles } from '@mui/styles';
import { useParams } from 'react-router-dom';
import { Image, Video, Transformation } from 'cloudinary-react';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  DateTimeInput,
  Edit,
  FormTab,
  FunctionField,
  List,
  ListButton,
  required,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  Toolbar,
  TopToolbar,
  useRefresh,
  useNotify,
  useDataProvider,
  UrlField,
  usePermissions,
  useRecordContext,
  FormDataConsumer,
  Filter,
  useResourceDefinition,
} from 'react-admin';
import {
  Avatar,
  Badge,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  // DialogContentText,
  DialogTitle,
  Divider,
  Fab,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItem,
  ListItemSecondaryAction,
  ListItemAvatar,
  ListItemText,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from '@mui/material';

import { Alert, AvatarGroup } from '@mui/lab';
import MaterialTextField from '@mui/material/TextField';
import MaterialList from '@mui/material/List';
import {
  Add,
  // Check,
  ChevronLeft,
  Delete,
  Done,
  Close,
  Instagram,
  LinkedIn,
  Facebook,
  Help,
} from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import { RichTextInput } from 'ra-input-rich-text';

import { DetailField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import Config from '../config/config';
import { openUploadWidget } from '../utils/CloudinaryService';

const entityName = 'Event';

const CustomEditActions = ({ basePath }) => (
  <TopToolbar>
    <ListButton
      basePath={basePath}
      label="Back to List"
      icon={<ChevronLeft />}
    />
  </TopToolbar>
);

const getRequired = (attr, record) => {
  const {
    includeInQuarterlyReportFlg,
    includeInPortfolioTimelineFlg,
    includeInProjectTimelineFlg,
    includeInInvestorDashboardFlg,
    createNotificationsFlg,
    createEmailsFlg,
  } = record;
};

const EventToolbar = (props) => {
  const record = useRecordContext();
  const notify = useNotify();
  const refresh = useRefresh();
  // set the date by default to the eventDt
  let lintedStartDt = record.eventDt && new Date(record.eventDt);
  if (lintedStartDt) {
    // if date is before now, set it to 2 days from now
    if (lintedStartDt < new Date()) {
      lintedStartDt = new Date();
      lintedStartDt.setDate(lintedStartDt.getDate() + 2);
    }
    // Set the time of day to 10am
    lintedStartDt.setHours(10);
    lintedStartDt.setMinutes(0);
    lintedStartDt.setSeconds(0);
    lintedStartDt = lintedStartDt.toISOString().split('.')[0];
  }

  const dataProvider = useDataProvider();
  const [createSocialPostOpen, setCreateSocialPostOpen] = useState(false);
  const [scheduleDt, setScheduleDt] = useState(lintedStartDt || null);
  const [selectedChannel, setSelectedChannel] = useState(null);
  // const [socialChannels, setSocialChannels] = useState(null);
  const [selectedImageIds, setSelectedImageIds] = useState([]);
  const [selectedVideoIds, setSelectedVideoIds] = useState([]);
  const [selectedMediaCount, setSelectedMediaCount] = useState(0);
  const [postContent, setPostContent] = useState(record.summary);

  const createNotifications = () => {
    dataProvider
      .update('Event', {
        data: {
          id: parseInt(record.id, 10),
          createNotifications: true,
          isComplete: record.isComplete,
        },
      })
      .then(
        (resp) => {
          const numOfNotifications = resp.data.notificationsCreated;
          notify(`${numOfNotifications} notifications created`, {
            type: 'success',
          });
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
        }
      );
  };
  const createEmails = () => {
    dataProvider
      .update('Event', {
        data: {
          id: parseInt(record.id, 10),
          createEmails: true,
          isComplete: record.isComplete,
        },
      })
      .then(
        (resp) => {
          const numOfEmails = resp.data.emailsSent;
          notify(`${numOfEmails} emails created`, { type: 'success' });
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
          notify(e.message, { type: 'error' });
          refresh();
        }
      );
  };

  // if (!socialChannels) {
  //   dataProvider
  //     .getList('HubSpotSocialChannel', {
  //       sort: { field: 'id', order: 'DESC' },
  //       filter: {},
  //     })
  //     .then(
  //       (resp) => {
  //         setSocialChannels(resp.data);
  //       },
  //       (e) => {
  //         // eslint disable-next-line no-console
  //         console.error('Error retrieving social channels', e);
  //       }
  //     );
  // }

  const handleCreateSocialPost = () => {
    setCreateSocialPostOpen(true);
  };

  const renderMedia = () => {
    let jsx = [];
    if (!record || !record.eventImages) return null;
    record.eventImages.forEach((el) => {
      jsx.push(
        <Grid item style={{ position: 'relative' }}>
          <Image cloud_name={Config.cloud_name} publicId={el.public_id}>
            <Transformation width="200" crop="scale" />
          </Image>
          <Checkbox
            required
            color="secondary"
            style={{
              backgroundColor: 'rgba(255,255,255,.8)',
              position: 'absolute',
              left: 12,
              top: 12,
            }}
            checked={selectedImageIds.indexOf(el.id) > -1}
            onChange={(event) => {
              setSelectedVideoIds([]);
              let consolidatedList = selectedImageIds;
              if (selectedImageIds.indexOf(el.id) === -1) {
                // consolidatedList.push(el.id);
                consolidatedList = [el.id];
              } else {
                consolidatedList = consolidatedList.filter(
                  (item) => item !== el.id
                );
              }
              setSelectedImageIds(consolidatedList);
              setSelectedMediaCount(consolidatedList.length);
            }}
          />
        </Grid>
      );
    });
    record.eventVideos.forEach((el) => {
      jsx.push(
        <Grid item style={{ position: 'relative' }}>
          <Video
            style={{}}
            cloud_name={Config.cloud_name}
            publicId={el.public_id}
          >
            <Transformation width="200" crop="scale" />
          </Video>
          <Checkbox
            required
            color="secondary"
            style={{
              backgroundColor: 'rgba(255,255,255,.8)',
              position: 'absolute',
              left: 12,
              top: 12,
            }}
            checked={selectedVideoIds.indexOf(el.id) > -1}
            onChange={(event) => {
              let consolidatedList = selectedVideoIds;
              setSelectedImageIds([]);
              if (selectedVideoIds.indexOf(el.id) === -1) {
                // consolidatedList.push(el.id);
                consolidatedList = [el.id];
              } else {
                consolidatedList = consolidatedList.filter(
                  (item) => item !== el.id
                );
              }
              setSelectedVideoIds(consolidatedList);
              setSelectedMediaCount(consolidatedList.length);
            }}
          />
        </Grid>
      );
    });
    return jsx;
  };
  return (
    <>
      <Toolbar>
        <SaveButton />
        <Button
          startIcon={<Add />}
          variant="contained"
          color="primary"
          style={{ marginLeft: '1rem' }}
          onClick={createNotifications}
          disabled={!record.createNotificationsFlg || !record.isComplete}
        >
          Create Notifications
        </Button>

        <Button
          startIcon={<Add />}
          variant="contained"
          color="primary"
          style={{ marginLeft: '1rem' }}
          onClick={createEmails}
          disabled={!record.createEmailsFlg || !record.isComplete}
        >
          Create Emails
        </Button>
        {/* <Tooltip title="DEPRECATED">
          <Button
            style={{ marginLeft: '1rem' }}
            startIcon={<Add />}
            variant="contained"
            color="primary"
            disabled
            onClick={handleCreateSocialPost}
          >
            Create Social Post
          </Button>
        </Tooltip> */}
      </Toolbar>
      {/* <Dialog
        open={createSocialPostOpen}
        onClose={() => setCreateSocialPostOpen(false)}
      >
        <DialogTitle>Create Social Post</DialogTitle>
        <DialogContent>
          {socialChannels ? (
            <>
              <Typography variant="body1" gutterBottom>
                Choose the platform you'd like to post to :
              </Typography>
              <FormControl
                variant="outlined"
                fullWidth
                required
                style={{ marginBottom: '1rem' }}
              >
                <InputLabel id="select-label">Platform</InputLabel>
                <Select
                  variant="outlined"
                  fullWidth
                  labelWidth={70}
                  value={selectedChannel}
                  onChange={(event) => {
                    const val = event.target.value;
                    setSelectedChannel(val);
                  }}
                  style={{ width: '200px' }}
                >
                  {socialChannels.map((el) => (
                    <MenuItem value={el.channelGuid}>{el.channelSlug}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </>
          ) : (
            <CircularProgress />
          )}
          <Typography variant="body1" gutterBottom>
            Choose the date you'd like to post :
          </Typography>
          <FormControl
            variant="outlined"
            fullWidth
            required
            style={{ marginBottom: '1rem' }}
          >
            <DateTimeInput
              source="effectiveDt"
              fullWidth
              label="Post Date"
              required
              inputProps={{
                style: { paddingTop: '18.5px', paddingBottom: '18.5px' },
              }}
              variant="outlined"
              parse={(event) => {
                setScheduleDt(event);
              }}
              format={(src) => {
                return scheduleDt;
              }}
              helperText="This is the date the post will schedule to post."
            />
          </FormControl>
          <Typography variant="body1" gutterBottom>
            Choose the caption of your post :
          </Typography>
          <FormControl
            variant="outlined"
            fullWidth
            required
            style={{ marginBottom: '1rem' }}
          >
            <MaterialTextField
              label="Caption"
              variant="outlined"
              multiline
              fullWidth
              value={postContent || ''}
              onChange={(event) => setPostContent(event.target.value)}
            />
          </FormControl>
          <Grid container>
            <Grid item xs={12}>
              <Typography variant="body1" gutterBottom>
                Selected media ({selectedMediaCount}) :
              </Typography>
            </Grid>
            <Grid container spacing={2}>
              {renderMedia()}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setCreateSocialPostOpen(false)}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              dataProvider
                .create('HubSpotSocialMediaPost', {
                  data: {
                    eventId: record.id,
                    socialPostDt: scheduleDt,
                    hubSpotSocialChannelId: selectedChannel,
                    eventImageIds: selectedImageIds,
                    eventVideoIds: selectedVideoIds,
                    postContent: postContent,
                  },
                })
                .then(
                  () => {
                    setCreateSocialPostOpen(false);
                    refresh();
                  },
                  (e) => {
                    notify(`Error: ${e}`, { type: 'warning' });
                  }
                );
            }}
            disabled={
              !scheduleDt ||
              !selectedChannel ||
              (selectedImageIds.length === 0 &&
                selectedVideoIds.length === 0) ||
              !postContent ||
              postContent === ''
            }
            color="primary"
            autoFocus
          >
            Create
          </Button>
        </DialogActions>
      </Dialog> */}
    </>
  );
};

export const EventEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const record = useRecordContext();
  const recordContext = useRecordContext();
  const [testEmail, setTestEmail] = useState('');
  const onPhotosUploaded = (aPhotos) => {
    const photos = aPhotos.map((photo) => {
      return {
        eventId: parseInt(id, 10),
        public_id: photo.public_id,
      };
    });
    dataProvider
      .update('Event', {
        data: { id: parseInt(id, 10), addEventImages: photos },
      })
      .then(
        () => {
          notify(`Successfully add photo`);
          refresh();
        },
        (e) => {
          notify(`Error: ${e}`, { type: 'warning' });
        }
      );
  };
  const onVideosUploaded = (aVideos) => {
    const videos = aVideos.map((video) => {
      return { public_id: video.public_id };
    });
    dataProvider
      .update('Event', {
        data: { id: parseInt(id, 10), addEventVideos: videos },
      })
      .then(
        () => {
          notify(`Successfully add photo`);
          refresh();
        },
        (e) => {
          notify(`Error: ${e}`, { type: 'warning' });
        }
      );
  };
  const handleClick = () => {
    dataProvider
      .update('Event', {
        data: { id: parseInt(id, 10), testEmail, createTestEmail: true },
      })
      .then(
        () => {
          notify(`Successfully sent test email to ${testEmail}`);
          // refresh();
        },
        (e) => {
          notify(`Error: ${e}`, { type: 'warning' });
        }
      );
  };
  const generateSignature = (callback, props) => {
    return dataProvider
      .getOne('VideoSignature', {
        data: JSON.stringify(props),
      })
      .then(
        (resp) => {
          return callback(resp.data.signature);
        },
        (e) => {
          console.error(e);
        }
      );
  };
  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['events'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.event_image_upload_preset,
      clientAllowedFormats: ['jpg', 'jpeg', 'png'],
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      } else {
        console.error('HIT open upload widget error', error, resp);
      }
    });
  };

  const handleRemovePhoto = (resource) => {
    return () => {
      dataProvider
        .update('Event', {
          data: {
            id: parseInt(id, 10),
            deleteEventImage: resource.id,
          },
        })
        .then(
          () => {
            notify('Successfully deleted image');
            refresh();
          },
          (e) => {
            notify(`Error: ${e}`, { type: 'warning' });
          }
        );
    };
  };

  const handleRemoveVideo = (resource) => {
    return () => {
      dataProvider
        .update('Event', {
          data: {
            id: parseInt(id, 10),
            deleteEventVideo: resource.id,
          },
        })
        .then(
          () => {
            notify('Successfully deleted video');
            refresh();
          },
          (e) => {
            notify(`Error: ${e}`, { type: 'warning' });
          }
        );
    };
  };

  const handleMakePhotoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryEventImageId: resource.id,
      };
      dataProvider
        .update('Event', {
          data: updatedRecord,
        })
        .then(
          () => {
            notify(`Successfully flagged image as primary.`);
            refresh();
          },
          (e) => {
            notify(`Error: ${e}`, { type: 'warning' });
          }
        );
    };
  };

  const handleMakeVideoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryEventVideoId: resource.id,
      };
      dataProvider
        .update('Event', {
          data: updatedRecord,
        })
        .then(
          () => {
            notify(`Successfully flagged video as primary.`);
            refresh();
          },
          (e) => {
            notify(`Error: ${e}`, { type: 'warning' });
          }
        );
    };
  };
  const socialIconMapping = {
    instagram: <Instagram style={{ fontSize: '3rem' }} />,
    linkedin: <LinkedIn style={{ fontSize: '3rem' }} />,
    facebook: <Facebook style={{ fontSize: '3rem' }} />,
  };

  const getColorFromStatus = (status) => {
    if (status === 'DRAFT') {
      return theme.palette.yellow.main;
    }
    return '#666';
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.event_video_upload_preset,
      uploadSignature: generateSignature,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideosUploaded([resp.info]);
      } else {
        console.error(error);
      }
    });
  };
  return (
    <Edit
      title={`${entityName} #${id}`}
      actions={<CustomEditActions />}
      undoable={false}
      redirect="edit"
    >
      <TabbedForm toolbar={<EventToolbar />} redirect={false}>
        <FormTab label="Summary">
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom variant="h5">
                Content
              </Typography>
              <DateInput
                validate={required()}
                fullWidth
                source="eventDt"
                helperText="This will determine the event's place in project/portfolio/report timelines."
              />
              <TextInput
                validate={required()}
                fullWidth
                source="title"
                helperText="Used as: 1) caption on portfolio gallery, 2) title of the timeline event"
              />
              <TextInput
                label="summary"
                fullWidth
                source="summary"
                helperText="Used as: 1) content of bell notification, 2) caption on the timeline event"
              />
              <TextInput
                fullWidth
                source="url"
                helperText="Fill this in if you'd like notifications and/or emails to link somewhere other than the update description page. Must be a relative url and start with a '/' (ie: /investment/5 or /users/dashboard)"
              />
              <RichTextInput
                multiline
                required
                fullWidth
                source="description"
                helperText="This will show if the user clicks on a timeline event. This is only relevant to timeline events. The popup will default to the 'summary' if not filled out."
              />
              <ReferenceInput source="eventType.id" reference="EventType">
                <SelectInput
                  required
                  label="Type"
                  fullWidth
                  helperText="Type determines how the event is displayed in timelines, and notifications."
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="project.id"
                reference="Project"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
              >
                <SelectInput
                  label="Project"
                  fullWidth
                  helperText="Only select a project if the event relates directly to a project."
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput source="portfolio.id" reference="PortfolioLite">
                <SelectInput
                  label="Portfolio"
                  fullWidth
                  helperText="Only select a portfolio if the event relates directly to a portfolio (if it relates to a project select a project and not a portfolio. If it relates to multiple projects in a portfolio select a portfolio and not a project)."
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="communicationGroup.id"
                reference="CommunicationGroup"
                perPage={100}
              >
                <SelectInput
                  label="User Group"
                  fullWidth
                  helperText="If you do not see a group that fits who you want to reach out to, get Gray (<EMAIL>) to make one."
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput
                source="sendgridEmailTemplate.id"
                reference="SendgridEmailTemplate"
              >
                <SelectInput
                  label="Email Template"
                  fullWidth
                  helperText="If you do not see a template that works for you, get Gray (<EMAIL>) to make one."
                  optionText="name"
                />
              </ReferenceInput>
              <ReferenceInput source="eventAuthor.id" reference="EventAuthor">
                <SelectInput
                  label="Event Author"
                  fullWidth
                  helperText="This will add a sender to the end of the email. If you do not see the person you want to send from, get Gray (<EMAIL>) to make one."
                  optionText="name"
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom paragraph variant="h5">
                Distribution Channels
              </Typography>
              {/* <BooleanInput
                fullWidth
                label="Include in quarterly report"
                source="includeInQuarterlyReportFlg"
              /> */}
              <Typography variant="body1">
                <b>Portfolio Page</b>
              </Typography>
              <Grid item style={{ marginLeft: '2rem' }}>
                <BooleanInput
                  fullWidth
                  label="Include in Portfolio Page Timeline"
                  source="includeInPortfolioTimelineFlg"
                />
                <BooleanInput
                  fullWidth
                  label="Include in Portfolio/Project Photo Gallery"
                  source="includeInPortfolioFeedFlg"
                />
                {/* <BooleanInput
                fullWidth
                label="Include in Project Page Timeline"
                source="includeInProjectTimelineFlg"
              /> */}
                {/* <BooleanInput
                fullWidth
                label="Include in Homepage Feed"
                source="includeInGlobalFeedFlg"
              /> */}
              </Grid>
              <Typography variant="body1">
                <b>Dashboard</b>
              </Typography>
              <Grid item style={{ marginLeft: '2rem' }}>
                {/* <BooleanInput
                  fullWidth
                  label="Include in Stay Connected"
                  source="includeInInvestorDashboardFlg"
                  disabled
                  // helperText="Coming Soon"
                /> */}
                <BooleanInput
                  fullWidth
                  label="Create User Group Bell Notifications"
                  source="createNotificationsFlg"
                />
              </Grid>
              <Typography variant="body1">
                <b>Email</b>
              </Typography>
              <Grid item style={{ marginLeft: '2rem' }}>
                <BooleanInput
                  fullWidth
                  label="Create User Group Emails"
                  source="createEmailsFlg"
                />
              </Grid>
              {/* <Typography variant="body1">
                <b>Mobile</b>
              </Typography>
              <Grid item style={{ marginLeft: '2rem' }}>
                <BooleanInput
                  fullWidth
                  label="Create User Group Push Notifications"
                  // helperText="Coming Soon"
                  disabled
                  source="createPushNotificationsFlg"
                />
              </Grid> */}
              <Divider
                style={{
                  width: '100%',
                  marginBottom: '1em',
                }}
              />
              <Alert
              // severity={recordContext?.isComplete ? 'success' : 'warning'} // TODO: not working
              >
                <Grid container alignItems="center">
                  <Grid item>
                    <BooleanInput
                      fullWidth
                      label="Data is checked and ready to be sent out."
                      source="isComplete"
                      helperText={false}
                    />
                  </Grid>
                </Grid>
              </Alert>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom variant="h5">
                Testing
              </Typography>
              <MaterialTextField
                label="Test Email Address"
                type="email"
                fullWidth
                value={testEmail}
                onChange={(event) => setTestEmail(event.target.value)}
              />
              <Button
                style={{ marginTop: '1rem' }}
                variant="contained"
                color="primary"
                onClick={handleClick}
                disabled={!testEmail || testEmail === ''}
              >
                Send Test Email
              </Button>
            </Grid>
          </Grid>
          <UrlField label="View Update's Page" source="eventPageLink" />
        </FormTab>
        <FormTab label="Media">
          <Grid style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Alert severity="info">
                <Typography>
                  All images : 1600px X 1200px (landscape)
                </Typography>
                <Typography variant="body2">
                  Avoid text in image unless logo or info-graphic. Pad important
                  content so that cropping of the edges of the edges does not
                  cutoff a word, or face, or something important.
                </Typography>
              </Alert>
            </Grid>
          </Grid>
          <Typography variant="h5" gutterBottom>
            Event Images
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.eventImages || formData?.eventImages.length == 0)
                return 'No photos';
              return formData.eventImages.map((record) => {
                return (
                  <Grid style={{ paddingBottom: '1rem' }}>
                    <Image
                      cloud_name={Config.cloud_name}
                      publicId={record.public_id}
                    >
                      <Transformation width="200" crop="scale" />
                    </Image>
                    <Grid
                      style={{ width: '100%' }}
                      justifyContent="center"
                      container
                      spacing={2}
                    >
                      <Grid item>
                        <Button
                          color="primary"
                          style={
                            record.primaryFlg
                              ? {
                                  backgroundColor: 'green',
                                  color: 'white',
                                }
                              : {}
                          }
                          disabled={record.primaryFlg}
                          variant={record.primaryFlg ? 'contained' : 'outlined'}
                          onClick={handleMakePhotoPrimary(record)}
                        >
                          <Done />
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          style={{ float: 'right' }}
                          onClick={handleRemovePhoto(record)}
                        >
                          <Delete />
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                );
              });
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button variant="outlined" onClick={uploadImageWithCloudinary}>
              Add photo
            </Button>
          </div>
          <Divider style={{ width: '100%', margin: '2em 0' }} />
          <Typography variant="h5" gutterBottom>
            Event Videos
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.eventVideos || formData?.eventVideos.length == 0)
                return 'No videos';
              return formData?.eventVideos.map((record) => {
                if (!record || !record.public_id) return null;
                return (
                  <>
                    <Video
                      cloud_name={Config.cloud_name}
                      publicId={record.public_id}
                      muted
                      width="200"
                      sourceTypes={['mp4']}
                      controls
                    >
                      {record.public_id ? (
                        <Transformation width={200} crop="scale" />
                      ) : null}
                    </Video>
                    <Button
                      color="primary"
                      style={
                        record.primaryFlg
                          ? {
                              backgroundColor: 'green',
                              color: 'white',
                            }
                          : {}
                      }
                      disabled={record.primaryFlg}
                      variant={record.primaryFlg ? 'contained' : 'outlined'}
                      onClick={handleMakeVideoPrimary(record)}
                    >
                      <Done />
                    </Button>
                    <Button
                      style={{ float: 'right' }}
                      onClick={handleRemoveVideo(record)}
                    >
                      <Delete />
                    </Button>
                  </>
                );
              });
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button variant="outlined" onClick={uploadVideoWithCloudinary}>
              Add video
            </Button>
          </div>
        </FormTab>
        <FormTab label="Distribution">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                gutterBottom
                style={{ marginTop: '1rem' }}
              >
                <b>Notifications :</b>
              </Typography>
              <FunctionField
                label="Sent / Read / Remaining Notifications"
                style={{ width: '100%' }}
                render={(record) => {
                  return (
                    <Typography variant="h5">
                      <span>
                        {record.eventNotifications &&
                          record.eventNotifications.length}{' '}
                        / {record.readEventNotificationsCount} /{' '}
                        {record.remainingNotificationUsers &&
                          record.remainingNotificationUsers.length}
                      </span>
                    </Typography>
                  );
                }}
              />
              <Typography
                variant="h6"
                gutterBottom
                style={{ marginTop: '1rem' }}
              >
                <b>Emails :</b>
              </Typography>

              <FunctionField
                label="Sent / Remaining Emails"
                style={{ width: '100%' }}
                render={(record) => {
                  return (
                    <Typography variant="h5">
                      <span>
                        {record.eventEmails && record.eventEmails.length} /{' '}
                        {record.remainingEmailUsers &&
                          record.remainingEmailUsers.length}
                      </span>
                    </Typography>
                  );
                }}
              />
              {/* <Typography
                variant="h6"
                gutterBottom
                style={{ marginTop: '1rem' }}
              >
                <b>Social Media :</b>
              </Typography> */}
              {/* <FunctionField
                label=" "
                style={{ width: '100%' }}
                render={(record) => {
                  if (!record || !record.socialMediaPosts) return null;
                  const jsx = record.socialMediaPosts
                    .map((el) => {
                      const { hubSpotSocialMediaPost } = el;
                      if (!hubSpotSocialMediaPost) return null;
                      const socialMediaIcon = hubSpotSocialMediaPost.channel ? (
                        socialIconMapping[
                          hubSpotSocialMediaPost.channel.channelSlug
                        ]
                      ) : (
                        <Help style={{ fontSize: '3rem' }} />
                      );
                      const image =
                        el.hubSpotSocialMediaPost.content &&
                        el.hubSpotSocialMediaPost.content.photoUrl ? (
                          <Avatar
                            variant="rounded"
                            style={{
                              width: '5rem',
                              height: '5rem',
                              marginRight: '.5rem',
                            }}
                            alt="Social Media Post Image"
                            src={el.hubSpotSocialMediaPost.content.photoUrl}
                          />
                        ) : (
                          <Avatar
                            variant="rounded"
                            style={{
                              width: '5rem',
                              height: '5rem',
                              marginRight: '.5rem',
                            }}
                            alt="Social Media Post Image"
                          >
                            {socialMediaIcon}
                          </Avatar>
                        );
                      return (
                        <ListItem
                          key={`social-post-${el.id}`}
                          style={{ paddingRight: '80px' }}
                        >
                          <ListItemAvatar>{image}</ListItemAvatar>
                          <ListItemText
                            primary={
                              <>
                                {`${
                                  hubSpotSocialMediaPost.channel
                                    ? hubSpotSocialMediaPost.channel.channelSlug
                                    : 'Unknown Channel'
                                } - `}
                                <b
                                  style={{
                                    color: getColorFromStatus(
                                      hubSpotSocialMediaPost.status
                                    ),
                                  }}
                                >{`${hubSpotSocialMediaPost.status}`}</b>
                              </>
                            }
                            secondary={
                              <>
                                {hubSpotSocialMediaPost.content.body}
                                <br />
                                <em>{`Scheduled for ${moment(
                                  hubSpotSocialMediaPost.triggerAt
                                ).format('lll')}`}</em>
                              </>
                            }
                          />
                          {hubSpotSocialMediaPost.status === 'DRAFT' ? (
                            <ListItemSecondaryAction>
                              <Tooltip title="Delete Post">
                                <IconButton
                                  onClick={(event) =>
                                    alert(
                                      'Delete Post Functionality Coming Soon. For now use the edit button.'
                                    )
                                  }
                                >
                                  <Delete />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit/Approve Post">
                                <IconButton
                                  color="primary"
                                  component={'a'}
                                  href={hubSpotSocialMediaPost.hubSpotUrl}
                                  target="_blank"
                                  size="large"
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Approve Post">
                                <IconButton
                                  onClick={(event) =>
                                    alert(
                                      'Approve Post Functionality Coming Soon. For now use the edit button.'
                                    )
                                  }
                                >
                                  <Check />
                                </IconButton>
                              </Tooltip>
                            </ListItemSecondaryAction>
                          ) : null}
                        </ListItem>
                      );
                    })
                    .filter((el) => !!el);
                  return <MaterialList>{jsx}</MaterialList>;
                }}
              /> */}
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const GreenBadge = withStyles((theme) => ({
  badge: {
    backgroundColor: '#44b700',
    color: '#44b700',
    boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
  },
}))(Badge);

const YellowBadge = withStyles((theme) => ({
  badge: {
    backgroundColor: theme.palette.yellow.main,
    color: theme.palette.yellow.main,
    boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
    '&::after': {
      position: 'absolute',
      width: '100%',
      height: '100%',
      borderRadius: '50%',
      boxSizing: 'border-box',
      animation: '$ripple 1.2s infinite ease-in-out',
      border: '1px solid currentColor',
      content: '""',
    },
  },
  '@keyframes ripple': {
    '0%': {
      transform: 'scale(.8)',
      opacity: 1,
    },
    '100%': {
      transform: 'scale(2.4)',
      opacity: 0,
    },
  },
}))(Badge);

const getTooltipTitle = (post) => {
  const { hubSpotSocialMediaPost } = post;
  if (!hubSpotSocialMediaPost) return 'Missing Post';
  return (
    hubSpotSocialMediaPost &&
    hubSpotSocialMediaPost.channel &&
    hubSpotSocialMediaPost.channel.channelSlug
  );
};
const getSocialBadge = (post) => {
  const iconLib = {
    instagram: 'fab fa-instagram',
    facebookpage: 'fab fa-facebook',
    linkedincompanypage: 'fab fa-linkedin',
    linkedinstatus: 'fab fa-linkedin',
    twitter: 'fab fa-twitter',
  };
  const { hubSpotSocialMediaPost } = post;

  if (!hubSpotSocialMediaPost) return 'Missing Post';
  const { status } = post.hubSpotSocialMediaPost;
  if (status === 'DRAFT') {
    return (
      <YellowBadge
        overlap="circular"
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        variant="dot"
      >
        <i
          className={
            iconLib[
              String(
                post.hubSpotSocialMediaPost.channel &&
                  post.hubSpotSocialMediaPost.channel.channelSlug
              )
            ] || 'fas fa-question'
          }
        ></i>
      </YellowBadge>
    );
  }
  return (
    <i
      className={
        iconLib[
          String(
            post.hubSpotSocialMediaPost.channel &&
              post.hubSpotSocialMediaPost.channel.channelSlug
          )
        ] || 'fas fa-question'
      }
    ></i>
  );
};

const EventFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Project"
      source="project.id"
      reference="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Event Type"
      source="eventType.id"
      reference="EventType"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Event Type" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const EventList = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const { permissions } = usePermissions();
  return (
    <>
      <List
        title={entityName}
        sort={{ field: 'id', order: 'DESC' }}
        perPage={25}
        filters={<EventFilter />}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <LinkField
            label="Type"
            linkSource="eventType.id"
            labelSource="eventType.name"
            reference="EventType"
          />
          <FunctionField
            label="Distribution"
            style={{ width: '100%', textAlign: 'center' }}
            render={(record) => {
              const jsx = [];
              const {
                includeInPortfolioTimelineFlg,
                includeInGlobalFeedFlg,
                includeInInvestorDashboardFlg,
                createNotificationsFlg,
                createEmailsFlg,
                isComplete,
              } = record;
              const booleanFields = [
                {
                  label: 'Portfolio Timeline',
                  attr: 'includeInPortfolioTimelineFlg',
                  icon: 'fas fa-timeline-arrow',
                },
                {
                  label: 'Global Feed',
                  attr: 'includeInGlobalFeedFlg',
                  icon: 'fas fa-globe',
                },
                {
                  label: 'Portfolio/Project Image Feed',
                  attr: 'includeInPortfolioFeedFlg',
                  icon: 'fas fa-images',
                },
                {
                  label: 'Dashboard Activity',
                  attr: 'includeInInvestorDashboardFlg',
                  icon: 'fas fa-chart-user',
                },
                {
                  label: 'Notification',
                  attr: 'createNotificationsFlg',
                  icon: 'fas fa-bell',
                },
                {
                  label: 'Email',
                  attr: 'createEmailsFlg',
                  icon: 'fas fa-envelope',
                },
              ];
              booleanFields.forEach((field) => {
                const val = record[String(field.attr)];
                const requiresApproval = val && !record.isComplete;
                const tooltipTitle = requiresApproval
                  ? `${field.label} (requires approval)`
                  : field.label;
                let badge;
                if (requiresApproval) {
                  badge = (
                    <YellowBadge
                      overlap="circular"
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'right',
                      }}
                      variant="dot"
                    >
                      <i className={field.icon}></i>
                    </YellowBadge>
                  );
                } else if (false) {
                  badge = (
                    <GreenBadge
                      overlap="circular"
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'right',
                      }}
                      variant="dot"
                    >
                      <i className={field.icon}></i>
                    </GreenBadge>
                  );
                } else {
                  badge = <i className={field.icon}></i>;
                }
                jsx.push(
                  <Tooltip
                    key={`tooltip-${field.attr}-${record.id}`}
                    title={tooltipTitle}
                  >
                    <Avatar
                      style={
                        val
                          ? { backgroundColor: theme.palette.appSecondary.main }
                          : {}
                      }
                    >
                      {badge}
                    </Avatar>
                  </Tooltip>
                );
                // }
              });
              // record.socialMediaPosts.forEach((post) => {
              //   const tooltipTitle = getTooltipTitle(post);
              //   const badge = getSocialBadge(post);
              //   jsx.push(
              //     <Tooltip title={tooltipTitle}>
              //       <Avatar
              //         style={{
              //           backgroundColor: theme.palette.appSecondary.main,
              //         }}
              //       >
              //         {badge}
              //       </Avatar>
              //     </Tooltip>
              //   );
              // });
              // if (record.requiresApproval) {
              return <AvatarGroup max={8}>{jsx}</AvatarGroup>;
              // }
            }}
          />
          <FunctionField
            label="Banner Image"
            style={{ width: '100%', textAlign: 'center' }}
            render={(record) => {
              if (!record.bannerImage) {
                return null;
              }
              return (
                <Image
                  cloud_name={Config.cloud_name}
                  publicId={record.bannerImage.public_id}
                  onClick={(event) => {
                    setSelectedImage(record.bannerImage);
                    event.preventDefault();
                    event.stopPropagation();
                  }}
                >
                  <Transformation width="200" crop="scale" />
                </Image>
              );
            }}
          />
          <TextField source="title" />
          <DateField source="eventDt" />
          <DetailField source="summary" sortable={false} />
          {/* <DetailField source="description" sortable={false} /> */}
          {/* <UrlField source="url" /> */}
          {/* <BooleanField source="isComplete" /> */}
          {/* <BooleanField source="includeInQuarterlyReportFlg" /> */}
          <BooleanField
            label="User Dashboard Activity"
            source="includeInInvestorDashboardFlg"
          />
          <BooleanField label="Global Feed" source="includeInGlobalFeedFlg" />
          <BooleanField
            label="Portfolio Timeline"
            source="includeInInvestorDashboardFlg"
          />
          <BooleanField
            label="Portfolio/Project Image Feed"
            source="includeInPortfolioFeedFlg"
          />

          <BooleanField
            label="In-App Notifications"
            source="createNotificationsFlg"
          />
          <BooleanField label="Email" source="createEmailsFlg" />
          {/* <FunctionField
        label="Sent / Read / Remaining Notifications"
        style={{ width: '100%', textAlign: 'center' }}
        render={(record) => {
          return (
            <span>
              {record.eventNotifications && record.eventNotifications.length} /{' '}
              {record.readEventNotificationsCount} /{' '}
              {record.remainingNotificationUsers &&
                record.remainingNotificationUsers.length}
            </span>
          );
        }}
      />
      <FunctionField
        label="Sent / Remaining Emails"
        style={{ width: '100%', textAlign: 'center' }}
        render={(record) => {
          return (
            <span>
              {record.eventEmails && record.eventEmails.length} /{' '}
              {record.remainingEmailUsers && record.remainingEmailUsers.length}
            </span>
          );
        }}
      /> */}
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.subtitle"
            label="Portfolio"
          />
          <LinkField
            reference="Project"
            linkSource="project.id"
            labelSource="project.name"
            label="Project"
          />
          <LinkField
            reference="CommunicationGroup"
            linkSource="communicationGroup.id"
            labelSource="communicationGroup.name"
            label="User Group"
          />
          <DateField source="createdAt" />
          {/* <DateField source="updatedAt" /> */}
        </Datagrid>
      </List>
      <Dialog
        maxWidth={false}
        open={selectedImage}
        onClose={() => setSelectedImage(null)}
      >
        <DialogContent style={{ padding: 0 }}>
          <Fab
            style={{ position: 'absolute', top: 8, right: 8 }}
            onClick={() => setSelectedImage(null)}
            color="primary"
          >
            <Close />
          </Fab>
          <Grid container>
            <Image
              cloud_name={Config.cloud_name}
              publicId={selectedImage && selectedImage.public_id}
            >
              <Transformation width="1080" crop="scale" />
            </Image>
          </Grid>
        </DialogContent>
      </Dialog>
    </>
  );
};

export const EventCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput variant="outlined" source="title" fullWidth />
          <ReferenceInput source="eventType.id" reference="EventType">
            <SelectInput
              required
              label="Type"
              fullWidth
              helperText="Type determines how the event is displayed in timelines, and notifications. If you do not see a category that your events fit into, reach <NAME_EMAIL> to request a new event type."
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
