import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid, Tooltip } from '@mui/material';
import { Alert } from '@mui/lab';
import { CloudDownload } from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Authentication Document';

export const AuthenticationDocumentEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <CustomNumberInput source="user.id" disabled fullWidth step={1} />
            <TextInput source="awsObjectKey" disabled fullWidth />
            <SelectInput
              disabled
              source="documentType"
              choices={[
                { id: 'license', name: 'U.S. Drivers License' },
                { id: 'passport', name: 'U.S. Passport' },
                { id: 'idCard', name: 'ID Card' },
                { id: 'other', name: 'Other' },
              ]}
              fullWidth
            />
            <TextInput source="beneficialOwnerDwollaId" disabled fullWidth />
            <TextInput source="dwollaDocumentId" disabled fullWidth />
            <FunctionField
              label="Photo"
              render={(record) => {
                return (
                  <Grid container direction="column" spacing={2}>
                    <Grid item>
                      <Tooltip title={'Click image to download'}>
                        <img
                          src={record.awsObjectUrl}
                          style={{ cursor: 'pointer' }}
                          onClick={() =>
                            window.location.assign(record.awsObjectUrl)
                          }
                        />
                      </Tooltip>
                    </Grid>
                    <Grid item>
                      <Button
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{ textTransform: 'none' }}
                        onClick={() =>
                          window.location.assign(record.awsObjectUrl)
                        }
                      >
                        Download Image
                      </Button>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const AuthenticationDocumentFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name, Last Name, or Email"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const AuthenticationDocumentList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={10}
      filters={<AuthenticationDocumentFilter />}
      sort={{ field: 'id', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <NumberField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="beneficialOwnerDwollaId" />
        <TextField source="documentType" />
        <FunctionField
          label="Photo"
          render={(record) => <img src={record.awsObjectUrl} height="150px" />}
        />
        <FunctionField
          label="Photo"
          render={(record) => {
            return (
              <Button
                variant="contained"
                startIcon={<CloudDownload />}
                style={{ textTransform: 'none' }}
                onClick={() => window.location.assign(record.awsObjectUrl)}
              >
                Download Image
              </Button>
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const AuthenticationDocumentCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <Alert severity="info">
            The purpose of this create page is to link previously uploaded
            documents. If you would like to upload new documents, go to Users >
            User > Summary > Dwolla User Profile
          </Alert>
          <CustomNumberInput source="userId" required fullWidth step={1} />
          <TextInput source="awsObjectKey" required fullWidth />
          <SelectInput
            source="documentType"
            choices={[
              { id: 'license', name: 'U.S. Drivers License' },
              { id: 'passport', name: 'U.S. Passport' },
              { id: 'idCard', name: 'ID Card' },
              { id: 'other', name: 'Other' },
            ]}
            fullWidth
          />
          <TextInput source="dwollaDocumentId" required fullWidth />
          <TextInput source="beneficialOwnerDwollaId" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
