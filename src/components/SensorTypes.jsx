import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Sensor Type';

export const SensorTypeEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
            <TextInput source="unit" fullWidth />
            <CustomNumberInput source="minValue" fullWidth />
            <CustomNumberInput source="maxValue" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const SensorTypeList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <TextField source="unit" />
        <NumberField source="minValue" />
        <NumberField source="maxValue" />
      </Datagrid>
    </List>
  );
};

export const SensorTypeCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="description" fullWidth />
          <TextInput source="unit" required fullWidth />
          <CustomNumberInput source="minValue" fullWidth />
          <CustomNumberInput source="maxValue" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
