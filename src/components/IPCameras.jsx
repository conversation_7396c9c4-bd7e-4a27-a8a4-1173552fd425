import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Datagrid,
  DateField,
  Edit,
  Filter,
  FunctionField,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  useRefresh,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Image, Transformation } from 'cloudinary-react';

import { Button, Grid } from '@mui/material';
import { Delete } from '@mui/icons-material';

import { openUploadWidget } from '../utils/CloudinaryService';
import { getEditable } from '../utils/applyRoleAuth';
import { DetailField, LinkField } from './CustomFields';
import Config from '../config/config';

const entityName = 'IP Camera';

export const IPCameraEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const onPhotoUploaded = (photo) => {
    dataProvider
      .update('IPCamera', {
        data: {
          id: parseInt(id, 10),
          thumbnailPublicId: photo.public_id,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
      })
      .then(() => {
        notify('Successfully uploaded image');
        refresh();
      });
  };

  const handleAddImage = () => {
    const uploadOptions = {
      tags: ['project-camera-thumbnail'],
      multiple: false,
      resourceType: 'image',
      showPoweredBy: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.site_camera_thumbnails,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotoUploaded(resp.info);
      }
    });
  };

  const handleRemoveImage = () => {
    dataProvider
      .update('IPCamera', {
        data: {
          id: parseInt(id, 10),
          thumbnailPublicId: null,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error removing image', { type: 'error' });
      })
      .then(() => {
        notify('Image successfully removed');
        refresh();
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
            <ReferenceInput
              source="project.id"
              reference="Project"
              sort={{ field: 'name', order: 'ASC' }}
              perPage={10000}
            >
              <SelectInput
                label="Project"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <TextInput source="username" fullWidth />
            <TextInput source="password" fullWidth />
            <TextInput source="ipAddress" fullWidth />
            <TextInput
              source="iframeUrl"
              fullWidth
              helperText="Go to https://rtsp.me/, fill out the information, and copy the permalink."
            />
          </Grid>
        </Grid>
        <FunctionField
          align="center"
          label="Camera thumbnail"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <>
                <Image
                  cloud_name={Config.cloud_name}
                  publicId={record.thumbnailPublicId}
                >
                  <Transformation width="200" crop="scale" />
                </Image>
                {record.thumbnailPublicId ? (
                  <Button onClick={handleRemoveImage}>
                    <Delete />
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={handleAddImage}
                  >
                    Add Thumbnail
                  </Button>
                )}
              </>
            );
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

const IPCameraFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      source="project.id"
      reference="Project"
      label="Project"
      sort={{ field: 'name', order: 'ASC' }}
      perPage={10000}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const IPCameraList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} filters={<IPCameraFilter />}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="name" />
        <TextField source="ipAddress" />
        <TextField source="username" />
        <TextField source="password" />
        <DetailField source="description" />
        <FunctionField
          label="Thumbnail"
          style={{ width: '100%', textAlign: 'center' }}
          render={(record) => {
            if (!record.thumbnailPublicId) {
              return null;
            }
            return (
              <Image
                cloud_name={Config.cloud_name}
                publicId={record.thumbnailPublicId}
              >
                <Transformation width="200" crop="scale" />
              </Image>
            );
          }}
        />
        <DateField source="createdAt" showTime={true} />
        <DateField source="updatedAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

export const IPCameraCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            perPage={10000}
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput label="Project" required fullWidth optionText="name" />
          </ReferenceInput>
          <TextInput source="name" required fullWidth />
          <TextInput source="description" fullWidth />
          <TextInput source="ipAddress" required fullWidth />
          <TextInput source="username" required fullWidth />
          <TextInput source="password" required fullWidth />
          <TextInput
            source="iframeUrl"
            fullWidth
            helperText="Go to https://rtsp.me/, fill out the information, and copy the permalink."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
