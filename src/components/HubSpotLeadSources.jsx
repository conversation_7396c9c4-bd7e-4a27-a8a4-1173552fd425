import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import numeral from 'numeral';
import {
  <PERSON>oleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import {
  Alert,
  Grid,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'HubSpot Lead Source';

export const HubSpotLeadSourceEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }} spacing={5}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <BooleanInput
              label="Unknown Lead Source"
              source="unknownFlg"
              helperText="Users with a lead source that has this turned on will receive a 'how did you hear about us' survey when they log in."
            />
            <ReferenceInput
              source="leadSourceCategory.id"
              reference="LeadSourceCategory"
            >
              <SelectInput
                label="Lead Source Category"
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
          <Grid item xs={12} md={6}>
            <FunctionField
              label="Users"
              render={(record) => {
                if (!record?.users?.length)
                  return (
                    <Alert severity="info">
                      No users in this Lead Source Category
                    </Alert>
                  );
                return (
                  <>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>
                            <b>Name</b>
                          </TableCell>
                          <TableCell>
                            <b>Email</b>
                          </TableCell>
                          <TableCell>Total Invested</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.users.map((user) => {
                          return (
                            <TableRow
                              key={`lead-source-user-${user.id}`}
                              component={Link}
                              to={`/User/${user.id}`}
                            >
                              <TableCell>{user.fullName}</TableCell>
                              <TableCell>{user.email}</TableCell>
                              <TableCell>
                                {numeral(user.investmentSum).format('$0,0.00')}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

export const HubSpotLeadSourceList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={50}
      pagination={<CustomPagination />}
      sort={{ field: 'id', order: 'desc' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField
          source="name"
          sx={{
            display: 'inline-block',
            maxWidth: '720px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        />
        {/* TODO: make this sortable by moving "userCount" to a database model virtual column */}
        <NumberField source="userCount" sortable={false} />
        <NumberField
          source="totalInvested"
          sortable={false}
          options={{ style: 'currency', currency: 'USD' }}
        />
        <BooleanField
          label="Unknown Lead Source"
          source="unknownFlg"
          helperText="Users with a lead source that has this turned on will receive a 'how did you hear about us' survey when they log in."
        />
        <LinkField
          label="Lead Source Category"
          linkSource="leadSourceCategory.id"
          labelSource="leadSourceCategory.name"
          reference="LeadSourceCategory"
        />
      </Datagrid>
    </List>
  );
};

export const HubSpotLeadSourceCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <ReferenceInput
            source="leadSourceCategory.id"
            reference="LeadSourceCategory"
          >
            <SelectInput
              label="Lead Source Category"
              required
              fullWidth
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
