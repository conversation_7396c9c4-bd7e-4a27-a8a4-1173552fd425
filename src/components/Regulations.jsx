import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayInput,
  Create,
  Datagrid,
  Edit,
  List,
  ArrayField,
  SingleFieldList,
  NumberField,
  NumberInput,
  SimpleForm,
  SimpleFormIterator,
  TextField,
  TextInput,
  usePermissions,
  useRecordContext,
  useResourceDefinition,
} from 'react-admin';

import { Chip, Grid } from '@mui/material';

import {
  CustomNumberInput,
  CustomReferenceField,
  DetailField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const TextArrayField = ({ source }) => {
  const record = useRecordContext();
  return (
    <>
      {record[String(source)] &&
        record[String(source)].map((item) => (
          <Chip
            style={{
              backgroundColor: 'red',
              color: 'white',
              fontWeight: 'bold',
            }}
            label={item}
            key={item}
          />
        ))}
    </>
  );
};
TextArrayField.defaultProps = { addLabel: true };

const entityName = 'Regulation';

export const RegulationEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item md={6} xs={12}>
            <TextInput source="name" fullWidth />
            <TextInput multiline source="description" fullWidth />
            <CustomNumberInput
              label="Accredited Investor Limit"
              source="numAccreditedInvestors"
              fullWidth
            />
            <CustomNumberInput
              label="Unaccredited Investor Limit"
              source="numUnaccreditedInvestors"
              fullWidth
            />
            <CustomNumberInput source="unaccreditedInvestmentMax" fullWidth />
            <CustomNumberInput source="accreditedInvestmentMax" fullWidth />
            <CustomNumberInput source="unaccreditedInvestmentMin" fullWidth />
            <CustomNumberInput source="accreditedInvestmentMin" fullWidth />
          </Grid>
        </Grid>
        <ArrayInput
          source="prohibitedStates"
          helperText="This will prevent a user who resides in this state from investing in portfolios under this regulation. Must be a 2 character state code (capitalized). One state per entry."
        >
          <SimpleFormIterator TransitionProps={{ enter: false, exit: false }}>
            <TextInput />
          </SimpleFormIterator>
        </ArrayInput>
        <ArrayField sortable={false} source="portfolios">
          <SingleFieldList>
            <CustomReferenceField source="name" />
          </SingleFieldList>
        </ArrayField>
      </SimpleForm>
    </Edit>
  );
};

export const RegulationList = () => {
  const { permissions } = usePermissions();
  return (
    <List>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <DetailField source="description" sortable={false} />
        <TextArrayField source="prohibitedStates" />
        <NumberField
          label="Accredited Investor Limit"
          source="numAccreditedInvestors"
        />
        <NumberField
          label="Unaccredited Investor Limit"
          source="numUnaccreditedInvestors"
        />
        <NumberField source="unaccreditedInvestmentMax" />
        <NumberField source="accreditedInvestmentMax" />
        <NumberField source="unaccreditedInvestmentMin" />
        <NumberField source="accreditedInvestmentMin" />
        {/* <ArrayField source="portfolios" sortable={false} >
        <SingleFieldList>
          <CustomReferenceField source="name" />
        </SingleFieldList>
      </ArrayField> */}
      </Datagrid>
    </List>
  );
};

export const RegulationCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
