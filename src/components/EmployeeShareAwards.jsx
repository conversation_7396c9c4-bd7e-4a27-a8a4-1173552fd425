import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';

import {
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  FormDataConsumer,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Button,
  CircularProgress,
  Collapse,
  Grid,
  Typography,
} from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CheckCircle, CloudDownload, CloudUpload } from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import moment from 'moment';
import theme from '../theme';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Employee Share Award';

export const EmployeeShareAwardEdit = () => {
  const [documentUploading, setDocumentUploading] = useState(false);
  const { id } = useParams();
  const refresh = useRefresh();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  const uploadToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `EmployeeShareAwards/${formData.employee.id}_${
          formData.awardedDt
        }_${moment().valueOf()}_${file.name}`
      );
      setDocumentUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_BUCKET_HUMAN_RESOURCES
      ).then(
        () => {
          dataProvider
            .update('EmployeeShareAward', {
              data: { id: parseInt(id, 10), awardAwsObjectKey: awsObjectKey },
            })
            .then(
              (res) => {
                notify('Document uploaded', { type: 'success' });
                setDocumentUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading document', { type: 'error' });
                setDocumentUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading invoice to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="employee.id"
              reference="EmployeeLite"
              perPage={10000}
              sort={{ field: 'firstName', order: 'ASC' }}
            >
              <SelectInput
                optionText="fullName"
                label="Employee"
                fullWidth
                required
              />
            </ReferenceInput>
            <DateInput source="awardedDt" fullWidth required />
            <CustomNumberInput source="shares" fullWidth required />
            <FunctionField
              label="Download"
              render={(record) => {
                return (
                  <Grid container spacing={2}>
                    <Grid item>
                      <Button
                        disabled={!record.awardDownloadUrl}
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{ textTransform: 'none' }}
                        onClick={(e) => {
                          e.stopPropagation();
                          window.location.assign(record.awardDownloadUrl);
                        }}
                      >
                        Download
                      </Button>
                    </Grid>
                    <Grid item>
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          return (
                            <Collapse
                              in={
                                formData.employee?.id &&
                                formData.awardedDt &&
                                (formData.shares || formData.shares === 0)
                              }
                            >
                              <Button
                                color="primary"
                                variant="contained"
                                component="label" // https://stackoverflow.com/a/54043619
                                startIcon={<CloudUpload />}
                                style={{ textTransform: 'none' }}
                                disabled={documentUploading}
                              >
                                {documentUploading ? (
                                  <CircularProgress
                                    style={{ position: 'absolute' }}
                                  />
                                ) : null}
                                {record.awardDownloadUrl
                                  ? 'Overwrite Document'
                                  : 'Upload Document'}
                                <input
                                  type="file"
                                  hidden
                                  onChange={(event) =>
                                    uploadToS3(event, formData)
                                  }
                                  accept="application/pdf"
                                />
                              </Button>
                              <Typography variant="body2">
                                This should be a PDF
                              </Typography>
                            </Collapse>
                          );
                        }}
                      </FormDataConsumer>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const EmployeeShareAwardList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Employee"
          linkSource="employee.id"
          labelSource="employee.fullName"
          label="Employee"
        />
        <DateField source="awardedDt" />
        <NumberField source="shares" />
        <FunctionField
          label="Download"
          render={(record) => (
            <Button
              variant="contained"
              color="primary"
              startIcon={<CloudDownload />}
              onClick={() => {
                window.location.assign(record.awardDownloadUrl);
              }}
              disabled={!record.awardDownloadUrl}
              style={{ textTransform: 'none' }}
            >
              Download
            </Button>
          )}
        />
      </Datagrid>
    </List>
  );
};

export const EmployeeShareAwardCreate = (props) => {
  const [awsObjectKey, setAwsObjectKey] = useState(null);
  const [documentUploading, setDocumentUploading] = useState(false);
  const notify = useNotify();

  const MyCreateButton = () => {
    const dataProvider = useDataProvider();
    const { getValues } = useFormContext();
    const redirect = useRedirect();
    const { id, ...data } = getValues();

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      data.awardAwsObjectKey = awsObjectKey;
      dataProvider.create('EmployeeShareAward', { data }).then(
        () => {
          notify('Element created');
          if (props?.onSuccess) {
            props.onSuccess();
          } else {
            redirect('/EmploymentAgreement');
          }
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={
            !awsObjectKey ||
            !data.awardedDt ||
            !data.employee?.id ||
            (!data.shares && data.shares !== 0)
          }
        />
      </Toolbar>
    );
  };

  const uploadToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      // const aFileName = file.name.split('.');
      // const fileExtension =
      //   aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `EmployeeShareAwards/${formData.employee.id}_${
          formData.awardedDt
        }_${moment().valueOf()}_${file.name}`
      );
      setDocumentUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_BUCKET_HUMAN_RESOURCES
      ).then(
        () => {
          setAwsObjectKey(awsObjectKey);
          setDocumentUploading(false);
        },
        (e) => {
          console.error(e);
          notify('Error uploading invoice to S3', { type: 'error' });
          setDocumentUploading(false);
        }
      );
    }
  };

  const isComponentWithinDialog = !!props?.withinDialog;

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm toolbar={<MyCreateButton />}>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={isComponentWithinDialog ? 12 : 6}>
            <ReferenceInput
              source="employee.id"
              reference="EmployeeLite"
              perPage={10000}
              sort={{ field: 'firstName', order: 'ASC' }}
              filter={
                props?.employeeId ? { id: parseInt(props.employeeId, 10) } : {}
              }
            >
              <SelectInput
                optionText="fullName"
                label="Employee"
                fullWidth
                required
              />
            </ReferenceInput>
            <CustomNumberInput source="shares" required fullWidth />
            <DateInput source="awardedDt" required fullWidth />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Collapse
                    in={
                      !!(
                        formData.employee?.id &&
                        formData.awardedDt &&
                        (formData.shares || formData.shares === 0)
                      )
                    }
                  >
                    {awsObjectKey ? (
                      <Grid container alignItems="center">
                        <Grid item>
                          <CheckCircle
                            style={{ color: theme.palette.success.main }}
                          />
                        </Grid>
                        <Grid item>
                          <Typography
                            style={{
                              color: theme.palette.success.main,
                              paddingLeft: '.5rem',
                            }}
                          >
                            Click 'Save' below to complete upload.
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <Button
                        color="primary"
                        disabled={awsObjectKey || documentUploading}
                        variant="contained"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        {documentUploading ? (
                          <CircularProgress style={{ position: 'absolute' }} />
                        ) : null}
                        Upload Document
                        <input
                          type="file"
                          hidden
                          onChange={(event) => uploadToS3(event, formData)}
                          accept="application/pdf"
                        />
                      </Button>
                    )}
                  </Collapse>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
