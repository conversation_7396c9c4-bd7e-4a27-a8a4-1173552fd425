import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import numeral from 'numeral';
import moment from 'moment';
import { useFormContext } from 'react-hook-form';

import {
  ArrayField,
  AutocompleteInput,
  BooleanInput,
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  Labeled,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  required,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TabbedForm,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import {
  Alert,
  Button,
  Card,
  Dialog,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField as MuiTextField,
  Tooltip,
  Typography,
  DialogActions,
  DialogTitle,
  DialogContent,
  FormControlLabel,
  CircularProgress,
  Chip,
  Avatar,
  Icon,
} from '@mui/material';
import {
  CustomBooleanField,
  CustomNumberInput,
  CustomReferenceField,
  DetailField,
  LinkField,
} from './CustomFields';
import { EmailExportButton } from './EmailExportButton';
import { getEditable } from '../utils/applyRoleAuth';
import {
  ArrowForward,
  CloudDownload,
  CloudUpload,
  Subject,
} from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';

import { BrCustomerCreate } from './BrCustomers';
import { downloadSimpleExcelFromRows } from '../utils/excel';
import theme from '../theme';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Consumer Unit';

const definitionJsx = (
  <Alert severity="info" style={{ marginBottom: '1rem' }}>
    <Typography variant="body2">
      A 'Consumer Unit' is an off-taker location (such as a single store front).
      A 'Customer' may have multiple 'Consumer Units' associated with it if they
      have multiple locations.
    </Typography>
    <Divider style={{ margin: '1rem 0' }} />
    <Typography variant="body2">
      Uma 'Unidade Consumidora' é um local de consumo (como uma única frente de
      loja). Um 'Cliente' pode ter várias 'Unidades Consumidoras' associadas a
      ele se tiver múltiplos locais.
    </Typography>
  </Alert>
);

const StageReferenceInput = ({ source, ...props }) => {
  const { getFieldState, getValues } = useFormContext();
  const [showNotification, setShowNotification] = useState(false);
  const [changedDt, setChangedDt] = useState(null);
  const { id, ...data } = getValues();
  const fieldState = getFieldState('brConsumerUnitStage.id');

  // isDirty says whether or not the field has changed from its initial value
  const stageChangeIds = [2, 3, 4, 9];
  if (
    fieldState.isDirty &&
    !showNotification &&
    stageChangeIds.indexOf(data.brConsumerUnitStage?.id) > -1
  ) {
    setShowNotification(true);
  }

  if (
    (!fieldState.isDirty ||
      stageChangeIds.indexOf(data.brConsumerUnitStage?.id) === -1) &&
    showNotification
  ) {
    setShowNotification(false);
  }

  return (
    <>
      <ReferenceInput
        source="brConsumerUnitStage.id"
        reference="BrConsumerUnitStage"
        perPage={10_000}
        sort={{ field: 'id', order: 'ASC' }}
      >
        <SelectInput
          optionText="name"
          label="Stage"
          fullWidth
          onChange={() => {
            // Use this to re-render this component and pick up on value changes
            setChangedDt(new Date());
          }}
        />
      </ReferenceInput>
      {showNotification && (
        <Alert severity="info" icon={false}>
          <FormControlLabel
            style={{ paddingLeft: '1rem' }}
            control={
              <BooleanInput
                source="sendStageChangeEmail"
                label="Click here to send an email to the primary contact about this stage change when you click 'Save'. This only applies when changing from certain stages to certain other stages."
              />
            }
          />
        </Alert>
      )}
    </>
  );
};

export const BrConsumerUnitEdit = () => {
  const [customerCreateDialogOpen, setCustomerCreateDialogOpen] =
    useState(false);
  const [newNote, setNewNote] = useState('');
  const [editNoteDialogOpen, setEditNoteDialogOpen] = useState(false);
  const [editNoteText, setEditNoteText] = useState('');
  const [loading, setLoading] = useState(false);
  const [invoices, setInvoices] = useState(null);
  const [invoicesLoading, setInvoicesLoading] = useState(false);
  const refresh = useRefresh();
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { id } = useParams();
  const { permissions } = usePermissions();

  const fetchInvoices = () => {
    setInvoicesLoading(true);
    dataProvider
      .getList('BrInvoiceLite', {
        pagination: { page: 1, perPage: 10_000 },
        filter: { brConsumerUnit: { id: parseInt(id, 10) } },
        sort: { field: 'dueDt', order: 'DESC' },
      })
      .then(
        (res) => {
          setInvoices(res.data);
          setInvoicesLoading(false);
        },
        (err) => {
          console.error(err);
          setInvoicesLoading(false);
          notify('Error fetching invoices.', { type: 'error' });
        }
      );
  };

  if (!invoices && !invoicesLoading) {
    fetchInvoices();
  }

  const handleCreateNote = () => {
    dataProvider
      .create('BrNote', {
        data: {
          note: String(newNote),
          brConsumerUnitId: parseInt(id, 10),
          employeeId: parseInt(permissions.employee.id, 10),
        },
      })
      .then(
        () => {
          setNewNote('');
          notify('Note created.', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error creating note.', { type: 'error' });
        }
      );
  };

  const handleUpdateNote = (brNoteId) => {
    dataProvider
      .update('BrNote', {
        data: {
          id: brNoteId,
          note: String(editNoteText),
        },
      })
      .then(
        () => {
          setEditNoteText('');
          setEditNoteDialogOpen(false);
          notify('Note updated.', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error editing note.', { type: 'error' });
        }
      );
  };

  const handleDeleteNote = (brNoteId) => {
    dataProvider
      .delete('BrNote', {
        id: brNoteId,
      })
      .then(
        () => {
          setEditNoteText('');
          setEditNoteDialogOpen(false);
          notify('Note deleted.', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error deleting note.', { type: 'error' });
        }
      );
  };

  const uploadFileToS3 = (event) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      let fileExtension = '';
      if (file.type === 'image/png') {
        fileExtension = 'png';
      } else if (file.type === 'image/jpeg') {
        fileExtension = 'jpeg';
      } else if (file.type === 'image/jpg') {
        fileExtension = 'jpg';
      } else if (file.type === 'application/pdf') {
        fileExtension = 'pdf';
      } else {
        notify('Unknown file type. Contact IT for help.', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `UtilityBills/UtilityBill_${moment().valueOf()}.${fileExtension}`
      );
      setLoading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          dataProvider
            .create('BrUtilityBill', {
              data: {
                brConsumerUnitId: parseInt(id, 10),
                awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Utility bill uploaded', { type: 'success' });
                setLoading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading utility bill', { type: 'error' });
                setLoading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading utility bill to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <>
      <Edit title={`${entityName} #${id}`} undoable={false}>
        <TabbedForm>
          <FormTab label="Summary">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                {definitionJsx}
                <Labeled fullWidth>
                  <LinkField
                    reference="BrCustomer"
                    linkSource="brCustomer.id"
                    labelSource="brCustomer.name"
                    label="Customer"
                  />
                </Labeled>
                <ReferenceInput
                  source="brCustomer.id"
                  reference="BrCustomer"
                  perPage={10_000}
                  label="Customer"
                  sort={{ field: 'name', order: 'ASC' }}
                >
                  <AutocompleteInput
                    optionText="name"
                    label="Customer"
                    fullWidth
                    validate={required()}
                    helperText={
                      <Typography variant="caption">
                        To add a new customer,{' '}
                        <a
                          onClick={() => {
                            setCustomerCreateDialogOpen(true);
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          click here.
                        </a>{' '}
                        Para adicionar um novo cliente,{' '}
                        <a
                          onClick={() => {
                            setCustomerCreateDialogOpen(true);
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          clique aqui.
                        </a>
                      </Typography>
                    }
                  />
                </ReferenceInput>
                <ReferenceInput
                  source="brSelfConsumptionOfftaker.id"
                  reference="BrSelfConsumptionOfftaker"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                >
                  <SelectInput
                    disabled
                    optionText="name"
                    label="Self-Consumption Offtaker"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
                <TextInput
                  source="installationCode"
                  fullWidth
                  required
                  label="Installation code / Código instalação"
                />
                <TextInput
                  source="utilityCustomerCode"
                  fullWidth
                  label="Utility customer code / Código do cliente"
                />
                <DateInput
                  source="previousConsortiumContractTerminationDt"
                  fullWidth
                  helperText="This is the date that the consumer unit's contract with their previous consortium is to be terminated. Setting this will set up a reminder to be sent via Slack when the date arrives so we know to add them to the rateio."
                />
                <StageReferenceInput />
                <ReferenceInput
                  source="brSalesPerson.id"
                  reference="BrSalesPerson"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                >
                  <AutocompleteInput
                    optionText="name"
                    label="Sales Partner / Parceiro"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
                <FormDataConsumer>
                  {({ formData }) => {
                    if (!formData.brSalesPerson?.id) {
                      return null;
                    }
                    return (
                      <ReferenceInput
                        source="salesPersonBrContact.id"
                        reference="BrContact"
                        perPage={10_000}
                        sort={{ field: 'id', order: 'ASC' }}
                        filter={{
                          brSalesPerson: { id: formData.brSalesPerson.id },
                        }}
                      >
                        <AutocompleteInput
                          optionText="fullName"
                          label="Sales Person / Vendedor"
                          fullWidth
                          allowEmpty
                        />
                      </ReferenceInput>
                    );
                  }}
                </FormDataConsumer>
                <TextInput
                  source="salesPartnerNotes"
                  fullWidth
                  multiline
                  label="Sales partner notes"
                  helperText="These are notes written by the sales partner that are not visible to the customer."
                />
                <ReferenceInput
                  source="utilityCompany.id"
                  reference="UtilityCompany"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                >
                  <SelectInput
                    optionText="name"
                    label="Utility Company / Distribuidora"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
                <ReferenceInput
                  source="brTariffClass.id"
                  reference="BrTariffClass"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                  filter={{ excludeB1B3: true }}
                >
                  <SelectInput
                    optionText="name"
                    label="Tariff Class / Classe tarifária"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
                <ReferenceInput
                  source="brVoltagePhase.id"
                  reference="BrVoltagePhase"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                >
                  <SelectInput
                    optionText="name"
                    label="Voltage Phase / Conexão"
                    fullWidth
                    allowEmpty
                  />
                </ReferenceInput>
                <FunctionField
                  label="Project / Projeto"
                  render={(record) => {
                    if (
                      !record.brCustomer?.brConsortium &&
                      !record.salesforceProject
                    )
                      return (
                        <Alert severity="info">
                          Customer's consortium must be set before a project can
                          be assigned.
                        </Alert>
                      );
                    const disabled = !record.brCustomer?.brConsortium;
                    return (
                      <>
                        {disabled ? (
                          <Alert severity="info">
                            The project this consumer unit is assigned to cannot
                            be edited until the customers consortium has been
                            selected.
                          </Alert>
                        ) : null}
                        <ReferenceInput
                          source="salesforceProject.id"
                          reference="SalesforceProject"
                          perPage={10_000}
                          sort={{ field: 'id', order: 'ASC' }}
                          filter={
                            disabled
                              ? null
                              : {
                                  brConsortium: {
                                    id: record.brCustomer?.brConsortium?.id,
                                  },
                                }
                          }
                        >
                          <SelectInput
                            optionText="name"
                            label="Project"
                            fullWidth
                            allowEmpty
                            disabled={disabled}
                          />
                        </ReferenceInput>
                      </>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Billing">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <BooleanInput
                  source="invoiceOfflineFlg"
                  fullWidth
                  label="Invoice offline flg / Não emitir boleto"
                  helperText="If this is enabled, the user will not receive an invoice or email."
                />
                <SelectInput
                  source="preferredPaymentMethod"
                  choices={[
                    { id: 'boleto', name: 'Boleto' },
                    { id: 'card', name: 'Credit/Debit Card' },
                  ]}
                  fullWidth
                  helperText="If boleto is selected, we will generate a boleto with stark bank and include it in the email. If card is selected, they will be billed through Stripe."
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Address">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6">Address / Endereço</Typography>
                <TextInput
                  source="postalCode"
                  fullWidth
                  label="Postal code / CEP unidade"
                />
                <TextInput
                  source="address1"
                  fullWidth
                  label="Address1 / Endereço"
                />
                <TextInput
                  source="address2"
                  fullWidth
                  label="Address2 / Complemento unidade"
                />
                <TextInput
                  source="district"
                  fullWidth
                  label="District / Bairro unidade"
                />
                <TextInput
                  source="city"
                  fullWidth
                  label="City / Cidade unidade"
                />
                <TextInput
                  source="state"
                  fullWidth
                  label="State / Estado unidade"
                />
                <TextInput
                  source="countryCode"
                  fullWidth
                  label="Country code / País"
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Consumption">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6">
                  Consumption / Consumo (kWh)
                </Typography>
                <CustomNumberInput
                  source="janConsumption"
                  fullWidth
                  label="January / Janiero"
                />
                <CustomNumberInput
                  source="febConsumption"
                  fullWidth
                  label="February / Fevereiro"
                />
                <CustomNumberInput
                  source="marConsumption"
                  fullWidth
                  label="March / Março"
                />
                <CustomNumberInput
                  source="aprConsumption"
                  fullWidth
                  label="April / Abril"
                />
                <CustomNumberInput
                  source="mayConsumption"
                  fullWidth
                  label="May / Maio"
                />
                <CustomNumberInput
                  source="junConsumption"
                  fullWidth
                  label="June / Junho"
                />
                <CustomNumberInput
                  source="julConsumption"
                  fullWidth
                  label="July / Julho"
                />
                <CustomNumberInput
                  source="augConsumption"
                  fullWidth
                  label="August / Agosto"
                />
                <CustomNumberInput
                  source="sepConsumption"
                  fullWidth
                  label="September / Setembro"
                />
                <CustomNumberInput
                  source="octConsumption"
                  fullWidth
                  label="October / Outubro"
                />
                <CustomNumberInput
                  source="novConsumption"
                  fullWidth
                  label="November / Novembro"
                />
                <CustomNumberInput
                  source="decConsumption"
                  fullWidth
                  label="December / Dezembro"
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Terms of Adhesion">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Grid
                  container
                  style={{ width: '100%' }}
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Grid item>
                    <Typography variant="h6">
                      Terms of Adhesion / Termos de adesão
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      component={Link}
                      to="/BrTermsOfAdhesion/create"
                    >
                      Create Terms of Adhesion
                    </Button>
                  </Grid>
                </Grid>
                <FunctionField
                  label="Terms of Adhesion"
                  render={(record) => {
                    return (
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>
                              <b>Power Plan</b>
                            </TableCell>
                            <TableCell>
                              <b>Discount Rate Adjustments</b>
                            </TableCell>
                            <TableCell>
                              <b>Start Date</b>
                            </TableCell>
                            <TableCell>
                              <b>End Date</b>
                            </TableCell>
                            <TableCell>
                              <b>Signature Date</b>
                            </TableCell>
                            <TableCell>
                              <b>Download</b>
                            </TableCell>
                            <TableCell>
                              <b>Edit</b>
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {record?.brTermsOfAdhesions?.map((toa) => (
                            <TableRow>
                              <TableCell>{toa.brPowerPlan?.name}</TableCell>
                              <TableCell>
                                {toa.brPromoDiscounts?.map(
                                  (brPromoDiscount) => (
                                    <Chip
                                      label={brPromoDiscount.label}
                                      style={{ margin: '.25rem' }}
                                    />
                                  )
                                )}
                              </TableCell>
                              <TableCell>
                                {toa.startDt
                                  ? moment(toa.startDt).format('MMM D, YYYY')
                                  : null}
                              </TableCell>
                              <TableCell>
                                {toa.endDt
                                  ? moment(toa.endDt).format('MMM D, YYYY')
                                  : null}
                              </TableCell>
                              <TableCell>
                                {toa.signatureDt
                                  ? moment(toa.signatureDt).format(
                                      'MMM D, YYYY'
                                    )
                                  : null}
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  disabled={!toa.downloadUrl}
                                  href={toa.downloadUrl}
                                  size="large"
                                >
                                  <CloudDownload />
                                </IconButton>
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  href={`/BrTermsOfAdhesion/${toa.id}`}
                                  size="large"
                                >
                                  <EditIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Utility Bills">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Grid
                  container
                  style={{ width: '100%' }}
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Grid item>
                    <Typography variant="h6">Utility Bills</Typography>
                  </Grid>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      component="label" // https://stackoverflow.com/a/54043619
                      startIcon={<CloudUpload />}
                      style={{ textTransform: 'none' }}
                      disabled={loading}
                    >
                      {loading ? (
                        <CircularProgress style={{ position: 'absolute' }} />
                      ) : null}
                      Upload Utility Bill
                      <input
                        type="file"
                        hidden
                        onChange={(event) => uploadFileToS3(event)}
                        accept="image/png, image/jpeg, image/jpg, application/pdf"
                      />
                    </Button>
                  </Grid>
                </Grid>
                <FunctionField
                  label="Utility Bill"
                  render={(record) => {
                    return (
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>
                              <b>Uploaded Date</b>
                            </TableCell>
                            <TableCell>
                              <b>Download</b>
                            </TableCell>
                            <TableCell>
                              <b>Edit</b>
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {record?.brUtilityBills?.map((utilityBill) => (
                            <TableRow>
                              <TableCell>
                                {moment(utilityBill.createdAt).format(
                                  'MMM D, YYYY HH:mm:ss Z'
                                )}
                              </TableCell>
                              <TableCell>
                                <Button
                                  disabled={!utilityBill.downloadUrl}
                                  href={utilityBill.downloadUrl}
                                  endIcon={<CloudDownload />}
                                  style={{ textTransform: 'none' }}
                                >
                                  Download
                                </Button>
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  href={`/BrUtilityBill/${utilityBill.id}`}
                                  size="large"
                                >
                                  <EditIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Invoices">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Typography variant="h6">
                  Invoices {invoicesLoading ? '' : `(${invoices?.length || 0})`}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                {invoicesLoading ? (
                  <CircularProgress />
                ) : (
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>
                          <b>Billing Cycle</b>
                        </TableCell>
                        <TableCell>
                          <b>Due Date</b>
                        </TableCell>
                        <TableCell>
                          <b>Amount Due</b>
                        </TableCell>
                        <TableCell>
                          <b>Amount Paid</b>
                        </TableCell>
                        <TableCell>
                          <b>Status</b>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {invoices?.map((invoice) => (
                        <TableRow
                          component={Link}
                          to={`/BrInvoice/${invoice.id}`}
                          hover
                        >
                          <TableCell>
                            <a
                              href={`/BrBillingCycle/${invoice.brBillingCycle?.id}`}
                              target="_blank"
                            >
                              {invoice.brBillingCycle?.label}
                            </a>
                          </TableCell>
                          <TableCell>
                            {invoice.dueDt &&
                              moment(invoice.dueDt).format('MMM D, YYYY')}
                          </TableCell>
                          <TableCell>
                            {numeral(invoice.amountDue).format('$0,0.00')}
                          </TableCell>
                          <TableCell>
                            {numeral(invoice.amountPaid).format('$0,0.00')}
                          </TableCell>
                          <TableCell>
                            <Grid
                              container
                              direction="column"
                              justifyContent="center"
                              alignItems="center"
                            >
                              <Grid item>
                                <Avatar
                                  style={{
                                    backgroundColor:
                                      invoice.paymentStatus.iconColor,
                                  }}
                                >
                                  <Icon
                                    className={invoice.paymentStatus.iconClass}
                                    style={{
                                      color: '#fff',
                                      width: 'auto',
                                    }}
                                  />
                                </Avatar>
                              </Grid>
                              <Grid item>
                                <Typography variant="body2" align="center">
                                  {invoice.paymentStatus.label}
                                </Typography>
                              </Grid>
                            </Grid>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Notes">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Typography variant="h6">Notes</Typography>
              </Grid>
              <Grid item xs={12}>
                <FunctionField
                  label="Notes"
                  render={(record) => {
                    if (!permissions?.employee?.id) return null;
                    const saveNewNoteDisabled =
                      !newNote || newNote.length === 0;
                    return (
                      <>
                        <MuiTextField
                          id="new-note-text-input"
                          label="New note"
                          multiline
                          fullWidth
                          value={newNote || ''}
                          onChange={(event) => setNewNote(event.target.value)}
                          style={{ marginBottom: '1rem' }}
                          InputProps={{
                            endAdornment: saveNewNoteDisabled ? null : (
                              <IconButton
                                onClick={() => {
                                  handleCreateNote();
                                }}
                                disabled={saveNewNoteDisabled}
                                style={{
                                  backgroundColor: saveNewNoteDisabled
                                    ? null
                                    : theme.palette.green.main,
                                  color: saveNewNoteDisabled ? null : 'white',
                                }}
                              >
                                <ArrowForward />
                              </IconButton>
                            ),
                          }}
                        />
                        {record?.brNotes?.length ? (
                          record.brNotes.map((note, index) => (
                            <Grid
                              container
                              style={{
                                borderBottom:
                                  index < record.brNotes.length - 1
                                    ? '1px solid #ddd'
                                    : null,
                                padding: '.5rem',
                              }}
                              justifyContent="space-between"
                              alignItems="center"
                            >
                              <Grid item>
                                <Grid item xs={12}>
                                  <Typography variant="body1">
                                    {note.note}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} variant="body2">
                                  {note.author?.fullName || 'Energea'} -{' '}
                                  {moment(note.createdAt).format(
                                    'MMM D, YYYY HH:mm:ss'
                                  )}
                                </Grid>
                              </Grid>
                              <Grid item>
                                {permissions.employee.id ===
                                  note.author?.id && (
                                  <>
                                    <IconButton
                                      size="large"
                                      onClick={() => {
                                        setEditNoteText(note.note);
                                        setEditNoteDialogOpen(true);
                                      }}
                                    >
                                      <EditIcon />
                                    </IconButton>
                                    <Dialog
                                      open={editNoteDialogOpen}
                                      onClose={() =>
                                        setEditNoteDialogOpen(false)
                                      }
                                    >
                                      <DialogTitle>Edit Note</DialogTitle>
                                      <DialogContent>
                                        <MuiTextField
                                          id="update-note-text-input"
                                          label="Edit note"
                                          multiline
                                          fullWidth
                                          value={editNoteText || ''}
                                          onChange={(event) =>
                                            setEditNoteText(event.target.value)
                                          }
                                          style={{ marginBottom: '1rem' }}
                                        />
                                      </DialogContent>
                                      <DialogActions>
                                        <Button
                                          onClick={() =>
                                            setEditNoteDialogOpen(false)
                                          }
                                        >
                                          Cancel
                                        </Button>
                                        <Button
                                          onClick={() =>
                                            handleDeleteNote(note.id)
                                          }
                                          color="error"
                                          variant="contained"
                                        >
                                          Delete
                                        </Button>
                                        <Button
                                          onClick={() =>
                                            handleUpdateNote(note.id)
                                          }
                                          disabled={
                                            !editNoteText ||
                                            editNoteText.length === 0
                                          }
                                          color="primary"
                                          variant="contained"
                                        >
                                          Save
                                        </Button>
                                      </DialogActions>
                                    </Dialog>
                                  </>
                                )}
                              </Grid>
                            </Grid>
                          ))
                        ) : (
                          <Alert severity="info">No notes yet.</Alert>
                        )}
                      </>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Tickets">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <Grid
                  container
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Grid item>
                    <Typography variant="h6">Tickets</Typography>
                  </Grid>
                  <Grid item>
                    <Button
                      color="primary"
                      variant="contained"
                      component={Link}
                      to="/BrTicket/create"
                    >
                      Create Ticket
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} style={{ marginTop: '1rem' }}>
                <FunctionField
                  label="Tickets"
                  render={(record) => {
                    return (
                      <>
                        {record?.brTickets?.length ? (
                          record.brTickets.map((ticket, index) => (
                            <Grid
                              container
                              style={{
                                borderBottom:
                                  index < record.brTickets.length - 1
                                    ? '1px solid #ddd'
                                    : null,
                                padding: '.5rem',
                              }}
                              justifyContent="space-between"
                              alignItems="center"
                            >
                              <Grid item>
                                <Grid item xs={12}>
                                  <Typography variant="body1">
                                    {`#${ticket.ticketNumber} - ${ticket.brTicketType.name} - ${ticket.description}`}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} variant="body2">
                                  {`Opened: ${moment(ticket.openedDt).format(
                                    'MMM D, YYYY'
                                  )} - Closed: ${
                                    ticket.closedDt
                                      ? moment(ticket.closedDt).format(
                                          'MMM D, YYYY'
                                        )
                                      : '---'
                                  }`}
                                </Grid>
                              </Grid>
                              <Grid item>
                                <IconButton
                                  component={Link}
                                  variant="contained"
                                  color="primary"
                                  size="large"
                                  to={`/BrTicket/${ticket.id}`}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Grid>
                            </Grid>
                          ))
                        ) : (
                          <Alert severity="info">No tickets yet.</Alert>
                        )}
                      </>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
        </TabbedForm>
      </Edit>
      <Dialog
        open={customerCreateDialogOpen}
        onClose={() => setCustomerCreateDialogOpen(false)}
      >
        <BrCustomerCreate
          withinDialog={true}
          onSuccess={() => {
            setCustomerCreateDialogOpen(false);
            refresh();
          }}
        />
      </Dialog>
    </>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Name, Utility Customer Code, or Installation Code"
      source="q"
      alwaysOn
    />
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by CPF or CNPJ"
      source="cpfCnpj"
      alwaysOn
    />
    <ReferenceInput
      perPage={10_000}
      source="brCustomer.id"
      reference="BrCustomer"
      label="Customer"
    >
      <AutocompleteInput
        allowEmpty={true}
        optionText="name"
        label="Customer"
        fullWidth
        style={{ minWidth: '420px' }}
      />
    </ReferenceInput>
    <ReferenceInput
      label="Stage"
      source="brConsumerUnitStage.id"
      reference="BrConsumerUnitStage"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <SelectInput label="Stage" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Project"
      source="salesforceProject.id"
      reference="SalesforceProject"
      perPage={10_000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Sales Partner"
      source="brSalesPerson.id"
      reference="BrSalesPerson"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <AutocompleteInput
        allowEmpty={true}
        optionText="name"
        label="Sales Partner"
        fullWidth
      />
    </ReferenceInput>
    <ReferenceInput
      label="Sales Person"
      source="salesPersonBrContact.id"
      reference="BrContact"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
      filter={{ isSalesPerson: true }}
    >
      <AutocompleteInput
        allowEmpty={true}
        optionText="fullName"
        label="Sales Person"
        fullWidth
      />
    </ReferenceInput>
    <BooleanInput label="Has signed ToA" source="hasSignedToa" />
  </Filter>
);

// const exporter = (rows) => {
//   const attrs = [
//     {
//       label: 'Número de Instalação',
//       value: (record) => record.installationCode,
//     },
//     {
//       label: 'Número de Cliente',
//       value: (record) => record.utilityCustomerCode,
//     },
//     {
//       label: 'Tipo',
//       value: (record) => record.brCustomer.type,
//     },
//     {
//       label: 'Documento',
//       value: (record) =>
//         record.brCustomer.type?.toLowerCase() === 'cpf'
//           ? record.brCustomer.cpf
//           : record.brCustomer.cnpj,
//     },
//     {
//       label: 'Cliente',
//       value: (record) => record.name,
//     },
//     { label: 'Status', value: (record) => record.brConsumerUnitStage?.name },
//     {
//       label: 'Parceiro',
//       value: (record) => record.brSalesPerson?.name,
//     },
//     {
//       label: 'Vendedor',
//       value: (record) => record.salesPersonBrContact?.fullName,
//     },
//     {
//       label: 'Projeto',
//       value: (record) => record?.salesforceProject?.name,
//     },
//     {
//       label: 'Desconto',
//       value: (record) => record.currentDiscountRate,
//     },
//     {
//       label: 'Conexão',
//       value: (record) => record.brVoltagePhase?.name,
//     },
//     {
//       label: 'Average Consumption',
//       value: (record) => record.avgMonthlyConsumption,
//     },
//     {
//       label: 'Adjusted Average Consumption',
//       value: (record) => record.adjustedAvgMonthlyConsumption,
//     },
//     {
//       label: 'Start Date',
//       value: (record) =>
//         record.startDt && moment(record.startDt).format('MMM D, YYYY'),
//     },
//     {
//       label: 'Address 1',
//       value: (record) => record.address1,
//     },
//     {
//       label: 'Address 2',
//       value: (record) => record.address2,
//     },
//     {
//       label: 'Bairro Unidade',
//       value: (record) => record.district,
//     },
//     {
//       label: 'Cidade Unidade',
//       value: (record) => record.city,
//     },
//     {
//       label: 'Estado Unidade',
//       value: (record) => record.state,
//     },
//     {
//       label: 'CEP Unidade',
//       value: (record) => record.postalCode,
//     },
//     {
//       label: 'Previous Consortium Contract Termination Dt',
//       value: (record) =>
//         record.previousConsortiumContractTerminationDt
//           ? moment(record.previousConsortiumContractTerminationDt).format(
//               'MMM D, YYYY'
//             )
//           : null,
//     },
//     {
//       label: 'Created Date',
//       value: (record) =>
//         moment(record.createdAt).format('MMM D, YYYY HH:mm:ss'),
//     },
//     {
//       label: 'Terms of adhesion download link',
//       value: (record) => {
//         const filteredTermsOfAdhesions = record.brTermsOfAdhesions.filter(
//           (toa) => toa.startDt && toa.signatureDt && toa.downloadUrl
//         );
//         filteredTermsOfAdhesions.sort((a, b) =>
//           a.startDt < b.startDt ? 1 : -1
//         );
//         return filteredTermsOfAdhesions?.[0]?.downloadUrl;
//       },
//     },
//   ];

//   const rowsForExport = rows.map((row) => {
//     const returnRow = {};
//     attrs.forEach((attr) => {
//       returnRow[String(attr.label)] = attr.value(row);
//     });
//     return returnRow;
//   });
//   return downloadSimpleExcelFromRows(
//     rowsForExport,
//     attrs.map((attr) => attr.label),
//     `ConsumerUnits.xlsx`
//   );
// };

const getIssues = (record) => {
  if (!record) {
    return [];
  }
  const {
    utilityCompany,
    brTariffClass,
    brTermsOfAdhesions,
    brVoltagePhase,
    brSalesPerson,
    brCustomer,
    salesforceProject,
    avgMonthlyConsumption,
    brSelfConsumptionOfftaker,
    brConsumerUnitStage,
  } = record;
  const issues = [];

  if ([4, 5].includes(brConsumerUnitStage?.id)) {
    if (
      brTermsOfAdhesions?.filter(
        (el) => el.signatureDt && el.startDt && !el.endDt
      ).length > 0
    ) {
      issues.push({
        reason: 'Cancelled CU has active terms of adhesion',
        severity: 'error',
      });
    }
  }

  if (brConsumerUnitStage?.id && ![4, 5].includes(brConsumerUnitStage?.id)) {
    if (
      !brTermsOfAdhesions ||
      brTermsOfAdhesions.filter(
        (el) => el.signatureDt && el.startDt && !el.endDt && el.downloadUrl
      ).length < 1
    ) {
      issues.push({
        reason: 'Missing valid terms of adhesion',
        severity: 'error',
      });
    }
  }
  if (!brCustomer) {
    issues.push({ reason: 'Not assigned to a customer', severity: 'error' });
  }
  if (avgMonthlyConsumption === 0) {
    issues.push({
      reason: 'Average monthly consumption is 0',
      severity: 'error',
    });
  }
  if (!utilityCompany && !brSelfConsumptionOfftaker) {
    issues.push({
      reason: 'Not assigned to a utility company',
      severity: 'error',
    });
  }
  if (!brTariffClass) {
    issues.push({
      reason: 'Not assigned to a tariff class',
      severity: 'error',
    });
  }
  if (!brVoltagePhase) {
    issues.push({
      reason: 'Not assigned to a voltage phase',
      severity: 'error',
    });
  }
  if (!brTermsOfAdhesions) {
    issues.push({
      reason: 'Not assigned to a terms of adhesion',
      severity: 'error',
    });
  }
  if (!brSalesPerson) {
    issues.push({
      reason: 'Not assigned to a sales partner',
      severity: 'warning',
    });
  }
  if (!salesforceProject) {
    issues.push({ reason: 'Not assigned to a project', severity: 'error' });
  }
  return issues;
};

const styleRow = (record, index) => {
  const issues = getIssues(record);

  const errorStyle = {
    backgroundColor: 'rgba(255,0,0,.2)',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  // If they're cancelled or cancelling, don't worry about flagging errors
  if (issues.filter((el) => el.severity === 'error').length > 0) {
    return errorStyle;
  }
  if (issues.filter((el) => el.severity === 'warning').length > 0) {
    return warningStyle;
  }
  return {};
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      <EmailExportButton
        {...props}
        resource={resource}
        filterValues={filterValues}
      />
    </TopToolbar>
  );
};

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100]} />
);

export const BrConsumerUnitList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <Grid container style={{ marginTop: '1rem' }}>
        {definitionJsx}
      </Grid>
      <List
        title={entityName}
        perPage={25}
        filters={<CustomFilter />}
        actions={<ListActions />}
        // exporter={exporter}
        sort={{ field: 'id', order: 'DESC' }}
        pagination={<CustomPagination />}
      >
        <Datagrid
          rowStyle={styleRow}
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="name" label="Name / Nome" />
          <LinkField
            reference="BrCustomer"
            linkSource="brCustomer.id"
            labelSource="brCustomer.name"
            label="Customer"
            sortable={false}
          />
          <LinkField
            reference="BrSelfConsumptionOfftaker"
            linkSource="brSelfConsumptionOfftaker.id"
            labelSource="brSelfConsumptionOfftaker.name"
            label="Self-Consumption Offtaker"
            sortable={false}
          />
          <FunctionField
            align="center"
            label="Stage"
            style={{ width: '100%' }}
            render={(record) => {
              if (record.brConsumerUnitStage) {
                return (
                  <Chip
                    variant="caption"
                    style={{
                      backgroundColor: record.brConsumerUnitStage.color,
                      color: '#fff',
                    }}
                    label={record.brConsumerUnitStage.name}
                  />
                );
              }
              return null;
            }}
          />
          <TextField
            source="onboardingStatus.status"
            label="Onboarding status / Status de integração"
            sortable={false}
          />
          <LinkField
            reference="BrSalesPerson"
            linkSource="brSalesPerson.id"
            labelSource="brSalesPerson.name"
            label="Sales Partner / Parceiro"
            sortable={false}
          />
          <LinkField
            reference="BrContact"
            linkSource="salesPersonBrContact.id"
            labelSource="salesPersonBrContact.fullName"
            label="Sales Person / Vendedor"
            sortable={false}
          />
          <DetailField source="salesPartnerNotes" sortable={false} />
          <TextField
            source="utilityCustomerCode"
            label="Utility customer code / Código do cliente"
          />
          <TextField
            source="installationCode"
            label="Installation code / Código instalação"
          />
          <TextField source="postalCode" label="Postal code / CEP unidade" />
          <TextField source="address1" label="Address1 / Endereço" />
          <TextField source="address2" label="Address2 / Complemento unidade" />
          <TextField source="district" label="District / Bairro unidade" />
          <TextField source="city" label="City / Cidade unidade" />
          <TextField source="state" label="State / Estado unidade" />
          {/* <TextField source="countryCode" label="Country code / País" /> */}
          <TextField source="preferredPaymentMethod" />
          <DateField source="previousConsortiumContractTerminationDt" />
          <LinkField
            reference="UtilityCompany"
            linkSource="utilityCompany.id"
            labelSource="utilityCompany.name"
            label="Utility Company / Distribuidora"
            sortable={false}
          />
          <LinkField
            reference="BrTariffClass"
            linkSource="brTariffClass.id"
            labelSource="brTariffClass.name"
            label="Tariff Class / Classe tarifária"
            sortable={false}
          />
          <LinkField
            reference="BrVoltagePhase"
            linkSource="brVoltagePhase.id"
            labelSource="brVoltagePhase.name"
            label="Voltage Phase / Conexão"
            sortable={false}
          />
          <FunctionField
            align="center"
            label="Monthly Consumption / Consumo (kWh)"
            style={{ width: '100%' }}
            render={(record) => {
              if (!record) return null;
              const data = [
                {
                  label: 'January Consumption',
                  value: numeral(record.janConsumption).format('0,0'),
                },
                {
                  label: 'February Consumption',
                  value: numeral(record.febConsumption).format('0,0'),
                },
                {
                  label: 'March Consumption',
                  value: numeral(record.marConsumption).format('0,0'),
                },
                {
                  label: 'April Consumption',
                  value: numeral(record.aprConsumption).format('0,0'),
                },
                {
                  label: 'May Consumption',
                  value: numeral(record.mayConsumption).format('0,0'),
                },
                {
                  label: 'June Consumption',
                  value: numeral(record.junConsumption).format('0,0'),
                },
                {
                  label: 'July Consumption',
                  value: numeral(record.julConsumption).format('0,0'),
                },
                {
                  label: 'August Consumption',
                  value: numeral(record.augConsumption).format('0,0'),
                },
                {
                  label: 'September Consumption',
                  value: numeral(record.sepConsumption).format('0,0'),
                },
                {
                  label: 'October Consumption',
                  value: numeral(record.octConsumption).format('0,0'),
                },
                {
                  label: 'November Consumption',
                  value: numeral(record.novConsumption).format('0,0'),
                },
                {
                  label: 'December Consumption',
                  value: numeral(record.decConsumption).format('0,0'),
                },
              ];
              const text = (
                <>
                  <Grid container>
                    {data.map((dataPoint, i) => (
                      <Grid
                        key={`data-point-key-${dataPoint.label}`}
                        container
                        // style={{
                        //   borderTop:
                        //     i === data.length - 1 ? 'thin solid #fff' : null,
                        // }}
                        justifyContent="space-between"
                      >
                        <Grid item>
                          <Typography>
                            <b>{dataPoint.label} : </b>
                          </Typography>
                        </Grid>
                        <Grid item>
                          <Typography>{dataPoint.value}</Typography>
                        </Grid>
                      </Grid>
                    ))}
                  </Grid>
                </>
              );
              return (
                <Tooltip title={text}>
                  <IconButton size="large">
                    <Subject />
                  </IconButton>
                </Tooltip>
              );
            }}
          />
          <NumberField
            source="avgMonthlyConsumption"
            sortable={false}
            label="Average Consumption (kWh)"
          />
          <NumberField
            source="adjustedAvgMonthlyConsumption"
            sortable={false}
            label="Adjusted Avg Consumption (kWh)"
          />
          <FunctionField
            align="center"
            label="Consumption Seasonality as % off max"
            style={{ width: '100%' }}
            render={(record) => {
              const { consumptionSeasonalityAsPercentageOffMax } = record;
              if (consumptionSeasonalityAsPercentageOffMax === null) {
                return null;
              }
              let style = {};
              if (consumptionSeasonalityAsPercentageOffMax > 50) {
                style = { fontWeight: 'bold', color: 'red' };
              } else if (consumptionSeasonalityAsPercentageOffMax > 40) {
                style = { fontWeight: 'bold', color: 'orange' };
              }
              return (
                <span style={style}>{`${Math.round(
                  consumptionSeasonalityAsPercentageOffMax
                )}%`}</span>
              );
            }}
          />
          <FunctionField
            align="center"
            label="Percentage of Consumption Data Inputted"
            style={{ width: '100%' }}
            render={(record) => {
              const { percentageOfConsumptionDataInput } = record;
              if (percentageOfConsumptionDataInput === null) {
                return null;
              }
              let style = {};
              if (percentageOfConsumptionDataInput < 10) {
                style = { fontWeight: 'bold', color: 'red' };
              } else if (percentageOfConsumptionDataInput < 50) {
                style = { fontWeight: 'bold', color: 'orange' };
              } else if (percentageOfConsumptionDataInput === 100) {
                style = { fontWeight: 'bold', color: 'green' };
              }
              return (
                <span style={style}>{`${Math.round(
                  percentageOfConsumptionDataInput
                )}%`}</span>
              );
            }}
          />
          <NumberField
            source="amountDue"
            sortable={false}
            label="Amount due / Valor devido (R$)"
          />
          <NumberField
            source="pastDue"
            sortable={false}
            label="Past due / Vencido (R$)"
          />
          <CustomBooleanField
            source="hasInvoiceSentToCollections"
            sortable={false}
            label="Has invoice sent to SERASA / Fatura enviada para SRARA"
          />
          <CustomBooleanField
            source="invoiceOfflineFlg"
            label="Invoice offline flg / Não emitir boleto"
          />
          <NumberField
            source="currentDiscountRate"
            sortable={false}
            label="Current Discount Rate / Desconto (%)"
          />
          <ArrayField
            source="brTermsOfAdhesions"
            label="All Terms of Adhesion / Todos os termos de adesão"
          >
            <SingleFieldList>
              <CustomReferenceField
                source="label"
                linkOverride={(record) => `/BrTermsOfAdhesion/${record.id}`}
              />
            </SingleFieldList>
          </ArrayField>
          <LinkField
            reference="SalesforceProject"
            linkSource="salesforceProject.id"
            labelSource="salesforceProject.name"
            label="Project / Projeto"
            sortable={false}
          />
          <FunctionField
            label="Account access"
            render={(record) => {
              return (
                <Button
                  component={Link}
                  to={`/BrCustomer/${record.brCustomer.id}/contacts`}
                  variant="contained"
                  color="primary"
                  onClick={(e) => e.stopPropagation()}
                >
                  Manage Login Access
                </Button>
              );
            }}
          />
          <DateField source="createdAt" showTime={true} />
          <DateField source="updatedAt" showTime={true} />
          <FunctionField
            label="Issue Descriptions"
            render={(record) => {
              const issues = getIssues(record);
              return (
                <ol>
                  {issues.map((el, i) => (
                    <li key={`brConsumerUnit-validation-${i}`}>{el.reason}</li>
                  ))}
                </ol>
              );
            }}
          />
        </Datagrid>
      </List>
    </>
  );
};

export const BrConsumerUnitCreate = () => {
  const [customerCreateDialogOpen, setCustomerCreateDialogOpen] =
    useState(false);
  const refresh = useRefresh();

  const CustomerReferenceInput = () => {
    const { setValue } = useFormContext();
    return (
      <>
        <ReferenceInput
          source="brCustomer.id"
          reference="BrCustomer"
          perPage={10_000}
          label="Customer"
          sort={{ field: 'name', order: 'ASC' }}
        >
          <AutocompleteInput
            optionText="name"
            label="Customer"
            fullWidth
            validate={required()}
            helperText={
              <Typography variant="caption">
                To add a new customer,{' '}
                <a
                  onClick={() => {
                    setCustomerCreateDialogOpen(true);
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  click here.
                </a>{' '}
                Para adicionar um novo cliente,{' '}
                <a
                  onClick={() => {
                    setCustomerCreateDialogOpen(true);
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  clique aqui.
                </a>
              </Typography>
            }
          />
        </ReferenceInput>
        <Dialog
          open={customerCreateDialogOpen}
          onClose={() => setCustomerCreateDialogOpen(false)}
        >
          <BrCustomerCreate
            withinDialog={true}
            onSuccess={(newCustomerId) => {
              setCustomerCreateDialogOpen(false);
              setValue('brCustomer.id', newCustomerId);
              refresh();
            }}
          />
        </Dialog>
      </>
    );
  };

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            {definitionJsx}
            <CustomerReferenceInput />
            <TextInput
              source="installationCode"
              fullWidth
              required
              label="Installation code / Código instalação"
            />
            <TextInput
              source="utilityCustomerCode"
              fullWidth
              label="Utility customer code / Código do cliente"
            />
            <StageReferenceInput />
            <ReferenceInput
              source="brSalesPerson.id"
              reference="BrSalesPerson"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <AutocompleteInput
                optionText="name"
                label="Sales Partner / Parceiro"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData }) => {
                if (!formData.brSalesPerson?.id) {
                  return null;
                }
                return (
                  <ReferenceInput
                    source="salesPersonBrContact.id"
                    reference="BrContact"
                    perPage={10_000}
                    sort={{ field: 'id', order: 'ASC' }}
                    filter={{
                      brSalesPerson: { id: formData.brSalesPerson.id },
                    }}
                  >
                    <AutocompleteInput
                      optionText="fullName"
                      label="Sales Person / Vendedor"
                      fullWidth
                      allowEmpty
                    />
                  </ReferenceInput>
                );
              }}
            </FormDataConsumer>
            <ReferenceInput
              source="utilityCompany.id"
              reference="UtilityCompany"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Utility Company / Distribuidora"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="brTariffClass.id"
              reference="BrTariffClass"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
              filter={{ excludeB1B3: true }}
            >
              <SelectInput
                optionText="name"
                label="Tariff Class / Classe tarifária"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="brVoltagePhase.id"
              reference="BrVoltagePhase"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Voltage Phase / Conexão"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <Card style={{ padding: '1rem' }}>
              <Typography variant="h6">Address / Endereço</Typography>
              <TextInput
                source="postalCode"
                fullWidth
                label="Postal code / CEP unidade"
              />
              <TextInput
                source="address1"
                fullWidth
                label="Address1 / Endereço"
              />
              <TextInput
                source="address2"
                fullWidth
                label="Address2 / Complemento unidade"
              />
              <TextInput
                source="district"
                fullWidth
                label="District / Bairro unidade"
              />
              <TextInput
                source="city"
                fullWidth
                label="City / Cidade unidade"
              />
              <TextInput
                source="state"
                fullWidth
                label="State / Estado unidade"
              />
              <TextInput
                source="countryCode"
                fullWidth
                label="Country code / País"
              />
            </Card>
            <Card style={{ padding: '1rem', marginTop: '1rem' }}>
              <Typography variant="h6">Consumption / Consumo (kWh)</Typography>
              <CustomNumberInput
                source="janConsumption"
                fullWidth
                label="January / Janiero"
              />
              <CustomNumberInput
                source="febConsumption"
                fullWidth
                label="February / Fevereiro"
              />
              <CustomNumberInput
                source="marConsumption"
                fullWidth
                label="March / Março"
              />
              <CustomNumberInput
                source="aprConsumption"
                fullWidth
                label="April / Abril"
              />
              <CustomNumberInput
                source="mayConsumption"
                fullWidth
                label="May / Maio"
              />
              <CustomNumberInput
                source="junConsumption"
                fullWidth
                label="June / Junho"
              />
              <CustomNumberInput
                source="julConsumption"
                fullWidth
                label="July / Julho"
              />
              <CustomNumberInput
                source="augConsumption"
                fullWidth
                label="August / Agosto"
              />
              <CustomNumberInput
                source="sepConsumption"
                fullWidth
                label="September / Setembro"
              />
              <CustomNumberInput
                source="octConsumption"
                fullWidth
                label="October / Outubro"
              />
              <CustomNumberInput
                source="novConsumption"
                fullWidth
                label="November / Novembro"
              />
              <CustomNumberInput
                source="decConsumption"
                fullWidth
                label="December / Dezembro"
              />
            </Card>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
