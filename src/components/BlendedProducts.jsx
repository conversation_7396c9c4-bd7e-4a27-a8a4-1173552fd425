import React from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrayField,
  ArrayInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SimpleFormIterator,
  SingleFieldList,
  TextField,
  TextInput,
  DateField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import {
  CustomNumberInput,
  CustomReferenceField,
} from './CustomFields';

import { Grid } from '@mui/material';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Blended Product';

export const BlendedProductEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
            <TextInput source="summary" fullWidth />
            <BooleanInput source="isPublic" fullWidth />
            <TextInput source="internalNotes" fullWidth />
            <SelectInput
              source="portfolioBreakdown"
              required
              fullWidth
              label="Portfolio Breakdown Type"
              choices={[
                { id: 'evenly', name: 'Evenly' },
                { id: 'evenCapSpace', name: 'Even Cap Space' },
                { id: 'custom', name: 'Custom' },
              ]}
            />
            <ArrayInput source="blendedProductPortfolios" fullWidth>
              <SimpleFormIterator
                TransitionProps={{ enter: false, exit: false }}
              >
                <CustomNumberInput
                  label="Percentage"
                  source="percentage"
                  fullWidth
                />
                <CustomNumberInput
                  disabled
                  label="Calculated Percentage"
                  source="calculatedPercentage"
                  fullWidth
                />
                <BooleanInput
                  source="lowCapSpaceFlg"
                  label="Low Cap Space Flag"
                  fullWidth
                  helperText="Displays a low cap space message on the blended product frontend"
                />
                <BooleanInput
                  source="closedFlg"
                  label="Closed Flag"
                  fullWidth
                  helperText="Shows the portfolio but doesn't allow them to move it off of '0'. A closed portfolio should use a '0' as the percentage."
                />
                <ReferenceInput source="portfolio.id" reference="PortfolioLite">
                  <SelectInput
                    label="Portfolio"
                    required
                    fullWidth
                    optionText="name"
                  />
                </ReferenceInput>
              </SimpleFormIterator>
            </ArrayInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BlendedProductList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <TextField source="portfolioBreakdown" />
        <BooleanField source="isPublic" />
        <ArrayField source="blendedProductPortfolios" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="label" />
          </SingleFieldList>
        </ArrayField>
        <NumberField source="orderNo" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const BlendedProductCreate = () => (
  <Create
    title={`Create ${entityName}`}
    helperText="This can be edited at any time so no need to be perfect."
  >
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
