import React from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Country';

export const CountryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput fullWidth source="name" />
            <TextInput fullWidth source="code" />
            <TextInput fullWidth source="currencyCode" />
            <BooleanInput fullWidth source="autoUpdateExchangeRateFlg" />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const CountryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="code" />
        <TextField source="currencyCode" />
        <BooleanField source="autoUpdateExchangeRateFlg" />
      </Datagrid>
    </List>
  );
};

export const CountryCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
          <TextInput source="code" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
