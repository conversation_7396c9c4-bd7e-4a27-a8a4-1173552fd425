import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON>reate,
  <PERSON>grid,
  DateField,
  Edit,
  Filter,
  FunctionField,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useNotify,
  usePermissions,
  useRedirect,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CloudDownload } from '@mui/icons-material';

const entityName = 'O&M Monthly Report';

export const OMMonthlyReportEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="month" fullWidth />
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
              filter={{ omFlg: true }}
            >
              <SelectInput
                required
                optionText="name"
                label="Project"
                fullWidth
              />
            </ReferenceInput>
            <FunctionField
              label="Report"
              render={(record) => {
                if (record.downloadUrl) {
                  return (
                    <Button
                      variant="contained"
                      startIcon={<CloudDownload />}
                      style={{ textTransform: 'none' }}
                      onClick={() => window.location.assign(record.downloadUrl)}
                    >
                      Download Report
                    </Button>
                  );
                }
                return null;
              }}
            />
            <TextInput source="awsObjectKey" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const OMMonthlyReportFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="project.id"
      reference="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const OMMonthlyReportList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'id', order: 'DESC' }}
      filters={
        <OMMonthlyReportFilter sort={{ field: 'orderNo', order: 'ASC' }} />
      }
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Portfolio"
          linkSource="project.portfolio.id"
          labelSource="project.portfolio.name"
          label="Portfolio"
        />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="month" />
        <FunctionField
          label="Report"
          render={(record) => {
            if (record.downloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={(event) => {
                    event.stopPropagation();
                    window.location.assign(record.downloadUrl);
                  }}
                >
                  Download Report
                </Button>
              );
            }
            return null;
          }}
        />
        <DateField source="createdAt" showTime />
        <DateField source="updatedAt" showTime />
      </Datagrid>
    </List>
  );
};

export const OMMonthlyReportCreate = () => {
  const notify = useNotify();
  const redirect = useRedirect();

  const onSuccess = () => {
    notify(
      `Successfully initiated Monthly O&M Report creation. Message will be sent to Slack once complete.`,
      { type: 'success' }
    );
    redirect('/OMMonthlyReport');
  };

  return (
    <Create
      title={`Create ${entityName}`}
      undoable={false}
      redirect="list"
      mutationOptions={{ onSuccess: onSuccess }}
    >
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="month"
              required
              fullWidth
              helperText="M-YYYY Ex: 7-2023 for July 2023's report"
            />
            <ReferenceInput
              source="project.id"
              reference="Project"
              perPage={10000}
              sort={{ field: 'name', order: 'ASC' }}
              filter={{ omFlg: true }}
            >
              <SelectInput
                optionText="name"
                label="Project"
                fullWidth
                helperText="Leaving this field blank will run a new report for the above month for all projects with 'omFlg' set to true."
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
