import React from 'react';
import { useParams } from 'react-router-dom';
import classnames from 'classnames';
import {
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Chip, Grid, Icon, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Article Category';

export const ArticleCategoryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" required fullWidth />
            <TextInput source="description" required fullWidth />
            <TextInput source="iconClass" required fullWidth />
            <TextInput source="color" required fullWidth />
            <CustomNumberInput source="orderNo" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const ArticleCategoryList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'orderNo', order: 'ASC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <TextField source="color" />
        <FunctionField
          label="Appearance"
          sortable={false}
          render={(record) => (
            // <Icon
            //   style={{ width: '2rem', textAlign: 'center', color: '#666' }}
            //   className={record.iconClass}
            // />
            <>
              <Chip
                clickable
                icon={
                  <Icon
                    className={classnames('fas', record.iconClass)}
                    style={{
                      color: '#fff',
                      fontSize: '1rem',
                    }}
                    size="small"
                  />
                }
                label={
                  <Typography
                    variant="body2"
                    style={{
                      color: '#fff',
                    }}
                  >
                    {record.name}
                  </Typography>
                }
                variant="outlined"
                style={{
                  margin: '0.25em',
                  backgroundColor: record.color,
                  borderColor: record.color,
                  padding: '4px',
                }}
              />
              <Chip
                icon={
                  <Icon
                    className={classnames('fas', record.iconClass)}
                    style={{
                      color: record.color,
                      fontSize: '1rem',
                    }}
                    size="small"
                  />
                }
                label={
                  <Typography
                    variant="body2"
                    style={{
                      color: record.color,
                    }}
                  >
                    {record.name}
                  </Typography>
                }
                variant="outlined"
                style={{
                  margin: '0.25em',
                  borderColor: record.color,
                  padding: '4px',
                }}
              />
            </>
          )}
        />
        <NumberField source="orderNo" />
      </Datagrid>
    </List>
  );
};

export const ArticleCategoryCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="description" required fullWidth />
          <TextInput source="iconClass" required fullWidth />
          <TextInput
            source="color"
            helperText="Ex: #ff0000"
            required
            fullWidth
          />
          <CustomNumberInput source="orderNo" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
