import React, { useState } from 'react';
import { <PERSON>, useParams } from 'react-router-dom';
import moment from 'moment';
import {
  ArrayField,
  AutocompleteInput,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  List,
  NumberField,
  ReferenceArrayInput,
  ReferenceInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Alert,
  Button,
  CircularProgress,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';

import {
  CustomBooleanField,
  CustomReferenceField,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import {
  AddCircleOutline,
  CloudDownload,
  CloudUpload,
  Email,
  Star,
  Edit as EditIcon,
} from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import { useFormContext } from 'react-hook-form';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Sales Partner';

const StatusReferenceInput = ({ source, ...props }) => {
  const { getFieldState, getValues } = useFormContext();
  const [showNotification, setShowNotification] = useState(false);
  const [changedDt, setChangedDt] = useState(null);
  const { id, ...data } = getValues();
  const fieldState = getFieldState('brSalesPartnerStatus.id');

  // isDirty says whether or not the field has changed from its initial value
  const stageChangeIds = [3, 4];
  if (
    fieldState.isDirty &&
    !showNotification &&
    stageChangeIds.indexOf(data.brSalesPartnerStatus?.id) > -1
  ) {
    setShowNotification(true);
  }

  if (
    (!fieldState.isDirty ||
      stageChangeIds.indexOf(data.brSalesPartnerStatus?.id) === -1) &&
    showNotification
  ) {
    setShowNotification(false);
  }

  return (
    <>
      <ReferenceInput
        source="brSalesPartnerStatus.id"
        reference="BrSalesPartnerStatus"
        perPage={10_000}
        sort={{ field: 'id', order: 'ASC' }}
      >
        <SelectInput
          optionText="name"
          label="Status"
          fullWidth
          onChange={() => {
            // Use this to re-render this component and pick up on value changes
            setChangedDt(new Date());
          }}
        />
      </ReferenceInput>
      {showNotification && (
        <Alert severity="info" icon={false}>
          <FormControlLabel
            style={{ paddingLeft: '1rem' }}
            control={
              <BooleanInput
                source="sendStageChangeEmail"
                label="Click here to send an email to the sales partner about this stage change when you click 'Save'. This only applies when changing from certain stages to certain other stages."
              />
            }
          />
        </Alert>
      )}
    </>
  );
};

export const BrSalesPersonEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [agreementUploading, setAgreementUploading] = useState(false);

  const uploadPartnershipAgreementToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `PartnershipAgreement/${
          formData.id
        }/Contrato_de_Sociedade_${moment().valueOf()}`
      );
      setAgreementUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          dataProvider
            .update('BrSalesPerson', {
              data: {
                id: parseInt(id, 10),
                agreementAwsObjectKey: awsObjectKey,
                signatureDt: new Date(),
              },
            })
            .then(
              (res) => {
                notify('Partnership agreement uploaded', { type: 'success' });
                setAgreementUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading partnership agreement', {
                  type: 'error',
                });
                setAgreementUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading partnership agreement to S3', {
            type: 'error',
          });
        }
      );
    }
  };

  const preparePartnershipAgreementForm = () => {
    setAgreementUploading(true);
    dataProvider
      .create('BrSalesPartnershipAgreement', {
        data: {
          brSalesPersonId: parseInt(id, 10),
        },
      })
      .then(
        () => {
          setAgreementUploading(false);
          refresh();
          notify(
            'Partnership agreement drafted. Contact will be asked to sign next time they log in.',
            { type: 'success' }
          );
        },
        (e) => {
          setAgreementUploading(false);
          console.error('Error drafting partnership agreement', e);
          let errorMsg = e;
          if (e.body?.graphQLErrors && e.body.graphQLErrors[0]) {
            errorMsg = e.body.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
          refresh();
        }
      );
  };

  const sendNewPartnershipAgreementEmail = () => {
    setAgreementUploading(true);
    dataProvider
      .update('BrSalesPerson', {
        data: {
          id: parseInt(id, 10),
          sendUnsignedPartnershipAgreementEmail: true,
        },
      })
      .then(
        (res) => {
          setAgreementUploading(false);
          refresh();
          notify('Email sent.', { type: 'success' });
        },
        (e) => {
          setAgreementUploading(false);
          console.error('Error sending email', e);
          let errorMsg = e;
          if (e.body?.graphQLErrors && e.body.graphQLErrors[0]) {
            errorMsg = e.body.graphQLErrors[0].message;
          }
          notify(String(errorMsg), {
            type: 'error',
          });
          refresh();
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <Alert severity="info">
                In order to give an email address access to this sales partner
                or sales partner's dashboard, either create or update the
                contact <a href="/BrContact">HERE</a> that you wish you grant
                access to. You can grant access by selecting the sales partner
                from the drop down on that contact's page.
              </Alert>
              <TextInput source="legalName" fullWidth />
              <TextInput source="name" fullWidth />
              <StatusReferenceInput />
              <ReferenceInput
                label="Sales Partner Admin"
                source="adminBrContact.id"
                reference="BrContact"
                perPage={10_000}
                sort={{ field: 'id', order: 'ASC' }}
                filter={{ brSalesPerson: { id: parseInt(id, 10) } }}
              >
                <AutocompleteInput
                  allowEmpty={true}
                  optionText="fullName"
                  label="Sales Partner Admin"
                  fullWidth
                  helperText="If the admin is not yet on this list, go to their contact and link them to this sales partner first."
                />
              </ReferenceInput>
              <BooleanInput
                source="hideCommissionSimulatorFlg"
                fullWidth
                defaultValue={false}
                helperText="Turn this on to hide the commission simulator from this sales partner's dashboard. The simulator will still be visible to the sales partner's admin."
              />
              <BooleanInput
                source="thirdPartyFlg"
                fullWidth
                defaultValue={false}
                helperText="This flags states whether this salesperson is an Energea employee or external sales partner"
              />
              <TextInput
                source="cnpj"
                fullWidth
                helperText="CNPJ of third party sales partner if applicable"
              />
              <TextInput source="salesExperience" fullWidth multiline />
              <TextInput source="referral" fullWidth />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Sales People">
          <Grid container style={{ width: '100%' }}>
            <FunctionField
              label="Sales People"
              render={(record) => {
                if (record.brContacts?.length > 0) {
                  return (
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Name</TableCell>
                          <TableCell>Email</TableCell>
                          <TableCell>WhatsApp</TableCell>
                          <TableCell>More info</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.brContacts.map((brContact) => {
                          const isAdmin =
                            record.adminBrContact &&
                            brContact.id === record.adminBrContact.id;
                          return (
                            <TableRow>
                              <TableCell>
                                <Grid container alignItems="center">
                                  <Grid item>{brContact.fullName}</Grid>
                                  <Grid item>{isAdmin && <Star />}</Grid>
                                </Grid>
                              </TableCell>
                              <TableCell>{brContact.email}</TableCell>
                              <TableCell>
                                <a
                                  href={brContact.whatsAppPhoneLink}
                                  target="_blank"
                                  onClick={(event) => {
                                    event.stopPropagation();
                                  }}
                                >
                                  {brContact.phone}
                                </a>
                              </TableCell>
                              <TableCell>
                                <IconButton
                                  component={Link}
                                  to={`/BrContact/${brContact.id}`}
                                >
                                  <EditIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  );
                }
                return (
                  <Alert severity="info">
                    No sales people have been added to this sales partner.
                  </Alert>
                );
              }}
            />
          </Grid>
        </FormTab>
        <FormTab label="Partnership Agreement">
          <Grid container style={{ width: '100%' }}>
            <FunctionField
              label="Partnership Agreement"
              render={(record) => {
                return (
                  <Grid container spacing={2}>
                    {!record.signatureDt ? (
                      <Grid container item spacing={1}>
                        <Grid item>
                          <Typography variant="h6">
                            Unsigned Partnership Agreement
                          </Typography>
                        </Grid>
                        <Grid item container>
                          <FormDataConsumer>
                            {({ formData, ...rest }) => {
                              if (formData.signatureDt) return null;
                              return (
                                <Grid item container direction="column">
                                  <Grid item>
                                    <Button
                                      color="primary"
                                      variant="contained"
                                      startIcon={<AddCircleOutline />}
                                      style={{ textTransform: 'none' }}
                                      disabled={agreementUploading}
                                      onClick={() => {
                                        preparePartnershipAgreementForm();
                                      }}
                                    >
                                      {agreementUploading ? (
                                        <CircularProgress
                                          style={{ position: 'absolute' }}
                                        />
                                      ) : null}
                                      Prepare Partnership Agreement for
                                      Signature
                                    </Button>
                                  </Grid>
                                  <Grid item>
                                    <Typography variant="body2">
                                      The next time the partner signs in, they
                                      will be presented with this form to sign.
                                    </Typography>
                                  </Grid>
                                </Grid>
                              );
                            }}
                          </FormDataConsumer>
                        </Grid>
                        <Grid item container direction="column">
                          <Grid item>
                            <Button
                              disabled={
                                !record.pendingAgreementDownloadUrl ||
                                agreementUploading
                              }
                              variant="contained"
                              startIcon={<CloudDownload />}
                              style={{ textTransform: 'none' }}
                              onClick={() =>
                                window.location.assign(
                                  record.pendingAgreementDownloadUrl
                                )
                              }
                            >
                              {agreementUploading ? (
                                <CircularProgress
                                  style={{ position: 'absolute' }}
                                />
                              ) : null}
                              Download Unsigned Partnership Agreement
                            </Button>
                          </Grid>
                          <Grid item>
                            <Typography variant="body2">
                              This is what the sales partner will be shown to
                              sign.
                            </Typography>
                          </Grid>
                        </Grid>
                        <Grid item container>
                          <FormDataConsumer>
                            {({ formData, ...rest }) => {
                              if (!formData.pendingAgreementAwsObjectKey)
                                return null;
                              if (!formData.pendingAgreementDownloadUrl)
                                return null;
                              return (
                                <Grid item container direction="column">
                                  <Grid item>
                                    <Button
                                      color="primary"
                                      variant="contained"
                                      startIcon={<Email />}
                                      style={{ textTransform: 'none' }}
                                      disabled={agreementUploading}
                                      onClick={() => {
                                        sendNewPartnershipAgreementEmail();
                                      }}
                                    >
                                      {agreementUploading ? (
                                        <CircularProgress
                                          style={{ position: 'absolute' }}
                                        />
                                      ) : null}
                                      Email Partner to Sign
                                      {formData.signatureRequestedDt
                                        ? ` (Sent: ${moment(
                                            formData.signatureRequestedDt
                                          ).format('MMM D, YYYY HH:mm')})`
                                        : ''}
                                    </Button>
                                  </Grid>
                                  <Grid item>
                                    <Typography variant="body2">
                                      This will send an email to the admin
                                      contact for this sales partner to log in
                                      to their account where they will sign
                                      their new Partnership Agreement.
                                    </Typography>
                                  </Grid>
                                </Grid>
                              );
                            }}
                          </FormDataConsumer>
                        </Grid>
                        <Divider style={{ width: '100%', margin: '1rem 0' }} />
                      </Grid>
                    ) : null}
                    <Grid item container spacing={1}>
                      <Grid item>
                        <Typography variant="h6">
                          Signed Partnership Agreement
                        </Typography>
                      </Grid>
                      <Grid item container>
                        <FormDataConsumer>
                          {({ formData, ...rest }) => {
                            return (
                              <Grid item>
                                <Button
                                  color="primary"
                                  variant="contained"
                                  component="label" // https://stackoverflow.com/a/54043619
                                  startIcon={<CloudUpload />}
                                  style={{ textTransform: 'none' }}
                                  disabled={agreementUploading}
                                >
                                  {agreementUploading ? (
                                    <CircularProgress
                                      style={{ position: 'absolute' }}
                                    />
                                  ) : null}
                                  {record.downloadUrl
                                    ? 'Overwrite Signed Partnership Agreement'
                                    : 'Upload Signed Partnership Agreement'}
                                  <input
                                    type="file"
                                    hidden
                                    onChange={(event) =>
                                      uploadPartnershipAgreementToS3(
                                        event,
                                        formData
                                      )
                                    }
                                    accept="application/pdf"
                                  />
                                </Button>
                                <Typography variant="body2">
                                  Upload a signed Partnership Agreement.
                                </Typography>
                              </Grid>
                            );
                          }}
                        </FormDataConsumer>
                      </Grid>
                      <Grid item container>
                        <Button
                          disabled={!record.agreementDownloadUrl}
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={() =>
                            window.location.assign(record.agreementDownloadUrl)
                          }
                        >
                          Download Signed Partnership Agreement
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Sales Partner Name"
      source="salesPartnerName"
      alwaysOn
    />
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Sales Person Name"
      source="salesPersonName"
      alwaysOn
    />
    <ReferenceInput
      label="Status"
      source="brSalesPartnerStatus.id"
      reference="BrSalesPartnerStatus"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <SelectInput label="Status" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const BrSalesPersonList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<CustomFilter />}
      sort={{ field: 'id', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="legalName" />
        <TextField source="name" />
        <LinkField
          reference="BrSalesPartnerStatus"
          linkSource="brSalesPartnerStatus.id"
          labelSource="brSalesPartnerStatus.name"
          label="Status"
        />
        <LinkField
          reference="BrContact"
          linkSource="adminBrContact.id"
          labelSource="adminBrContact.fullName"
          label="Sales Partner Admin"
        />
        <ArrayField source="brContacts" label="Sales People">
          <SingleFieldList>
            <CustomReferenceField source="fullName" />
          </SingleFieldList>
        </ArrayField>
        <NumberField
          sortable={false}
          source="brConsumerUnitCount"
          label="Consumer Unit Count"
        />
        <CustomBooleanField source="thirdPartyFlg" />
        <TextField source="cnpj" label="CNPJ" />
        <FunctionField
          label="Partnership Agreement"
          render={(record) => {
            if (record.agreementDownloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.assign(record.agreementDownloadUrl);
                  }}
                >
                  Download
                </Button>
              );
            }
            return null;
          }}
        />
      </Datagrid>
    </List>
  );
};

export const BrSalesPersonCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="legalName" fullWidth required />
          <TextInput source="name" fullWidth required />
          <BooleanInput
            source="thirdPartyFlg"
            fullWidth
            defaultValue={false}
            helperText="This flags states whether this salesperson is an Energea employee or external sales partner"
          />
          <TextInput
            source="cnpj"
            fullWidth
            helperText="CNPJ of third party sales partner if applicable"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
