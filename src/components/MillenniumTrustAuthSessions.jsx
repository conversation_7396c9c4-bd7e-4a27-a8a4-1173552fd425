import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON>oleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FormDataConsumer,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'MT Auth Session';

export const MillenniumTrustAuthSessionEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const notify = useNotify();

  const handleBypassOTPSentStatus = () => {
    dataProvider
      .update('MillenniumTrustAuthSession', {
        data: {
          id: parseInt(id, 10),
          bypassOTPSentVerificationStatus: true,
        },
      })
      .then(
        (res) => {
          notify('Successfully bypassed OTP Sent status', { type: 'success' });
        },
        (err) => {
          console.error(err);
          notify('Error occurred bypassing OTP Sent status', { type: 'error' });
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="verificationId" fullWidth disabled />
            <TextInput
              source="verificationStatus"
              fullWidth
              disabled
              helperText="If user is stuck in 'OTP Sent' or 'OTP Failed' statuses and we want to push them through, email request to '<EMAIL>'"
            />
            <BooleanInput
              source="verificationDocumentStatusPollingFlg"
              fullWidth
            />
            <DateInput
              source="verificationStatusUpdatedDt"
              fullWidth
              disabled
            />
            {/* <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleBypassOTPSentStatus}
                    disabled={
                      [
                        'OTP Sent',
                        'Awaiting Documentation',
                        'In Review',
                      ].indexOf(formData.verificationStatus) === -1
                    }
                  >
                    Bypass MT Verification
                  </Button>
                );
              }}
            </FormDataConsumer> */}
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const AuthSessionFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Investor Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const MillenniumTrustAuthSessionList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<AuthSessionFilter />}
      sort={{ field: 'verificationStatus', order: 'ASC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="verificationStatus" />
        <DateField source="verificationStatusUpdatedDt" showTime={true} />
        <BooleanField source="verificationDocumentStatusPollingFlg" />
        <DateField source="createdAt" showTime={true} />
        <DateField source="updatedAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

export const MillenniumTrustAuthSessionCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <DateInput source="date" required fullWidth />
          <CustomNumberInput source="amount" fullWidth />
          <ReferenceInput
            source="example.id"
            reference="Example"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              label="Example"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
