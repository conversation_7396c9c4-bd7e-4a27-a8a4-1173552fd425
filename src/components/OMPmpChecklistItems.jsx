import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Typography } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'PMP Checklist Item';

export const OMPmpChecklistItemEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false} redirect={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <Typography>
              <a href="/OMPmp">Click here to go to the PMP dashboard</a>
            </Typography>
          </Grid>
        </Grid>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="omPmp.id"
              reference="OMPmp"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
              required
            >
              <AutocompleteInput
                optionText="label"
                label="PMP"
                fullWidth
                required
              />
            </ReferenceInput>
            <ReferenceInput
              source="omChecklistItem.id"
              reference="OMChecklistItem"
              perPage={10_000}
              sort={{ field: 'code', order: 'ASC' }}
              required
            >
              <AutocompleteInput
                optionText="label"
                label="Checklist Item"
                fullWidth
                required
                helperText={
                  <Typography variant="caption">
                    To add a new checklist item,{' '}
                    <a href={`/OMChecklistItem/create`}>click here.</a> Para
                    adicionar um novo item,{' '}
                    <a href={`/OMChecklistItem/create`}>clique aqui.</a>
                  </Typography>
                }
              />
            </ReferenceInput>
            <TextInput
              source="specifier"
              fullWidth
              helperText="Ex: 'All trackers for inverter 1'"
            />
            <ReferenceInput
              source="omPmpPeriodicity.id"
              reference="OMPmpPeriodicity"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
              required
            >
              <SelectInput
                optionText="name"
                label="Periodicity"
                fullWidth
                required
              />
            </ReferenceInput>
            <DateInput
              source="startDt"
              fullWidth
              required
              helperText="This is the date that is used to schedule future tickets."
            />
            <ReferenceInput
              source="defaultEquipmentItem.id"
              reference="EquipmentItem"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <AutocompleteInput
                optionText="label"
                label="Default equipment item"
                fullWidth
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const OMPmpChecklistItemList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <LinkField
          reference="OMPmp"
          linkSource="omPmp.id"
          labelSource="omPmp.label"
          label="PMP"
        />
        <LinkField
          reference="OMChecklistItem"
          linkSource="omChecklistItem.id"
          labelSource="omChecklistItem.label"
          label="Checklist Item"
        />
        <TextField source="specifier" />
        <LinkField
          reference="OMPmpPeriodicity"
          linkSource="omPmpPeriodicity.id"
          labelSource="omPmpPeriodicity.name"
          label="Periodicity"
        />
        <DateField source="startDt" />
        <LinkField
          reference="EquipmentItem"
          linkSource="defaultEquipmentItem.id"
          labelSource="defaultEquipmentItem.label"
          label="Default equipment item"
        />
      </Datagrid>
    </List>
  );
};

export const OMPmpChecklistItemCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="omPmp.id"
            reference="OMPmp"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <AutocompleteInput
              optionText="label"
              label="PMP"
              fullWidth
              required
            />
          </ReferenceInput>
          <ReferenceInput
            source="omChecklistItem.id"
            reference="OMChecklistItem"
            perPage={10_000}
            sort={{ field: 'code', order: 'ASC' }}
            required
          >
            <AutocompleteInput
              optionText="label"
              label="Checklist Item"
              fullWidth
              required
              helperText={
                <Typography variant="caption">
                  To add a new checklist item,{' '}
                  <a href={`/OMChecklistItem/create`}>click here.</a> Para
                  adicionar um novo item,{' '}
                  <a href={`/OMChecklistItem/create`}>clique aqui.</a>
                </Typography>
              }
            />
          </ReferenceInput>
          <TextInput
            source="specifier"
            fullWidth
            helperText="Ex: 'All trackers for inverter 1'"
          />
          <ReferenceInput
            source="omPmpPeriodicity.id"
            reference="OMPmpPeriodicity"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <SelectInput
              optionText="name"
              label="Periodicity"
              fullWidth
              required
            />
          </ReferenceInput>
          <DateInput
            source="startDt"
            fullWidth
            required
            helperText="This is the date used to schedule future tickets."
          />
          <ReferenceInput
            source="defaultEquipmentItem.id"
            reference="EquipmentItem"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <AutocompleteInput
              optionText="label"
              label="Default equipment item"
              fullWidth
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
