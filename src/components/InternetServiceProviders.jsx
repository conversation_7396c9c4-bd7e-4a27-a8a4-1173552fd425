import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Internet Service Provider';

export const InternetServiceProviderEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth helperText="ISP company name" />
            <TextInput label="Login URL" source="url" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const InternetServiceProviderList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <FunctionField
          label="Login Url"
          render={(record) => {
            return (
              <a
                onClick={(event) => {
                  event.stopPropagation();
                }}
                target="_blank"
                rel="noopener noreferrer"
                href={record.url}
              >
                {record.url}
              </a>
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const InternetServiceProviderCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="name"
            helperText="ISP company name (ie: Starlink)"
            required
            fullWidth
          />
          <TextInput
            label="Login URL"
            source="url"
            required
            fullWidth
            helperText="Full url (ie: https://www.sample.com/login)"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
