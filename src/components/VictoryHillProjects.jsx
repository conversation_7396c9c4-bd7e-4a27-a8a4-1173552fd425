import React, { useState } from 'react';
import numeral from 'numeral';
import {
  ArrayField,
  ArrayInput,
  BooleanInput,
  BooleanField,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  FormDataConsumer,
  FormTab,
  FunctionField,
  TabbedForm,
  List,
  Show,
  SimpleFormIterator,
  SingleFieldList,
  required,
  NumberField,
  Pagination,
  ReferenceField,
  ReferenceInput,
  SelectInput,
  TabbedShowLayout,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  useRefresh,
  useRecordContext,
  useShowContext,
} from 'react-admin';

import {
  Button,
  Divider,
  FormHelperText,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Delete,
  Description,
  Folder,
  GetApp,
  Help,
  PictureAsPdf,
} from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import { Alert } from '@mui/lab';
import { useParams } from 'react-router-dom';
import { Image, Video, Transformation } from 'cloudinary-react';

import Config from '../config/config';
import {
  CustomReferenceField,
  DetailField,
  CustomBooleanField,
  CustomNumberInput,
  LinkField,
} from './CustomFields';

import { openUploadWidget } from '../utils/CloudinaryService';
import theme from '../theme';
import EPCConversionGrid from './EPCConversionGrid';

const entityName = 'Project';

const Title = () => {
  const record = useRecordContext();
  return (
    <span>
      {entityName}
      {record ? ` #${record.id} - ${record.name}` : ''}
    </span>
  );
};

export const VictoryHillProjectEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [editDropboxLocation, setEditDropboxLocation] = useState(false);

  const { id } = useParams();

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['projects'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.project_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.project_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideosUploaded([resp.info]);
      }
    });
  };

  const uploadDocumentWithCloudinary = (id, type) => {
    return () => {
      const uploadOptions = {
        tags: [type, id],
        showPoweredBy: false,
        multiple: false,
        cloudName: Config.cloud_name,
        sources: ['local', 'url'],
        // dropboxAppKey: '1y1wsyzgzfb5f2g',
        uploadPreset: Config.project_document_upload_preset,
      };
      openUploadWidget(uploadOptions, (error, resp) => {
        if (!error && resp.event === 'success') {
          onDocumentUploaded(resp.info, type);
        }
      });
    };
  };

  const handleMakeVideoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryVideo: resource.id,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Videos successfully updated');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error updating videos', { type: 'error' });
        });
    };
  };

  const handleMakePhotoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryImage: resource.id,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Images successfully updated');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error updating images', { type: 'error' });
        });
    };
  };

  const handleRemovePhoto = (resource) => {
    return () => {
      dataProvider
        .delete('ProjectImage', {
          projectId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Image successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing image', { type: 'error' });
        });
    };
  };

  const handleRemoveDocument = (documentId) => {
    return () => {
      dataProvider
        .delete('ProjectDocument', {
          id: documentId,
        })
        .then(() => {
          notify('Document successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing document', { type: 'error' });
        });
    };
  };

  const handleRemoveVideo = (resource) => {
    return () => {
      dataProvider
        .delete('ProjectVideo', {
          projectId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Video successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing video', { type: 'error' });
        });
    };
  };

  const onPhotosUploaded = (aPhotos) => {
    const photos = aPhotos.map((photo) => {
      return {
        public_id: photo.public_id,
        projectId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('ProjectImage', {
        input: photos,
      })
      .then((record) => {
        notify('Image successfully uploaded');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading image.', { type: 'error' });
      });
  };

  const onVideosUploaded = (videos) => {
    videos = videos.map((video) => {
      return {
        public_id: video.public_id,
        projectId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('ProjectVideo', {
        input: videos,
      })
      .then((record) => {
        notify('Video uploaded successfully');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading video.', { type: 'error' });
      });
  };

  const onDocumentUploaded = (document, type) => {
    const docObj = document;
    delete docObj.id;
    docObj.title = `${type}-(${id})`;
    dataProvider
      .update('Project', {
        data: { id: parseInt(id, 10), [type]: docObj },
      })
      .then(() => {
        notify('Document successfully added');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error adding document', { type: 'error' });
      });
  };

  const handleDownloadDropboxFile = (fileLocation) => {
    dataProvider
      .getOne('DropboxFile', {
        fileLocation,
      })
      .then((res) => {
        if (!res.data) {
          notify('Error getting dropbox download url', { type: 'error' });
          return null;
        }
        const link = document.createElement('a');
        link.href = res.data.downloadUrl;
        link.download = true;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch((e) => {
        console.error(e);
        notify('Error getting dropbox download url', { type: 'error' });
      });
  };

  const handleDeleteDropboxFile = (fileLocation) => {
    dataProvider
      .delete('DropboxFile', {
        fileLocation,
      })
      .then((res) => {
        notify('File deleted from Dropbox', { type: 'success' });
        refresh();
      })
      .catch((e) => {
        console.error(e);
        notify('Error deleting file from Dropbox', { type: 'error' });
      });
  };

  const handleUploadFileToDropbox = (event, dropboxDirectory) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 100) {
        notify('Document must be less than 100MB', { type: 'error' });
        return;
      }
      dataProvider
        .create('DropboxFile', {
          fileLocation: `${dropboxDirectory}/${file.name}`,
        })
        .then((res) => {
          const uploadUrl = res.data;
          const formData = new FormData();
          formData.append('File', file);
          fetch(uploadUrl, {
            method: 'POST',
            body: formData,
            headers: { 'Content-type': 'application/octet-stream' },
          })
            .then((response) => response.json())
            .then((result) => {
              console.log('Success:', result);
              notify('File uploaded to Dropbox', { type: 'success' });
              refresh();
            })
            .catch((error) => {
              console.error('Error:', error);
              notify('Error uploading file to Dropbox', { type: 'error' });
              refresh();
            });
        })
        .catch((e) => {
          console.error(e);
          notify('Error getting file upload url from Dropbox', {
            type: 'error',
          });
        });
    }
  };

  return (
    <Edit title={<Title />} undoable={false}>
      <TabbedForm redirect={false}>
        <FormTab label="Summary">
          <Grid
            container
            style={{ width: '100%', marginTop: '.5rem' }}
            spacing={5}
          >
            <Grid item xs={12} md={6}>
              <Typography variant="h5">Details</Typography>
              <TextInput
                label="Name"
                validate={required()}
                source="name"
                fullWidth
              />
              <ReferenceInput source="portfolio.id" reference="PortfolioLite">
                <SelectInput label="Portfolio" fullWidth optionText="name" />
              </ReferenceInput>
              <TextInput
                required
                source="shortSummary"
                fullWidth
                helperText="Shows up on project popup tile."
              />
              <TextInput
                source="shortSummaryPT"
                fullWidth
                helperText="Project description used only on the .br site (only required for consortium projects)."
              />
              {/* <TextInput multiline source="summary" fullWidth helperText="Not currently in use." /> */}
              <ReferenceInput
                source="installationType.id"
                reference="InstallationType"
              >
                <SelectInput
                  label="Installation Type"
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput>
              <DateInput
                label="NTP date"
                helperText="Notice to proceed date. If this is not set, or the date is prior to the current date, than the project will not be included in the portfolio's investment cap."
                source="ntpDt"
                fullWidth
              />
              <CustomNumberInput
                required
                source="debt"
                fullWidth
                helperText="Portion of total project cost funded by debt."
              />
              <CustomNumberInput
                required
                source="sponsorEquity"
                fullWidth
                helperText="Portion of total project cost funded by sponsor equity (crowd funding)."
              />
              <CustomNumberInput
                required
                source="taxEquity"
                fullWidth
                helperText="Portion of total project cost funded by tax equity."
              />
              {/* <CustomNumberInput
                source="projectedAnnualProduction"
                label="Projected Annual Production (kWh AC)"
                fullWidth
              /> */}
              <CustomNumberInput
                label="Proj. Sys Size (MW) AC"
                helperText="MW AC"
                source="systemSizeAC"
                fullWidth
              />
              <CustomNumberInput
                label="Proj. Sys Size (MW) DC"
                helperText="MW DC"
                source="systemSizeDC"
                fullWidth
              />
              <CustomNumberInput source="percentageOwnership" fullWidth />
              <DateInput
                label="Projected COD"
                source="projectedCOD"
                fullWidth
              />
              <DateInput label="Actual COD" source="actualCOD" fullWidth />
              <TextInput
                label="Internal Notes"
                multiline
                source="internalNotes"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h5">Status</Typography>
              <ReferenceInput
                source="projectInvestmentStatus.id"
                reference="ProjectInvestmentStatus"
                filter={{ activeOnly: true }}
              >
                <SelectInput
                  label="Investment Status"
                  fullWidth
                  optionText="name"
                  helperText="Adds badge on project list in portfolio detail page"
                />
              </ReferenceInput>
              <BooleanInput
                source="isPublic"
                label="Is Public?"
                helperText="Determines whether or not the project is included in it's parent portfolio...if it is not public, it essentially does not exist."
                fullWidth
              />
              <BooleanInput
                source="newFlg"
                label="Is New?"
                helperText="Determines whether or not the project is marked as new in it's parent portfolio."
                fullWidth
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Address">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="address1" fullWidth />
              <TextInput source="address2" fullWidth />
              <TextInput source="city" fullWidth />
              <TextInput source="postalCode" fullWidth />
              <TextInput source="state" fullWidth />
              <ReferenceInput source="country.id" reference="Country">
                <SelectInput
                  label="Country"
                  required
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput>
              <CustomNumberInput required source="latitude" fullWidth />
              <CustomNumberInput required source="longitude" fullWidth />
              <FormHelperText>
                To get longitude and latitude in decimal form{' '}
                <a
                  href="https://www.fcc.gov/media/radio/dms-decimal"
                  target="_blank"
                >
                  go here
                </a>
                ).
              </FormHelperText>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Media">
          <Grid style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Alert severity="info">
                <Typography>
                  All images : 1600px X 1200px (landscape)
                </Typography>
              </Alert>
            </Grid>
          </Grid>
          <Typography variant="h4" gutterBottom>
            Images
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.images || formData?.images?.length === 0)
                return 'No photos';
              return formData.images.map((record) => (
                <Grid style={{ paddingBottom: '1rem' }}>
                  <Image
                    style={{}}
                    cloud_name={Config.cloud_name}
                    publicId={record.public_id}
                  >
                    <Transformation width="200" crop="scale" />
                  </Image>
                  <Grid>
                    {/* <Button
                      color="primary"
                      style={
                        record.primaryFlg
                          ? {
                              backgroundColor: 'green',
                              color: 'white',
                            }
                          : {}
                      }
                      disabled={record.primaryFlg}
                      variant={record.primaryFlg ? 'contained' : 'outlined'}
                      onClick={handleMakePhotoPrimary(record)}
                    >
                      Make Primary Photo
                    </Button> */}
                    {/* <Button
                      style={{ float: 'right' }}
                      onClick={handleRemovePhoto(record)}
                    >
                      <Delete />
                    </Button> */}
                  </Grid>
                </Grid>
              ));
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button variant="outlined" onClick={uploadImageWithCloudinary}>
              Add photo
            </Button>
          </div>
          <Divider style={{ width: '100%', margin: '2em 0' }} />
          <Typography variant="h4" gutterBottom>
            Videos
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.videos || formData?.videos?.length === 0)
                return 'No videos';
              return formData.videos.map((record) => {
                if (!record?.public_id) return null;
                return (
                  <Grid style={{ paddingBottom: '1rem' }}>
                    <Video
                      cloud_name={Config.cloud_name}
                      publicId={record.public_id}
                      muted
                      width="200"
                      sourceTypes={['mp4']}
                      controls
                    >
                      {record.public_id ? (
                        <Transformation width={200} crop="scale" />
                      ) : null}
                    </Video>
                    <Grid>
                      {/* <Button
                        color="primary"
                        style={
                          record.primaryFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        disabled={record.primaryFlg}
                        variant={record.primaryFlg ? 'contained' : 'outlined'}
                        onClick={handleMakeVideoPrimary(record)}
                      >
                        Make Primary Video
                      </Button> */}
                      {/* <Button
                        style={{ float: 'right' }}
                        onClick={handleRemoveVideo(record)}
                      >
                        <Delete />
                      </Button> */}
                    </Grid>
                  </Grid>
                );
              });
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button variant="outlined" onClick={uploadVideoWithCloudinary}>
              Add video
            </Button>
          </div>
        </FormTab>
        <FormTab label="Documents">
          <Typography>IC Memo</Typography>
          <FunctionField
            source="ICMemo"
            label="ICMemo"
            render={(record) => {
              return (
                <>
                  <div>
                    {!record.ICMemo ? (
                      <>
                        <Button
                          // style={{ float: 'right' }}
                          variant="contained"
                          color="primary"
                          onClick={uploadDocumentWithCloudinary(
                            record.id,
                            'ICMemo'
                          )}
                        >
                          Add IC-Memo
                        </Button>
                        <FormHelperText>
                          IC-Memo must be in pdf form and don't forget to reduce
                          your file size before uploading (
                          <a
                            href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                            target="_blank"
                          >
                            Mac directions
                          </a>
                          ).
                        </FormHelperText>
                      </>
                    ) : (
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          // eslint-disable-next-line security/detect-non-literal-fs-filename
                          window.open(record.ICMemo.url, '_blank');
                        }}
                      >
                        <PictureAsPdf /> - View file
                      </Button>
                    )}
                    {record.ICMemo ? (
                      <Button
                        // style={{ float: 'right' }}
                        onClick={handleRemoveDocument(record.ICMemo.id)}
                      >
                        <Delete />
                      </Button>
                    ) : null}
                  </div>
                  {record.ICMemo ? (
                    <TextInput
                      label="Title"
                      helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - IC Memo')"
                      source="ICMemo.title"
                    />
                  ) : null}
                </>
              );
            }}
          />

          <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />

          <Typography>Project Model</Typography>
          <FunctionField
            source="projectModel"
            label="Project Model"
            render={(record) => {
              return (
                <>
                  <div>
                    {!record.projectModel ? (
                      <>
                        <Button
                          // style={{ float: 'right' }}
                          variant="contained"
                          color="primary"
                          onClick={uploadDocumentWithCloudinary(
                            record.id,
                            'projectModel'
                          )}
                        >
                          Add Project Model
                        </Button>

                        <FormHelperText>
                          Project Model must be in pdf form. Don't forget to
                          reduce your file size before uploading (
                          <a
                            href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                            target="_blank"
                          >
                            Mac directions
                          </a>
                          ).
                        </FormHelperText>
                      </>
                    ) : (
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          // eslint-disable-next-line security/detect-non-literal-fs-filename
                          window.open(record.projectModel.url, '_blank');
                        }}
                      >
                        <PictureAsPdf /> - View file
                      </Button>
                    )}
                    {record.projectModel ? (
                      <Button
                        // style={{ float: 'right' }}
                        onClick={handleRemoveDocument(record.projectModel.id)}
                      >
                        <Delete />
                      </Button>
                    ) : null}
                  </div>
                  {record.projectModel ? (
                    <TextInput
                      label="Title"
                      helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - Financial Model')"
                      source="projectModel.title"
                    />
                  ) : null}
                </>
              );
            }}
          />
        </FormTab>
        <FormTab label="Monitoring" path="monitoring">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <DateInput label="Actual COD" source="actualCOD" fullWidth />
              <DateInput
                label="Monitoring Start Dt"
                source="monitoringStartDt"
                helperText="This date is used to discount expected production when it is after the Actual COD on the Asset Management Dashboard"
                fullWidth
              />
              <ReferenceInput
                source="projectMonitoringStatus.id"
                reference="ProjectMonitoringStatus"
              >
                <SelectInput
                  label="Monitoring Status"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput source="monitoringNotes" multiline fullWidth />
              <CustomNumberInput
                label="Degradation constant"
                source="degradationConstant"
                fullWidth
              />
              <CustomNumberInput
                source="transformerLoss"
                helperText="After changing, run 'Apply Transformer Loss to Production Periods' on the CMS Dashboard to update production values. Input the decimal form of the percentage of power loss for projects where the meter is before the transformer. Write 5% as 0.05."
                fullWidth
              />
              <CustomNumberInput
                label="January P50 Production (kWh)"
                source="janP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="February P50 Production (kWh)"
                source="febP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="March P50 Production (kWh)"
                source="marP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="April P50 Production (kWh)"
                source="aprP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="May P50 Production (kWh)"
                source="mayP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="June P50 Production (kWh)"
                source="junP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="July P50 Production (kWh)"
                source="julP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="August P50 Production (kWh)"
                source="augP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="September P50 Production (kWh)"
                source="sepP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="October P50 Production (kWh)"
                source="octP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="November P50 Production (kWh)"
                source="novP50Prod"
                fullWidth
              />
              <CustomNumberInput
                label="December P50 Production (kWh)"
                source="decP50Prod"
                fullWidth
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="O&M" path={'O&M'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12}>
              <ReferenceInput
                source="projectManager.id"
                reference="EmployeeLite"
                disabled
                sort={{ field: 'firstName', order: 'ASC' }}
              >
                <SelectInput
                  label="Project Manager"
                  fullWidth
                  allowEmpty
                  optionText="fullName"
                  disabled
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6">
                <b>Platform Credentials</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="omPlatformCredentials"
                id="om-platform-credentials"
              >
                <SimpleFormIterator>
                  <TextInput
                    fullWidth
                    required
                    label="Platform name"
                    source="name"
                  />
                  <TextInput label="Platform url" source="url" fullWidth />
                  <TextInput label="Username" source="username" fullWidth />
                  <TextInput label="Password" source="password" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6">
                <b>Contacts</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectContacts"
                id="project-contacts"
              >
                <SimpleFormIterator>
                  <TextInput
                    fullWidth
                    required
                    label="Label"
                    helperText="ex: O&M Contractor"
                    source="name"
                  />
                  <TextInput
                    label="Primary Contact Name"
                    source="primaryContactName"
                    fullWidth
                  />
                  <TextInput label="Phone" source="phone" fullWidth />
                  <TextInput label="Email" source="email" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6">
                <b>Equipment</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectEquipmentItems"
                id="project-equipment"
              >
                <SimpleFormIterator>
                  <ReferenceInput
                    source="equipmentItem.id"
                    reference="EquipmentItem"
                  >
                    <SelectInput
                      label="Equipment"
                      required
                      fullWidth
                      optionText="model"
                    />
                  </ReferenceInput>
                  <CustomNumberInput
                    required
                    label="Quantity"
                    source="quantity"
                    fullWidth
                  />
                </SimpleFormIterator>
              </ArrayInput>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/EquipmentItem/create"
              >
                Add new equipment
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Insurance Policies</b>
              </Typography>
              <ArrayField source="projectInsurancePolicies" sortable={false}>
                <SingleFieldList>
                  <CustomReferenceField
                    source="label"
                    color={() => 'primary'}
                  />
                </SingleFieldList>
              </ArrayField>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/InsurancePolicy/create"
              >
                Add new insurance policy
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contracts</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  // if (!formData.omContractsDropboxLocation && !editDropboxLocation) {
                  //   setEditDropboxLocation(true)
                  // }
                  return (
                    <>
                      {!editDropboxLocation ? (
                        <Grid container>
                          <Grid item xs={11} style={{ paddingLeft: '1rem' }}>
                            <Typography variant="body2" gutterBottom>
                              <b>Dropbox directory:</b>{' '}
                              {formData.omContractsDropboxLocation ||
                                'None specified. Click the pencil to edit.'}
                            </Typography>
                          </Grid>
                          <Grid item xs={1}>
                            <Grid container item justifyContent="flex-end">
                              <IconButton
                                onClick={() => {
                                  setEditDropboxLocation(true);
                                }}
                                size="large"
                              >
                                <EditIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Grid>
                      ) : (
                        <TextInput
                          source="omContractsDropboxLocation"
                          fullWidth
                          helperText="The '8.1.1 O&M Agreement' directory for this project. Ex: '/Energea Global/Market II - USA/Portfolio/Projects/Portfolio 4 - MA - Waltham/8. Asset Management/8.1 O&M/8.1.1 O&M Agreement'"
                        />
                      )}
                      <Table>
                        <TableBody>
                          {formData.contracts &&
                          formData.contracts.length > 0 ? (
                            formData.contracts.map((contract, index) => (
                              <TableRow key={`contracts-row-${contract.id}`}>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none', width: '1rem' }
                                      : { width: '1rem' }
                                  }
                                >
                                  {contract.isDirectory ? (
                                    <Folder />
                                  ) : contract.isFile ? (
                                    <Description />
                                  ) : (
                                    <Help />
                                  )}
                                </TableCell>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <b>{contract.name}</b>
                                </TableCell>
                                <TableCell
                                  align="right"
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <Tooltip title="Download">
                                    <IconButton
                                      color="primary"
                                      disabled={!contract.isDownloadable}
                                      onClick={() =>
                                        handleDownloadDropboxFile(
                                          contract.location
                                        )
                                      }
                                      size="large"
                                    >
                                      <GetApp />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      style={{
                                        color: !contract.isFile
                                          ? null
                                          : theme.palette.error.main,
                                      }}
                                      disabled={!contract.isFile}
                                      onClick={() => {
                                        if (
                                          window.confirm(
                                            `Clicking 'OK' will PERMANENTLY DELETE this document from Dropbox. Are you sure you wish to delete '${contract.name}'?`
                                          )
                                        ) {
                                          handleDeleteDropboxFile(
                                            contract.location
                                          );
                                        }
                                      }}
                                      size="large"
                                    >
                                      <Delete />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <Alert severity="info">
                              No files found in Dropbox directory
                            </Alert>
                          )}
                        </TableBody>
                      </Table>
                      <Button
                        style={{ marginTop: '1rem' }}
                        variant="contained"
                        color="secondary"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        Upload Contract to Dropbox
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            handleUploadFileToDropbox(
                              event,
                              formData.omContractsDropboxLocation
                            )
                          }
                        />
                      </Button>
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Env. Impact">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Projected</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} will create{' '}
                      <b>
                        {numeral(
                          formData.lifetimeEnergyProjection / 1000
                        ).format('0,0')}{' '}
                        MWh
                      </b>{' '}
                      in its lifetime. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.lifetimeEnergyProjection / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Actual</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} has created{' '}
                      <b>
                        {numeral(formData.allTimeActual / 1000).format('0,0.0')}{' '}
                        MWh
                      </b>{' '}
                      so far. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.allTimeActual / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const ProjectShowView = () => {
  const showContext = useShowContext();
  if (!showContext) return null;
  const { record } = showContext;
  if (!record) {
    return null;
  }
  return (
    <TabbedShowLayout redirect={false}>
      <TabbedShowLayout.Tab label="Summary">
        <Typography variant="h6" gutterBottom>
          Details
        </Typography>
        <TextField source="name" />
        <ReferenceField
          source="portfolio.id"
          reference="PortfolioLite"
          label="Portfolio"
        />
        <TextField source="shortSummary" />
        <TextField source="summary" label="Long Summary" />
        <ReferenceField
          source="installationType.id"
          reference="InstallationType"
          label="Installation Type"
        >
          <TextField source="name" />
        </ReferenceField>
        <Divider style={{ margin: '1rem 0' }} />

        <Typography variant="h6" gutterBottom>
          Project Cost
        </Typography>
        <NumberField
          source="debt"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="sponsorEquity"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="taxEquity"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <Divider style={{ margin: '1rem 0' }} />

        <Typography variant="h6" gutterBottom>
          System
        </Typography>
        <NumberField label="Proj. Sys Size (MW) AC" source="systemSizeAC" />
        <NumberField label="Proj. Sys Size (MW) DC" source="systemSizeDC" />
        <NumberField source="percentageOwnership" />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Milestones
        </Typography>
        <DateField label="NTP date" source="ntpDt" />
        <DateField label="Acquisition Dt" source="acquisitionDt" />
        <DateField
          label="Projected Construction Start Dt"
          source="projectedConstructionStartDt"
        />
        <DateField
          label="Actual Construction Start Dt"
          source="actualConstructionStartDt"
        />
        <DateField label="Projected COD" source="projectedCOD" />
        <DateField label="Actual COD" source="actualCOD" />
        <DateField label="Projected End Dt" source="projectedEndDt" />
      </TabbedShowLayout.Tab>
      <TabbedShowLayout.Tab label="Address" path="address">
        <Typography variant="h6" gutterBottom>
          Address
        </Typography>
        <TextField source="address1" />
        <TextField source="address2" />
        <TextField source="city" />
        <TextField source="postalCode" />
        <TextField source="state" />
        <TextField source="countryName" label="Country" />
        <NumberField source="latitude" />
        <NumberField source="longitude" />
      </TabbedShowLayout.Tab>

      <TabbedShowLayout.Tab label="Media" path="media">
        <Typography variant="h6" gutterBottom>
          Images
        </Typography>
        {!record?.images || record?.images?.length === 0 ? (
          'No photos'
        ) : (
          <Grid container>
            {record.images.map((image) => (
              <Grid
                key={`project-show-img-${image.id}`}
                style={{ padding: '1rem' }}
              >
                <Image
                  cloud_name={Config.cloud_name}
                  publicId={image.public_id}
                >
                  <Transformation width="240" crop="scale" />
                </Image>
              </Grid>
            ))}
          </Grid>
        )}
        <Divider style={{ width: '100%', margin: '2em 0' }} />
        <Typography variant="h6" gutterBottom>
          Videos
        </Typography>
        {!record?.videos
          ? 'No videos'
          : record.videos.map((video) =>
              !video?.public_id ? null : (
                <Grid
                  key={`project-show-video-${video.id}`}
                  style={{ paddingBottom: '1rem' }}
                >
                  <Video
                    cloud_name={Config.cloud_name}
                    publicId={video.public_id}
                    muted
                    width="240"
                    sourceTypes={['mp4']}
                    controls
                  >
                    {/* {video.public_id ? (
                      <Transformation width={200} crop="scale" />
                    ) : null} */}
                  </Video>
                </Grid>
              )
            )}
      </TabbedShowLayout.Tab>

      <TabbedShowLayout.Tab label="Documents" path="documents">
        <Typography>IC Memo</Typography>
        <FunctionField
          source="ICMemo"
          label="ICMemo"
          render={(record) => {
            return (
              <>
                <div>
                  {!record.ICMemo ? (
                    <>
                      <FormHelperText>
                        IC-Memo must be in pdf form and don't forget to reduce
                        your file size before uploading (
                        <a
                          href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                          target="_blank"
                        >
                          Mac directions
                        </a>
                        ).
                      </FormHelperText>
                    </>
                  ) : (
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        // eslint-disable-next-line security/detect-non-literal-fs-filename
                        window.open(record.ICMemo.url, '_blank');
                      }}
                    >
                      <PictureAsPdf /> - View file
                    </Button>
                  )}
                </div>
                {record.ICMemo ? (
                  <TextField
                    label="Title"
                    helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - IC Memo')"
                    source="ICMemo.title"
                  />
                ) : null}
              </>
            );
          }}
        />

        <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />

        <Typography>Project Model</Typography>
        <FunctionField
          source="projectModel"
          label="Project Model"
          render={(record) => {
            return (
              <>
                <div>
                  {!record.projectModel ? (
                    <>
                      <FormHelperText>
                        Project Model must be in pdf form. Don't forget to
                        reduce your file size before uploading (
                        <a
                          href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                          target="_blank"
                        >
                          Mac directions
                        </a>
                        ).
                      </FormHelperText>
                    </>
                  ) : (
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        // eslint-disable-next-line security/detect-non-literal-fs-filename
                        window.open(record.projectModel.url, '_blank');
                      }}
                    >
                      <PictureAsPdf /> - View file
                    </Button>
                  )}
                </div>
                {record.projectModel ? (
                  <TextField
                    label="Title"
                    helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - Financial Model')"
                    source="projectModel.title"
                  />
                ) : null}
              </>
            );
          }}
        />
      </TabbedShowLayout.Tab>

      <TabbedShowLayout.Tab label="PVSyst" path="pvsyst">
        <Typography variant="h6" gutterBottom>
          Module Specs
        </Typography>
        <NumberField
          label="Degradation constant"
          source="degradationConstant"
        />
        <NumberField
          label="Temp. Coefficient (PMax)"
          helperText="The temperature coefficient of Pmax for the site\'s modules. this should be a loss coefficient per degrees celsius. (ex: 0.0034)"
          source="tempCoeffPmax"
        />
        <NumberField
          label="NOCT"
          helperText="The nominal operating cell temperature of the modules. (ex: 45)"
          source="noct"
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          P50 Production
        </Typography>
        <NumberField label="January P50 Production (kWh)" source="janP50Prod" />
        <NumberField
          label="February P50 Production (kWh)"
          source="febP50Prod"
        />
        <NumberField label="March P50 Production (kWh)" source="marP50Prod" />
        <NumberField label="April P50 Production (kWh)" source="aprP50Prod" />
        <NumberField label="May P50 Production (kWh)" source="mayP50Prod" />
        <NumberField label="June P50 Production (kWh)" source="junP50Prod" />
        <NumberField label="July P50 Production (kWh)" source="julP50Prod" />
        <NumberField label="August P50 Production (kWh)" source="augP50Prod" />
        <NumberField
          label="September P50 Production (kWh)"
          source="sepP50Prod"
        />
        <NumberField label="October P50 Production (kWh)" source="octP50Prod" />
        <NumberField
          label="November P50 Production (kWh)"
          source="novP50Prod"
        />
        <NumberField
          label="December P50 Production (kWh)"
          source="decP50Prod"
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Performance Ratio
        </Typography>
        <NumberField label="January Performance Ratio" source="janPR" />
        <NumberField label="February Performance Ratio" source="febPR" />
        <NumberField label="March Performance Ratio" source="marPR" />
        <NumberField label="April Performance Ratio" source="aprPR" />
        <NumberField label="May Performance Ratio" source="mayPR" />
        <NumberField label="June Performance Ratio" source="junPR" />
        <NumberField label="July Performance Ratio" source="julPR" />
        <NumberField label="August Performance Ratio" source="augPR" />
        <NumberField label="September Performance Ratio" source="sepPR" />
        <NumberField label="October Performance Ratio" source="octPR" />
        <NumberField label="November Performance Ratio" source="novPR" />
        <NumberField label="December Performance Ratio" source="decPR" />
        <Typography variant="h6" gutterBottom>
          POA Irradiance
        </Typography>
        <NumberField
          label="January Global Effective (Inclined) Irradiance (kWh/m2)"
          source="janGlobEff"
        />
        <NumberField
          label="February Global Effective (Inclined) Irradiance (kWh/m2)"
          source="febGlobEff"
        />
        <NumberField
          label="March Global Effective (Inclined) Irradiance (kWh/m2)"
          source="marGlobEff"
        />
        <NumberField
          label="April Global Effective (Inclined) Irradiance (kWh/m2)"
          source="aprGlobEff"
        />
        <NumberField
          label="May Global Effective (Inclined) Irradiance (kWh/m2)"
          source="mayGlobEff"
        />
        <NumberField
          label="June Global Effective (Inclined) Irradiance (kWh/m2)"
          source="junGlobEff"
        />
        <NumberField
          label="July Global Effective (Inclined) Irradiance (kWh/m2)"
          source="julGlobEff"
        />
        <NumberField
          label="August Global Effective (Inclined) Irradiance (kWh/m2)"
          source="augGlobEff"
        />
        <NumberField
          label="September Global Effective (Inclined) Irradiance (kWh/m2)"
          source="sepGlobEff"
        />
        <NumberField
          label="October Global Effective (Inclined) Irradiance (kWh/m2)"
          source="octGlobEff"
        />
        <NumberField
          label="November Global Effective (Inclined) Irradiance (kWh/m2)"
          source="novGlobEff"
        />
        <NumberField
          label="December Global Effective (Inclined) Irradiance (kWh/m2)"
          source="decGlobEff"
        />
        <Typography variant="h6" gutterBottom>
          Avg Module Temp
        </Typography>
        <NumberField
          label="January Module Temperature Reference (celsius)"
          source="janModuleTempRef"
        />
        <NumberField
          label="February Module Temperature Reference (celsius)"
          source="febModuleTempRef"
        />
        <NumberField
          label="March Module Temperature Reference (celsius)"
          source="marModuleTempRef"
        />
        <NumberField
          label="April Module Temperature Reference (celsius)"
          source="aprModuleTempRef"
        />
        <NumberField
          label="May Module Temperature Reference (celsius)"
          source="mayModuleTempRef"
        />
        <NumberField
          label="June Module Temperature Reference (celsius)"
          source="junModuleTempRef"
        />
        <NumberField
          label="July Module Temperature Reference (celsius)"
          source="julModuleTempRef"
        />
        <NumberField
          label="August Module Temperature Reference (celsius)"
          source="augModuleTempRef"
        />
        <NumberField
          label="September Module Temperature Reference (celsius)"
          source="sepModuleTempRef"
        />
        <NumberField
          label="October Module Temperature Reference (celsius)"
          source="octModuleTempRef"
        />
        <NumberField
          label="November Module Temperature Reference (celsius)"
          source="novModuleTempRef"
        />
        <NumberField
          label="December Module Temperature Reference (celsius)"
          source="decModuleTempRef"
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Average Ambient Temperature (optional)
        </Typography>
        <NumberField
          label="January Ambient Temperature Reference (celsius)"
          source="janAmbientTempRef"
        />
        <NumberField
          label="February Ambient Temperature Reference (celsius)"
          source="febAmbientTempRef"
        />
        <NumberField
          label="March Ambient Temperature Reference (celsius)"
          source="marAmbientTempRef"
        />
        <NumberField
          label="April Ambient Temperature Reference (celsius)"
          source="aprAmbientTempRef"
        />
        <NumberField
          label="May Ambient Temperature Reference (celsius)"
          source="mayAmbientTempRef"
        />
        <NumberField
          label="June Ambient Temperature Reference (celsius)"
          source="junAmbientTempRef"
        />
        <NumberField
          label="July Ambient Temperature Reference (celsius)"
          source="julAmbientTempRef"
        />
        <NumberField
          label="August Ambient Temperature Reference (celsius)"
          source="augAmbientTempRef"
        />
        <NumberField
          label="September Ambient Temperature Reference (celsius)"
          source="sepAmbientTempRef"
        />
        <NumberField
          label="October Ambient Temperature Reference (celsius)"
          source="octAmbientTempRef"
        />
        <NumberField
          label="November Ambient Temperature Reference (celsius)"
          source="novAmbientTempRef"
        />
        <NumberField
          label="December Ambient Temperature Reference (celsius)"
          source="decAmbientTempRef"
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Horizontal to POA Constant (optional)
        </Typography>
        <NumberField
          label="January Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="janPOAIrradianceFactor"
        />
        <NumberField
          label="February Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="febPOAIrradianceFactor"
        />
        <NumberField
          label="March Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="marPOAIrradianceFactor"
        />
        <NumberField
          label="April Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="aprPOAIrradianceFactor"
        />
        <NumberField
          label="May Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="mayPOAIrradianceFactor"
        />
        <NumberField
          label="June Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="junPOAIrradianceFactor"
        />
        <NumberField
          label="July Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="julPOAIrradianceFactor"
        />
        <NumberField
          label="August Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="augPOAIrradianceFactor"
        />
        <NumberField
          label="September Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="sepPOAIrradianceFactor"
        />
        <NumberField
          label="October Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="octPOAIrradianceFactor"
        />
        <NumberField
          label="November Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="novPOAIrradianceFactor"
        />
        <NumberField
          label="December Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="decPOAIrradianceFactor"
        />
      </TabbedShowLayout.Tab>

      {/* <TabbedShowLayout.Tab label="O&M" path={'O&M'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (!formData.omFlg) {
                    return (
                      <Alert severity="warning">
                        This project is not currently flagged for O&M. Monthly
                        reports will not be auto-generated.
                      </Alert>
                    );
                  }
                  return null;
                }}
              </FormDataConsumer>
              <Grid item xs={12} md={6}>
                <BooleanInput
                  source="omFlg"
                  label="O&M Flag"
                  helperText="Turning this on will enable monthly O&M reports to be generated for this project"
                  fullWidth
                />
                <ReferenceInput
                  source="omTruck.id"
                  reference="OMTruck"
                  perPage={10000}
                >
                  <SelectInput optionText="name" label="O&M Truck" fullWidth />
                </ReferenceInput>
                <ReferenceInput
                  source="projectManager.id"
                  reference="EmployeeLite"
                  sort={{ field: 'firstName', order: 'ASC' }}
                  perPage={10000}
                  // filter={{ employeeTypeId: 1 }} // TODO: This should be turned back on but it will require upkeep
                >
                  <SelectInput
                    label="Project Manager"
                    fullWidth
                    allowEmpty
                    optionText="fullName"
                  />
                </ReferenceInput>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Credentials</b>
              </Typography>
              <ArrayInput fullWidth source="omPlatformCredentials" label="">
                <SimpleFormIterator
                  TransitionProps={{ enter: false, exit: false }}
                  disableReordering
                  inline
                >
                  <TextInput
                    fullWidth
                    required
                    label="Platform name"
                    source="name"
                  />
                  <TextInput label="Platform url" source="url" fullWidth />
                  <TextInput label="Username" source="username" fullWidth />
                  <TextInput label="Password" source="password" fullWidth />
                  <RichTextInput label="Notes" source="notes" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contacts</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectContacts"
                // id="project-contacts"
                label=""
              >
                <SimpleFormIterator
                  disableReordering
                  inline
                  TransitionProps={{ enter: false, exit: false }}
                >
                  <TextInput
                    fullWidth
                    required
                    label="Label"
                    helperText="ex: O&M Contractor"
                    source="name"
                  />
                  <TextInput
                    label="Primary Contact Name"
                    source="primaryContactName"
                    fullWidth
                  />
                  <TextInput label="Phone" source="phone" fullWidth />
                  <TextInput label="Email" source="email" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Insurance Policies</b>
              </Typography>
              <ArrayField source="projectInsurancePolicies" sortable={false}>
                <SingleFieldList>
                  <CustomReferenceField
                    source="label"
                    color={() => 'primary'}
                  />
                </SingleFieldList>
              </ArrayField>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/InsurancePolicy/create"
              >
                Add new insurance policy
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contracts</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  // if (!formData.omContractsDropboxLocation && !editDropboxLocation) {
                  //   setEditDropboxLocation(true)
                  // }
                  return (
                    <>
                      {!editDropboxLocation ? (
                        <Grid container>
                          <Grid item xs={11} style={{ paddingLeft: '1rem' }}>
                            <Typography variant="body2" gutterBottom>
                              <b>Dropbox directory:</b>{' '}
                              {formData.omContractsDropboxLocation ||
                                'None specified. Click the pencil to edit.'}
                            </Typography>
                          </Grid>
                          <Grid item xs={1}>
                            <Grid container item justifyContent="flex-end">
                              <IconButton
                                onClick={() => {
                                  setEditDropboxLocation(true);
                                }}
                                size="large"
                              >
                                <EditIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Grid>
                      ) : (
                        <TextInput
                          source="omContractsDropboxLocation"
                          fullWidth
                          helperText="The '8.1.1 O&M Agreement' directory for this project. Ex: '/Energea Global/Market II - USA/Portfolio/Projects/Portfolio 4 - MA - Waltham/8. Asset Management/8.1 O&M/8.1.1 O&M Agreement'"
                        />
                      )}
                      <Table>
                        <TableBody>
                          {formData.contracts &&
                          formData.contracts.length > 0 ? (
                            formData.contracts.map((contract, index) => (
                              <TableRow key={`contracts-row-${contract.id}`}>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none', width: '1rem' }
                                      : { width: '1rem' }
                                  }
                                >
                                  {contract.isDirectory ? (
                                    <Folder />
                                  ) : contract.isFile ? (
                                    <Description />
                                  ) : (
                                    <Help />
                                  )}
                                </TableCell>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <b>{contract.name}</b>
                                </TableCell>
                                <TableCell
                                  align="right"
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <Tooltip title="Download">
                                    <IconButton
                                      color="primary"
                                      disabled={!contract.isDownloadable}
                                      onClick={() =>
                                        handleDownloadDropboxFile(
                                          contract.location
                                        )
                                      }
                                      size="large"
                                    >
                                      <GetApp />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      style={{
                                        color: !contract.isFile
                                          ? null
                                          : theme.palette.error.main,
                                      }}
                                      disabled={!contract.isFile}
                                      onClick={() => {
                                        if (
                                          window.confirm(
                                            `Clicking 'OK' will PERMANENTLY DELETE this document from Dropbox. Are you sure you wish to delete '${contract.name}'?`
                                          )
                                        ) {
                                          handleDeleteDropboxFile(
                                            contract.location
                                          );
                                        }
                                      }}
                                      size="large"
                                    >
                                      <Delete />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <Alert severity="info">
                              No files found in Dropbox directory
                            </Alert>
                          )}
                        </TableBody>
                      </Table>
                      <Button
                        style={{ marginTop: '1rem' }}
                        variant="contained"
                        color="secondary"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        Upload Contract to Dropbox
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            handleUploadFileToDropbox(
                              event,
                              formData.omContractsDropboxLocation
                            )
                          }
                        />
                      </Button>
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </TabbedShowLayout.Tab> */}

      <TabbedShowLayout.Tab label="Equipment Inventory" path={'Inventory'}>
        <Typography variant="h6" gutterBottom>
          <b>Equipment Inventory</b>
        </Typography>
        <ArrayField source="projectEquipmentItems">
          <Datagrid>
            <ReferenceField
              source="equipmentItem.id"
              label="Equipment Item"
              reference="EquipmentItem"
            >
              <TextField source="label" />
            </ReferenceField>
            <NumberField label="Quantity" source="quantity" />
            <BooleanField source="sparePartFlg" label="Is Spare Part?" />
          </Datagrid>
        </ArrayField>
      </TabbedShowLayout.Tab>

      {/* <TabbedShowLayout.Tab label="Credit Mgmt" path={'CreditMgmt'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Credit Management</b>
              </Typography>
              <ReferenceInput
                source="utilityCompany.id"
                reference="UtilityCompany"
                perPage={10000}
              >
                <SelectInput
                  optionText="name"
                  label="Utility Company"
                  fullWidth
                />
              </ReferenceInput>
              <ReferenceInput
                source="brConsortium.id"
                reference="BrConsortium"
                perPage={10000}
              >
                <SelectInput
                  optionText="legalName"
                  label="Consortium"
                  fullWidth
                />
              </ReferenceInput>
              <SelectInput
                source="consortiumType"
                choices={[
                  {
                    id: 'Distributed generation',
                    name: 'Distributed generation',
                  },
                  {
                    id: 'Self-consumption',
                    name: 'Self-consumption',
                  },
                ]}
                fullWidth
              />
              <BooleanInput
                source="creditManagementExternalFlg"
                fullWidth
                helperText="Are this project's offtaker(s) managed by a third party externally?"
              />
            </Grid>
          </Grid>
        </TabbedShowLayout.Tab> */}

      <TabbedShowLayout.Tab label="Env. Impact">
        <Typography variant="h5" gutterBottom>
          <b>Projected</b>
        </Typography>
        <Typography gutterBottom>
          {record?.name} will create{' '}
          <b>
            {numeral(record?.lifetimeEnergyProjection / 1000).format('0,0')} MWh
          </b>{' '}
          in its lifetime. For perspective, that&apos;s equal to:
        </Typography>
        <EPCConversionGrid
          production={record?.lifetimeEnergyProjection / 1000}
          columns={3}
          dataPointKeys={[
            'tonsReduced',
            'homesPowered',
            'treesPlanted',
            'gallonsOfGasConsumed',
            'poundsCoalBurned',
            'smartPhonesCharged',
          ]}
        />
        <Divider style={{ margin: '1rem 0' }} />

        <Typography variant="h5" gutterBottom>
          <b>Actual</b>
        </Typography>
        <Typography gutterBottom>
          {record?.name} has created{' '}
          <b>{numeral(record?.allTimeActual / 1000).format('0,0.0')} MWh</b> so
          far. For perspective, that&apos;s equal to:
        </Typography>
        <EPCConversionGrid
          production={record?.allTimeActual / 1000}
          columns={3}
          dataPointKeys={[
            'tonsReduced',
            'homesPowered',
            'treesPlanted',
            'gallonsOfGasConsumed',
            'poundsCoalBurned',
            'smartPhonesCharged',
          ]}
        />
      </TabbedShowLayout.Tab>
    </TabbedShowLayout>
  );
};

export const VictoryHillProjectShow = () => {
  return (
    <Show title={<Title />}>
      <ProjectShowView />
      {/* <SimpleShowLayout> */}
    </Show>
  );
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <ExportButton maxResults={100000} />
    </TopToolbar>
  );
};

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);
export const VictoryHillProjectList = () => (
  <List
    perPage={25}
    pagination={<CustomPagination />}
    actions={<ListActions />}
  >
    <Datagrid rowClick={'show'}>
      <TextField source="id" />
      <TextField source="name" />
      <DetailField source="shortSummary" sortable={false} />
      {/* <DetailField source="shortSummaryPT" sortable={false} /> */}
      <CustomBooleanField source="isPublic" />
      <DateField label="NTP" source="ntpDt" />
      <DateField label="Projected COD" source="projectedCOD" />
      <DateField label="Actual COD" source="actualCOD" />
      <LinkField
        label="Status"
        linkSource="projectInvestmentStatus.id"
        labelSource="projectInvestmentStatus.name"
        reference="ProjectInvestmentStatus"
      />
      <LinkField
        label="Project Manager"
        linkSource="projectManager.id"
        labelSource="projectManager.fullName"
        reference="Employee"
      />
      <CustomBooleanField
        label="P50 Data Inputted"
        source="P50ProdCompleteFlg"
        sortable={false}
      />
      <CustomBooleanField
        label="Has primary video?"
        source="hasPrimaryVideo"
        sortable={false}
      />
      <FunctionField
        label="Main Image"
        render={(record) => {
          if (!record.primaryImage) return null; // TODO return placeholder
          return (
            <Image
              cloud_name={Config.cloud_name}
              publicId={record.primaryImage.public_id}
            >
              <Transformation width="120" crop="scale" />
            </Image>
          );
        }}
      />
      <NumberField
        label="Cost Assessment"
        source="costAssessment"
        options={{ style: 'currency', currency: 'USD' }}
      />
      <NumberField label="Proj. Sys Size (MW) DC" source="systemSizeDC" />
      <NumberField label="Proj. Sys Size (MW) AC" source="systemSizeAC" />
      <DetailField source="internalNotes" sortable={false} />
    </Datagrid>
  </List>
);
