import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'User Review';

export const UserReviewEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput perPage={100} source="user.id" reference="UserLite">
              <AutocompleteInput
                allowEmpty={true}
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
                label="User (Investor)"
                required
                fullWidth
              />
            </ReferenceInput>
            <CustomNumberInput required source="rating" fullWidth />
            <TextInput source="comment" fullWidth multiline />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const UserReviewList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <NumberField source="rating" />
        <TextField source="comment" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const UserReviewCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput perPage={100} source="user.id" reference="UserLite">
            <AutocompleteInput
              allowEmpty={true}
              optionText="fullName"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
              label="User (Investor)"
              required
              fullWidth
            />
          </ReferenceInput>
          <CustomNumberInput source="rating" required fullWidth />
          <TextInput source="comment" fullWidth multiline />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
