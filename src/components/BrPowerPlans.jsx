import React from 'react';
import { useParams } from 'react-router-dom';
import numeral from 'numeral';

import {
  ArrayField,
  AutocompleteInput,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  NumberField,
  ReferenceArrayInput,
  ReferenceInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Alert, AlertTitle, Card, Grid, Typography } from '@mui/material';

import {
  CustomBooleanField,
  CustomNumberInput,
  CustomReferenceField,
  LinkField,
  PercentageInput,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Power Plan';

export const BrPowerPlanEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <FormDataConsumer>
              {({ formData }) => {
                const hasTermsOfAdhesions =
                  formData.brTermsOfAdhesions?.length > 0;
                const projectFilter = {};
                if (formData.utilityCompany?.id) {
                  projectFilter.utilityCompany = {
                    id: formData.utilityCompany?.id,
                  };
                }
                return (
                  <>
                    <Alert severity="info">
                      <AlertTitle>
                        Terms of adhesions using this power plan:{' '}
                        {formData.brTermsOfAdhesions?.length}
                      </AlertTitle>
                    </Alert>
                    <TextInput
                      source="name"
                      fullWidth
                      disabled={!!hasTermsOfAdhesions}
                    />
                    <ReferenceInput
                      source="brTariffClass.id"
                      reference="BrTariffClass"
                      perPage={10_000}
                      sort={{ field: 'id', order: 'ASC' }}
                    >
                      <SelectInput
                        optionText="name"
                        label="Tariff Class"
                        fullWidth
                        allowEmpty
                        disabled={!!hasTermsOfAdhesions}
                      />
                    </ReferenceInput>
                    <ReferenceInput
                      source="utilityCompany.id"
                      reference="UtilityCompany"
                      perPage={10_000}
                      sort={{ field: 'id', order: 'ASC' }}
                    >
                      <SelectInput
                        optionText="name"
                        label="Utility Company"
                        fullWidth
                        allowEmpty
                        disabled={!!hasTermsOfAdhesions}
                      />
                    </ReferenceInput>
                    <ReferenceArrayInput
                      source="salesPersonBrContactIds"
                      reference="BrContact"
                      fullWidth
                      sort={{ field: 'firstName', order: 'ASC' }}
                      filter={{ isSalesPerson: true }}
                      perPage={10_000}
                    >
                      <SelectArrayInput
                        optionText="fullName"
                        fullWidth
                        label="Sales People"
                        helperText="These are the sales people that have access to this power plan"
                      />
                    </ReferenceArrayInput>
                    <ReferenceInput
                      source="overrideSalesforceProject.id"
                      reference="SalesforceProject"
                      perPage={10_000}
                      sort={{ field: 'id', order: 'ASC' }}
                      filter={projectFilter}
                    >
                      <SelectInput
                        optionText="name"
                        label="Project Override (Optional)"
                        fullWidth
                        allowEmpty
                        disabled={!!hasTermsOfAdhesions}
                        helperText={
                          <Typography variant="caption">
                            This is an optional field. Selecting a project for a
                            power plan will assign all new consumer units that
                            sign a terms of adhesion associated with this power
                            plan to this project. If none is set, then the
                            default project for the CUs utility company will be
                            used instead (which can be set{' '}
                            <a href={'/UtilityCompany'}>here</a>).{' '}
                          </Typography>
                        }
                      />
                    </ReferenceInput>
                    <PercentageInput
                      source="discountRate"
                      fullWidth
                      disabled={!!hasTermsOfAdhesions}
                    />
                    <Card style={{ padding: '1rem' }}>
                      <Typography variant="h6">
                        Commission Rate Payment Structure
                      </Typography>
                      <PercentageInput
                        source="adminResidualCommissionRate"
                        fullWidth
                        // disabled={!!hasTermsOfAdhesions}
                      />
                      <PercentageInput
                        source="adminUpfrontCommissionRate"
                        fullWidth
                        // disabled={!!hasTermsOfAdhesions}
                      />
                      <PercentageInput
                        source="salesPersonResidualCommissionRate"
                        fullWidth
                        // disabled={!!hasTermsOfAdhesions}
                      />
                      <PercentageInput
                        source="salesPersonUpfrontCommissionRate"
                        fullWidth
                        // disabled={!!hasTermsOfAdhesions}
                      />
                    </Card>
                    <BooleanInput
                      source="defaultFlg"
                      fullWidth
                      defaultValue={false}
                      helperText="If turned on, this will be the power plan selected for organic traffic from energea.com.br"
                    />
                  </>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Power Plan name"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="Sales People"
      source="salesPersonBrContact.id"
      reference="BrContact"
      perPage={10_000}
      sort={{ field: 'firstName', order: 'ASC' }}
      filter={{ isSalesPerson: true }}
      alwaysOn
    >
      <AutocompleteInput
        label="Sales Person"
        optionText="fullName"
        style={{ minWidth: '300px' }}
      />
    </ReferenceInput>
    <ReferenceInput
      label="Tariff class"
      source="brTariffClass.id"
      reference="BrTariffClass"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <SelectInput label="Tariff class" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Utility company"
      source="utilityCompany.id"
      reference="UtilityCompany"
      perPage={10_000}
      sort={{ field: 'id', order: 'ASC' }}
    >
      <SelectInput label="Utility company" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const BrPowerPlanList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<CustomFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <LinkField
          reference="BrTariffClass"
          linkSource="brTariffClass.id"
          labelSource="brTariffClass.name"
          label="Tariff Class"
        />
        <LinkField
          reference="UtilityCompany"
          linkSource="utilityCompany.id"
          labelSource="utilityCompany.name"
          label="Utility Company"
        />
        <LinkField
          reference="SalesforceProject"
          linkSource="overrideSalesforceProject.id"
          labelSource="overrideSalesforceProject.name"
          label="Project Override"
        />
        <FunctionField
          label="Terms of adhesion count"
          sortable={false}
          render={(record) => (
            <Typography variant="body2">
              {record.brTermsOfAdhesions?.length}
            </Typography>
          )}
        />
        <ArrayField source="salesPersonBrContacts" label="Sales People">
          <SingleFieldList>
            <CustomReferenceField source="fullName" />
          </SingleFieldList>
        </ArrayField>
        <FunctionField
          label="Discount rate"
          render={(record) => numeral(record.discountRate).format('0,0[.]00%')}
          sortBy="discountRate"
        />
        <FunctionField
          label="Admin residual commission rate"
          render={(record) =>
            numeral(record.adminResidualCommissionRate).format('0,0[.]00%')
          }
          sortBy="adminResidualCommissionRate"
        />
        <FunctionField
          label="Admin upfront commission rate"
          render={(record) =>
            numeral(record.adminUpfrontCommissionRate).format('0,0[.]00%')
          }
          sortBy="adminUpfrontCommissionRate"
        />
        <FunctionField
          label="Sales person residual commission rate"
          render={(record) =>
            numeral(record.salesPersonResidualCommissionRate).format(
              '0,0[.]00%'
            )
          }
          sortBy="salesPersonResidualCommissionRate"
        />
        <FunctionField
          label="Sales person upfront commission rate"
          render={(record) =>
            numeral(record.salesPersonUpfrontCommissionRate).format('0,0[.]00%')
          }
          sortBy="salesPersonUpfrontCommissionRate"
        />
        <CustomBooleanField source="defaultFlg" />
      </Datagrid>
    </List>
  );
};

export const BrPowerPlanCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <ReferenceInput
            source="brTariffClass.id"
            reference="BrTariffClass"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Tariff Class"
              fullWidth
              allowEmpty
            />
          </ReferenceInput>
          <ReferenceInput
            source="utilityCompany.id"
            reference="UtilityCompany"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Utility Company"
              fullWidth
              allowEmpty
            />
          </ReferenceInput>
          <ReferenceArrayInput
            source="salesPersonBrContactIds"
            reference="BrContact"
            fullWidth
            sort={{ field: 'firstName', order: 'ASC' }}
            filter={{ isSalesPerson: true }}
            perPage={10_000}
          >
            <SelectArrayInput
              optionText="fullName"
              fullWidth
              label="Sales People"
              helperText="These are the sales people that have access to this power plan"
            />
          </ReferenceArrayInput>
          <FormDataConsumer>
            {({ formData }) => {
              const projectFilter = {};
              if (formData.utilityCompany?.id) {
                projectFilter.utilityCompany = {
                  id: formData.utilityCompany?.id,
                };
              }
              return (
                <ReferenceInput
                  source="overrideSalesforceProject.id"
                  reference="SalesforceProject"
                  perPage={10_000}
                  sort={{ field: 'id', order: 'ASC' }}
                  filter={projectFilter}
                >
                  <SelectInput
                    optionText="name"
                    label="Project Override (Optional)"
                    fullWidth
                    allowEmpty
                    helperText={
                      <Typography variant="caption">
                        This is an optional field. Selecting a project for a
                        power plan will assign all new consumer units that sign
                        a terms of adhesion associated with this power plan to
                        this project. If none is set, then the default project
                        for the CUs utility company will be used instead (which
                        can be set <a href={'/UtilityCompany'}>here</a>).{' '}
                      </Typography>
                    }
                  />
                </ReferenceInput>
              );
            }}
          </FormDataConsumer>
          <Card style={{ padding: '1rem' }}>
            <Typography variant="h6">
              Commission Rate Payment Structure
            </Typography>
            <PercentageInput source="discountRate" fullWidth />
            <PercentageInput source="adminResidualCommissionRate" fullWidth />
            <PercentageInput source="adminUpfrontCommissionRate" fullWidth />
            <PercentageInput
              source="salesPersonResidualCommissionRate"
              fullWidth
            />
            <PercentageInput
              source="salesPersonUpfrontCommissionRate"
              fullWidth
            />
          </Card>
          <BooleanInput
            source="defaultFlg"
            fullWidth
            defaultValue={false}
            helperText="If turned on, this will be the power plan selected for organic traffic from energea.com.br"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
