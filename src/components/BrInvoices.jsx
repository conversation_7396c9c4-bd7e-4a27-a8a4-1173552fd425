import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  DateTimeInput,
  Edit,
  ExportButton,
  Filter,
  FormDataConsumer,
  FunctionField,
  Labeled,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Avatar,
  Button,
  Card,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  Icon,
  TextField as MuiTextField,
  Typography,
} from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import {
  AddCircleOutline,
  Cancel,
  CloudDownload,
  CloudUpload,
} from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import moment from 'moment';
import { downloadSimpleExcelFromRows } from '../utils/excel';
import { EmailExportButton } from './EmailExportButton';
import theme from '../theme';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Invoice';

export const BrInvoiceEdit = () => {
  const [invoiceUploading, setInvoiceUploading] = useState(false);
  const { id } = useParams();
  const { permissions } = usePermissions();
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  const getBrInvoiceDownloadUrl = (id) => {
    dataProvider
      .getOne('BrInvoiceDownloadUrl', { data: { brInvoiceId: id } })
      .then(
        (res) => {
          if (res?.data?.brInvoiceDownloadUrl) {
            window.location.assign(res.data.brInvoiceDownloadUrl);
          }
        },
        (err) => {
          notify('Error getting download URL', { type: 'error' });
        }
      );
  };

  const uploadInvoiceToS3 = (event, formData) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `OfftakerInvoices/${formData.brBillingCycle.label}/${
          formData.brCreditCompensation.label
        }_${moment().valueOf()}`
      );
      setInvoiceUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          dataProvider
            .update('BrInvoice', {
              data: { id: parseInt(id, 10), awsObjectKey },
            })
            .then(
              (res) => {
                notify('Invoice uploaded', { type: 'success' });
                setInvoiceUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading invoice', { type: 'error' });
                setInvoiceUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading invoice to S3', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                const isStripeInvoice = formData.stripeInvoice;
                const isStarkBankBoleto = formData.starkBankBoletoId;
                const isSelfConsumption =
                  formData?.brSelfConsumptionOfftaker?.id;
                return (
                  <>
                    <Labeled fullWidth>
                      <LinkField
                        reference="BrBillingCycleLite"
                        linkSource="brBillingCycle.id"
                        labelSource="brBillingCycle.label"
                        label="Billing Cycle"
                      />
                    </Labeled>
                    <Labeled fullWidth>
                      <LinkField
                        reference="BrSelfConsumptionOfftaker"
                        linkSource="brSelfConsumptionOfftaker.id"
                        labelSource="brSelfConsumptionOfftaker.name"
                        label="Self-Consumption Offtaker"
                      />
                    </Labeled>
                    <CustomNumberInput
                      source="amountDue"
                      fullWidth
                      disabled={isStripeInvoice}
                    />
                    <CustomNumberInput
                      source="amountPaid"
                      fullWidth
                      disabled={isStripeInvoice}
                    />
                    <CustomNumberInput
                      source="paymentProcessingFees"
                      fullWidth
                    />
                    <DateInput
                      source="dueDt"
                      fullWidth
                      disabled={isStripeInvoice}
                    />
                    <DateInput source="originalDueDt" fullWidth />
                    <DateTimeInput source="invoicePaidDt" fullWidth />
                    <DateInput
                      source="cancelledDt"
                      fullWidth
                      disabled={isStripeInvoice}
                    />
                    <DateInput
                      source="sentToCollectionsDt"
                      fullWidth
                      label="Sent to SERASA dt"
                    />
                    <DateInput
                      source="markedAsUncollectibleDt"
                      fullWidth
                      helperText="Setting this date will cause these invoices to be counted separately on the Credit Mgmt Dashboard. This should be used if we do not expect to receive any payment for this invoice and we want to remove it from the over due revenue metrics. This should not be set if 'Cancelled Dt' is also set."
                    />
                    {isSelfConsumption && (
                      <>
                        <CustomNumberInput
                          source="selfConsumptionOmRevenue"
                          fullWidth
                          label="Self-Consumption O&M Revenue (Issued)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionLandRevenue"
                          fullWidth
                          label="Self-Consumption Land Revenue (Issued)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionEquipmentRevenue"
                          fullWidth
                          label="Self-Consumption Equipment Revenue (Issued)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionUmbrellaRevenue"
                          fullWidth
                          label="Self-Consumption Umbrella Revenue (Issued)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionOmRevenueApproved"
                          fullWidth
                          label="Self-Consumption O&M Revenue (Approved)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionLandRevenueApproved"
                          fullWidth
                          label="Self-Consumption Land Revenue (Approved)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionEquipmentRevenueApproved"
                          fullWidth
                          label="Self-Consumption Equipment Revenue (Approved)"
                        />
                        <CustomNumberInput
                          source="selfConsumptionUmbrellaRevenueApproved"
                          fullWidth
                          label="Self-Consumption Umbrella Revenue (Approved)"
                        />
                      </>
                    )}
                    <FormDataConsumer>
                      {({ formData, ...rest }) => {
                        if (formData?.brSelfConsumptionOfftaker?.id)
                          return null;
                        return (
                          <Labeled fullWidth>
                            <LinkField
                              reference="BrCreditCompensation"
                              linkSource="brCreditCompensation.id"
                              labelSource="brCreditCompensation.label"
                              label="Credit Compensation"
                            />
                          </Labeled>
                        );
                      }}
                    </FormDataConsumer>
                    <FunctionField
                      label="Invoice"
                      render={(record) => {
                        return (
                          <Button
                            disabled={!record.energeaStatementDownloadUrl}
                            variant="contained"
                            startIcon={<CloudDownload />}
                            style={{ textTransform: 'none' }}
                            onClick={() =>
                              window.location.assign(
                                record.energeaStatementDownloadUrl
                              )
                            }
                          >
                            Download Energea Statement
                          </Button>
                        );
                      }}
                    />
                    {isStripeInvoice && (
                      <FunctionField
                        label="Invoice"
                        render={(record) => {
                          return (
                            <Grid item>
                              <Typography variant="body2">
                                <b>Invoice payment page:</b>{' '}
                                <a
                                  href={record.stripeInvoice?.invoicePaymentUrl}
                                  target="_blank"
                                >
                                  {record.stripeInvoice?.invoicePaymentUrl}
                                </a>
                              </Typography>
                            </Grid>
                          );
                        }}
                      />
                    )}
                    <FunctionField
                      label="Invoice"
                      render={(record) => {
                        const downloadExists =
                          record.awsObjectKey ||
                          record.stripeInvoiceId ||
                          record.starkBankBoletoId;
                        return (
                          <Grid container spacing={2}>
                            <Grid item>
                              <Button
                                disabled={!downloadExists}
                                variant="contained"
                                startIcon={<CloudDownload />}
                                style={{ textTransform: 'none' }}
                                onClick={() =>
                                  getBrInvoiceDownloadUrl(record.id)
                                }
                              >
                                Download Invoice
                              </Button>
                            </Grid>
                            <Grid item>
                              <FormDataConsumer>
                                {({ formData, ...rest }) => {
                                  return (
                                    <Collapse
                                      in={
                                        formData.brBillingCycle?.label &&
                                        formData.brCreditCompensation?.label &&
                                        !record.stripeInvoiceId &&
                                        !record.starkBankBoletoId
                                      }
                                    >
                                      <Button
                                        color="primary"
                                        variant="contained"
                                        component="label" // https://stackoverflow.com/a/********
                                        startIcon={<CloudUpload />}
                                        style={{ textTransform: 'none' }}
                                        disabled={invoiceUploading}
                                      >
                                        {invoiceUploading ? (
                                          <CircularProgress
                                            style={{ position: 'absolute' }}
                                          />
                                        ) : null}
                                        {downloadExists
                                          ? 'Overwrite Invoice'
                                          : 'Upload Invoice'}
                                        <input
                                          type="file"
                                          hidden
                                          onChange={(event) =>
                                            uploadInvoiceToS3(event, formData)
                                          }
                                          accept="application/pdf"
                                        />
                                      </Button>
                                      <Typography variant="body2">
                                        This should be a PDF
                                      </Typography>
                                    </Collapse>
                                  );
                                }}
                              </FormDataConsumer>
                            </Grid>
                          </Grid>
                        );
                      }}
                    />
                    <Card
                      style={{
                        backgroundColor: '#eee',
                        borderRadius: theme.shape.borderRadius,
                        padding: '1rem',
                        margin: '1rem 0',
                      }}
                    >
                      <Typography variant="body1">
                        <b>Only one of the following should be filled out:</b>
                      </Typography>
                      <TextInput
                        source="starkBankBoletoId"
                        label="Stark bank boleto Id"
                        fullWidth
                        helperText="This is what allows for automatic updates to the CMS made by Stark bank such as marking an invoice as 'Paid' or 'Cancelled'. If you reissue a boleto from Stark Banks platform, the Id of the new boleto will need to go here."
                      />
                      <TextInput
                        source="stripeInvoiceId"
                        label="Stripe invoice Id"
                        fullWidth
                        helperText="This is what allows for automatic updates to the CMS made by Stripe such as marking an invoice as 'Paid' or 'Cancelled'."
                      />
                    </Card>
                  </>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => {
  const invoicePaymentMonthChoices = [];
  const currentYear = new Date().getFullYear();
  const startYear = 2020;
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  for (let y = currentYear; y >= startYear; y--) {
    for (let m = 12; m >= 1; m--) {
      invoicePaymentMonthChoices.push({
        id: `${y}-${m}`,
        name: `${y} ${months[m - 1]}`,
      });
    }
  }

  return (
    <Filter {...props}>
      <TextInput
        style={{ minWidth: '420px' }}
        label="Search by Consumer Unit"
        source="q"
        alwaysOn
      />
      <SelectInput
        alwaysOn
        source="paymentStatus"
        choices={[
          { id: 'paid', name: 'Paid' },
          { id: 'pastDue', name: 'Past Due' },
          { id: 'pending', name: 'Pending' },
          { id: 'cancelled', name: 'Cancelled' },
          { id: 'collectionsPaid', name: 'SERASA Paid' },
          { id: 'collectionsUnpaid', name: 'SERASA Unpaid' },
        ]}
        label="Payment Status"
      />
      <ReferenceInput
        label="Billing Cycle"
        source="brBillingCycle.id"
        reference="BrBillingCycleLite"
        perPage={10_000}
        sort={{ field: 'billingMonth', order: 'DESC' }}
      >
        <SelectInput label="Billing Cycle" optionText="label" />
      </ReferenceInput>
      <ReferenceInput
        label="Project"
        source="salesforceProject.id"
        reference="SalesforceProject"
        perPage={10_000}
        sort={{ field: 'name', order: 'ASC' }}
      >
        <SelectInput label="Project" optionText="name" />
      </ReferenceInput>
      <ReferenceInput
        label="Sales Partner"
        source="brSalesPerson.id"
        reference="BrSalesPerson"
        perPage={10_000}
        sort={{ field: 'id', order: 'ASC' }}
      >
        <AutocompleteInput label="Sales Partner" optionText="name" />
      </ReferenceInput>
      <SelectInput
        source="invoicePaidMonth"
        choices={invoicePaymentMonthChoices}
        label="Payment Month"
      />
    </Filter>
  );
};

const exporter = (rows) => {
  const attrs = [
    {
      label: 'Id',
      value: (record) => record.id,
    },
    {
      label: 'Billing cycle',
      value: (record) => record.brBillingCycle?.label,
    },
    {
      label: 'Consumer unit',
      value: (record) => record.brConsumerUnit?.name,
    },
    {
      label: 'Self-consumption offtaker',
      value: (record) => record.brSelfConsumptionOfftaker?.name,
    },
    {
      label: 'Payment status',
      value: (record) => record.paymentStatus?.label,
    },
    {
      label: 'Amount due',
      value: (record) => record.amountDue,
    },
    {
      label: 'Amount paid',
      value: (record) => record.amountPaid,
    },
    {
      label: 'Payment processing fees',
      value: (record) => record.paymentProcessingFees,
    },
    {
      label: 'Due date',
      value: (record) =>
        record.dueDt && moment(record.dueDt).format('DD/MM/YYYY'),
    },
    {
      label: 'Original Due date',
      value: (record) =>
        record.originalDueDt &&
        moment(record.originalDueDt).format('DD/MM/YYYY'),
    },
    {
      label: 'Invoice sent date',
      value: (record) =>
        record.invoiceSentDt &&
        moment(record.invoiceSentDt).format('DD/MM/YYYY'),
    },
    {
      label: 'Invoice paid date',
      value: (record) =>
        record.invoicePaidDt &&
        moment(record.invoicePaidDt).format('DD/MM/YYYY'),
    },
    {
      label: 'Cancelled date',
      value: (record) =>
        record.cancelledDt && moment(record.cancelledDt).format('DD/MM/YYYY'),
    },
    {
      label: 'Sent to SERASA date',
      value: (record) =>
        record.sentToCollectionsDt &&
        moment(record.sentToCollectionsDt).format('DD/MM/YYYY'),
    },
    {
      label: 'Created Date',
      value: (record) =>
        record.createdAt &&
        moment(record.createdAt).format('MMM D, YYYY HH:mm:ss'),
    },
    {
      label: 'Current Terms of Adhesion',
      value: (record) => record.brConsumerUnit?.currentBrTermsOfAdhesion?.label,
    },
    {
      label: 'Discount Rate (Contracted)',
      value: (record) =>
        record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan
          ?.discountRate,
    },
    {
      label: 'Discount Rate (Realized)',
      value: (record) => record.brCreditCompensation?.discountRate,
    },
    {
      label: 'Project',
      value: (record) => record.brBillingCycle?.salesforceProject?.name,
    },
    {
      label: 'Injected Electricity Price',
      value: (record) => record.brCreditCompensation?.injectedElectricityPrice,
    },
    {
      label: 'Utility Electricity Price',
      value: (record) => record.brCreditCompensation?.utilityElectricityPrice,
    },
    {
      label: 'Energea Electricity Price',
      value: (record) =>
        record.brCreditCompensation?.discountedInjectedElectricityPrice,
    },
    {
      label: 'Sales Partner',
      value: (record) => record.brConsumerUnit?.brSalesPerson?.name,
    },
    {
      label: 'Admin Upfront Commission Rate',
      value: (record) =>
        record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan
          ?.adminUpfrontCommissionRate,
    },
    {
      label: 'Admin Residual Commission Rate',
      value: (record) =>
        record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan
          ?.adminResidualCommissionRate,
    },
    {
      label: 'Sales Partner Upfront Commission Rate',
      value: (record) =>
        record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan
          ?.salesPersonUpfrontCommissionRate,
    },
    {
      label: 'Sales Partner Residual Commission Rate',
      value: (record) =>
        record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan
          ?.salesPersonResidualCommissionRate,
    },
  ];

  const rowsForExport = rows.map((row) => {
    const returnRow = {};
    attrs.forEach((attr) => {
      returnRow[String(attr.label)] = attr.value(row);
    });
    return returnRow;
  });

  return downloadSimpleExcelFromRows(
    rowsForExport,
    attrs.map((attr) => attr.label),
    `Invoices.xlsx`
  );
};

const styleRow = (record, index) => {
  const { cancelledDt, stripeInvoice } = record;
  const cancelledStyle = {
    backgroundColor: '#ddd',
    fontStyle: 'italic',
  };
  const errorStyle = {
    backgroundColor: 'rgba(255,0,0,.2)',
    color: '#fff',
  };
  if (stripeInvoice) {
    if (
      (stripeInvoice.status === 'void' && !cancelledDt) ||
      (stripeInvoice.status !== 'void' && cancelledDt)
    ) {
      return errorStyle;
    }
  }
  if (cancelledDt) {
    return cancelledStyle;
  }
  return {};
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      {/* <ExportButton maxResults={10_000} /> */}
      <EmailExportButton
        {...props}
        resource={resource}
        filterValues={filterValues}
      />
    </TopToolbar>
  );
};

export const BrInvoiceList = () => {
  const [voidInvoiceDialogOpenId, setVoidInvoiceDialogOpenId] = useState(null);
  const [updateInvoiceDialogOpenId, setUpdateInvoiceDialogOpenId] =
    useState(null);
  const [updatedDueDate, setUpdatedDueDate] = useState(null);
  const [reissueInvoiceDialogOpenId, setReissueInvoiceDialogOpenId] =
    useState(null);
  const [sendInvoiceEmailDialogOpenId, setSendInvoiceEmailDialogOpenId] =
    useState(null);
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const handleVoidInvoice = (id) => {
    dataProvider.update('BrInvoice', { data: { voidInvoice: true, id } }).then(
      () => {
        notify('Invoice voided', { type: 'success' });
        refresh();
      },
      (err) => {
        console.error(err);
        notify('Error voiding invoice', { type: 'error' });
      }
    );
  };

  const handleUpdateInvoice = (id) => {
    dataProvider
      .update('BrInvoice', {
        data: { updateInvoice: true, id, dueDt: updatedDueDate },
      })
      .then(
        () => {
          notify('Invoice updated', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error updating invoice', { type: 'error' });
        }
      );
  };

  const handleReissueInvoice = (id) => {
    dataProvider
      .update('BrInvoice', { data: { reissueInvoice: true, id } })
      .then(
        () => {
          notify('Invoice created/reissued', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error reissuing invoice', { type: 'error' });
        }
      );
  };

  const handleSendInvoiceEmail = (id) => {
    dataProvider
      .update('BrInvoice', { data: { sendInvoiceEmail: true, id } })
      .then(
        () => {
          notify('Invoice email sent', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error sending invoice email', { type: 'error' });
        }
      );
  };

  const getBrInvoiceDownloadUrl = (id) => {
    dataProvider
      .getOne('BrInvoiceDownloadUrl', {
        data: { brInvoiceId: id, includeStatement: true },
      })
      .then(
        (res) => {
          if (res?.data?.brInvoiceDownloadUrl) {
            window.location.assign(res.data.brInvoiceDownloadUrl);
          }
        },
        (err) => {
          notify('Error getting download URL', { type: 'error' });
        }
      );
  };

  const CustomPagination = () => (
    <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
  );

  return (
    <>
      <List
        title={entityName}
        perPage={25}
        filters={<CustomFilter />}
        sort={{ field: 'id', order: 'desc' }}
        actions={<ListActions />}
        exporter={exporter}
        pagination={<CustomPagination />}
      >
        <Datagrid
          rowStyle={styleRow}
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <LinkField
            reference="BrBillingCycle"
            linkSource="brBillingCycle.id"
            labelSource="brBillingCycle.label"
            label="Billing Cycle"
          />
          <LinkField
            reference="BrConsumerUnit"
            linkSource="brConsumerUnit.id"
            labelSource="brConsumerUnit.name"
            label="Consumer Unit"
          />
          <LinkField
            reference="BrSelfConsumptionOfftaker"
            linkSource="brSelfConsumptionOfftaker.id"
            labelSource="brSelfConsumptionOfftaker.name"
            label="Self-Consumption Offtaker"
            sortable={false}
          />
          <FunctionField
            label="Payment Status"
            sortable={false}
            render={(record) => (
              <Grid
                container
                direction="column"
                justifyContent="center"
                alignItems="center"
              >
                <Grid item>
                  <Avatar
                    style={{
                      backgroundColor: record.paymentStatus.iconColor,
                    }}
                  >
                    <Icon
                      className={record.paymentStatus.iconClass}
                      style={{
                        color: '#fff',
                        width: 'auto',
                      }}
                    />
                  </Avatar>
                </Grid>
                <Grid item>
                  <Typography variant="body2" align="center">
                    {record.paymentStatus.label}
                  </Typography>
                </Grid>
              </Grid>
            )}
          />
          {/* <TextField source="paymentStatus.label" label="Payment status" /> */}
          <NumberField source="amountDue" />
          <NumberField source="amountPaid" />
          <NumberField source="paymentProcessingFees" />
          <DateField source="dueDt" />
          <DateField source="originalDueDt" />
          <DateField source="invoiceSentDt" />
          <DateField source="invoicePaidDt" showTime={true} />
          <DateField source="cancelledDt" />
          <DateField source="sentToCollectionsDt" label="Sent to SERASA dt" />
          <DateField source="markedAsUncollectibleDt" />
          <LinkField
            reference="BrCreditCompensation"
            linkSource="brCreditCompensation.id"
            labelSource="brCreditCompensation.label"
            label="Credit Compensation"
          />
          <FunctionField
            label="Invoice"
            render={(record) => {
              const downloadExists =
                record.starkBankBoletoId ||
                record.stripeInvoiceId ||
                record.awsObjectKey;
              const label = record.starkBankBoletoId ? 'Boleto' : 'Invoice';
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={(event) => {
                    if (!downloadExists) return;
                    event.stopPropagation();
                    getBrInvoiceDownloadUrl(record.id);
                  }}
                  color={downloadExists ? 'primary' : 'error'}
                  disabled={!downloadExists}
                >
                  {downloadExists ? `Download ${label}` : `${label} Missing`}
                </Button>
              );
            }}
          />
          <FunctionField
            label="Create/Reissue Invoice"
            render={(record) => {
              if (record.amountDue > 0) {
                return (
                  <Button
                    variant="contained"
                    startIcon={<AddCircleOutline />}
                    style={{ textTransform: 'none' }}
                    onClick={(event) => {
                      event.stopPropagation();
                      setReissueInvoiceDialogOpenId(record.id);
                    }}
                    disabled={
                      record.stripeInvoice &&
                      ['paid'].indexOf(record.stripeInvoice.status) > -1
                    }
                    color="primary"
                  >
                    {record.stripeInvoice || record.starkBankBoletoId
                      ? 'Reissue'
                      : 'Create'}{' '}
                    {record.stripeInvoice ? 'Invoice' : 'Boleto'}
                  </Button>
                );
              }
            }}
          />
          <FunctionField
            label="Send Invoice Email"
            render={(record) => {
              if (record.stripeInvoice || record.starkBankBoletoId)
                return (
                  <Button
                    variant="contained"
                    startIcon={<AddCircleOutline />}
                    style={{ textTransform: 'none', minWidth: '150px' }}
                    onClick={(event) => {
                      event.stopPropagation();
                      setSendInvoiceEmailDialogOpenId(record.id);
                    }}
                    disabled={
                      (record.stripeInvoice &&
                        ['paid'].indexOf(record.stripeInvoice.status) > -1) ||
                      record.amountPaid >= record.amountDue
                    }
                    color="primary"
                  >
                    Send Invoice Email{' '}
                    {record.invoiceSentDt &&
                      `(Last sent: ${moment(record.invoiceSentDt).format(
                        'MMM D, YYYY'
                      )})`}
                  </Button>
                );
            }}
          />
          <FunctionField
            label="Void Invoice"
            render={(record) => {
              if (record.stripeInvoice || record.starkBankBoletoId) {
                return (
                  <Button
                    variant="contained"
                    startIcon={<Cancel />}
                    style={{ textTransform: 'none' }}
                    onClick={(event) => {
                      event.stopPropagation();
                      setVoidInvoiceDialogOpenId(record.id);
                    }}
                    disabled={
                      record.stripeInvoice &&
                      ['void'].indexOf(record.stripeInvoice.status) > -1
                    }
                    color="error"
                  >
                    Cancel {record.starkBankBoletoId ? 'Boleto' : 'Invoice'}
                  </Button>
                );
              }
            }}
          />
          {/* <FunctionField
            label="Change Invoice Due Date"
            render={(record) => {
              // NOTE: Backend logic needs fixing
              return null;
              if (record.stripeInvoice)
                return (
                  <Button
                    variant="contained"
                    startIcon={<Cancel />}
                    style={{ textTransform: 'none' }}
                    onClick={(event) => {
                      event.stopPropagation();
                      setUpdateInvoiceDialogOpenId(record.id);
                    }}
                    disabled={
                      ['void'].indexOf(record.stripeInvoice.status) > -1
                    }
                    color="error"
                  >
                    Change Invoice Due Date
                  </Button>
                );
            }}
          /> */}
        </Datagrid>
      </List>
      <Dialog
        open={!!voidInvoiceDialogOpenId}
        onClose={() => setVoidInvoiceDialogOpenId(null)}
      >
        <DialogTitle>Void Invoice</DialogTitle>
        <DialogContent>
          <Typography>
            Voiding this invoice will make it unpayable by the client and it
            will be marked as cancelled. Are you sure you want to void this
            invoice?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setVoidInvoiceDialogOpenId(null)}
            style={{ textTransform: 'none' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              setVoidInvoiceDialogOpenId(null);
              handleVoidInvoice(voidInvoiceDialogOpenId);
            }}
            color="primary"
            variant="contained"
            style={{ textTransform: 'none' }}
          >
            Void Invoice
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={!!updateInvoiceDialogOpenId}
        onClose={() => setUpdateInvoiceDialogOpenId(null)}
      >
        <DialogTitle>Update Invoice</DialogTitle>
        <DialogContent>
          <MuiTextField
            label="Due Date"
            value={updatedDueDate}
            type="date"
            onChange={(event) => setUpdatedDueDate(event.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setUpdateInvoiceDialogOpenId(null)}
            style={{ textTransform: 'none' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              setUpdateInvoiceDialogOpenId(null);
              handleUpdateInvoice(updateInvoiceDialogOpenId);
            }}
            color="primary"
            variant="contained"
            style={{ textTransform: 'none' }}
          >
            Update Invoice
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={!!reissueInvoiceDialogOpenId}
        onClose={() => setReissueInvoiceDialogOpenId(null)}
      >
        <DialogTitle>Create/Reissue Invoice</DialogTitle>
        <DialogContent>
          <Typography>
            This action is intended to be used when the Credit Management team
            wishes to recreate an invoice to change the amount or due date. If
            this invoice has already been created in Stripe or Stark bank,
            reissuing this invoice will cancel it, making it unpayable by the
            client and a new invoice will be created. This new invoice will be
            for the amount due on this invoice record and not take into account
            the discount rate, injected electricity, or the electricity rate.
            <br />
            <br />
            Are you sure you want to continue?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setReissueInvoiceDialogOpenId(null)}
            style={{ textTransform: 'none' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              setReissueInvoiceDialogOpenId(null);
              handleReissueInvoice(reissueInvoiceDialogOpenId);
            }}
            color="primary"
            variant="contained"
            style={{ textTransform: 'none' }}
          >
            Create/Reissue Invoice
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={!!sendInvoiceEmailDialogOpenId}
        onClose={() => setSendInvoiceEmailDialogOpenId(null)}
      >
        <DialogTitle>Send Invoice Email</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to send this invoice via email?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setSendInvoiceEmailDialogOpenId(null)}
            style={{ textTransform: 'none' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              setSendInvoiceEmailDialogOpenId(null);
              handleSendInvoiceEmail(sendInvoiceEmailDialogOpenId);
            }}
            color="primary"
            variant="contained"
            style={{ textTransform: 'none' }}
          >
            Send Invoice Email
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export const BrInvoiceCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="brBillingCycle.id"
            reference="BrBillingCycleLite"
            perPage={10_000}
            sort={{ field: 'billingMonth', order: 'DESC' }}
            required
          >
            <SelectInput
              optionText="label"
              label="Billing Cycle"
              fullWidth
              required
            />
          </ReferenceInput>
          <DateInput source="dueDt" fullWidth />
          <DateInput source="originalDueDt" fullWidth />
          <CustomNumberInput source="amountDue" fullWidth required />
          <CustomNumberInput source="amountPaid" fullWidth required />
          <Typography variant="body2">
            Fill out one of the following:
          </Typography>
          <ReferenceInput
            source="brSelfConsumptionOfftaker.id"
            reference="BrSelfConsumptionOfftaker"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Self-Consumption Offtaker"
              fullWidth
              allowEmpty
            />
          </ReferenceInput>
          or
          <FormDataConsumer>
            {({ formData, ...rest }) => (
              <ReferenceInput
                source="brCreditCompensation.id"
                reference="BrCreditCompensationLite"
                perPage={10_000}
                sort={{ field: 'id', order: 'ASC' }}
                filter={{ brBillingCycle: { id: formData.brBillingCycle?.id } }}
              >
                <AutocompleteInput
                  optionText="label"
                  label="Credit Compensation"
                  fullWidth
                />
              </ReferenceInput>
            )}
          </FormDataConsumer>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
