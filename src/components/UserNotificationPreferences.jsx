import React, { useState, useEffect } from 'react';

import { useDataProvider, useNotify, useRefresh } from 'react-admin';
import {
  CircularProgress,
  Grid,
  ListItemText,
  Switch,
  TableContainer,
  TableHead,
  Table,
  TableBody,
  TableCell,
  TableRow,
} from '@mui/material';

import MuiList from '@mui/material/List';

export const UserNotificationPreferences = (props) => {
  const [eventTypes, setEventTypes] = useState(null);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const {
    data: { id: userId, userEventTypeCommunicationPreferences },
  } = props;

  if (!eventTypes) {
    dataProvider
      .getList('EventType', {
        sort: {
          field: 'id',
          order: 'ASC',
        },
        pagination: {
          page: 1,
          perPage: 10000,
        },
      })
      .then((eventTypes) => {
        setEventTypes(eventTypes.data);
      });
  }

  const handleSwitchEvent = ({ target }, eventTypeId, type, curState) => {
    let { preventEmailsFlg, hideNotificationsFlg } = curState;
    if (type === 'email') {
      preventEmailsFlg = !target.checked;
    } else if (type === 'notification') {
      hideNotificationsFlg = !target.checked;
    }
    dataProvider
      .update('UserEventTypeCommunicationPreference', {
        data: {
          userId,
          eventTypeId,
          preventEmailsFlg,
          hideNotificationsFlg,
        },
      })
      .then(
        () => {
          notify('Preferences successfully updated.');
          refresh();
        },
        () =>
          notify(`Error updating preferences. Please try again later.`, {
            type: 'error',
          })
      );
  };

  if (eventTypes && userEventTypeCommunicationPreferences) {
    return (
      <TableContainer component={Grid} item xs={12}>
        <Table size="medium">
          <TableHead>
            <TableRow>
              <TableCell />
              <TableCell
                style={{ fontWeight: 'bold', padding: '0 4px' }}
                align="center"
              >
                Emails
              </TableCell>
              <TableCell
                style={{ fontWeight: 'bold', padding: '0 4px' }}
                align="center"
              >
                Notifications
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {eventTypes.map((eventType) => {
              const index = userEventTypeCommunicationPreferences.findIndex(
                (element) => element.eventType.id === eventType.id
              );
              const preventEmailsFlg =
                index === -1
                  ? false
                  : userEventTypeCommunicationPreferences[Number(index)]
                      .preventEmailsFlg;
              const hideNotificationsFlg =
                index === -1
                  ? false
                  : userEventTypeCommunicationPreferences[Number(index)]
                      .hideNotificationsFlg;
              return (
                <TableRow key={`event-type-row-${eventType.id}`}>
                  <TableCell component="th" scope="row">
                    <MuiList dense={false}>
                      <ListItemText
                        primaryTypographyProps={{
                          style: { fontWeight: 'bold' },
                        }}
                        primary={eventType.name}
                        secondary={eventType.description}
                      />
                    </MuiList>
                  </TableCell>
                  <TableCell align="center">
                    <Switch
                      // disabled={pending}
                      onChange={(event) =>
                        handleSwitchEvent(event, eventType.id, 'email', {
                          preventEmailsFlg,
                          hideNotificationsFlg,
                        })
                      }
                      checked={!preventEmailsFlg}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Switch
                      // disabled={pending}
                      onChange={(event) =>
                        handleSwitchEvent(event, eventType.id, 'notification', {
                          preventEmailsFlg,
                          hideNotificationsFlg,
                        })
                      }
                      checked={!hideNotificationsFlg}
                    />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    );
  } else {
    return <CircularProgress />;
  }
};
