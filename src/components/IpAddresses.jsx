import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>y<PERSON>ield,
  Create,
  Datagrid,
  Edit,
  Filter,
  List,
  NumberField,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, CustomReferenceField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'IP Address';

export const IpAddressEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="ipAddress" fullWidth />
            <CustomNumberInput source="fraudScore" fullWidth />
            <TextInput source="city" fullWidth />
            <TextInput source="state" fullWidth />
            <TextInput source="country" fullWidth />
            <TextInput source="postalCode" fullWidth />
            <CustomNumberInput source="longitude" fullWidth />
            <CustomNumberInput source="latitude" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const IpAddressFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by IP Address"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const IpAddressList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<IpAddressFilter />}
      sort={{ field: 'id', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="ipAddress" />
        <NumberField source="fraudScore" />
        <TextField source="city" />
        <TextField source="state" />
        <TextField source="country" />
        <TextField source="postalCode" />
        <NumberField source="latitude" />
        <NumberField source="longitude" />
        <ArrayField source="userLogins">
          <SingleFieldList>
            <CustomReferenceField
              color={(resource) => 'primary'}
              source="label"
            />
          </SingleFieldList>
        </ArrayField>
      </Datagrid>
    </List>
  );
};

export const IpAddressCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="ipAddress" required fullWidth />
          <CustomNumberInput source="fraudScore" required fullWidth />
          <TextInput source="city" fullWidth />
          <TextInput source="state" fullWidth />
          <TextInput source="country" fullWidth />
          <TextInput source="postalCode" fullWidth />
          <CustomNumberInput source="latitude" fullWidth />
          <CustomNumberInput source="longitude" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
