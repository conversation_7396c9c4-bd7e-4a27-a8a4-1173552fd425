import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateInput,
  Edit,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'State Portfolio Tax Info';

export const StatePortfolioTaxInfoEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item md={6} xs={12}>
            <DateInput source="periodStartDt" fullWidth />
            <ReferenceInput source="portfolio.id" reference="PortfolioLite">
              <SelectInput label="Portfolio" fullWidth optionText="name" />
            </ReferenceInput>
            <ReferenceInput source="usState.id" reference="USState">
              <SelectInput label="State" fullWidth optionText="id" />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const StatePortfolioTaxInfoList = () => {
  const { permissions } = usePermissions();
  return (
    <List>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <LinkField
          label="State"
          linkSource="usState.id"
          labelSource="usState.id"
          reference="USState"
        />
        <TextField source="periodStartDt" label="Period Start Date" />
      </Datagrid>
    </List>
  );
};

export const StatePortfolioTaxInfoCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <DateInput
            source="periodStartDt"
            fullWidth
            required
            helperText="If one of these does not exist for a given state and portfolio, then the period start date will default to January 1st on the table in a portfolios 'Tax Info' tab."
          />
          <ReferenceInput source="portfolio.id" reference="PortfolioLite">
            <SelectInput
              label="Portfolio"
              fullWidth
              required
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="usState.id" reference="USState">
            <SelectInput label="State" fullWidth required optionText="id" />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
