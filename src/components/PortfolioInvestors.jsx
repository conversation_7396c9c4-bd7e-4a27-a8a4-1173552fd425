import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import numeral from 'numeral';
import { useDataProvider } from 'react-admin';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  CircularProgress,
  Grid,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import { Alert } from '@mui/lab';

export const PortfolioInvestors = () => {
  const [data, setData] = useState(null);
  const dataProvider = useDataProvider();
  const { id } = useParams();

  if (!data) {
    dataProvider
      .getOne('PortfolioInvestorData', {
        id: parseInt(id, 10),
      })
      .then(
        (resp) => {
          setData(resp.data);
        },
        (e) => {
          console.error('HIT AN ERROR', e);
          return new Error(e);
        }
      );
  }

  if (!data)
    return (
      <Grid
        container
        justifyContent="center"
        style={{
          width: '100%',
          textAlign: 'center',
        }}
      >
        <Grid item style={{ textAlign: 'center' }} xs={12}>
          <CircularProgress />
        </Grid>
      </Grid>
    );

  if (data.userPortfolioEquity.length === 0)
    return (
      <Grid style={{ width: '100%' }} container spacing={3}>
        <Grid item xs={12}>
          <Alert severity="info">
            No investors to date for this Portfolio.
          </Alert>
        </Grid>
      </Grid>
    );

  return (
    <Grid container direction="column" spacing={2}>
      <Grid item container direction="column">
        <Grid item>
          <Typography variant="body2">Investor count</Typography>
        </Grid>
        <Grid item>
          <Typography>{data.userPortfolioEquity.length} </Typography>
        </Grid>
      </Grid>
      <Grid item container direction="column">
        <Grid item>
          <Typography variant="body2">
            All states with investors in this portfolio (for Marta)
          </Typography>
        </Grid>
        <Grid item>
          <Typography>{data.investorStates.sort().join(', ')}</Typography>
        </Grid>
      </Grid>
      <Grid item container direction="column">
        <Grid item>
          <Typography variant="body2">Portfolio equity breakdown</Typography>
        </Grid>
        <Grid item>
          {data.userPortfolioEquity.map((investor, index) => {
            let dividendSum = 0;
            const hasDividend =
              investor.dividends && investor.dividends.length > 0;
            if (hasDividend) {
              investor.dividends.forEach((dividend) => {
                dividendSum += dividend.value;
              });
            }
            let investmentSum = 0;
            investor.investments.forEach((investment) => {
              investmentSum += investment.value;
            });
            const equity =
              (investor.currentSharesOwned * 100) /
              data.currentEquityData.ownedShares;
            const label = investor.subAccount
              ? `Sub Account: ${investor.user.fullName} (${investor.subAccount.name})`
              : `User: ${investor.user.fullName}`;
            return (
              <Accordion
                key={`investor-data-${investor.user.id}-${index}`}
                style={{ backgroundColor: 'inherit' }}
                elevation={0}
              >
                <AccordionSummary style={{ width: '100%' }}>
                  <ListItem
                    classes={{
                      container: 'energea-full-width',
                    }}
                    style={{ width: '100%' }}
                    dense
                  >
                    {/* {location.pathname.endsWith('/investors') ? (
                      <ListItemAvatar>
                        <Doughnut
                          width={50}
                          data={{
                            datasets: [
                              {
                                data: [equity || 0, 100 - equity || 0],
                                backgroundColor: ['green', 'grey'],
                              },
                            ],
                          }}
                          options={{
                            animation: {
                              duration: 0,
                            },
                            plugins: {
                              tooltip: { enabled: false },
                              legend: { display: false },
                            },
                          }}
                        />
                      </ListItemAvatar>
                    ) : null} */}
                    <ListItemText
                      style={{
                        color: 'rgba(0,0,0,.8)',
                        marginLeft: '1rem',
                      }}
                      primary={
                        <b>{`${label}, 
                                      Shares: ${numeral(
                                        investor.currentSharesOwned
                                      ).format('0,0.[000]')}, 
                                      Equity: ${numeral(equity).format(
                                        '0.0000'
                                      )} %`}</b>
                      }
                      secondary={
                        <>
                          {`Total Dividends Received: ${numeral(
                            dividendSum
                          ).format('$0,0.00')}`}
                          <br />
                          {`Total Sold Equity: ${numeral(
                            investor.currentSharesSoldValue
                          ).format('$0,0.00')}`}
                          <br />
                          {`Total Invested: ${numeral(investmentSum).format(
                            '$0,0.00'
                          )}`}
                          <br />
                          {`Current NAV: ${
                            data.currentEquityData &&
                            numeral(
                              investor.currentSharesOwned *
                                data.currentEquityData.sharePrice
                            ).format('$0,0.00')
                          }`}
                        </>
                      }
                    />{' '}
                  </ListItem>
                  {/* TODO: add these details to a dialog /*{' '} */}
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container>
                    <Grid item xs={12}>
                      <Typography
                        style={{
                          textAlign: 'left',
                        }}
                        variant="h6"
                      >
                        Investments{' '}
                        <b>
                          (
                          {numeral(investor.grossSharesPurchased).format(
                            '0,0.[000]'
                          )}{' '}
                          gross shares purchased)
                        </b>
                      </Typography>
                      {investor.investments.map((investment) => {
                        return (
                          <ListItem
                            key={`investment-list-item-key-${investment.id}`}
                            classes={{
                              container: 'energea-full-width',
                            }}
                            style={{ width: '100%' }}
                            dense
                            button
                            component={Link}
                            to={`/Investment/${investment.id}`}
                          >
                            <ListItemText primary={investment.label} />
                          </ListItem>
                        );
                      })}
                    </Grid>
                    {investor.shareTransfers && investor.shareTransfers[0] ? (
                      <Grid item xs={12}>
                        <Typography
                          style={{
                            textAlign: 'left',
                          }}
                          variant="h6"
                        >
                          Share Transfers{' '}
                          <b>
                            (
                            {numeral(investor.currentSharesSold).format(
                              '0,0.[000]'
                            )}{' '}
                            shares sold)
                          </b>
                        </Typography>
                        {investor.shareTransfers.map((shareTransfer) => {
                          return (
                            <ListItem
                              key={`share-transfer-list-item-key-${shareTransfer.id}`}
                              classes={{
                                container: 'energea-full-width',
                              }}
                              style={{ width: '100%' }}
                              dense
                              button
                              component={Link}
                              to={`/ShareTransfer/${shareTransfer.id}`}
                            >
                              <ListItemText primary={shareTransfer.label} />
                            </ListItem>
                          );
                        })}
                      </Grid>
                    ) : null}
                    <Grid item xs={12}>
                      <Typography
                        style={{
                          textAlign: 'left',
                        }}
                        variant="h6"
                      >
                        Dividends Received{' '}
                        <b>({numeral(dividendSum).format('$0,0.00')})</b>
                      </Typography>
                      {investor.dividends.map((dividend) => {
                        return (
                          <ListItem
                            key={`dividend-list-item-key-${dividend.id}`}
                            classes={{
                              container: 'energea-full-width',
                            }}
                            style={{ width: '100%' }}
                            dense
                            button
                            component={Link}
                            to={`/Dividend/${dividend.id}`}
                          >
                            <ListItemText primary={dividend.label} />
                          </ListItem>
                        );
                      })}
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            );
          })}
        </Grid>
      </Grid>
    </Grid>
  );
};
