import React, { useState } from 'react';
import moment from 'moment';
import { useDataProvider, useNotify } from 'react-admin';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  CircularProgress,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import MuiTextField from '@mui/material/TextField';
import { ExpandMore } from '@mui/icons-material';

export const AccountingUtilities = () => {
  const [
    monthlyAccountingReconReportPortfolio,
    setMonthlyAccountingReconReportPortfolio,
  ] = useState(null);
  const [
    monthlyAccountingReconReportEmail,
    setMonthlyAccountingReconReportEmail,
  ] = useState(null);
  const [
    monthlyAccountingReconReportMonth,
    setMonthlyAccountingReconReportMonth,
  ] = useState(moment().add(-1, 'month').format('YYYY-MM'));

  const [equitySummaryReportPortfolio, setEquitySummaryReportPortfolio] =
    useState(null);
  const [equitySummaryEmail, setEquitySummaryEmail] = useState(null);
  const [equitySummaryEndDt, setEquitySummaryEndDt] = useState(null);

  const [portfolioList, setPortfolioList] = useState(null);
  const [portfolioFetchLoading, setPortfolioFetchLoading] = useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();

  const fetchPortfolioList = () => {
    setPortfolioFetchLoading(true);
    dataProvider.getMany('Portfolio', {}).then(
      (res) => {
        setPortfolioList(res.data);
        setPortfolioFetchLoading(false);
      },
      (e) => {
        notify(e.message, { type: 'error' });
        setPortfolioFetchLoading(false);
      }
    );
  };

  if (!portfolioList && !portfolioFetchLoading) {
    fetchPortfolioList();
  }

  const runReconciliationReport = () => {
    if (!monthlyAccountingReconReportPortfolio) {
      notify('Portfolio must be selected', { type: 'error' });
      return;
    }
    if (
      !monthlyAccountingReconReportMonth ||
      monthlyAccountingReconReportMonth.length !== 7
    ) {
      notify("Month must be set and be of the form 'YYYY-MM'", {
        type: 'error',
      });
      return;
    }
    dataProvider
      .update('PortfolioMonthReconciliation', {
        data: {
          portfolioId: parseInt(monthlyAccountingReconReportPortfolio, 10),
          month: monthlyAccountingReconReportMonth,
          emailOverride: monthlyAccountingReconReportEmail,
          sendMonthlyReconciliationReports: true,
        },
      })
      .then(
        (returnObj) => {
          notify(
            `Successfully initiated portfolio month reconciliation report`,
            { type: 'success' }
          );
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
  };

  const runEquitySummary = () => {
    if (!equitySummaryReportPortfolio) {
      notify('Portfolio must be selected', { type: 'error' });
      return;
    }
    if (!equitySummaryEmail) {
      notify('Email must be set', { type: 'error' });
      return;
    }
    if (!equitySummaryEndDt) {
      notify('Month must be set', {
        type: 'error',
      });
      return;
    }
    dataProvider
      .create('EquitySummaryReport', {
        data: {
          endDt: equitySummaryEndDt,
          email: equitySummaryEmail,
          portfolioId: parseInt(equitySummaryReportPortfolio, 10),
        },
      })
      .then(
        (returnObj) => {
          notify(`Successfully initiated equity summary`, {
            type: 'success',
          });
          setEquitySummaryEmail(null);
          setEquitySummaryEndDt(null);
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
  };

  return (
    <Grid container style={{ padding: '2rem' }}>
      <Grid item xs={12} style={{ marginBottom: '1rem' }}>
        <Typography gutterBottom variant="h4">
          Accounting Utilities
        </Typography>
      </Grid>
      <Grid item xs={12} style={{ marginBottom: '1rem' }}>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Grid container direction="column">
              <Grid item>
                <Typography gutterBottom variant="h6">
                  Accounting Monthly Reconciliation Documents
                </Typography>
              </Grid>
              <Grid item>
                <Typography variant="body2">
                  This tool will send the monthly accounting reconciliation
                  report for a given month and portfolio to a specified email.
                  This is helpful when accounting needs updated reports after
                  the automated reports are sent on the 1st of the month.
                </Typography>
              </Grid>
            </Grid>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container>
              <Grid container style={{ width: '100%' }}>
                <Grid item xs={12} style={{ paddingBottom: '1rem' }}>
                  <FormControl fullWidth>
                    <InputLabel id="monthly-reconciliation-report-portfolio-select-input">
                      Portfolio
                    </InputLabel>
                    <Select
                      id="monthly-reconciliation-report-portfolio-select-input"
                      labelId="monthly-reconciliation-report-portfolio-select-input"
                      variant="outlined"
                      value={monthlyAccountingReconReportPortfolio || ''}
                      onChange={(event) => {
                        setMonthlyAccountingReconReportPortfolio(
                          event.target.value
                        );
                      }}
                    >
                      {portfolioList ? (
                        portfolioList.map((portfolio) => (
                          <MenuItem
                            value={portfolio.id}
                            key={`menu-item-portfolio-${portfolio.id}`}
                          >
                            {portfolio.name}
                          </MenuItem>
                        ))
                      ) : (
                        <CircularProgress />
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} style={{ paddingBottom: '1rem' }}>
                  <MuiTextField
                    id="monthly-reconciliation-report-month-text-input"
                    label="Month (YYYY-MM)"
                    fullWidth
                    helperText="Must be of the form 'YYYY-MM'"
                    value={monthlyAccountingReconReportMonth || ''}
                    onChange={(event) =>
                      setMonthlyAccountingReconReportMonth(event.target.value)
                    }
                  />
                </Grid>
                <Grid item xs={12}>
                  <MuiTextField
                    id="monthly-reconciliation-report-email-text-input"
                    label="Email"
                    fullWidth
                    value={monthlyAccountingReconReportEmail || ''}
                    onChange={(event) =>
                      setMonthlyAccountingReconReportEmail(event.target.value)
                    }
                  />
                </Grid>
                <Grid
                  item
                  container
                  justifyContent="flex-end"
                  style={{ marginTop: '1rem' }}
                >
                  <Button
                    onClick={() => {
                      runReconciliationReport();
                    }}
                    color="primary"
                    variant="contained"
                    disabled={
                      !monthlyAccountingReconReportPortfolio ||
                      !monthlyAccountingReconReportMonth ||
                      !monthlyAccountingReconReportEmail
                    }
                  >
                    Go
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
      <Grid item xs={12}>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Grid container direction="column">
              <Grid item>
                <Typography gutterBottom variant="h6">
                  Equity Summary
                </Typography>
              </Grid>
              <Grid item>
                <Typography variant="body2">
                  This tool will send an equity summary to the specified email
                  that includes all investments up until a provided date.
                </Typography>
              </Grid>
            </Grid>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container>
              <Grid container style={{ width: '100%' }}>
                <Grid item xs={12} style={{ paddingBottom: '1rem' }}>
                  <FormControl fullWidth>
                    <InputLabel id="equity-summary-report-portfolio-select-input">
                      Portfolio
                    </InputLabel>
                    <Select
                      id="equity-summary-report-portfolio-select-input"
                      labelId="equity-summary-report-portfolio-select-input"
                      variant="outlined"
                      value={equitySummaryReportPortfolio || ''}
                      onChange={(event) => {
                        setEquitySummaryReportPortfolio(event.target.value);
                      }}
                    >
                      {portfolioList ? (
                        portfolioList.map((portfolio) => (
                          <MenuItem
                            value={portfolio.id}
                            key={`menu-item-portfolio-${portfolio.id}`}
                          >
                            {portfolio.name}
                          </MenuItem>
                        ))
                      ) : (
                        <CircularProgress />
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} style={{ paddingBottom: '1rem' }}>
                  <MuiTextField
                    id="end-date-text-input"
                    label="Investment Completed End Date"
                    fullWidth
                    type="date"
                    value={equitySummaryEndDt || ''}
                    onChange={(event) =>
                      setEquitySummaryEndDt(
                        moment(event.target.value).format('yyyy-MM-DD')
                      )
                    }
                    InputLabelProps={{
                      shrink: true,
                    }}
                    InputProps={{
                      inputProps: {
                        min: moment('2000-01-01', 'YYYY-MM-DD').format(
                          'yyyy-MM-DD'
                        ),
                        max: moment().add(1, 'year').format('yyyy-MM-DD'),
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <MuiTextField
                    id="email-text-input"
                    label="Email"
                    fullWidth
                    value={equitySummaryEmail || ''}
                    onChange={(event) =>
                      setEquitySummaryEmail(event.target.value)
                    }
                  />
                </Grid>
                <Grid
                  item
                  container
                  justifyContent="flex-end"
                  style={{ marginTop: '1rem' }}
                >
                  <Button
                    onClick={() => {
                      runEquitySummary();
                    }}
                    color="primary"
                    variant="contained"
                    disabled={
                      !equitySummaryEmail ||
                      !equitySummaryEndDt ||
                      !equitySummaryReportPortfolio
                    }
                  >
                    Go
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  );
};
