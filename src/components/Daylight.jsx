import React from 'react';
import moment from 'moment-timezone';
import { Card, Grid, Typography, CardContent } from '@mui/material';

export default (props) => {
  const { timezone, sunrise, sunset } = props;
  const localTime = timezone ? moment().tz(timezone) : moment();
  const secondsOfDaylight = moment(sunset).diff(moment(sunrise), 'seconds');
  const secondsPassed = localTime.diff(moment(sunrise), 'seconds');
  const percentageOfDaylightPassed = Math.min(
    secondsPassed / secondsOfDaylight,
    1
  );
  const nightTime = localTime < moment(sunrise) || localTime > moment(sunset);
  const skyColor = nightTime ? 'rgba(1, 25, 61,0.4)' : 'rgba(255, 255, 0, 0.4)';
  const sunDegrees = -75 + percentageOfDaylightPassed * 150;
  let totalSunWidth = 0;
  if (sunDegrees > 0) {
    totalSunWidth += 50;
    totalSunWidth +=
      Math.abs(Math.cos(((sunDegrees + 90) * Math.PI) / 180)) * 50;
  } else {
    totalSunWidth += 50 - Math.cos(((sunDegrees + 90) * Math.PI) / 180) * 50;
  }
  if (nightTime) {
    totalSunWidth = 100;
  }

  const size = 170;
  return (
    <Grid item>
      <Typography variant="body1">
        <b>Local time : {localTime.format('h:mm a')}</b>
      </Typography>
      <Grid
        container
        class="sunmoon"
        style={{
          position: 'relative',
        }}
      >
        <Grid
          item
          class="sun-times"
          style={{
            //   marginTop: '40px',
            paddingTop: '1rem',
            width: '230px',
            height: '65px',
            borderBottom: '1px solid #999',
            overflowY: 'hidden',
          }}
          container
          justifyContent="center"
        >
          <Grid item style={{ position: 'relative', textAlign: 'center' }}>
            <div
              style={{
                overflow: 'hidden',
                width: `${size}px`,
                height: `${size}px`,
                margin: 'auto',
                border: '1px dashed #999',
                borderRadius: '50%',
              }}
            >
              <div
                class="sun-animation"
                style={{
                  width: `${totalSunWidth}%`,
                  height: '150px',
                  backgroundColor: skyColor,
                  '-webkit-transition': 'width 2s linear',
                  transition: 'width 2s linear',
                }}
              ></div>
            </div>
            {!nightTime && (
              <div
                style={{
                  '-webkit-transform': `rotateZ(${sunDegrees}deg)`,
                  height: `${size / 2}px`,
                  left: '50%',
                  bottom: '50%',
                  marginLeft: '-8px',
                  position: 'absolute',
                  color: 'yellow',
                  textShadow: '0 0 5px black',
                  '-webkit-transition': '-webkit-transform 2s linear',
                  '-webkit-transform-origin': '50% 100%',
                }}
              >
                <span
                  class="symbol"
                  style={{
                    position: 'relative',
                    fontSize: '16px',
                    top: '-12px',
                  }}
                >
                  ☀
                </span>
              </div>
            )}
          </Grid>
          <span> {moment().tz(timezone).format('h:mm a')}</span>
        </Grid>
        <Grid container justifyContent="space-between">
          <Grid item>
            <Typography variant="body2">
              {moment(sunrise).tz(timezone).format('h:mm a')}
            </Typography>
          </Grid>
          <Grid item>
            <Typography variant="body2">
              {moment(sunset).tz(timezone).format('h:mm a')}
            </Typography>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};
