import React, { useState } from 'react';
import moment from 'moment-timezone';
import { Bar } from 'react-chartjs-2';

import 'chart.js/auto';
import numeral from 'numeral';
// import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-moment';
import { useDataProvider } from 'react-admin';

import theme from '../theme';
import {
  Alert,
  Button,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  MenuItem,
  Select,
  Skeleton,
  TextField,
  Typography,
} from '@mui/material';
import { Close, Settings, Refresh } from '@mui/icons-material';

const getStartDtFromTimeFrame = (timeFrame, dt) => {
  switch (timeFrame) {
    case 'Last 24 hours':
      return moment().add(-1, 'days').format();
    case 'Last 72 hours':
      return moment().add(-3, 'days').format();
    case 'Last week':
      return moment().add(-7, 'days').format();
    case 'Last 30 days':
      return moment().add(-30, 'days').format();
    case 'All time':
      return null;
    case 'Custom':
      return moment(dt).startOf('day').format();
    default:
      console.error('Unknown time frame');
      return moment().add(-7, 'days').format();
  }
};

const getEndDtFromTimeFrame = (timeFrame, dt) => {
  switch (timeFrame) {
    case 'Last 24 hours':
    case 'Last 72 hours':
    case 'Last week':
    case 'Last 30 days':
    case 'All time':
      return moment().format();
    case 'Custom':
      return moment(dt).endOf('day').format();
    default:
      console.error('Unknown time frame');
      return moment().format();
  }
};

const PortfolioGenerationChart = ({
  portfolioId,
  portfolioName,
  open,
  onClose,
}) => {
  const dataProvider = useDataProvider();
  // const notify = useNotify();
  const [generationDataLoading, setGenerationDataLoading] = useState(false);
  const [productionTotals, setProductionTotals] = useState(null);
  const [generationToDt, setGenerationToDt] = useState(null);
  const [generationFromDt, setGenerationFromDt] = useState(null);
  const [openCustomizeEnergyChartDialog, setOpenCustomizeEnergyChartDialog] =
    useState(false);
  const [loading, setLoading] = useState(false);
  const [generationDataErrors, setGenerationDataErrors] = useState(null);
  const [energyTotalsBucketSize, setEnergyTotalsBucketSize] = useState({
    current: 'day',
    next: 'day',
  }); // 'month', 'week', 'day'
  const [energyTotalsTimeFrame, setEnergyTotalsTimeFrame] = useState({
    current: 'Last 30 days',
    next: 'Last 30 days',
  }); // 'Last 30 days', 'All time', 'Last week'

  let generationChartJsx = null;
  if (!portfolioId || !open) return null;

  const renderEnergyChartCustomizeDialog = () => {
    return (
      <Dialog open={openCustomizeEnergyChartDialog} style={{ margin: '2rem' }}>
        <DialogTitle>
          <Typography variant="h5">Chart Customization</Typography>
        </DialogTitle>
        <DialogContent>
          <Grid
            container
            item
            justifyContent="space-between"
            style={{ marginBottom: '1rem' }}
          >
            <Grid item>
              <Typography variant="h6">Group By:</Typography>
            </Grid>
            <Grid item>
              <Select
                label="filter"
                value={
                  energyTotalsBucketSize.next || energyTotalsBucketSize.current
                }
                onChange={(event) => {
                  setEnergyTotalsBucketSize({
                    current: energyTotalsBucketSize.current,
                    next: event.target.value,
                  });
                  if (
                    event.target.value === 'month' ||
                    event.target.value === 'year'
                  ) {
                    setEnergyTotalsTimeFrame({
                      current: energyTotalsTimeFrame.current,
                      next: 'All time',
                    });
                  }
                }}
                style={{ width: '150px' }}
              >
                <MenuItem value="day">Day</MenuItem>
                <MenuItem value="week">Week</MenuItem>
                <MenuItem value="month">Month</MenuItem>
                <MenuItem value="year">Year</MenuItem>
              </Select>
            </Grid>
          </Grid>
          <Grid container>
            <Grid
              container
              item
              alignItems="center"
              justifyContent="space-between"
            >
              <Typography variant="h6">Date Range Selector:</Typography>
              <Grid item>
                <Select
                  label="filter"
                  value={
                    energyTotalsTimeFrame.next || energyTotalsTimeFrame.current
                  }
                  onChange={(event) =>
                    setEnergyTotalsTimeFrame({
                      current: energyTotalsTimeFrame.current,
                      next: event.target.value,
                    })
                  }
                  style={{ width: '200px' }}
                >
                  <MenuItem
                    value="Last week"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Last week
                  </MenuItem>
                  <MenuItem
                    value="Last 30 days"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Last 30 days
                  </MenuItem>
                  <MenuItem value="All time">All time</MenuItem>
                  <Divider />
                  <MenuItem
                    value="Custom"
                    // disabled={
                    //   (energyTotalsBucketSize.next ||
                    //     energyTotalsBucketSize.current) === 'month'
                    // }
                  >
                    Custom
                  </MenuItem>
                </Select>
              </Grid>
            </Grid>
            <Grid
              container
              justifyContent="flex-end"
              style={{ marginBottom: '1rem' }}
            >
              <Collapse in={energyTotalsTimeFrame.next === 'Custom'}>
                <Grid container spacing={2}>
                  <Grid item>
                    <TextField
                      id="fromDate"
                      label="From"
                      type="date"
                      value={generationFromDt || ''}
                      onChange={(event) => {
                        setGenerationFromDt(
                          moment(event.target.value).format('yyyy-MM-DD')
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        inputProps: {
                          // min: moment(
                          //   projectDetails.actualCOD,
                          //   'YYYY-MM-DD'
                          // ).format('yyyy-MM-DD'),
                          max: moment().format('yyyy-MM-DD'),
                        },
                      }}
                      error={
                        !!(
                          generationToDt &&
                          generationFromDt &&
                          generationToDt <= generationFromDt
                        )
                      }
                      helperText={
                        generationToDt &&
                        generationFromDt &&
                        generationToDt <= generationFromDt
                          ? "'From' date must precede 'To' date"
                          : null
                      }
                    />
                  </Grid>
                  <Grid item>
                    <TextField
                      id="toDate"
                      label="To"
                      type="date"
                      value={generationToDt || ''}
                      onChange={(event) =>
                        setGenerationToDt(
                          moment(event.target.value).format('yyyy-MM-DD')
                        )
                      }
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        inputProps: {
                          // min: moment(
                          //   projectDetails.actualCOD,
                          //   'YYYY-MM-DD'
                          // ).format('yyyy-MM-DD'),
                          max: moment().format('yyyy-MM-DD'),
                        },
                      }}
                      error={
                        !!(
                          generationToDt &&
                          generationFromDt &&
                          generationToDt <= generationFromDt
                        )
                      }
                      helperText={
                        generationToDt &&
                        generationFromDt &&
                        generationToDt <= generationFromDt
                          ? "'From' date must precede 'To' date"
                          : null
                      }
                    />
                  </Grid>
                </Grid>
              </Collapse>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setEnergyTotalsBucketSize({
                current: energyTotalsBucketSize.current,
                next: energyTotalsBucketSize.current,
              });
              setEnergyTotalsTimeFrame({
                current: energyTotalsTimeFrame.current,
                next: energyTotalsTimeFrame.current,
              });
              setOpenCustomizeEnergyChartDialog(false);
            }}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              fetchGenerationChartData();
              setOpenCustomizeEnergyChartDialog(false);
            }}
            variant="contained"
            color="primary"
          >
            Go
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const fetchGenerationChartData = () => {
    setGenerationDataLoading(true);
    dataProvider
      .getOne('PortfolioProductionTotals', {
        input: {
          portfolioId: parseInt(portfolioId, 10),
          groupedBy: energyTotalsBucketSize.next,
          startDt: getStartDtFromTimeFrame(
            energyTotalsTimeFrame.next,
            generationFromDt
          ),
          endDt: getEndDtFromTimeFrame(
            energyTotalsTimeFrame.next,
            generationToDt
          ),
        },
      })
      .then(
        (res) => {
          setEnergyTotalsBucketSize({
            current: energyTotalsBucketSize.next,
            next: energyTotalsBucketSize.next,
          });
          setEnergyTotalsTimeFrame({
            current: energyTotalsTimeFrame.next,
            next: energyTotalsTimeFrame.next,
          });
          setProductionTotals(res.data?.portfolioProductionTotals);
          setGenerationDataLoading(false);
          if (!res.data?.portfolioProductionTotals) {
            setGenerationDataErrors(
              'Error retrieving portfolio production data'
            );
          }
        },
        (e) => {
          setGenerationDataLoading(false);
          console.error('Error retrieving portfolio production data', e);
        }
      );
  };

  if (loading || generationDataLoading) {
    generationChartJsx = (
      <Grid item style={{ padding: '1rem' }}>
        <Skeleton
          animation="wave"
          variant="rectangular"
          height="31.25rem"
          style={{
            borderRadius: theme.shape.borderRadius,
          }}
        />
      </Grid>
    );
  } else if (generationDataErrors) {
    <Alert severity="error">
      Error retrieving generation data. Please try again later.
    </Alert>;
  } else if (!loading && !generationDataLoading && !productionTotals) {
    fetchGenerationChartData();
  } else {
    if (productionTotals) {
      let actualDataset = null;
      let inverterDataset = null;
      let expectedDataset = null;
      let p50Dataset = null;
      // let moduleTempRefDataset = null;
      // let globEffIrradianceDataset = null;
      // let actualIrradianceDataset = null;
      // let irradianceDataCoverageDataset = null;
      // let inverterAvailabilityDataset = null;
      // let actualPRDataset = null;
      // let avgModuleTemperatureDataset = null;
      let tusdDataset = null;
      let xScale = null;
      // const hasExpectedProduction =
      //   productionTotals.filter((pt) => !!pt?.expectedProduction).length > 0;
      const actualDatasetColor = theme.palette.green.main;
      const inverterDatasetColor = theme.palette.primary.main;
      const expectedDatasetColor = '#76D299';
      const p50DatasetColor = theme.palette.green.dark;
      const tusdDatasetColor = 'rgba(42, 201, 186, .4)';
      // const globEffIrradianceDatasetColor = 'orange';
      // const actualIrradianceDatasetColor = 'rgba(220, 70, 20)';
      // const irradianceDataCoverageDatasetColor = '#fcd97e';
      // const inverterAvailabilityDatasetColor = 'rgba(75,0,130, .3)';
      // const actualPRDatasetColor = theme.palette.appSecondary.main;
      // const avgModuleTemperatureDatasetColor = '#F0F000';
      // const moduleTempRefDatasetColor = '#bfba00';
      if (energyTotalsBucketSize.current === 'year') {
        actualDataset = {
          label: 'Actual Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'YYYY'),
            y: point.actualProduction,
          })),
          pointRadius: 0,
          backgroundColor: actualDatasetColor,
          fill: true,
        };
        inverterDataset = {
          label: 'Inverter Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'YYYY'),
            y: point.inverterProduction,
          })),
          pointRadius: 0,
          backgroundColor: inverterDatasetColor,
          fill: true,
          hidden: true,
        };
        expectedDataset = {
          label: 'Expected Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'YYYY'),
            y: point.expectedProduction,
          })),
          pointRadius: 0,
          backgroundColor: expectedDatasetColor,
          fill: true,
          hidden: true,
          // hidden: !hasExpectedProduction,
        };
        p50Dataset = {
          label: 'P-50 Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'YYYY'),
            y: point.p50Production,
          })),
          pointRadius: 0,
          backgroundColor: p50DatasetColor,
        };
        tusdDataset = {
          label: 'TUSD Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'YYYY'),
            y: point.tusdProduction || null,
          })),
          pointRadius: 0,
          backgroundColor: tusdDatasetColor,
          fill: true,
          hidden: true,
          // hidden: !hasExpectedProduction,
        };
        // globEffIrradianceDataset = {
        //   label: 'Projected Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.globEffIrradiance,
        //   })),
        //   // backgroundColor: 'rgba(255, 181, 0, .3)',
        //   backgroundColor: globEffIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        // };
        // actualIrradianceDataset = {
        //   label: 'Actual Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.actualIrradiance,
        //   })),
        //   backgroundColor: actualIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        // };
        // irradianceDataCoverageDataset = {
        //   label: 'Irradiance Data Coverage',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.irradianceDataCoverage,
        //   })),
        //   backgroundColor: irradianceDataCoverageDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // inverterAvailabilityDataset = {
        //   label: 'Availability',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.inverterAvailability,
        //   })),
        //   backgroundColor: inverterAvailabilityDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // actualPRDataset = {
        //   label: 'Actual Performance Ratio',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.actualPR,
        //   })),
        //   backgroundColor: actualPRDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // avgModuleTemperatureDataset = {
        //   label: 'Actual Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.avgModuleTemperature,
        //   })),
        //   backgroundColor: avgModuleTemperatureDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        // };
        // moduleTempRefDataset = {
        //   label: 'Projected Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'YYYY'),
        //     y: point.moduleTempRef,
        //   })),
        //   backgroundColor: moduleTempRefDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        // };
        xScale = {
          type: 'time',
          time: {
            tooltipFormat: 'YYYY',
            unit: 'year',
          },
        };
      } else if (energyTotalsBucketSize.current === 'month') {
        actualDataset = {
          label: 'Actual Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM YYYY'),
            y: point.actualProduction,
          })),
          pointRadius: 0,
          backgroundColor: actualDatasetColor,
          fill: true,
        };
        inverterDataset = {
          label: 'Inverter Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM YYYY'),
            y: point.inverterProduction,
          })),
          pointRadius: 0,
          backgroundColor: inverterDatasetColor,
          fill: true,
          hidden: true,
        };
        expectedDataset = {
          label: 'Expected Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM YYYY'),
            y: point.expectedProduction,
          })),
          pointRadius: 0,
          backgroundColor: expectedDatasetColor,
          fill: true,
          hidden: true,
          // hidden: !hasExpectedProduction,
        };
        p50Dataset = {
          label: 'P-50 Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM YYYY'),
            y: point.p50Production,
          })),
          pointRadius: 0,
          backgroundColor: p50DatasetColor,
        };
        tusdDataset = {
          label: 'TUSD Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM YYYY'),
            y: point.tusdProduction || null,
          })),
          pointRadius: 0,
          backgroundColor: tusdDatasetColor,
          fill: true,
          hidden: true,
          // hidden: !hasExpectedProduction,
        };
        // globEffIrradianceDataset = {
        //   label: 'Projected Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.globEffIrradiance,
        //   })),
        //   // backgroundColor: 'rgba(255, 181, 0, .3)',
        //   backgroundColor: globEffIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        // };
        // actualIrradianceDataset = {
        //   label: 'Actual Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.actualIrradiance,
        //   })),
        //   backgroundColor: actualIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        // };
        // irradianceDataCoverageDataset = {
        //   label: 'Irradiance Data Coverage',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.irradianceDataCoverage,
        //   })),
        //   backgroundColor: irradianceDataCoverageDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // inverterAvailabilityDataset = {
        //   label: 'Availability',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.inverterAvailability,
        //   })),
        //   backgroundColor: inverterAvailabilityDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // actualPRDataset = {
        //   label: 'Actual Performance Ratio',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.actualPR,
        //   })),
        //   backgroundColor: actualPRDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // avgModuleTemperatureDataset = {
        //   label: 'Actual Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.avgModuleTemperature,
        //   })),
        //   backgroundColor: avgModuleTemperatureDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        // };
        // moduleTempRefDataset = {
        //   label: 'Projected Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM YYYY'),
        //     y: point.moduleTempRef,
        //   })),
        //   backgroundColor: moduleTempRefDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        // };
        xScale = {
          type: 'time',
          time: {
            tooltipFormat: 'MMM YYYY',
            unit: 'month',
          },
        };
      } else if (energyTotalsBucketSize.current === 'week') {
        actualDataset = {
          label: 'Actual Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.actualProduction,
          })),
          pointRadius: 0,
          backgroundColor: actualDatasetColor,
          fill: true,
        };
        inverterDataset = {
          label: 'Inverter Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.inverterProduction,
          })),
          pointRadius: 0,
          backgroundColor: inverterDatasetColor,
          fill: true,
          hidden: true,
        };
        expectedDataset = {
          label: 'Expected Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.expectedProduction,
          })),
          pointRadius: 0,
          backgroundColor: expectedDatasetColor,
          fill: true,
          hidden: true,
          // hidden: !hasExpectedProduction,
        };
        p50Dataset = {
          label: 'P-50 Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.p50Production,
          })),
          pointRadius: 0,
          backgroundColor: p50DatasetColor,
          hidden: true,
        };
        tusdDataset = {
          label: 'TUSD Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.tusdProduction || null,
          })),
          pointRadius: 0,
          backgroundColor: tusdDatasetColor,
          fill: true,
          hidden: true,
          // hidden: !hasExpectedProduction,
        };
        // globEffIrradianceDataset = {
        //   label: 'Projected Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.globEffIrradiance,
        //   })),
        //   // backgroundColor: 'rgba(255, 181, 0, .3)',
        //   backgroundColor: globEffIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        // };
        // actualIrradianceDataset = {
        //   label: 'Actual Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.actualIrradiance,
        //   })),
        //   backgroundColor: actualIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        // };
        // irradianceDataCoverageDataset = {
        //   label: 'Irradiance Data Coverage',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.irradianceDataCoverage,
        //   })),
        //   backgroundColor: irradianceDataCoverageDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // inverterAvailabilityDataset = {
        //   label: 'Availability',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.inverterAvailability,
        //   })),
        //   backgroundColor: inverterAvailabilityDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // actualPRDataset = {
        //   label: 'Actual Performance Ratio',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.actualPR,
        //   })),
        //   backgroundColor: actualPRDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        // };
        // avgModuleTemperatureDataset = {
        //   label: 'Actual Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.avgModuleTemperature,
        //   })),
        //   backgroundColor: avgModuleTemperatureDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        // };
        // moduleTempRefDataset = {
        //   label: 'Projected Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.moduleTempRef,
        //   })),
        //   backgroundColor: moduleTempRefDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        // };
        xScale = {
          type: 'time',
          time: {
            tooltipFormat: 'ddd, MMM D, YYYY',
            unit: 'week',
          },
          title: {
            display: true,
            text: 'Weeks (Sunday to Saturday)',
          },
        };
      } else {
        actualDataset = {
          label: 'Actual',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.actualProduction,
          })),
          pointRadius: 0,
          backgroundColor: actualDatasetColor,
          fill: true,
          order: 1000,
        };
        inverterDataset = {
          label: 'Inverter Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.inverterProduction,
          })),
          pointRadius: 0,
          backgroundColor: inverterDatasetColor,
          fill: true,
          hidden: true,
          order: 1000,
        };
        expectedDataset = {
          label: 'Expected',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.expectedProduction,
          })),
          pointRadius: 0,
          // backgroundColor: 'rgba(21, 48, 76, .4)',
          // backgroundColor: theme.palette.green.light,
          backgroundColor: expectedDatasetColor,
          // borderColor: theme.palette.green.main,
          fill: true,
          hidden: true,
          order: 1000,
          // hidden: !hasExpectedProduction,
        };
        p50Dataset = {
          label: 'P50 Projected',
          type: 'line',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.p50Production,
          })),
          pointRadius: 0,
          borderColor: p50DatasetColor,
          backgroundColor: 'rgba(0,0,0,0)',
          borderDash: [10, 5],
          order: 1,
        };
        tusdDataset = {
          label: 'TUSD Production',
          data: productionTotals.map((point) => ({
            x: moment(point.date, 'MMM D, YYYY'),
            y: point.tusdInvoiceProduction,
          })),
          pointRadius: 0,
          backgroundColor: tusdDatasetColor,
          fill: true,
          hidden: true,
          order: 1000,
          // hidden: !hasExpectedProduction,
        };
        // globEffIrradianceDataset = {
        //   label: 'Projected Irradiance',
        //   type: 'line',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.globEffIrradiance,
        //   })),
        //   pointRadius: 0,
        //   backgroundColor: 'rgba(0,0,0,0)',
        //   borderColor: globEffIrradianceDatasetColor,
        //   borderDash: [10, 5],
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        //   order: 2,
        // };
        // actualIrradianceDataset = {
        //   label: 'Actual Irradiance',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.actualIrradiance,
        //   })),
        //   backgroundColor: actualIrradianceDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yIrradiance',
        //   hidden: true,
        //   order: 1000,
        // };
        // irradianceDataCoverageDataset = {
        //   label: 'Irradiance Data Coverage',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.irradianceDataCoverage,
        //   })),
        //   backgroundColor: irradianceDataCoverageDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        //   order: 1000,
        // };
        // inverterAvailabilityDataset = {
        //   label: 'Availability',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.inverterAvailability,
        //   })),
        //   backgroundColor: inverterAvailabilityDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        //   order: 1000,
        // };
        // actualPRDataset = {
        //   label: 'Actual PR',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.actualPR,
        //   })),
        //   backgroundColor: actualPRDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yPercentage',
        //   hidden: true,
        //   order: 1000,
        // };
        // avgModuleTemperatureDataset = {
        //   label: 'Actual Module Temp',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.avgModuleTemperature,
        //   })),
        //   backgroundColor: avgModuleTemperatureDatasetColor,
        //   pointRadius: 0,
        //   yAxisID: 'yTemperature',
        //   hidden: true,
        //   order: 1000,
        // };
        // moduleTempRefDataset = {
        //   label: 'Projected Module Temp',
        //   type: 'line',
        //   data: productionTotals.map((point) => ({
        //     x: moment(point.date, 'MMM D, YYYY'),
        //     y: point.moduleTempRef,
        //   })),
        //   pointRadius: 0,
        //   borderColor: moduleTempRefDatasetColor,
        //   backgroundColor: 'rgba(0,0,0,0)',
        //   borderDash: [10, 5],
        //   order: 3,
        //   hidden: true,
        //   yAxisID: 'yTemperature',
        // };
        xScale = {
          type: 'time',
          time: {
            tooltipFormat: 'MMM D, YYYY',
            unit: 'day',
          },
        };
      }

      if (productionTotals.length === 0) {
        generationChartJsx = (
          <Alert severity="warning" style={{ marginTop: '1rem' }}>
            No production data for this time period.
          </Alert>
        );
      } else {
        generationChartJsx = (
          <Grid item xs={12} style={{ maxHeight: '700px' }}>
            <Bar
              height={500}
              data={{
                datasets: [
                  actualDataset,
                  expectedDataset,
                  p50Dataset,
                  inverterDataset,
                  tusdDataset,
                  // actualIrradianceDataset,
                  // globEffIrradianceDataset,
                  // irradianceDataCoverageDataset,
                  // inverterAvailabilityDataset,
                  // actualPRDataset,
                  // avgModuleTemperatureDataset,
                  // moduleTempRefDataset,
                ],
              }}
              options={{
                pointRadius: 100,
                grouped: true,
                maintainAspectRatio: false,
                interaction: {
                  mode: 'index',
                },
                borderRadius: 4,
                plugins: {
                  tooltip: {
                    callbacks: {
                      label: function (context, chartData) {
                        let label = context.dataset.label || '';
                        if (label) {
                          label += ': ';
                        }
                        label += `${numeral(context.parsed.y).format(
                          '0,0'
                        )} kWh`;
                        if ([1, 2, 3].indexOf(context.datasetIndex) > -1) {
                          const actual =
                            actualDataset.data[context.dataIndex].y;
                          label += ` (${
                            !context.parsed.y
                              ? '∞'
                              : numeral(
                                  (actual - context.parsed.y) / context.parsed.y
                                ).format('0,0.0%')
                          })`;
                        }
                        // } else if ([1, 6].indexOf(context.datasetIndex) > -1) {
                        //   label += `${numeral(context.parsed.y).format(
                        //     '0,0.[00]'
                        //   )} kWh/m2`;
                        // } else {
                        //   label += `${numeral(context.parsed.y).format(
                        //     '0,0.[00]'
                        //   )}`;
                        // }
                        return label;
                      },
                    },
                  },
                },
                scales: {
                  x: xScale,
                  y: {
                    ticks: {
                      callback: (value) =>
                        `${numeral(value).format('0,0.[0]')} kWh`,
                    },
                  },
                  // yIrradiance: {
                  //   title: {
                  //     display: true,
                  //     text: 'Irradiance (kWh/m2)',
                  //   },
                  //   grid: {
                  //     display: false,
                  //   },
                  //   ticks: {
                  //     callback: (value) =>
                  //       `${numeral(value).format('0,0.[0]')} kWh/m2`,
                  //   },
                  //   position: 'right',
                  // },
                  // yPercentage: {
                  //   title: {
                  //     display: true,
                  //     text: 'Data Coverage / Availability / PR (%)',
                  //   },
                  //   grid: {
                  //     display: false,
                  //   },
                  //   ticks: {
                  //     callback: (value) =>
                  //       `${numeral(value).format('0,0.[0]')}%`,
                  //   },
                  //   position: 'right',
                  // },
                  // yTemperature: {
                  //   title: {
                  //     display: true,
                  //     text: 'Temperature (°C)',
                  //   },
                  //   grid: {
                  //     display: false,
                  //   },
                  //   ticks: {
                  //     callback: (value) =>
                  //       `${numeral(value).format('0,0.[0]')} °C`,
                  //   },
                  //   position: 'right',
                  // },
                },
              }}
            />
          </Grid>
        );
      }
    }
  }
  const getTotalGeneration = () => {
    if (!productionTotals || generationDataLoading)
      return <Skeleton variant="text" width="10rem" />;
    return `${numeral(
      productionTotals?.reduce((acc, curr) => acc + curr.actualProduction, 0) /
        1000
    ).format('0,0.0')} MWh`;
  };
  return (
    <>
      <Dialog fullScreen onClose={onClose} open={open}>
        <DialogTitle>
          <Grid
            container
            justifyContent="space-between"
            alignItems="center"
            style={{ width: '100%' }}
          >
            <Grid item>{portfolioName} - Generation Summary</Grid>
            <Grid item>
              <IconButton onClick={onClose}>
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent component={Grid} container spacing={2}>
          <Divider style={{ marginBottom: '2rem' }} />
          {/* <Grid
            container
            // justifyContent="center"
            // direction="column"
            alignItems="center"
            style={{ minHeight: '100%' }}
          > */}
          <Grid item xs={12} style={{ marginBottom: '1rem' }}>
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
                <Typography gutterBottom variant="body1">
                  <b>
                    <Grid container>Generation : {getTotalGeneration()}</Grid>
                  </b>
                </Typography>
              </Grid>
              <Grid item>
                <Grid container>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<Settings />}
                      style={{ textTransform: 'none', marginLeft: '1rem' }}
                      disabled={loading}
                      onClick={() => setOpenCustomizeEnergyChartDialog(true)}
                    >
                      Customize
                    </Button>
                  </Grid>
                  <Grid item>
                    <IconButton
                      style={{ marginLeft: '1rem' }}
                      color="primary"
                      onClick={() => fetchGenerationChartData()}
                    >
                      <Refresh />
                    </IconButton>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            {generationChartJsx}
          </Grid>
          {/* </Grid> */}
        </DialogContent>
      </Dialog>
      {renderEnergyChartCustomizeDialog()}
    </>
  );
};
export default PortfolioGenerationChart;
