// cachebust 2
import React, { Component, useState } from 'react';

import {
  Button,
  ButtonGroup,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Radio,
  RadioGroup,
  Skeleton,
  Switch,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useDataProvider } from 'react-admin';
import numeral from 'numeral';
import moment from 'moment';
import { Bar, Line } from 'react-chartjs-2';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Chart } from 'chart.js';
import 'chart.js/auto';
import 'chartjs-adapter-moment';
import { Funnel } from 'funnel-react-2';

import theme from '../theme';
import MuiButton from '@mui/material/Button';
import { Settings } from '@mui/icons-material';
import { getGradient } from '../utils/global';

// utility functions
const generateDateRange = (startDate, endDate, timeSpan = 'day') => {
  const dates = [];
  const currentDate = moment(startDate);
  while (currentDate <= endDate) {
    dates.push(moment(currentDate));
    currentDate.add(1, timeSpan);
  }
  return dates;
};

const getUserGraphData = (users, investors, investorsExcludingReferrals) => {
  const userDateBuckets = {};
  const firstUserCreatedDt = '2020-03-01';
  const userDays = generateDateRange(
    moment(firstUserCreatedDt, 'YYYY-MM-DD').subtract(1, 'day').startOf('day'),
    moment()
  );
  userDays.forEach(
    (d) => (userDateBuckets[String(d.format('MM-DD-YYYY'))] = 0)
  );
  users.forEach((user) => {
    userDateBuckets[String(moment(user.createdAt).format('MM-DD-YYYY'))] += 1;
  });

  const getInvestorDateBuckets = (investors) => {
    const buckets = {};
    const firstInvestmentCreatedDt = '2019-11-01';
    const investorDays = generateDateRange(
      moment(firstInvestmentCreatedDt, 'YYYY-MM-DD')
        .subtract(1, 'day')
        .startOf('day'),
      moment()
    );
    investorDays.forEach((d) => (buckets[String(d.format('MM-DD-YYYY'))] = 0));
    investors?.forEach((investor) => {
      if (investor.firstInvestmentDt) {
        buckets[
          String(moment(investor.firstInvestmentDt).format('MM-DD-YYYY'))
        ] += 1;
      }
    });
    return buckets;
  };

  const investorDateBuckets = getInvestorDateBuckets(investors);
  const investorsExcludingReferralsDateBuckets = getInvestorDateBuckets(
    investorsExcludingReferrals
  );

  return {
    users: userDateBuckets,
    investors: investorDateBuckets,
    investorsExcludingReferrals: investorsExcludingReferralsDateBuckets,
  };
};

export default (args) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const { open } = args;
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  const fetchData = () => {
    setLoading(true);
    dataProvider.getOne('CMSDashboardUserData', { data: null }).then(
      (resp) => {
        setData(resp.data);
        setLoading(false);
      },
      (e) => {
        setLoading(false);
        console.error('HIT AN ERROR', e);
        return new Error(e);
      }
    );
  };

  if (!data && !loading) {
    fetchData();
  }

  if (!open) return null;
  if (!data || loading)
    return (
      <Grid container>
        <Grid item xs={12}>
          <Skeleton
            animation="wave"
            variant="rectangular"
            height="16rem"
            style={{
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </Grid>
      </Grid>
    );
  return (
    <Collapse in={open}>
      <UserChart {...args} data={data} fullScreen={fullScreen} />
      <Divider
        style={{
          width: '100%',
          marginTop: '2em',
          marginBottom: '2em',
        }}
      />
      <Typography gutterBottom variant="h6">
        User Funnel :{' '}
      </Typography>
      <Grid container justifyContent="center">
        <Grid item xs={12}>
          <CustomFunnel
            crowdInvestmentTotal={
              data.getCMSDashboardData?.crowdInvestmentTotal
            }
            gaVisitorCount={data.getAllTimeNewUsers}
            profileCompleteUserCount={data.getProfileCompleteUserCount}
            investorCount={data.getInvestorCount}
            investorCountExcludingReferrals={
              data.getInvestorCountExcludingReferrals
            }
            userCount={data.allUsers.length}
            theme={theme}
          />
        </Grid>
      </Grid>
    </Collapse>
  );
};

class UserChart extends Component {
  constructor(props) {
    super(props);
    this.chartData = getUserGraphData(
      props.data.allUsers,
      props.data.allInvestors,
      props.data.allInvestorsExcludingReferralRewards
    );
    this.state = {
      barChartBucketSize: 'month', // 'day', 'week', 'month', 'quarter'
      chartType: 'new', // 'total', 'new'
      userType: 'investors', // 'users', 'investors'

      dialogOpen: false,
    };

    this.state.selectBarChartBucketSize = this.state.barChartBucketSize;
    this.state.selectChartType = this.state.chartType;
    this.state.selectUserType = this.state.userType;
  }

  render() {
    const { chartType, userType, barChartBucketSize, timePeriod } = this.state;

    let userData = [];
    let userLabels = [];
    let avgNewUsersLast30Data = [];

    let counts = null;
    let labels = null;
    if (userType === 'users') {
      counts = Object.values(this.chartData.users);
      labels = Object.keys(this.chartData.users);
    } else if (userType === 'investors') {
      counts = Object.values(this.chartData.investors);
      labels = Object.keys(this.chartData.investors);
    } else if (userType === 'investorsExcludingReferrals') {
      counts = Object.values(this.chartData.investorsExcludingReferrals);
      labels = Object.keys(this.chartData.investorsExcludingReferrals);
    }

    const average = (arr) => arr.reduce((acc, v) => acc + v) / arr.length;
    const avgNewUsersLast30 =
      (counts.slice(-30) && average(counts.slice(-30))) || 0;

    if (chartType === 'new') {
      if (barChartBucketSize === 'day') {
        userData = [...counts];
        userLabels = [...labels];
      } else {
        const userBuckets = {};
        for (let index = 0; index < counts.length; index++) {
          const startOfTimePeriod = moment(labels[Number(index)], 'MM-DD-YYYY')
            .startOf(barChartBucketSize)
            .format('MM-DD-YYYY');
          const bucket = userBuckets[String(startOfTimePeriod)];
          userBuckets[String(startOfTimePeriod)] = bucket
            ? bucket + counts[Number(index)]
            : counts[Number(index)];
        }
        userData = Object.values(userBuckets);
        userLabels = Object.keys(userBuckets);
      }
    } else {
      userLabels = labels;
      let sum = 0;
      let rolling30DaySum = 0;
      counts.forEach((val, index) => {
        userData.push((sum += val));
        if (index >= 30) {
          rolling30DaySum -= counts[index - 30];
        }
        rolling30DaySum += val;
        avgNewUsersLast30Data.push(rolling30DaySum / 30);
      });
    }

    // Without this, graphs don't render in safari due to date support across browsers
    // https://stackoverflow.com/questions/60308048/why-chart-js-charts-are-not-plotting-data-in-safari-works-in-chrome
    userLabels = userLabels.map((dtStr) =>
      moment(dtStr, 'MM-DD-YYYY').toDate()
    );

    let data = {};
    let chartOptions = {};
    if (chartType === 'total') {
      data = {
        labels: userLabels,
        datasets: [
          {
            label: `Average daily new ${userType} (last 30 days)`,
            fill: false,
            data: avgNewUsersLast30Data,
            pointRadius: 0,
            borderColor: theme.palette.green.main,
            yAxisID: 'yRollingAvg',
          },
          {
            label: `Total ${userType} to date`,
            fill: true,
            backgroundColor: getGradient(theme.palette.primary.main, 0.8),
            data: userData,
            pointRadius: 0,
            borderColor: theme.palette.primary.main,
          },
        ],
      };
      chartOptions = {
        maintainAspectRatio: false,
        plugins: {
          // legend: { display: false },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              footer: (tooltipItem) => {
                const xIndex = tooltipItem[0].dataIndex;
                const newUsers =
                  xIndex === 0
                    ? 0
                    : userData[parseInt(xIndex, 10)] -
                      userData[parseInt(xIndex - 1, 10)];
                return `Daily new ${userType}: ${newUsers}`;
              },
            },
          },
          zoom: {
            zoom: {
              mode: 'x',
              scaleMode: 'x',
              drag: {
                enabled: true,
              },
              pinch: {
                enabled: true,
              },
            },
          },
        },
        scales: {
          x: {
            type: 'time',
            time: {
              tooltipFormat: 'MM-DD-YYYY',
              unit: 'month',
            },
            // grid: {
            //   display: false,
            // },
          },
          y: {
            title: {
              text: 'Users',
              display: true,
            },
            // grid: {
            //   display: false,
            // },
          },
          yRollingAvg: {
            title: {
              text: `Average Daily New ${userType}`,
              display: true,
            },
            position: 'right',
            grid: {
              display: false,
            },
          },
        },
      };
    } else {
      data = {
        labels: userLabels,
        datasets: [
          {
            label: `New Users`,
            data: userData,
            backgroundColor: theme.palette.primary.main,
          },
        ],
      };

      // Add projection for the final time bucket
      if (userLabels.length > 0 && this.state.barChartBucketSize !== 'day') {
        const lastBucketDate = moment(userLabels[userLabels.length - 1]);
        const now = moment();

        // Only add projection if we're in the current time bucket
        if (lastBucketDate.isSame(now, this.state.barChartBucketSize)) {
          // Calculate days elapsed and remaining in current bucket
          const startOfBucket = moment(lastBucketDate).startOf(
            this.state.barChartBucketSize
          );
          const endOfBucket = moment(startOfBucket).endOf(
            this.state.barChartBucketSize
          );
          const daysElapsed = now.diff(startOfBucket, 'days');
          const daysRemaining = endOfBucket.diff(now, 'days', true);

          // Only project if we have days remaining in the bucket
          if (daysRemaining > 0 && daysElapsed > 0) {
            // Calculate total current for the last bucket
            let totalCurrentValue = userData[userData.length - 1] || 0;
            // Calculate daily average using the exact same method as in the tooltip
            // This ensures consistency between the projection and the tooltip
            const startOfBucket = moment(lastBucketDate).startOf(
              this.state.barChartBucketSize
            );
            const endOfBucket = moment(startOfBucket).endOf(
              this.state.barChartBucketSize
            );
            const exactDayCount = now.diff(startOfBucket, 'days', true);
            const dailyAverage = totalCurrentValue / Math.max(exactDayCount, 1);

            const projectedAdditional = dailyAverage * daysRemaining;

            // Create a single projected dataset
            if (projectedAdditional > 0) {
              const projectedDataset = {
                label: 'Projected Additional',
                data: Array(userLabels.length).fill(0),
                backgroundColor: 'rgba(76, 175, 80, 0.3)',
                borderColor: 'rgba(76, 175, 80, 0.6)',
                borderWidth: 2,
                borderDash: [5, 5], // Add dashed border
                // Add diagonal line pattern to indicate projection
                hoverBackgroundColor: 'rgba(76, 175, 80, 0.8)',
                label: 'Projected Additional',
              };

              // Set only the last bar to the projected additional amount
              projectedDataset.data[projectedDataset.data.length - 1] =
                projectedAdditional;

              // Add the single projected dataset to the chart data
              data.datasets.push(projectedDataset);
            }
          }
        }
      }

      chartOptions = {
        maintainAspectRatio: false,
        borderRadius: 4,
        plugins: {
          legend: { display: false },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: (tooltipItem) => {
                // Skip datasets with zero value
                if (tooltipItem.raw === 0) {
                  return null;
                }

                // Skip the projected dataset for tooltip calculations
                if (tooltipItem.dataset.label === 'Projected Additional') {
                  return `${tooltipItem.dataset.label}: ${numeral(
                    tooltipItem.formattedValue
                  ).format('0,0')}`;
                }

                return `${tooltipItem.dataset.label}: ${numeral(
                  tooltipItem.formattedValue
                ).format('0,0')}`;
                // `${tooltipItem.formattedValue} new users this ${barChartBucketSize}`,
              },
            },
          },
        },
        scales: {
          x: {
            stacked: true,
            type: 'time',
            time: {
              tooltipFormat: 'MM-DD-YYYY',
              unit: barChartBucketSize,
            },
          },
        },
      };
    }

    if (!this.props.fullScreen) {
      const toggleButtonStyle = { textTransform: 'none' };
      return (
        <>
          <Grid
            container
            justifyContent="space-between"
            alignItems="center"
            spacing={1}
          >
            <Grid item>
              <Typography gutterBottom variant="h6">
                {userType !== 'users' ? 'Investors' : 'Users'} :{' '}
                {`${numeral(avgNewUsersLast30).format('0,0.0')} / day`}{' '}
                <span style={{ fontSize: '14px' }}>(last 30 days)</span>
              </Typography>
            </Grid>
            <Grid item style={{ margin: '4px' }}>
              <ButtonGroup aria-label="switch user graphs" size="small">
                <Button
                  variant={
                    this.state.chartType === 'total' ? 'contained' : 'outlined'
                  }
                  aria-label="number of users over time"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ chartType: 'total' })}
                >
                  Total Users
                </Button>
                <Button
                  variant={
                    this.state.chartType === 'new' ? 'contained' : 'outlined'
                  }
                  aria-label="new users per time frame"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ chartType: 'new' })}
                >
                  New Users
                </Button>
              </ButtonGroup>
            </Grid>
            <Grid item style={{ margin: '4px' }}>
              <ButtonGroup aria-label="new users time-frame" size="small">
                <Button
                  variant={
                    this.state.barChartBucketSize === 'day'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="daily new users"
                  style={{ ...toggleButtonStyle }}
                  onClick={() =>
                    this.setState(
                      this.state.chartType === 'total'
                        ? null
                        : { barChartBucketSize: 'day' }
                    )
                  }
                  disabled={this.state.chartType === 'total'}
                >
                  Per Day
                </Button>
                <Button
                  variant={
                    this.state.barChartBucketSize === 'week'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="weekly new users"
                  style={{ ...toggleButtonStyle }}
                  onClick={() =>
                    this.setState(
                      this.state.chartType === 'total'
                        ? null
                        : { barChartBucketSize: 'week' }
                    )
                  }
                  disabled={this.state.chartType === 'total'}
                >
                  Per Week
                </Button>
                <Button
                  variant={
                    this.state.barChartBucketSize === 'month'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="monthly new users"
                  style={{ ...toggleButtonStyle }}
                  onClick={() =>
                    this.setState(
                      this.state.chartType === 'total'
                        ? null
                        : { barChartBucketSize: 'month' }
                    )
                  }
                  disabled={this.state.chartType === 'total'}
                >
                  Per Month
                </Button>
                <Button
                  variant={
                    this.state.barChartBucketSize === 'quarter'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="quarterly new users"
                  style={{ ...toggleButtonStyle }}
                  onClick={() =>
                    this.setState(
                      this.state.chartType === 'total'
                        ? null
                        : { barChartBucketSize: 'quarter' }
                    )
                  }
                  disabled={this.state.chartType === 'total'}
                >
                  Per Quarter
                </Button>
              </ButtonGroup>
            </Grid>
            <Grid item style={{ margin: '4px' }}>
              <ButtonGroup aria-label="switch user type" size="small">
                <Button
                  variant={
                    this.state.userType === 'users' ? 'contained' : 'outlined'
                  }
                  aria-label="all users"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ userType: 'users' })}
                >
                  All Users
                </Button>
                <Button
                  variant={
                    this.state.userType === 'investors'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="users with investments"
                  style={{ ...toggleButtonStyle }}
                  onClick={() => this.setState({ userType: 'investors' })}
                >
                  Investors (Incl. referrals)
                </Button>
                <Button
                  variant={
                    this.state.userType === 'investorsExcludingReferrals'
                      ? 'contained'
                      : 'outlined'
                  }
                  aria-label="users with investments excluding referral rewards"
                  style={{ ...toggleButtonStyle }}
                  onClick={() =>
                    this.setState({
                      userType: 'investorsExcludingReferrals',
                    })
                  }
                >
                  Investors
                </Button>
              </ButtonGroup>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            {chartType === 'total' ? (
              <Line
                key={`user-line-chart-${timePeriod}`}
                height={360}
                data={data}
                options={chartOptions}
              />
            ) : (
              <Bar height={360} data={data} options={chartOptions} />
            )}
          </Grid>
        </>
      );
    } else {
      return (
        <>
          <Grid container justifyContent="space-between" alignItems="center">
            <Typography gutterBottom variant="h6">
              {userType !== 'users' ? 'Investors' : 'Users'} :{' '}
              {`${numeral(avgNewUsersLast30).format('0,0.0')} / day`}{' '}
              <span style={{ fontSize: '14px' }}>(last 30 days)</span>
            </Typography>
            <IconButton
              variant="contained"
              color="primary"
              onClick={() => this.setState({ dialogOpen: true })}
            >
              <Settings />
            </IconButton>
          </Grid>
          <Dialog open={this.state.dialogOpen} fullWidth>
            <DialogTitle>
              <Typography variant="h5">Chart Customization</Typography>
            </DialogTitle>
            <DialogContent>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Chart Type:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectChartType"
                    defaultValue={this.state.selectChartType}
                    onChange={(event) => {
                      this.setState({ selectChartType: event.target.value });
                    }}
                  >
                    <FormControlLabel
                      value="total"
                      control={<Radio />}
                      label="Total Users"
                    />
                    <FormControlLabel
                      value="new"
                      control={<Radio />}
                      label="New Users"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Bar Chart Bucket Size:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectBarChartBucketSize"
                    defaultValue={this.state.selectBarChartBucketSize}
                    onChange={(event) => {
                      this.setState({
                        selectBarChartBucketSize: event.target.value,
                      });
                    }}
                  >
                    <FormControlLabel
                      value="day"
                      control={<Radio />}
                      label="Per Day"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="week"
                      control={<Radio />}
                      label="Per Week"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="month"
                      control={<Radio />}
                      label="Per Month"
                      disabled={this.state.selectChartType === 'total'}
                    />
                    <FormControlLabel
                      value="quarter"
                      control={<Radio />}
                      label="Per Quarter"
                      disabled={this.state.selectChartType === 'total'}
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
              <Grid
                container
                item
                alignItems="center"
                justifyContent="space-between"
                style={{ marginBottom: '1rem' }}
              >
                <Typography variant="h6">Switch User Type:</Typography>
                <Grid item>
                  <RadioGroup
                    row
                    name="selectUserType"
                    defaultValue={this.state.selectUserType}
                    onChange={(event) => {
                      this.setState({ selectUserType: event.target.value });
                    }}
                  >
                    <FormControlLabel
                      value="users"
                      control={<Radio />}
                      label="All Users"
                    />
                    <FormControlLabel
                      value="investors"
                      control={<Radio />}
                      label="Investors (Incl. referrals)"
                    />
                    <FormControlLabel
                      value="investorsExcludingReferrals"
                      control={<Radio />}
                      label="Investors"
                    />
                  </RadioGroup>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <MuiButton
                onClick={() => this.setState({ dialogOpen: false })}
                color="primary"
              >
                Cancel
              </MuiButton>
              <MuiButton
                onClick={() => {
                  this.setState({
                    chartType: this.state.selectChartType,
                    barChartBucketSize:
                      this.state.selectChartType === 'total'
                        ? null
                        : this.state.selectBarChartBucketSize,
                    userType: this.state.selectUserType,
                    dialogOpen: false,
                  });
                }}
                color="primary"
                variant="contained"
              >
                Go
              </MuiButton>
            </DialogActions>
          </Dialog>
          <Grid item xs={12}>
            {chartType === 'total' ? (
              <Line
                key={`user-line-chart-${timePeriod}`}
                height={360}
                data={{
                  labels: userLabels,
                  datasets: [
                    {
                      label: `Average daily new ${userType} (last 30 days)`,
                      fill: false,
                      data: avgNewUsersLast30Data,
                      pointRadius: 0,
                      borderColor: theme.palette.green.main,
                      yAxisID: 'yRollingAvg',
                    },
                    {
                      label: `Total ${userType} to date`,
                      fill: true,
                      backgroundColor: 'rgba(21, 48, 76, 0.5)',
                      data: userData,
                      pointRadius: 0,
                      borderColor: theme.palette.primary.main,
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    // legend: { display: false },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        footer: (tooltipItem) => {
                          const xIndex = tooltipItem[0].dataIndex;
                          const newUsers =
                            xIndex === 0
                              ? 0
                              : userData[parseInt(xIndex, 10)] -
                                userData[parseInt(xIndex - 1, 10)];
                          return `Daily new ${userType}: ${newUsers}`;
                        },
                      },
                    },
                    zoom: {
                      zoom: {
                        mode: 'x',
                        scaleMode: 'x',
                        drag: {
                          enabled: true,
                        },
                        pinch: {
                          enabled: true,
                        },
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MM-DD-YYYY',
                        unit: 'month',
                      },
                      // grid: {
                      //   display: false,
                      // },
                    },
                    y: {
                      title: {
                        text: 'Users',
                        display: true,
                      },
                      // grid: {
                      //   display: false,
                      // },
                    },
                    yRollingAvg: {
                      title: {
                        text: `Average Daily New ${userType}`,
                        display: true,
                      },
                      position: 'right',
                      grid: {
                        display: false,
                      },
                    },
                  },
                }}
              />
            ) : (
              <Bar
                height={360}
                data={{
                  labels: userLabels,
                  datasets: [
                    {
                      data: userData,
                      backgroundColor: theme.palette.primary.main,
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  borderRadius: 4,
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      callbacks: {
                        label: (tooltipItem) =>
                          `${tooltipItem.formattedValue} new users this ${barChartBucketSize}`,
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: 'time',
                      time: {
                        tooltipFormat: 'MM-DD-YYYY',
                        unit: barChartBucketSize,
                      },
                    },
                  },
                }}
              />
            )}
          </Grid>
        </>
      );
    }
  }
}

const renderBank = {};
for (let i = 0; i < 1 << 5; i++) {
  const combo = [
    !!(i & (1 << 4)),
    !!(i & (1 << 3)),
    !!(i & (1 << 2)),
    !!(i & (1 << 1)),
    !!(i & 1),
  ];
  const sKey = combo.join('-');
  renderBank[String(sKey)] = combo;
}
class CustomFunnel extends Component {
  constructor(props) {
    super(props);
    this.state = {
      funnelIncludeGAVisitors: false,
      funnelIncludeUsers: true,
      funnelIncludeProfilesComplete: true,
      funnelIncludeInvestors: true,
      funnelIncludeInvestorsExcludingReferrals: true,
    };
    this.createComponent = this.createComponent.bind(this);
  }

  createComponent(newSKey) {
    const {
      theme,
      crowdInvestmentTotal,
      gaVisitorCount,
      profileCompleteUserCount,
      investorCount,
      investorCountExcludingReferrals,
      userCount,
    } = this.props;

    const funnelData = [
      {
        label: 'Site Visitors',
        quantity: gaVisitorCount,
        isVisible: (state) => state.funnelIncludeGAVisitors,
      },
      {
        label: 'Energea Users',
        quantity: userCount,
        isVisible: (state) => state.funnelIncludeUsers,
      },
      {
        label: 'Profile Completed',
        quantity: profileCompleteUserCount,
        isVisible: (state) => state.funnelIncludeProfilesComplete,
      },
      {
        label: 'Investors (incl. referral rewards)',
        quantity: investorCount,
        isVisible: (state) => state.funnelIncludeInvestors,
      },
      {
        label: 'Investors',
        quantity: investorCountExcludingReferrals,
        isVisible: (state) => state.funnelIncludeInvestorsExcludingReferrals,
      },
    ];
    const filteredData = funnelData.filter((dataPoint) =>
      dataPoint.isVisible(this.state)
    );
    renderBank[String(newSKey)].component = (
      <Funnel
        key={newSKey}
        height={200}
        valueKey="quantity"
        labelKey="label"
        displayPercent={true}
        colors={{
          graph: [theme.palette.primary.main, '#BAE7FF'],
        }}
        renderPercentage={(index, value) => {
          return (
            <span
              style={{
                padding: '4px',
                borderRadius: '8px',
                background: 'rgba(255,255,255,0.9)',
                border: 'solid 1px lightgrey',
              }}
            >
              {value}
            </span>
          );
        }}
        renderLabel={(index, value) => <span>{value}</span>}
        renderDropOffPercentage={(index, value) => null}
        renderValue={(index, data) => (
          <Typography
            style={{ fontWeight: 'bold' }}
            gutterBottom={false}
            variant="h5"
          >
            {numeral(data).format('0,0')}{' '}
            <span
              style={{
                color: '#5292c3',
                marginLeft: '4px',
                fontSize: '.9rem',
                fontWeight: 'regular',
                fontFamily: 'Lato',
              }}
            >
              <b>{numeral(crowdInvestmentTotal / data).format('$0,0')}</b> /
              user
            </span>
          </Typography>
        )}
        data={filteredData}
      />
    );
    return renderBank[String(newSKey)].component;
  }

  renderToggles() {
    const oLabels = {
      funnelIncludeGAVisitors: 'Site Visitors',
      funnelIncludeUsers: 'Energea Users',
      funnelIncludeProfilesComplete: 'Profile Completed',
      funnelIncludeInvestors: 'Investors (incl. referral rewards)',
      funnelIncludeInvestorsExcludingReferrals: 'Investors',
    };
    return Object.keys(oLabels).map((stateName) => {
      return (
        <FormControlLabel
          key={`funnel-label-${stateName}`}
          control={
            <Switch
              checked={this.state[String(stateName)]}
              onChange={() => {
                this.setState({ [stateName]: !this.state[String(stateName)] });
              }}
            />
          }
          label={oLabels[String(stateName)]}
        />
      );
    });
  }

  render() {
    const {
      funnelIncludeGAVisitors,
      funnelIncludeUsers,
      funnelIncludeProfilesComplete,
      funnelIncludeInvestors,
      funnelIncludeInvestorsExcludingReferrals,
    } = this.state;
    const curSKey = [
      funnelIncludeGAVisitors,
      funnelIncludeUsers,
      funnelIncludeProfilesComplete,
      funnelIncludeInvestors,
      funnelIncludeInvestorsExcludingReferrals,
    ].join('-');
    renderBank[String(curSKey)].component || this.createComponent(curSKey);
    return (
      <>
        <Grid item style={{ marginBottom: '2rem' }}>
          {this.renderToggles()}
        </Grid>
        <Grid item style={{ height: '364px', position: 'relative' }}>
          {Object.keys(renderBank).map((key) => {
            const el = renderBank[String(key)];
            if (el.component) {
              return (
                <Collapse
                  key={`collapse-container-${key}`}
                  style={{ position: 'absolute', width: '100%' }}
                  in={key === curSKey}
                >
                  {el.component}
                </Collapse>
              );
            }
            return null;
          })}
        </Grid>
      </>
    );
  }
}
