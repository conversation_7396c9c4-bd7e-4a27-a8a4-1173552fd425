import React, { useState } from 'react';
import {
  CircularProgress,
  Grid,
  Skeleton,
  TableCell,
  TableRow,
} from '@mui/material';
import { Alert } from '@mui/lab';
import { useDataProvider } from 'react-admin';

export const FaturamentosExpansionPanel = (props) => {
  const {
    open,
    salesforceProjectId,
    tableData,
    handleHistoricalDataQuerySuccess,
  } = props;

  const [loading, setLoading] = useState(null);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const dataProvider = useDataProvider();

  const fetchData = () => {
    setLoading(true);
    dataProvider
      .getList('CreditMgmtDashboardBrBillingCycle', {
        filter: {
          salesforceProject: { id: salesforceProjectId },
        },
        sort: { field: 'billingMonth', order: 'desc' },
        pagination: { page: 1, perPage: 1000 },
      })
      .then(
        (res) => {
          setData(res.data);
          setLoading(false);
        },
        (err) => {
          setLoading(false);
          setError(err);
          console.error('Error fetching project billing cycles');
        }
      );
  };

  if (data && !loading && !error) {
    handleHistoricalDataQuerySuccess(salesforceProjectId, data);
  }

  if (!open) return null;

  if (open && !data && !loading && !error) {
    setLoading(true);
    fetchData();
  }

  if (error && open) {
    return (
      <Alert severity="error">
        There was an issue loading project billing cycles.
      </Alert>
    );
  }

  if ((loading || !data) && open) {
    return [1, 2, 3].map((_, index) => (
      <TableRow
        key={`billing-cycle-row-${index}-loading-project-${salesforceProjectId}`}
        selected
      >
        {tableData.map(() => (
          <TableCell>
            <Skeleton variant="text" height="2.5rem" />
          </TableCell>
        ))}
      </TableRow>
    ));
  }

  return (
    data?.map((brBillingCycle, index) => {
      if (index === 0) return null;
      return (
        <TableRow
          key={`billing-cycle-row-p-${data?.id}-f-${brBillingCycle.id}`}
          selected
        >
          {tableData.map((column) =>
            column.valueJsx(
              column.value(
                {
                  ...brBillingCycle.project,
                  latestBrBillingCycle: brBillingCycle,
                },
                true
              ),
              {
                latestBrBillingCycle: brBillingCycle,
              }
            )
          )}
        </TableRow>
      );
    }) || null
  );
};
