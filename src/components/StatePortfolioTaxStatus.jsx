import React from 'react';

import {
  Datagrid,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import numeral from 'numeral';
import { LinkField } from './CustomFields';

const entityName = 'State Portfolio Tax Status';

const StatePortfolioTaxFilter = (props) => (
  <Filter {...props}>
    <SelectInput
      label="Period"
      source="period"
      choices={[
        { id: 'previous', name: 'Previous' },
        { id: 'current', name: 'Current' },
        { id: 'allTime', name: 'All-Time' },
      ]}
      // defaultValue={'current'}
      alwaysOn
      allowEmpty={false}
    />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      source="state.id"
      reference="USState"
      label="State"
      alwaysOn
      perPage={1000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="State" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const StatePortfolioTaxStatusList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      filters={<StatePortfolioTaxFilter />}
      sort={{ field: 'state.name', order: 'ASC' }}
      pagination={null}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="state.name" label="State" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <FunctionField
          label="Period"
          render={(record) => {
            return `${record.periodStartDt} to ${record.periodEndDt}`;
          }}
        />
        <FunctionField
          label="Taxes Owed"
          render={(record) => {
            return record.taxesOwed > 0 ? (
              <b style={{ color: 'red' }}>
                {numeral(record.taxesOwed).format('$0,0.00')}
              </b>
            ) : (
              '$0.00'
            );
          }}
        />
        <NumberField
          source="investmentTotal"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField source="investorCount" />
        <NumberField source="sharesPurchased" />
        <NumberField
          source="state.threshold"
          label="Threshold"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="state.divisor"
          label="Overage Interval"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="state.divisorFee"
          label="Overage Fee"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="state.divisorFeeCap"
          label="Overage Fee Cap"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="state.processingFee"
          label="Processing Fee"
          options={{ style: 'currency', currency: 'USD' }}
        />
      </Datagrid>
    </List>
  );
};
