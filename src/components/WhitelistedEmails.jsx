import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  Edit,
  List,
  ListButton,
  SimpleForm,
  TextField,
  TextInput,
  TopToolbar,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';
import { ChevronLeft } from '@mui/icons-material';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Whitelisted Email';

const CustomEditActions = ({ basePath }) => (
  <TopToolbar>
    <ListButton
      basePath={basePath}
      label="Back to List"
      icon={<ChevronLeft />}
    />
  </TopToolbar>
);

export const WhitelistedEmailEdit = () => {
  const { id } = useParams();
  return (
    <Edit
      title={`${entityName} #${id}`}
      undoable={false}
      actions={<CustomEditActions />}
    >
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="email" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const WhitelistedEmailList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="email" />
        <DateField source="createdAt" showTime />
        <DateField source="updatedAt" showTime />
      </Datagrid>
    </List>
  );
};

export const WhitelistedEmailCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="email" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
