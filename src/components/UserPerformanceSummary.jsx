import React, { Component, useState } from 'react';

// @material-ui
import { withStyles } from '@mui/styles';
import {
  <PERSON>ert,
  AlertTitle,
  Button,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useDataProvider, useNotify } from 'react-admin';
import numeral from 'numeral';

import { GetApp } from '@mui/icons-material';

const styles = () => ({});

const UserPerformanceSummary = (props) => {
  const { userId, subAccount } = props;
  const [performanceData, setPerformanceData] = useState(null);
  const [performanceDataLoading, setPerformanceDataLoading] = useState(false);
  const [error, setError] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  const handleDownloadAccountActivity = () => {
    dataProvider
      .create('UserAccountActivitySummary', {
        input: {
          userId: parseInt(userId, 10),
          accountFilter: {
            allAccounts: false,
            subAccountId: subAccount?.id ? parseInt(subAccount.id, 10) : null,
          },
        },
      })
      .then((res) => {
        if (!res?.data?.downloadUrl) {
          notify('Error downloading excel', { type: 'error' });
          return null;
        }
        const link = document.createElement('a');
        link.href = res.data.downloadUrl;
        link.download = true;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch((e) => {
        console.error(e);
        notify('Error downloading excel', { type: 'error' });
      });
  };

  const fetchPerformanceData = () => {
    setPerformanceDataLoading(true);
    dataProvider
      .getOne('UserPerformanceSummaryData', {
        input: {
          id: userId,
          accountFilter: {
            allAccounts: false,
            subAccountId: subAccount?.id ? parseInt(subAccount.id, 10) : null,
          },
        },
      })
      .then(
        (res) => {
          setPerformanceDataLoading(false);
          setError(false);
          setPerformanceData(res.data);
        },
        (err) => {
          setPerformanceDataLoading(false);
          setError(true);
          notify('Error getting user performance summary data', {
            type: 'error',
          });
        }
      );
  };

  if (!performanceData && !performanceDataLoading && !error) {
    fetchPerformanceData();
  }

  if (performanceDataLoading) {
    return (
      <Grid
        style={{
          position: 'fixed',
          top: '50%',
          width: '100%',
          textAlign: 'center',
        }}
      >
        <CircularProgress />
      </Grid>
    );
  }

  if (error) {
    return <Alert severity="error">Error getting data</Alert>;
  }

  if (!performanceData) {
    return null;
  }

  const accountName =
    subAccount === null
      ? `Individual Account #${performanceData.energeaAccountNumber}`
      : subAccount.name;
  const dividendsReinvested =
    performanceData.dividendSum - performanceData.nonReinvestedDividendSum;
  const platformLevelPerformance = [
    {
      label: 'Market Value',
      value: numeral(performanceData.nav).format('$0,0.00'),
    },
    {
      label: 'Cost Basis',
      value: numeral(performanceData.costBasis).format('$0,0.00'),
    },
    {
      label: 'Total Dividends',
      value: numeral(performanceData.dividendSum).format('$0,0.00'),
    },
    {
      label: 'Dividends Reinvested',
      value: numeral(
        Math.abs(dividendsReinvested) < 0.001 ? 0 : dividendsReinvested
      ).format('$0,0.00'),
    },
    {
      label: 'Dividends Paid',
      value: numeral(performanceData.nonReinvestedDividendSum).format(
        '$0,0.00'
      ),
    },
    {
      label: 'IRR',
      value: `${numeral(performanceData.navBasedIRR).format('0,0.00')}% ${
        performanceData.showIRR ? '' : '(hidden)'
      }`,
    },
    {
      label: 'IRR (ignoring early exit penalties)',
      value: `${numeral(
        performanceData.navBasedIRRExcludingEarlyExitPenalties
      ).format('0,0.00')}% ${performanceData.showIRR ? '' : '(hidden)'}`,
    },
  ];
  return (
    <>
      <Grid item xs={12} md={6}>
        <Grid item xs={12}>
          <Typography variant="h4" gutterBottom style={{ fontWeight: 'bold' }}>
            {accountName}
          </Typography>
        </Grid>
        <Grid container>
          <Grid item>
            <Typography variant="h5" gutterBottom>
              Account Level Performance
            </Typography>
          </Grid>
          <Grid item style={{ paddingLeft: '2rem' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<GetApp />}
              onClick={handleDownloadAccountActivity}
            >
              Download Account Activity
            </Button>
          </Grid>
        </Grid>
        <Table>
          <TableBody>
            {platformLevelPerformance.map((row) => (
              <TableRow key={`account-level-user-performance-${row.label}`}>
                <TableCell>
                  <b>{row.label}</b>
                </TableCell>
                <TableCell>{row.value}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Grid>
      <Grid item xs={12} style={{ marginTop: '2rem' }}>
        <Typography variant="h5" gutterBottom>
          Portfolio Level Performance
        </Typography>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <b>Portfolio</b>
              </TableCell>
              <TableCell>
                <b>Market Value</b>
              </TableCell>
              <TableCell>
                <b>Cost Basis</b>
              </TableCell>
              <TableCell>
                <b>Total Dividends</b>
              </TableCell>
              <TableCell>
                <b>Dividends Reinvested</b>
              </TableCell>
              <TableCell>
                <b>Dividends Paid</b>
              </TableCell>
              <TableCell>
                <b>IRR</b>
              </TableCell>
              <TableCell>
                <b>Shares Owned</b>
              </TableCell>
              <TableCell>
                <b>Current Share Price</b>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {performanceData.userPortfolioInvestments &&
              performanceData.userPortfolioInvestments.map((portfolioRow) => {
                const dividendsPaidOut =
                  portfolioRow.dividendSum -
                  portfolioRow.dividendsAutoReinvested;
                const portfolioIRRData =
                  performanceData.userPortfolioIRRs.filter(
                    (pIRR) => pIRR.portfolioId === portfolioRow.portfolio.id
                  )[0];
                return (
                  <TableRow
                    key={`portfolio-level-user-performance-p-${portfolioRow.portfolio.id}`}
                  >
                    <TableCell>{portfolioRow.portfolio.subtitle}</TableCell>
                    <TableCell>
                      {numeral(portfolioRow.nav).format('$0,0.00')}
                    </TableCell>
                    <TableCell>
                      {numeral(portfolioRow.costBasis).format('$0,0.00')}
                    </TableCell>
                    <TableCell>
                      {numeral(portfolioRow.dividendSum).format('$0,0.00')}
                    </TableCell>
                    <TableCell>
                      {numeral(portfolioRow.dividendsAutoReinvested).format(
                        '$0,0.00'
                      )}
                    </TableCell>
                    <TableCell>
                      {numeral(
                        Math.abs(dividendsPaidOut) < 0.001
                          ? 0
                          : dividendsPaidOut
                      ).format('$0,0.00')}
                    </TableCell>
                    <TableCell>{`${numeral(portfolioIRRData.navBasedIRR).format(
                      '0,0.00'
                    )}% ${
                      portfolioIRRData.showIRR ? '' : '(hidden)'
                    }`}</TableCell>
                    <TableCell>
                      {numeral(portfolioRow.shareSum).format('0,0.00')}
                    </TableCell>
                    <TableCell>
                      {numeral(portfolioRow.latestSharePrice).format('$0,0.00')}
                    </TableCell>
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
        {!performanceData.userPortfolioInvestments ||
          (performanceData.userPortfolioInvestments.length === 0 && (
            <Alert severity="info" style={{ width: '100%' }}>
              <AlertTitle>No investments to report on</AlertTitle>
            </Alert>
          ))}
      </Grid>
    </>
  );
};

export default withStyles(styles)(UserPerformanceSummary);
