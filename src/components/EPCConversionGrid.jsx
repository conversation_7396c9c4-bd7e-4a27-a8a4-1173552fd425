import React, { PureComponent } from 'react';
import numeral from 'numeral';

import { Grid, Icon, Typography } from '@mui/material';
import { constants } from '../utils/global';

class EPCConversionGrid extends PureComponent {
  render() {
    // NOTE: production is in MWh
    const { production, dataPointKeys, columns } = this.props;
    const dataPoints = {
      homesPowered: {
        label: 'U.S. Homes powered for 1 year',
        iconClass: 'fas fa-home',
        formatter: (p) =>
          numeral(p * constants.epcConversionMultipliers.homesPowered).format(
            '0,0.[00]a'
          ),
      },
      tonsReduced: {
        label: 'Tons of carbon emissions reduced',
        iconClass: 'fas fa-industry',
        formatter: (p) =>
          numeral(
            p * constants.epcConversionMultipliers.tonsCarbonEmissionsReduced
          ).format('0,0.[00]a'),
      },
      treesPlanted: {
        label: 'Trees planted',
        iconClass: 'fas fa-tree',
        formatter: (p) =>
          numeral(p * constants.epcConversionMultipliers.treesPlanted).format(
            '0,0a'
          ),
      },
      gallonsOfGasConsumed: {
        label: 'Gallons of gasoline avoided',
        iconClass: 'fas fa-gas-pump',
        formatter: (p) =>
          numeral(p * constants.epcConversionMultipliers.gallonsOfGas).format(
            '0,0a'
          ),
      },
      poundsCoalBurned: {
        label: 'Pounds of coal avoided',
        iconClass: 'fas fa-fire-alt',
        formatter: (p) =>
          numeral(p * constants.epcConversionMultipliers.poundsOfCoal).format(
            '0,0a'
          ),
      },
      smartPhonesCharged: {
        label: 'Smartphones charged',
        iconClass: 'fas fa-mobile-alt',
        formatter: (p) =>
          numeral(
            p * constants.epcConversionMultipliers.smartPhonesCharged
          ).format('0,0a'),
      },
    };
    return (
      <Grid
        style={{ marginTop: '1rem' }}
        container
        justifyContent="space-evenly"
        spacing={5}
      >
        {dataPointKeys.map((dataPointKey) => {
          const data = dataPoints[String(dataPointKey)];
          if (!data) return null;
          return (
            <Grid
              key={`epc-conversion-grid-key-${dataPointKey}`}
              item
              container
              direction="column"
              justifyContent="center"
              xs={12 / columns}
            >
              <Grid
                item
                style={{
                  textAlign: 'center',
                }}
              >
                <Icon
                  // color="#5292C3"
                  style={{
                    width: '100%',
                    height: '2.5rem',
                    fontSize: '2rem',
                    color: '#5292C3',
                  }}
                >
                  <i className={data.iconClass} />
                </Icon>{' '}
              </Grid>{' '}
              <Grid
                item
                style={{
                  textAlign: 'center',
                }}
              >
                <Typography color="primary" variant="h3">
                  {data.formatter(production)}
                </Typography>{' '}
              </Grid>{' '}
              <Grid
                item
                style={{
                  textAlign: 'center',
                  minHeight: '63px',
                }}
              >
                <Typography variant="body2">{data.label}</Typography>{' '}
              </Grid>{' '}
            </Grid>
          );
        })}
        <Grid item style={{ textAlign: 'right' }} xs={12}>
          <Typography variant="caption">
            <em>
              Calculations derived from the latest findings by the{' '}
              <a
                target="_blank"
                href="https://www.epa.gov/energy/greenhouse-gas-equivalencies-calculator"
              >
                {' '}
                United States Environmental Protection Agency.
              </a>
            </em>
          </Typography>
        </Grid>
      </Grid>
    );
  }
}

export default EPCConversionGrid;
