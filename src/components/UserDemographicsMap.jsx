import { useState } from 'react';
import { CircularProgress, Grid, Popover } from '@mui/material';
import { withStyles } from '@mui/styles';

import UserMap from './UserMap';
import UserPopover from './UserPopover';
import { interpolateColors } from '../utils/global';
import theme from '../theme';

const styles = (theme) => ({});

export default withStyles(styles)((props) => {
  const [selectedMapUser, setSelectedMapUser] = useState(null);
  const [userPopoverOpen, setUserPopoverOpen] = useState(false);
  const [userPopoverAnchorEl, setUserPopoverAnchorEl] = useState(null);

  const { users, loading } = props;

  const onMarkerClick = (user) => {
    return (event) => {
      setSelectedMapUser(user);
      setUserPopoverOpen(true);
      setUserPopoverAnchorEl(event.currentTarget);
    };
  };

  const handleClose = () => {
    setUserPopoverOpen(false);
    setUserPopoverAnchorEl(null);
    setUserPopoverAnchorEl(null);
  };

  const getTotalInvestedSaturation = (value) => {
    if (value < 500) return 0.1;
    if (value < 5000) return 0.3;
    if (value < 25000) return 0.6;
    return 1;
  };

  const colors = interpolateColors(
    6,
    'rgb(214, 255, 189)',
    'rgb(75, 135, 40)',
    0.9
  );
  const getTotalInvestedColor = (value) => {
    if (value < 500) return colors[0];
    if (value < 5000) return colors[1];
    if (value < 10000) return colors[2];
    if (value < 25000) return colors[3];
    if (value < 50000) return colors[4];
    return colors[5];
  };

  const getTotalInvestedSize = (value) => {
    if (value < 500) return '50px';
    if (value < 5000) return '70px';
    if (value < 25000) return '90px';
    return '110px';
  };

  return (
    <Grid
      item
      xs={12}
      style={{
        margin: '1rem',
        borderRadius: theme.shape.borderRadius,
        overflow: 'hidden',
      }}
    >
      {!users || loading ? (
        <Grid
          container
          alignItems="center"
          justifyContent="center"
          style={{ minHeight: '400px' }}
        >
          <CircularProgress />
        </Grid>
      ) : (
        <Grid>
          {users ? (
            <Grid item xs={12}>
              <UserMap
                markers={users
                  .filter((i) => i.latitude && i.longitude)
                  .map((investor) => ({
                    onMarkerClick: onMarkerClick(investor),
                    onMarkerClose: handleClose,
                    latitude: parseFloat(investor.latitude),
                    longitude: parseFloat(investor.longitude),
                    color: getTotalInvestedColor(investor.investmentSum),
                    // saturation: getTotalInvestedSaturation(
                    //   investor.investmentSum
                    // ),
                    sizePx: '40px', //getTotalInvestedSize(investor.investmentSum),
                  }))}
              />
              {userPopoverOpen && userPopoverAnchorEl ? (
                <Popover
                  open={userPopoverOpen}
                  anchorEl={userPopoverAnchorEl}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                  }}
                >
                  <UserPopover
                    handleClose={handleClose}
                    data={selectedMapUser}
                  />
                </Popover>
              ) : null}
            </Grid>
          ) : (
            <CircularProgress />
          )}
        </Grid>
      )}
    </Grid>
  );
});
