import React, { useState } from 'react';

import {
  AutocompleteInput,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  DeleteButton,
  Edit,
  Filter,
  FunctionField,
  Labeled,
  List,
  Pagination,
  ReferenceInput,
  regex,
  required,
  SaveButton,
  SimpleForm,
  TextField,
  TextInput,
  Toolbar,
  useNotify,
  usePermissions,
  useRedirect,
  useResourceDefinition,
} from 'react-admin';
import { useParams } from 'react-router-dom';
import { Button, Grid } from '@mui/material';
import { makeStyles } from '@mui/styles';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'Quarterly User Financial Statement';

const useStyles = makeStyles({
  toolbar: {
    display: 'flex',
    justifyContent: 'space-between',
  },
});

const QuarterlyUserFinancialStatementEditToolbar = () => (
  <Toolbar classes={useStyles()}>
    <SaveButton
      label="Overwrite Statement"
      // redirect="list"
      submitOnEnter={true}
    />
    <DeleteButton undoable={false} />
  </Toolbar>
);

export const QuarterlyUserFinancialStatementEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm toolbar={<QuarterlyUserFinancialStatementEditToolbar />}>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="id" disabled fullWidth />
            <Labeled fullWidth>
              <LinkField
                reference="User"
                linkSource="user.id"
                labelSource="user.fullName"
                label="User"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                label="Sub-Account"
                linkSource="subAccount.id"
                labelSource="subAccount.name"
                reference="SubAccount"
              />
            </Labeled>
            <TextInput source="awsObjectKey" fullWidth disabled={!isIT} />
            <TextInput source="quarter" disabled fullWidth />
            <DateInput source="updatedAt" disabled fullWidth />
            <DateInput source="createdAt" disabled fullWidth />
          </Grid>
        </Grid>
        <FunctionField
          label="Statement URL"
          render={(record) => {
            return (
              <Button
                variant="contained"
                startIcon={<CloudDownloadIcon />}
                style={{ textTransform: 'none' }}
                onClick={() => window.location.assign(record.awsObjectUrl)}
              >
                Download PDF
              </Button>
            );
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

const QuarterlyUserFinancialStatementFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <TextInput label="Quarter" source="quarter" />
  </Filter>
);

const QuarterlyUserFinancialStatementPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100]} />
);

export const QuarterlyUserFinancialStatementList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      filters={<QuarterlyUserFinancialStatementFilter />}
      sort={{ field: 'id', order: 'DESC' }}
      perPage={25}
      pagination={<QuarterlyUserFinancialStatementPagination />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          label="SubAccount"
          linkSource="subAccount.id"
          labelSource="subAccount.name"
          reference="SubAccount"
        />
        <TextField source="quarter" />
        <FunctionField
          label="Statement URL"
          render={(record) => {
            return (
              <Button
                variant="contained"
                startIcon={<CloudDownloadIcon />}
                style={{ textTransform: 'none' }}
                onClick={(event) => {
                  window.location.assign(record.awsObjectUrl);
                  event.stopPropagation();
                }}
              >
                Download PDF
              </Button>
            );
          }}
        />
        <TextField source="awsObjectKey" sortable={false} />
        <DateField source="updatedAt" showTime />
        <DateField source="createdAt" showTime />
      </Datagrid>
    </List>
  );
};

const QuarterlyUserFinancialStatementCreateToolbar = (props) => {
  const { data } = props;
  return (
    <Toolbar {...props}>
      <SaveButton
        label={
          data.batchCreate
            ? 'Batch Create Statements'
            : 'Create Single Statement'
        }
        redirect="list"
        submitOnEnter
      />
    </Toolbar>
  );
};

export const QuarterlyUserFinancialStatementCreate = (props) => {
  const [batchCreate, setBatchCreate] = useState(false);
  const notify = useNotify();
  const redirect = useRedirect();
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((role) => role.name)?.indexOf('ITWrite') > -1;

  const onSuccess = () => {
    notify(
      `Successfully initiated Quarterly Statement creation. Message will be sent to Slack once complete.`,
      { type: 'success' }
    );
    redirect('/QuarterlyUserFinancialStatement');
  };

  return (
    <Create
      title={`Create ${entityName}`}
      mutationOptions={{ onSuccess: onSuccess }}
    >
      <SimpleForm
        toolbar={
          <QuarterlyUserFinancialStatementCreateToolbar
            data={{ batchCreate }}
          />
        }
      >
        <Grid style={{ width: '100%' }} container spacing={5}>
          <Grid item md={6} xs={12}>
            <BooleanInput
              label="Batch Create Statements"
              source="batchCreate"
              onChange={() => setBatchCreate(!batchCreate)}
              fullWidth
              disabled={!isIT}
            />
            <ReferenceInput
              perPage={10000}
              source="user.id"
              reference="UserLite"
            >
              <AutocompleteInput
                validate={batchCreate ? [] : [required()]}
                label="User (Investor)"
                fullWidth
                disabled={batchCreate}
                allowEmpty
                optionText="label"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            {/* <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (!formData.user?.id) return null;
                return (
                  <ReferenceInput
                    label="SubAccount"
                    // required
                    fullWidth
                    perPage={10000}
                    source="subAccount.id"
                    reference="SubAccountLite"
                    disabled={batchCreate}
                    filter={{ userId: parseInt(formData.user.id, 10) }}
                  >
                    <SelectInput
                      allowEmpty
                      optionText="name"
                      fullWidth
                      shouldRenderSuggestions={(value) =>
                        value.trim().length > 0
                      }
                    />
                  </ReferenceInput>
                );
              }}
            </FormDataConsumer> */}
            <TextInput
              source="quarter"
              validate={[
                required(),
                regex(/^([0-9]{4}-Q[1-4])$/, 'Must be a valid quarter'),
              ]}
              helperText="Example: 2020-Q1"
              fullWidth
            />
            <BooleanInput
              label="Overwrite Existing Statement(s)"
              source="overwriteExistingStatements"
              defaultValue={false}
              fullWidth
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
