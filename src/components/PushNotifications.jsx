import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Alert, Grid, Typography } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Push Notification';

export const PushNotificationList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        bulkActionButtons={false}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="title" />
        <TextField source="body" />
        <LinkField
          reference="PushNotificationToken"
          linkSource="pushNotificationToken.id"
          labelSource="pushNotificationToken.label"
          label="Token"
        />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const PushNotificationCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false} redirect="list">
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <Alert severity="warning">
            Creating a push notification will send it to the device as well.
          </Alert>
          <ReferenceInput
            source="pushNotificationToken.id"
            reference="PushNotificationToken"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <SelectInput optionText="label" label="Token" fullWidth required />
          </ReferenceInput>
          <TextInput
            source="title"
            required
            fullWidth
            helperText="If you make this too long, it will be truncated depending on the device screen size. 32 characters or less should be safe."
          />
          <TextInput source="body" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
