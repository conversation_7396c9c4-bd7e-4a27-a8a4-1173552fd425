import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>y<PERSON>ield,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceArrayInput,
  ReferenceInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import numeral from 'numeral';
import { Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import {
  CustomNumberInput,
  CustomReferenceField,
  LinkField,
} from './CustomFields';

const entityName = 'Project';

export const SalesforceProjectEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();
  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth disabled />
            <FunctionField
              label="Utility Company"
              render={(record) => {
                const projectIsDefaultForCurrentUtilityCompany =
                  String(record.utilityCompany?.defaultSalesforceProjectId) ===
                  id;
                return (
                  <ReferenceInput
                    source="utilityCompany.id"
                    reference="UtilityCompany"
                    perPage={10000}
                  >
                    <SelectInput
                      optionText="name"
                      label="Utility Company"
                      fullWidth
                      disabled={projectIsDefaultForCurrentUtilityCompany}
                      helperText={
                        projectIsDefaultForCurrentUtilityCompany
                          ? 'This project is the default project for this utility company. To change the default project, go to the Utility Company edit page.'
                          : ''
                      }
                    />
                  </ReferenceInput>
                );
              }}
            />
            <FunctionField
              label="Consortium"
              render={(record) => {
                const disabled = !!record.brConsortium?.id;
                return (
                  <ReferenceInput
                    source="brConsortium.id"
                    reference="BrConsortium"
                    perPage={10000}
                  >
                    <SelectInput
                      optionText="internalName"
                      label="Consortium"
                      fullWidth
                      disabled={disabled}
                    />
                  </ReferenceInput>
                );
              }}
            />
            <SelectInput
              source="consortiumType"
              choices={[
                {
                  id: 'Distributed generation',
                  name: 'Distributed generation',
                },
                {
                  id: 'Self-consumption',
                  name: 'Self-consumption',
                },
              ]}
              fullWidth
            />
            <BooleanInput
              source="creditManagementExternalFlg"
              fullWidth
              helperText="Are this project's offtaker(s) managed by a third party externally?"
            />
            {isIT && (
              <TextInput
                source="starkBankProjectKey"
                fullWidth
                helperText="The Project ID for the Energea API integration in it's Stark Bank workspace"
              />
            )}
            <ReferenceArrayInput
              source="projectIds"
              reference="Project"
              fullWidth
              sort={{ field: 'name', order: 'ASC' }}
              perPage={10000}
            >
              <SelectArrayInput optionText="name" fullWidth />
            </ReferenceArrayInput>
            <CustomNumberInput
              source="orderNo"
              fullWidth
              helperText="This field will determine the order that the projects are displayed on the Credit Mgmt Dashboard and the Credit Sales Dashboard"
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const SalesforceProjectFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Project Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const SalesforceProjectList = () => {
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  return (
    <List
      title={entityName}
      perPage={25}
      filters={<SalesforceProjectFilter />}
      pagination={null}
      sort={{
        field: 'orderNo',
        order: 'asc',
      }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="name" />
        <LinkField
          reference="UtilityCompany"
          linkSource="utilityCompany.id"
          labelSource="utilityCompany.name"
          label="Utility Company"
        />
        <LinkField
          reference="BrConsortium"
          linkSource="brConsortium.id"
          labelSource="brConsortium.internalName"
          label="Consortium"
        />
        <BooleanField source="creditManagementExternalFlg" />
        <TextField source="consortiumType" />
        <FunctionField
          label="Average Monthly Generation (Avg. P50)"
          render={(record) => {
            const projectSummedAvgP50 = record.projects.reduce(
              (acc, cur) => acc + parseFloat(cur.avgP50Prod || 0),
              0
            );
            return (
              <Typography variant="body2">
                {numeral(projectSummedAvgP50).format('0,0')}
              </Typography>
            );
          }}
        />
        <FunctionField
          label="Stark bank connection"
          render={(record) => {
            if (isIT) {
              return (
                <Typography variant="body2">
                  {record.starkBankProjectKey}
                </Typography>
              );
            } else {
              return (
                <Typography variant="body2">
                  {record.starkBankProjectKey ? 'Yes' : 'No'}
                </Typography>
              );
            }
          }}
        />
        <ArrayField source="projects" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="name" />
          </SingleFieldList>
        </ArrayField>
        <NumberField source="orderNo" />
      </Datagrid>
    </List>
  );
};

export const SalesforceProjectCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth required />
          <ReferenceArrayInput
            source="projectIds"
            reference="Project"
            fullWidth
            sort={{ field: 'name', order: 'ASC' }}
            perPage={10000}
          >
            <SelectArrayInput optionText="name" fullWidth required />
          </ReferenceArrayInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
