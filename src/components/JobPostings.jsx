import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>an<PERSON>ield,
  BooleanInput,
  Create,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { RichTextInput } from 'ra-input-rich-text';
import { CustomBooleanField, CustomNumberInput } from './CustomFields';

const entityName = 'Job Posting';

export const JobPostingEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="title" required fullWidth />
            <TextInput source="location" fullWidth />
            <RichTextInput source="description" required fullWidth />
            <DateInput source="deadlineDt" fullWidth />
            <ReferenceInput
              source="manager.id"
              reference="EmployeeLite"
              sort={{ field: 'firstName', order: 'ASC' }}
              perPage={10000}
            >
              <SelectInput
                label="Manager"
                required
                fullWidth
                optionText="fullName"
              />
            </ReferenceInput>
            <BooleanInput source="inactiveFlg" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const JobPostingList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="title" />
        <FunctionField
          label="Description"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <div dangerouslySetInnerHTML={{ __html: record.description }} />
            );
          }}
        />
        <DateField source="deadlineDt" />
        <BooleanField source="inactiveFlg" />
        <NumberField source="orderNo" />
      </Datagrid>
    </List>
  );
};

export const JobPostingCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="title" required fullWidth />
          <RichTextInput source="description" required fullWidth />
          <DateInput source="deadlineDt" fullWidth />
          <BooleanInput source="inactiveFlg" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
