import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>y<PERSON>ield,
  Create,
  Datagrid,
  DateField,
  Edit,
  FunctionField,
  List,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { Delete } from '@mui/icons-material';
import { Video, Image, Transformation } from 'cloudinary-react';

import { Button, Grid } from '@mui/material';
import { getEditable } from '../utils/applyRoleAuth';
import { CustomReferenceField, DetailField } from './CustomFields';
import Config from '../config/config';
import { openUploadWidget } from '../utils/CloudinaryService';

const entityName = 'Market';

export const MarketEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const { id } = useParams();

  const onPhotoUploaded = (photo) => {
    dataProvider
      .update('Market', {
        data: {
          id: parseInt(id, 10),
          bannerImageCloudinaryPublicId: photo.public_id,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading image', { type: 'error' });
      })
      .then(() => {
        notify('Image successfully uploading');
        refresh();
      });
  };

  const onVideoUploaded = (video, attr) => {
    dataProvider
      .update('Market', {
        data: {
          id: parseInt(id, 10),
          [attr]: video.public_id,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading video', { type: 'error' });
      })
      .then(() => {
        // TODO: replace page reload with useRefresh and add useNotify
        notify('Video successfully uploaded');
        refresh();
      });
  };

  const uploadMobileBannerVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        // { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.market_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideoUploaded(resp.info, 'mobileBannerVideoCloudinaryPublicId');
      }
    });
  };

  const uploadBannerVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.market_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideoUploaded(resp.info, 'bannerVideoCloudinaryPublicId');
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.market_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideoUploaded(resp.info, 'videoCloudinaryPublicId');
      }
    });
  };

  const handleAddImage = () => {
    const uploadOptions = {
      tags: ['market-banner-image'],
      multiple: false,
      resourceType: 'image',
      showPoweredBy: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.market_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotoUploaded(resp.info);
      }
    });
  };

  const handleRemoveVideo = (resource, attr) => {
    return () => {
      dataProvider
        .update('Market', {
          data: {
            id: parseInt(id, 10),
            [attr]: null,
          },
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing video', { type: 'error' });
        })
        .then(() => {
          notify('Video successfully removed');
          refresh();
        });
    };
  };

  const handleRemoveImage = () => {
    dataProvider
      .update('Market', {
        data: {
          id: parseInt(id, 10),
          bannerImageCloudinaryPublicId: null,
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error removing image', { type: 'error' });
      })
      .then(() => {
        notify('Image successfully removed');
        refresh();
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="title" fullWidth />
            <TextInput
              helperText="2-4 sentences that will be used as the subtitle on the market page."
              multiline
              source="summary"
              fullWidth
            />
            <TextInput multiline source="description" fullWidth />
          </Grid>
        </Grid>
        <FunctionField
          align="center"
          label="Sharable Text Image"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <>
                <Image
                  style={{}}
                  cloud_name={Config.cloud_name}
                  publicId={record.bannerImageCloudinaryPublicId}
                >
                  <Transformation width="200" crop="scale" />
                </Image>
                {record.bannerImageCloudinaryPublicId ? (
                  <Button onClick={handleRemoveImage}>
                    <Delete />
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={handleAddImage}
                  >
                    Add Image
                  </Button>
                )}
              </>
            );
          }}
        />
        <FunctionField
          align="center"
          label="Main Video"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record.videoCloudinaryPublicId)
              return (
                <div className="actions">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={uploadVideoWithCloudinary}
                  >
                    Add Main Learn More Video
                  </Button>
                </div>
              );
            return (
              <>
                <Video
                  cloud_name={Config.cloud_name}
                  publicId={record.videoCloudinaryPublicId}
                  muted
                  width="200"
                  crop="scale"
                  sourceTypes={['mp4']}
                  controls
                />
                <Button
                  style={{ float: 'right' }}
                  onClick={handleRemoveVideo(record, 'videoCloudinaryPublicId')}
                >
                  <Delete />
                </Button>
              </>
            );
          }}
        />
        <FunctionField
          align="center"
          label="Desktop Banner Video"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record.bannerVideoCloudinaryPublicId)
              return (
                <div className="actions">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={uploadBannerVideoWithCloudinary}
                  >
                    Add Desktop Banner Video
                  </Button>
                </div>
              );
            return (
              <>
                <Video
                  cloud_name={Config.cloud_name}
                  publicId={record.bannerVideoCloudinaryPublicId}
                  muted
                  width="200"
                  crop="scale"
                  sourceTypes={['mp4']}
                  controls
                />
                <Button
                  style={{ float: 'right' }}
                  onClick={handleRemoveVideo(
                    record,
                    'bannerVideoCloudinaryPublicId'
                  )}
                >
                  <Delete />
                </Button>
              </>
            );
          }}
        />
        <FunctionField
          align="center"
          label="Mobile Banner Video"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record.mobileBannerVideoCloudinaryPublicId)
              return (
                <div className="actions">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={uploadMobileBannerVideoWithCloudinary}
                  >
                    Add Mobile Banner Video
                  </Button>
                </div>
              );
            return (
              <>
                <Video
                  cloud_name={Config.cloud_name}
                  publicId={record.mobileBannerVideoCloudinaryPublicId}
                  muted
                  width="200"
                  crop="scale"
                  sourceTypes={['mp4']}
                  controls
                />
                <Button
                  style={{ float: 'right' }}
                  onClick={handleRemoveVideo(
                    record,
                    'mobileBannerVideoCloudinaryPublicId'
                  )}
                >
                  <Delete />
                </Button>
              </>
            );
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

export const MarketList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="title" />
        <DetailField source="summary" sortable={false} />
        <ArrayField source="portfolios" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField source="subtitle" />
          </SingleFieldList>
        </ArrayField>
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const MarketCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={8}>
          <TextInput
            source="title"
            helperText="This can be edited at any time so no need to be perfect."
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
