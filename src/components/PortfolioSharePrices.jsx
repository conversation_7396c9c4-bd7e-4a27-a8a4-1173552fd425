import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Portfolio Share Price';

export const PortfolioSharePriceEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextField source="id" />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              margin="none"
              source="portfolio.id"
              reference="PortfolioLite"
            >
              <SelectInput label="Portfolio" fullWidth optionText="name" />
            </ReferenceInput>
            <ReferenceInput
              margin="none"
              source="monthlyPortfolioFinancialActual.id"
              reference="MonthlyPortfolioFinancialActual"
            >
              <SelectInput
                label="Monthly Actual"
                fullWidth
                optionText="label"
              />
            </ReferenceInput>
            <DateTimeInput source="date" fullWidth />
            <CustomNumberInput source="sharePrice" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const PortfolioSharePricePagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

export const PortfolioSharePriceList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <List
        title="Portfolio Share Prices"
        perPage={25}
        pagination={<PortfolioSharePricePagination />}
        sort={{ field: 'id', order: 'DESC' }}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />
          <LinkField
            label="Monthly Actual"
            linkSource="monthlyPortfolioFinancialActual.id"
            labelSource="monthlyPortfolioFinancialActual.label"
            reference="MonthlyPortfolioFinancialActual"
          />
          <DateField source="date" showTime={true} />
          <NumberField
            source="sharePrice"
            options={{
              style: 'currency',
              currency: 'USD',
              maximumFractionDigits: 6,
            }}
          />
          <DateField source="createdAt" showTime={true} />
          <DateField source="updatedAt" showTime={true} />
        </Datagrid>
      </List>
    </>
  );
};

export const PortfolioSharePriceCreate = () => {
  return (
    <Create title={`Create ${entityName}`}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={8}>
            <ReferenceInput
              margin="none"
              source="portfolio.id"
              reference="PortfolioLite"
            >
              <SelectInput label="Portfolio" fullWidth optionText="name" />
            </ReferenceInput>
            <CustomNumberInput source="sharePrice" fullWidth />
            <DateTimeInput source="date" helperText="Date of new share price" />
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
