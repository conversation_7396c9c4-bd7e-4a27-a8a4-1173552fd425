import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Typography } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { Line } from 'react-chartjs-2';
import moment from 'moment';
import theme from '../theme';

const entityName = 'Energea Global Share Price';

export const EnergeaGlobalSharePriceEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <DateInput source="date" fullWidth required />
            <CustomNumberInput source="sharePrice" fullWidth required />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const EnergeaGlobalSharePriceList = () => {
  const { permissions } = usePermissions();
  const [allSharePrices, setAllSharePrices] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dataProvider = useDataProvider();

  const fetchAllSharePrices = () => {
    setLoading(true);
    dataProvider
      .getList('EnergeaGlobalSharePrice', {
        pagination: { page: 1, perPage: 100000 },
        sort: { field: 'date', order: 'ASC' },
      })
      .then(
        (res) => {
          setLoading(false);
          setError(null);
          setAllSharePrices(res.data);
        },
        (err) => {
          setLoading(false);
          setError(err);
        }
      );
  };

  if (!allSharePrices && !loading && !error) {
    fetchAllSharePrices();
  }

  return (
    <>
      {allSharePrices && allSharePrices.length && (
        <Grid container style={{ marginTop: '2rem' }}>
          <Grid item>
            <Typography variant="h6">Energea Global Share Prices</Typography>
          </Grid>
          <Grid item xs={12}>
            <Line
              height="300"
              data={{
                datasets: [
                  {
                    label: 'Energea Global Share Price',
                    data: allSharePrices,
                    borderColor: theme.palette.green.main,
                    backgroundColor: theme.palette.green.main,
                  },
                ],
              }}
              options={{
                maintainAspectRatio: false,
                parsing: {
                  xAxisKey: 'date',
                  yAxisKey: 'sharePrice',
                },
                plugins: {
                  legend: { display: false },
                  tooltip: {
                    callbacks: {
                      title: (tooltipItem) =>
                        moment(tooltipItem[0].label).format('MMM D, YYYY'),
                    },
                  },
                },
                scales: {
                  x: {
                    type: 'time',
                    grid: {
                      display: false,
                    },
                  },
                  y: {
                    beginAtZero: true,
                  },
                },
              }}
            />
          </Grid>
        </Grid>
      )}
      <List title={entityName} perPage={25}>
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <DateField source="date" />
          <NumberField source="sharePrice" />
        </Datagrid>
      </List>
    </>
  );
};

export const EnergeaGlobalSharePriceCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false} redirect="list">
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <DateInput source="date" required fullWidth />
          <CustomNumberInput source="sharePrice" required fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
