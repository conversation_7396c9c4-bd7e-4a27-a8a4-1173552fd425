import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import numeral from 'numeral';
import {
  ArrayField,
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  Labeled,
  List,
  NumberField,
  Pagination,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  UrlField,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import {
  Button,
  Collapse,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { Alert } from '@mui/lab';

// import { CustomReferenceField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import theme from '../theme';
import { CustomNumberInput, <PERSON>Field } from './CustomFields';
import { constants } from '../utils/global';
import { Doughnut } from 'react-chartjs-2';
import moment from 'moment';

const entityName = 'Sell Order';

export const SellOrderEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const cancelSellOrder = () => {
    dataProvider
      .update('SellOrder', {
        data: { id: parseInt(id, 10), action: 'cancel' },
      })
      .then(() => {
        notify('Successfully cancelled sell order.', { type: 'success' });
        refresh();
      })
      .catch((error) => {
        notify(`Error cancelling sell order. ${error}`, { type: 'error' });
      });
  };

  const cashOutSellOrder = () => {
    dataProvider
      .update('SellOrder', {
        data: { id: parseInt(id, 10), action: 'cashOut' },
      })
      .then(() => {
        notify('Successfully Cashed Out Sell Order.', { type: 'success' });
        refresh();
      })
      .catch((error) => {
        notify(`Error cashing out sell order. ${error}`, { type: 'error' });
      });
  };

  const redeemSellOrder = (manuallyCloseFlg) => {
    dataProvider
      .update('SellOrder', {
        data: {
          id: parseInt(id, 10),
          action: 'redeem',
          manuallyCloseFlg: !!manuallyCloseFlg,
        },
      })
      .then(() => {
        notify(
          manuallyCloseFlg
            ? 'Successfully redeemed sell order dwolla label balance, closed the sellOrder and emailed/notified the seller.'
            : 'Successfully redeemed sell order dwolla label balance.',
          { type: 'success' }
        );
        refresh();
      })
      .catch((error) => {
        notify(
          `Error redeeming sell order dwolla label balance. ${error}`,
          'error'
        );
      });
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm>
        <FormTab label="Details">
          <Labeled fullWidth>
            <LinkField
              reference="User"
              linkSource="user.id"
              labelSource="user.fullName"
              label="User"
            />
          </Labeled>
          <Labeled fullWidth>
            <LinkField
              label="SubAccount"
              linkSource="subAccount.id"
              labelSource="subAccount.name"
              reference="SubAccount"
              fullWidth
            />
          </Labeled>
          <Labeled fullWidth>
            <LinkField
              reference="Portfolio"
              linkSource="portfolio.id"
              labelSource="portfolio.subtitle"
              label="Portfolio"
            />
          </Labeled>
          <Labeled fullWidth>
            <NumberField source="daysInQueue" fullWidth />
          </Labeled>
          <Labeled fullWidth>
            <NumberField
              source="dwollaLabelBalance"
              options={{ style: 'currency', currency: 'USD' }}
              fullWidth
            />
          </Labeled>
          <Labeled fullWidth>
            <UrlField
              target="_blank"
              onClick={(e) => e.stopPropagation()}
              label="Dwolla Label URL"
              source="dwollaLabelUrl"
              fullWidth
            />
          </Labeled>
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <DateTimeInput source="requestDt" fullWidth />
              <Alert severity="info">
                <Typography variant="body2">
                  <b>Sold shares to date :</b>
                </Typography>
                <TextField source="soldShares" fullWidth />
                <Typography variant="body2">
                  <b>Gross shares purchased by seller :</b>
                </Typography>
                <TextField source="grossSharesOwnedBySeller" fullWidth />
              </Alert>
              <CustomNumberInput source="shares" fullWidth />
              <CustomNumberInput source="priority" fullWidth />
              <TextInput source="reason" fullWidth multiline />
              <BooleanInput
                source="inactiveFlg"
                defaultValue={false}
                helperText="When on, this pauses the sell order from selling shares"
              />
              <BooleanInput
                source="earlyExitFlg"
                defaultValue={false}
                helperText="When on, this sell order will not be in the queue for the crowd to fulfill. It is only fulfillable by Energea. If Energea decides to purchase, then they will do so at a discount."
              />
              <DateTimeInput source="cancelledDt" fullWidth />
              <DateTimeInput source="closedDt" fullWidth />
            </Grid>
          </Grid>
          <Grid
            container
            style={{
              width: '100%',
              padding: '2rem',
              backgroundColor: '#ddd',
              border: '4px solid black',
              borderRadius: '10px',
            }}
          >
            <Grid item xs={12}>
              <Typography gutterBottom variant="h5">
                Sell Order Actions
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FunctionField
                label="Redeem Sell Order Dwolla Label Balance"
                render={(record) => {
                  return (
                    <Grid item style={{ marginBottom: '2rem' }}>
                      <Button
                        onClick={() => {
                          if (
                            window.confirm(
                              `This will be done automatically once the sell order completes. Are you sure you wish to continue?`
                            )
                          ) {
                            redeemSellOrder(false);
                          }
                        }}
                        variant="contained"
                        size="large"
                        style={{
                          backgroundColor: theme.palette.error.main,
                          color: '#fff',
                        }}
                      >
                        Redeem Dwolla Label Balance
                      </Button>
                      <br />
                      <Typography variant="caption">
                        This button will move any funds from this sell orders
                        dwolla label, into their dwolla wallet making it
                        accessible to them immediately. This is done
                        automatically onces the sell order has been completed.
                      </Typography>
                    </Grid>
                  );
                }}
              />
              <FunctionField
                label="Manually Close Sell Order"
                render={(record) => {
                  const btnDisabled = !!record.cancelledDt;
                  return (
                    <Grid item style={{ marginBottom: '2rem' }}>
                      <Button
                        onClick={() => {
                          if (
                            window.confirm(
                              `This action may email the client. Are you sure you wish to continue?`
                            )
                          ) {
                            redeemSellOrder(true);
                          }
                        }}
                        variant="contained"
                        size="large"
                        style={
                          btnDisabled
                            ? {}
                            : {
                                backgroundColor: theme.palette.error.main,
                                color: '#fff',
                              }
                        }
                        disabled={btnDisabled}
                      >
                        Close Sell Order
                      </Button>
                      <br />
                      <Typography variant="caption">
                        This button will set the closedDt, un-label the funds,
                        email the seller, and create a notification for them. If
                        the closedDt is already set, then the email and
                        notifications will not go out.
                      </Typography>
                    </Grid>
                  );
                }}
              />
              <FunctionField
                label="Cancel Sell Order"
                render={(record) => {
                  const btnDisabled = !!record.cancelledDt;
                  return (
                    <Grid
                      container
                      direction="column"
                      style={{ marginBottom: '2rem' }}
                    >
                      <Grid item>
                        <Button
                          onClick={() => {
                            if (
                              window.confirm(
                                "This will stop selling shares and email the client to let them know their sell order has been cancelled. If shares have already been sold, we should use the 'Close Sell Order' button instead. Are you sure you wish to continue?"
                              )
                            ) {
                              cancelSellOrder();
                            }
                          }}
                          variant="contained"
                          size="large"
                          style={
                            btnDisabled
                              ? {}
                              : {
                                  backgroundColor: theme.palette.error.main,
                                  color: '#fff',
                                }
                          }
                          disabled={btnDisabled}
                        >
                          Cancel Sell Order
                        </Button>
                      </Grid>
                      {record.soldShares > 0 ? (
                        <Alert severity="warning" style={{ margin: '.5rem 0' }}>
                          Some of the shares in this sell order have already
                          been sold. Make sure you read all of the notes below
                          before continuing and know that all share transfers
                          will need to be manually cancelled. If this is not
                          what you intend to do, use the close action above
                          instead.
                        </Alert>
                      ) : null}
                      <Grid item>
                        <Typography variant="caption">
                          This will email the client a "Sell Order Cancelled"
                          email. If this sell order already has share transfers
                          associated with it, this must be done manually by:
                          <br />
                          1) setting the closedDt to prevent future share
                          transfers from being created,
                          <br />
                          2) updating the shares requested to sell to the
                          current sold shares value,
                          <br />
                          3) unlabeling any funds in the dwolla sell order
                          label,
                          <br />
                          4) handling or waiting for pending transfers to
                          complete.
                        </Typography>
                      </Grid>
                    </Grid>
                  );
                }}
              />
              <FunctionField
                label="Buy Out Sell Order"
                render={(record) => {
                  const allSharesPurchased =
                    Math.abs(record.shares - record.soldShares) <
                    constants.shareCountFloor;
                  const containsNewSharesCopy =
                    'WARNING: This sell order contains shares that were purchased within the last 60 days so the seller is still able to dispute any charges with the ACH network. Be cautious with which users you buyout before these 60 days due to the potential double payout a bad actor could achieve.';
                  const btnDisabled =
                    allSharesPurchased ||
                    !record?.user?.firstInvestmentDt ||
                    (moment(record.user.firstInvestmentDt).isAfter(
                      moment().subtract(60, 'days')
                    ) &&
                      record.shares > 50);
                  return (
                    <Grid container direction="column">
                      <Grid item>
                        <Button
                          onClick={() => {
                            if (
                              window.confirm(
                                `${
                                  record.includesSharesPurchasedLessThan60DaysAgo
                                    ? containsNewSharesCopy
                                    : ''
                                }
                              
Clicking 'OK' will create an $0 and 0 share investment from the Issue Buyback account and a transfer will be made from the portfolio to the seller for all remaining unsold shares in this sell order. If this sell order is flagged as an early exit, then the portfolio will buy these shares at a fixed discount. 

Are you sure you wish to continue?`
                              )
                            ) {
                              cashOutSellOrder();
                            }
                          }}
                          variant="contained"
                          size="large"
                          style={
                            btnDisabled
                              ? {}
                              : {
                                  backgroundColor: theme.palette.error.main,
                                  color: '#fff',
                                }
                          }
                          disabled={btnDisabled}
                        >
                          Execute Portfolio Buy Back
                        </Button>
                      </Grid>
                      {record.includesSharesPurchasedLessThan60DaysAgo &&
                      !allSharesPurchased ? (
                        <Grid item>
                          <Alert
                            severity="warning"
                            style={{ margin: '.5rem 0' }}
                          >
                            <Typography variant="body2">
                              {containsNewSharesCopy}
                              <br />
                              <br />
                              <b>
                                First Investment Dt:{' '}
                                {moment(record.user.firstInvestmentDt).format(
                                  'MMM D, YYYY'
                                )}
                              </b>
                            </Typography>
                          </Alert>
                        </Grid>
                      ) : null}
                      {allSharesPurchased ? (
                        <Grid item>
                          <Alert severity="info" style={{ margin: '.5rem 0' }}>
                            <Typography variant="body2">
                              All shares in this sell order have already been
                              purchased.
                            </Typography>
                          </Alert>
                        </Grid>
                      ) : null}
                      <Grid item>
                        <Typography variant="caption">
                          This button creates an transfer from the portfolio for
                          all remaining unsold shares in this sell order. If
                          this sell order is flagged as an early exit, then the
                          portfolio will buy these shares at a fixed discount
                          and they will be retired.
                        </Typography>
                      </Grid>
                    </Grid>
                  );
                }}
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Share Transfers">
          <ArrayField source="shareTransfers" label="Share Transfers">
            <Datagrid>
              <TextField source="id" />
              <DateField source="sellDt" label="Sell Date" />
              <NumberField
                source="soldShares"
                label="Sold Shares"
                options={{ minimumFractionDigits: 3, maximumFractionDigits: 3 }}
              />
              <NumberField
                source="value"
                label="Value"
                options={{ style: 'currency', currency: 'USD' }}
              />
              <NumberField
                source="exitPenaltyAmount"
                label="Exit Penalty"
                options={{ style: 'currency', currency: 'USD' }}
              />
              <BooleanField source="historicalFlg" label="Historical" />
              <BooleanField source="issuerBuybackFlg" label="Issuer Buyback" />
              <FunctionField
                label="Status"
                render={(record) => {
                  if (record.status?.completedFlg) {
                    return 'Completed';
                  }
                  return 'Pending';
                }}
              />
              <FunctionField
                label="Investment"
                render={(record) => {
                  if (!record.investment) return '-';
                  return (
                    <a href={`/Investment/${record.investment.id}`}>
                      {record.investment.label}
                    </a>
                  );
                }}
              />
              <FunctionField
                label="Transfer"
                render={(record) => {
                  if (!record.transfer) return '-';
                  return (
                    <a href={`/Transfer/${record.transfer.id}`}>
                      {record.transfer.label}
                    </a>
                  );
                }}
              />
              <DateField source="createdAt" label="Created" showTime />
            </Datagrid>
          </ArrayField>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const SellOrderFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="Portfolio"
      source="portfolio.id"
      reference="PortfolioLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Portfolio" optionText="name" />
    </ReferenceInput>
    <BooleanInput source="earlyExitFlg" alwaysOn />
    <BooleanInput source="cancelledFlg" alwaysOn />
    <BooleanInput source="closedFlg" alwaysOn />
  </Filter>
);

const styleRow = (record, index) => {
  // const { dwollaTransfers, dbAmount } = record;
  const errorStyle = {
    backgroundColor: theme.palette.error.light,
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  const successStyle = {
    backgroundColor: theme.palette.green.light,
  };
  const cancelledStyle = {
    backgroundColor: '#ddd',
    fontStyle: 'italic',
  };

  if (record.cancelledDt) return cancelledStyle;
  const completed =
    Math.abs(record.shares - record.soldShares) < constants.shareCountFloor &&
    record.investmentDollarsInTransit === 0 &&
    record.shareTransferDollarsInTransit === 0;
  if (completed && !record.closedDt) {
    return errorStyle;
  }
  if (completed) return successStyle;
  if (
    record.sellersFirstSuccessfulInvestmentDt &&
    moment(record.sellersFirstSuccessfulInvestmentDt).isAfter(
      moment().subtract(60, 'days')
    )
  ) {
    return warningStyle;
  }
  if (record.daysInQueue > 90) return warningStyle;

  return {};
};

const CustomPagination = () => <Pagination rowsPerPageOptions={[10, 25, 50]} />;

export const SellOrderList = () => {
  const [sellOrderData, setSellOrderData] = useState(null);
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  if (!sellOrderData) {
    dataProvider.getOne('SellOrderData', {}).then((res) => {
      setSellOrderData(res.data);
    });
  }

  const lintNumeralInput = (val) => {
    if (Math.abs(val) < 0.0001) return 0;
    return val;
  };

  const totalSharesForSale = sellOrderData?.allPortfolios
    .filter((portfolio) => portfolio.sharesForSale > 0)
    .reduce((acc, curr) => acc + curr.sharesForSale, 0);

  const earlyExitPenalty = 0.05;

  const totalEarlyExitSharesForSaleValue =
    sellOrderData?.allPortfolios
      .filter((portfolio) => portfolio.sharesForSale > 0)
      .reduce(
        (acc, curr) =>
          acc +
            curr.sharesForSaleEarlyExit *
              curr.latestMonthlyPortfolioFinancialActual?.sharePrice || 0,
        0
      ) *
    (1 - earlyExitPenalty);

  const totalSharesForSaleNotEarlyExitValue = sellOrderData?.allPortfolios
    .filter((portfolio) => portfolio.sharesForSale > 0)
    .reduce((acc, curr) => {
      console.log(acc, curr);
      return (
        acc +
        ((curr.sharesForSale - curr.sharesForSaleEarlyExit) *
          curr.latestMonthlyPortfolioFinancialActual?.sharePrice || 0)
      );
    }, 0);

  return (
    <>
      <Grid container style={{ width: '100%', padding: '2rem 1rem' }}>
        <Grid item xs={12}>
          <Typography variant="h5" paragraph>
            <Grid container spacing={4}>
              <Grid item>Sell Order Stats</Grid>
              <Grid item>
                <Button
                  variant="contained"
                  onClick={() =>
                    alert(
                      'Under Development : This will show retention and sell order request data over time so we can monitor trends.'
                    )
                  }
                >
                  View Trends
                </Button>
              </Grid>
            </Grid>
          </Typography>
        </Grid>
        {sellOrderData && (
          <Grid item>
            <Alert icon={false} severity="info">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Portfolio</b>
                    </TableCell>
                    <TableCell>
                      <b>Shares for sale</b>
                    </TableCell>
                    <TableCell>
                      <b>Value of Early Exit Shares for Sale</b>
                    </TableCell>
                    <TableCell>
                      <b>Value of Non-Early Exit Shares for Sale</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sellOrderData && sellOrderData.allPortfolios
                    ? sellOrderData.allPortfolios
                        .filter((portfolio) => portfolio.sharesForSale > 0)
                        .map((portfolio) => {
                          const sharesForSale = portfolio.sharesForSale;
                          const sharesForSaleEarlyExit =
                            portfolio.sharesForSaleEarlyExit;
                          const sharesForSaleNotEarlyExit =
                            sharesForSale - sharesForSaleEarlyExit;
                          const sharePrice =
                            portfolio.latestMonthlyPortfolioFinancialActual
                              .sharePrice;

                          return (
                            <TableRow
                              key={`sell-order-summary-row-${portfolio.id}`}
                            >
                              <TableCell>
                                <b>{portfolio.subtitle}</b>
                              </TableCell>
                              <TableCell align="right">{`${numeral(
                                lintNumeralInput(sharesForSale)
                              ).format('0,0.000')}`}</TableCell>
                              <TableCell align="right">{`${numeral(
                                lintNumeralInput(
                                  sharesForSaleEarlyExit *
                                    sharePrice *
                                    (1 - earlyExitPenalty)
                                )
                              ).format('$0,0.00')}`}</TableCell>
                              <TableCell align="right">{`${numeral(
                                lintNumeralInput(
                                  sharesForSaleNotEarlyExit * sharePrice
                                )
                              ).format('$0,0.00')}`}</TableCell>
                            </TableRow>
                          );
                        })
                    : null}
                  <TableRow>
                    <TableCell
                      style={{
                        borderBottom: 'none',
                        borderTop: '2px solid black',
                      }}
                    >
                      <b>Totals</b>
                    </TableCell>
                    <TableCell
                      style={{
                        borderBottom: 'none',
                        borderTop: '2px solid black',
                      }}
                      align="right"
                    >{`${numeral(lintNumeralInput(totalSharesForSale)).format(
                      '0,0.000'
                    )}`}</TableCell>
                    <TableCell
                      style={{
                        borderBottom: 'none',
                        borderTop: '2px solid black',
                      }}
                      align="right"
                    >{`${numeral(
                      lintNumeralInput(totalEarlyExitSharesForSaleValue)
                    ).format('$0,0.00')}`}</TableCell>
                    <TableCell
                      style={{
                        borderBottom: 'none',
                        borderTop: '2px solid black',
                      }}
                      align="right"
                    >{`${numeral(
                      lintNumeralInput(totalSharesForSaleNotEarlyExitValue)
                    ).format('$0,0.00')}`}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </Alert>
          </Grid>
        )}
        <List
          title={entityName}
          sort={{ field: 'id', order: 'DESC' }}
          filters={<SellOrderFilter />}
          filterDefaultValues={{
            earlyExitFlg: true,
            cancelledFlg: false,
            closedFlg: false,
          }}
          perPage={25}
          pagination={<CustomPagination />}
          sx={{
            '& .RaList-content': {
              marginTop: '.5rem',
            },
          }}
        >
          <Datagrid
            rowStyle={styleRow}
            rowClick={
              getEditable(useResourceDefinition().name, permissions)
                ? 'edit'
                : 'show'
            }
          >
            <NumberField source="id" />
            <LinkField
              reference="User"
              linkSource="user.id"
              labelSource="user.fullName"
              label="User"
            />
            <LinkField
              label="SubAccount"
              linkSource="subAccount.id"
              labelSource="subAccount.name"
              reference="SubAccount"
            />
            <LinkField
              reference="Portfolio"
              linkSource="portfolio.id"
              labelSource="portfolio.subtitle"
              label="Portfolio"
            />
            <FunctionField
              align="center"
              label="Progress"
              style={{ width: '100%' }}
              render={(record) => {
                return (
                  <div style={{ width: '4em' }}>
                    <Doughnut
                      data={{
                        labels: ['Sold', 'Unsold'],
                        datasets: [
                          {
                            data: [
                              record.soldShares,
                              record.shares - record.soldShares,
                            ],
                            backgroundColor: ['green', 'grey'],
                          },
                        ],
                      }}
                      options={{
                        animation: {
                          duration: 0,
                        },
                        plugins: {
                          legend: { display: false },
                          tooltip: { enabled: false },
                        },
                      }}
                    />
                  </div>
                );
              }}
            />
            <NumberField label="Total shares" source="shares" />
            <FunctionField
              label="Sold shares (value $)"
              render={(record) => {
                return (
                  <Grid container direction="column">
                    <Grid item>
                      <Typography variant="body2">
                        {numeral(lintNumeralInput(record.soldShares)).format(
                          '0,0.000'
                        )}
                      </Typography>
                    </Grid>
                    <Grid item>
                      <Typography variant="body2">
                        (
                        {numeral(lintNumeralInput(record.soldValue)).format(
                          '$0,0.00'
                        )}
                        )
                      </Typography>
                    </Grid>
                  </Grid>
                );
              }}
            />
            <FunctionField
              label="Unsold shares (current value $)"
              render={(record) => {
                const sharesForSale = record.shares - record.soldShares;
                return (
                  <Grid container direction="column">
                    <Grid item>
                      <Typography variant="body2">
                        {numeral(lintNumeralInput(sharesForSale)).format(
                          '0,0.000'
                        )}
                      </Typography>
                    </Grid>
                    <Grid item>
                      <Typography variant="body2">
                        (
                        {numeral(
                          lintNumeralInput(
                            sharesForSale *
                              record.portfolio
                                .latestMonthlyPortfolioFinancialActual
                                ?.sharePrice
                          )
                        ).format('$0,0.00')}
                        )
                      </Typography>
                    </Grid>
                  </Grid>
                );
              }}
            />
            <NumberField
              label="Total dollars from sale of shares"
              source="soldValue"
              options={{ style: 'currency', currency: 'USD' }}
              sortable={false}
            />
            <NumberField
              label="Investment dollars in transit to portfolio"
              source="investmentDollarsInTransit"
              options={{ style: 'currency', currency: 'USD' }}
              sortable={false}
            />
            <NumberField
              label="Share transfer dollars in transit to client wallet"
              source="shareTransferDollarsInTransit"
              options={{ style: 'currency', currency: 'USD' }}
              sortable={false}
            />
            <NumberField
              label="Dollars on hold in client wallet"
              source="dwollaLabelBalance"
              options={{ style: 'currency', currency: 'USD' }}
              sortable={false}
            />
            <FunctionField
              label="Dollars redeemed to client wallet"
              render={(record) => {
                const value =
                  record.soldValue -
                  (record.dwollaLabelBalance +
                    record.shareTransferDollarsInTransit +
                    record.investmentDollarsInTransit);
                return numeral(lintNumeralInput(value)).format('$0,0.00');
              }}
            />
            <NumberField source="priority" />
            <BooleanField source="earlyExitFlg" />
            <DateField source="sellersFirstSuccessfulInvestmentDt" />
            <BooleanField source="inactiveFlg" />
            <NumberField source="daysInQueue" />
            <DateField source="requestDt" />
            <DateField source="cancelledDt" />
            <DateField source="closedDt" />
            {/* <ArrayField source="shareTransfers" sortable={false}>
        <SingleFieldList>
          <CustomReferenceField
            color={(record) => {
              return record.status && record.status.completedFlg
                ? 'primary'
                : 'default';
            }}
            source="label"
          />
        </SingleFieldList>
      </ArrayField> */}
            <TextField source="reason" />
            <UrlField
              target="_blank"
              onClick={(e) => e.stopPropagation()}
              sortable={false}
              label="Dwolla Label URL"
              source="dwollaLabelUrl"
            />
            <DateField source="updatedAt" showTime={true} />
            <DateField source="createdAt" showTime={true} />
          </Datagrid>
        </List>
      </Grid>
    </>
  );
};

export const SellOrderCreate = () => {
  const [userPortfolioSharesOwned, setUserPortfolioSharesOwned] =
    useState(null);
  const [giftFlg, setGiftFlg] = useState(false);
  const dataProvider = useDataProvider();

  return (
    <Create
      title={`Create ${entityName}`}
      helperText="This can be edited at any time so no need to be perfect."
    >
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              margin="none"
              source="user.id"
              reference="UserLite"
              perPage={20_000}
              onChange={() => setUserPortfolioSharesOwned(null)}
            >
              <AutocompleteInput
                label="Seller"
                fullWidth
                required
                allowEmpty={true}
                optionText="label"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (!formData.user) {
                  return null;
                }
                return (
                  <Collapse in={formData.user && formData.user.id}>
                    <ReferenceInput
                      source="subAccount.id"
                      reference="SubAccountLite"
                      sort={{ field: 'id', order: 'ASC' }}
                      perPage={10000}
                      filter={{ userId: formData.user.id }}
                      onChange={() => setUserPortfolioSharesOwned(null)}
                    >
                      <SelectInput
                        label="SubAccount (optional)"
                        fullWidth
                        optionText="name"
                      />
                    </ReferenceInput>
                  </Collapse>
                );
              }}
            </FormDataConsumer>
            <ReferenceInput
              margin="none"
              source="portfolio.id"
              reference="PortfolioLite"
              onChange={() => setUserPortfolioSharesOwned(null)}
            >
              <SelectInput
                label="Portfolio"
                fullWidth
                required
                optionText="subtitle"
              />
            </ReferenceInput>
            <BooleanInput
              source="giftFlg"
              defaultValue={false}
              helperText="When on, this creates the sellOrder as a gift."
              onChange={(event) => {
                setGiftFlg(event.target.checked);
              }}
            />
            <Collapse in={giftFlg}>
              <ReferenceInput
                margin="none"
                source="recipient.id"
                reference="UserLite"
                perPage={20000}
                // onChange={() => setUserPortfolioSharesOwned(null)}
              >
                <AutocompleteInput
                  label="Recipient"
                  fullWidth
                  required
                  allowEmpty={true}
                  optionText="label"
                  helperText="The user that will receive the gift."
                  shouldRenderSuggestions={(value) => value.trim().length > 0}
                />
              </ReferenceInput>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (!formData.recipient) {
                    return null;
                  }
                  return (
                    <Collapse in={formData.recipient && formData.recipient.id}>
                      <ReferenceInput
                        source="recipientSubAccount.id"
                        reference="SubAccountLite"
                        sort={{ field: 'id', order: 'ASC' }}
                        perPage={10000}
                        filter={{ userId: formData.recipient.id }}
                        onChange={() => setUserPortfolioSharesOwned(null)}
                      >
                        <SelectInput
                          label="Recipient SubAccount (optional)"
                          fullWidth
                          optionText="name"
                        />
                      </ReferenceInput>
                    </Collapse>
                  );
                }}
              </FormDataConsumer>
            </Collapse>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (
                  formData.user &&
                  formData.user.id &&
                  formData.portfolio &&
                  formData.portfolio.id &&
                  userPortfolioSharesOwned === null
                ) {
                  dataProvider
                    .getOne('UserPortfolioEquityBreakdown', {
                      data: {
                        userId: formData.user.id,
                        portfolioId: formData.portfolio.id,
                        subAccountId: formData.subAccount?.id,
                      },
                    })
                    .then((res) => {
                      setUserPortfolioSharesOwned(res.data);
                    });
                }
                if (userPortfolioSharesOwned) {
                  return (
                    <>
                      <CustomNumberInput
                        required
                        source="shares"
                        fullWidth
                        helperText={`Shares available to sell: ${userPortfolioSharesOwned.sharesAvailableForSale}. Shares pending sale: ${userPortfolioSharesOwned.sharesPendingSale}`}
                      />
                      <DateTimeInput source="requestDt" fullWidth />
                    </>
                  );
                }
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
