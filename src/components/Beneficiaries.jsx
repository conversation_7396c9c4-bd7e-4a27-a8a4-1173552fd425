import React from 'react';
import { useParams } from 'react-router-dom';

import {
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import {
  CustomBooleanField,
  CustomNumberInput,
  LinkField,
} from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Beneficiary';

export const BeneficiaryEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="user.id"
              reference="UserLite"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="fullName"
                label="Account holder"
                fullWidth
              />
            </ReferenceInput>
            <ReferenceInput
              source="type.id"
              reference="BeneficiaryType"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Beneficiary type"
                fullWidth
              />
            </ReferenceInput>
            <TextInput source="firstName" fullWidth />
            <TextInput source="lastName" fullWidth />
            <TextInput source="trustName" fullWidth />
            <TextInput source="entityName" fullWidth />
            <TextInput source="email" fullWidth />
            <TextInput source="phone" fullWidth />
            <DateInput source="dateOfBirth" fullWidth />
            <DateInput source="formationDt" fullWidth />
            <BooleanInput source="primaryFlg" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BeneficiaryList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="UserLite"
          linkSource="user.id"
          labelSource="user.fullName"
          label="Account holder"
        />
        <LinkField
          reference="BeneficiaryType"
          linkSource="type.id"
          labelSource="type.name"
          label="Beneficiary type"
        />
        <TextField source="firstName" />
        <TextField source="lastName" />
        <TextField source="trustName" />
        <TextField source="entityName" />
        <TextField source="email" />
        <TextField source="phone" />
        <DateField source="dateOfBirth" />
        <DateField source="formationDt" />
        <CustomBooleanField source="primaryFlg" />
      </Datagrid>
    </List>
  );
};

export const BeneficiaryCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="user.id"
            reference="UserLite"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="fullName"
              label="Account holder"
              fullWidth
              required
            />
          </ReferenceInput>
          <ReferenceInput
            source="type.id"
            reference="BeneficiaryType"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Beneficiary type"
              fullWidth
              required
            />
          </ReferenceInput>
          <TextInput source="firstName" fullWidth />
          <TextInput source="lastName" fullWidth />
          <TextInput source="trustName" fullWidth />
          <TextInput source="entityName" fullWidth />
          <TextInput source="email" required fullWidth />
          <TextInput source="phone" required fullWidth />
          <DateInput source="dateOfBirth" fullWidth />
          <DateInput source="formationDt" fullWidth />
          <BooleanInput source="primaryFlg" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
