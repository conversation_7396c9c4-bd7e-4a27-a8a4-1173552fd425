import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  Edit,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Alert, Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Communication Group';

export const CommunicationGroupEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <TextField source="id" />
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput fullWidth source="name" />
            <TextInput fullWidth source="description" />
            <CustomNumberInput fullWidth source="orderNo" />
          </Grid>
        </Grid>
        {/* <FunctionField
        label="Num. of Users"
        render={(record) => {
          return (
            <>
              <Typography>{record.users.length} users in this group</Typography>
            </>
          );
        }}
      /> */}
      </SimpleForm>
    </Edit>
  );
};

export const CommunicationGroupList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Alert severity="warning">
        After creating a new Communication Group, you must create the backend
        logic by editing the "getUsersByCommunicationGroupId" function in
        /server/schema/resolvers/event.js
      </Alert>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <NumberField source="orderNo" />
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const CommunicationGroupCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
