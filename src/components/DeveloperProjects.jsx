import React from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  Edit,
  FunctionField,
  List,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid, Tooltip, Typography } from '@mui/material';
import {
  Check,
  CloudDownload,
  Email,
  Event,
  VisibilityOff,
} from '@mui/icons-material';

import { Alert } from '@mui/lab';
import moment from 'moment';

import { getEditable } from '../utils/applyRoleAuth';
import theme from '../theme';

const entityName = 'Project Proposal';

export const DeveloperProjectEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6">Basic Info:</Typography>
            <TextInput source="businessName" fullWidth />
            <TextInput source="location" fullWidth />
            <TextInput source="businessDescription" fullWidth multiline />
            <TextInput source="additionalNotes" fullWidth multiline />
            <Typography variant="h6">Contact Info:</Typography>
            <TextInput source="contactName" fullWidth />
            <TextInput source="email" fullWidth />
            <TextInput source="phone" fullWidth />
            <Typography variant="h6">Documents:</Typography>
            <FunctionField
              label="Documents"
              render={(record) => (
                <Grid container direction="column" spacing={2}>
                  {record.developerProjectDocuments &&
                  record.developerProjectDocuments.length > 0 ? (
                    record.developerProjectDocuments.map((doc) => (
                      <Grid item>
                        <Button
                          disabled={!doc.downloadUrl}
                          variant="contained"
                          startIcon={<CloudDownload />}
                          style={{ textTransform: 'none' }}
                          onClick={() =>
                            window.location.assign(doc.downloadUrl)
                          }
                        >
                          {doc.type}
                        </Button>
                      </Grid>
                    ))
                  ) : (
                    <Alert severity="info" style={{ marginTop: '1rem' }}>
                      No documents
                    </Alert>
                  )}
                </Grid>
              )}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const DeveloperProjectList = () => {
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const handleShowInterest = (id) => {
    dataProvider
      .update('DeveloperProject', {
        id,
        showInterest: true,
      })
      .then(
        () => {
          notify('Emailed contact', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error showing interest', { type: 'error' });
        }
      );
  };

  const handleArchive = (id) => {
    dataProvider
      .update('DeveloperProject', {
        data: { id, archivedDt: new Date() },
      })
      .then(
        () => {
          notify('Proposal archived', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error archiving proposal', { type: 'error' });
        }
      );
  };

  const handleReject = (id) => {
    dataProvider
      .update('DeveloperProject', {
        id,
        reject: true,
      })
      .then(
        () => {
          notify('Emailed contact', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error rejecting proposal', { type: 'error' });
        }
      );
  };

  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
        bulkActionButtons={false}
      >
        <TextField source="id" />
        <TextField source="businessName" />
        <TextField source="contactName" />
        <FunctionField
          label="Email"
          render={(record) => (
            <Typography
              variant="body2"
              onClick={(event) => {
                event.stopPropagation();
              }}
            >
              <a href={`mailto:${record.email}`}>{record.email}</a>
            </Typography>
          )}
        />
        <TextField source="phone" />
        <TextField source="businessDescription" sortable={false} />
        <TextField source="additionalNotes" sortable={false} />
        <TextField source="location" />
        <DateField source="createdAt" />
        <FunctionField
          render={(record) => (
            <Tooltip title="This will automatically send an email to the developer with a link to book a meeting.">
              <Button
                variant="contained"
                onClick={(event) => {
                  event.stopPropagation();
                  handleShowInterest(record.id);
                }}
                style={{
                  textTransform: 'none',
                  whiteSpace: record.interestDt ? undefined : 'nowrap',
                }}
                disabled={record.interestDt}
                startIcon={record.interestDt ? <Check /> : <Event />}
              >
                Book a Meeting{' '}
                {record.interestDt
                  ? `Contacted: ${moment(record.interestDt).format(
                      'MM/DD/YYYY'
                    )}`
                  : ''}
              </Button>
            </Tooltip>
          )}
        />
        <FunctionField
          render={(record) => (
            <Tooltip title="This will automatically send a 'not interested' email to the developer and close out the proposal process.">
              <Button
                variant="contained"
                onClick={(event) => {
                  event.stopPropagation();
                  handleReject(record.id);
                }}
                style={{ textTransform: 'none', whiteSpace: 'nowrap' }}
                startIcon={<Email />}
              >
                Not Interested
              </Button>
            </Tooltip>
          )}
        />
        <FunctionField
          render={(record) => (
            <Tooltip title="This will archive the proposal but not email them.">
              <Button
                variant="contained"
                onClick={(event) => {
                  event.stopPropagation();
                  handleArchive(record.id);
                }}
                startIcon={<VisibilityOff />}
                style={{
                  textTransform: 'none',
                  backgroundColor: theme.palette.error.main,
                  whiteSpace: 'nowrap',
                }}
              >
                Archive
              </Button>
            </Tooltip>
          )}
        />
      </Datagrid>
    </List>
  );
};

export const DeveloperProjectCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="businessName" required fullWidth />
          <TextInput source="businessDescription" required fullWidth />
          <TextInput source="contactName" required fullWidth />
          <TextInput source="email" required fullWidth />
          <TextInput source="phone" required fullWidth />
          <TextInput source="location" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
