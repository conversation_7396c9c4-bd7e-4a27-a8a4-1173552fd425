import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  List,
  SimpleForm,
  TextField,
  TextInput,
  NumberField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput } from './CustomFields';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Share Class';

export const ShareClassEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <TextInput source="description" fullWidth />
            <CustomNumberInput
              source="frontEndLoadRate"
              label="Front End Load Rate (%)"
              fullWidth
              helperText="Front end load rate for this share class (e.g., 5 for 5%)"
              step={0.01}
              min={0}
              max={100}
            />
            <CustomNumberInput
              source="trailFeeRate"
              label="Trail Fee Rate (%)"
              fullWidth
              helperText="Trail fee rate for this share class (e.g., 5 for 5%)"
              step={0.01}
              min={0}
              max={100}
            />
            <CustomNumberInput
              source="investmentMinimum"
              label="Investment Minimum"
              fullWidth
              helperText="Minimum investment amount for this share class"
              step={1}
              min={0}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const ShareClassList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <NumberField source="frontEndLoadRate" />
        <NumberField source="trailFeeRate" />
        <NumberField source="investmentMinimum" />
      </Datagrid>
    </List>
  );
};

export const ShareClassCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <TextInput source="description" required fullWidth />
          {/* <ReferenceInput
            source="example.id"
            reference="Example"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Example"
              fullWidth
              allowEmpty
            />
          </ReferenceInput> */}
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
