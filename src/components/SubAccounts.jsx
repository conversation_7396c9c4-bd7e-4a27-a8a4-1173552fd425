import React, { useState } from 'react';
import numeral from 'numeral';
import moment from 'moment';
import {
  ArrayField,
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  Labeled,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';
import { RichTextInput } from 'ra-input-rich-text';
import { useParams } from 'react-router-dom';
import {
  Backdrop,
  Button,
  Card,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Divider,
  FormControlLabel,
  Grid,
  Link,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import MuiTextField from '@mui/material/TextField';
import MuiList from '@mui/material/List';

import { Alert, AlertTitle } from '@mui/lab';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomReferenceField, LinkField } from './CustomFields';

const entityName = 'Sub Accounts';

export const SubAccountEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const [uploadPaymentMethodDialogOpen, setUploadPaymentMethodDialogOpen] =
    useState(false);
  const [dwollaFundingSourceId, setDwollaFundingSourceId] = useState(null);
  const [transferEversignDocumentOpen, setTransferEversignDocumentOpen] =
    useState(false);
  const [transferEversignDocumentLoaded, setTransferEversignDocumentLoaded] =
    useState(false);
  const [
    entrustInKindTransferEversignDocumentOpen,
    setEntrustInKindTransferEversignDocumentOpen,
  ] = useState(false);
  const [
    entrustInKindTransferEversignDocumentLoaded,
    setEntrustInKindTransferEversignDocumentLoaded,
  ] = useState(false);
  const [rolloverEversignDocumentOpen, setRolloverEversignDocumentOpen] =
    useState(false);
  const [rolloverEversignDocumentLoaded, setRolloverEversignDocumentLoaded] =
    useState(false);
  const [sourceSubAccountId, setSourceSubAccountId] = useState(null);
  const [accountBalance, setAccountBalance] = useState(null);
  const [accountBalanceLoading, setAccountBalanceLoading] = useState(false);
  const [accountCloseLoading, setAccountCloseLoading] = useState(false);
  const [completeInKindDialogOpen, setCompleteInKindDialogOpen] =
    useState(false);
  const [
    completeInKindDestinationSubAccountId,
    setCompleteInKindDestinationSubAccountId,
  ] = useState(null);

  const { id } = useParams();
  const handleUploadMillenniumTrustPaymentMethod = () => {
    dataProvider
      .create('MillenniumTrustPaymentMethod', {
        data: {
          subAccountId: parseInt(id, 10),
          dwollaFundingSourceId,
        },
      })
      .then(
        (res) => {
          setUploadPaymentMethodDialogOpen(false);
          notify('Millennium Trust payment method uploaded', {
            type: 'success',
          });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('There was an error uploading payment method', {
            type: 'error',
          });
        }
      );
  };

  const handleIRAtoIRATransferForm = () => {
    dataProvider
      .create('MillenniumTrustIRAtoIRATransferForm', {
        data: {
          subAccountId: parseInt(id, 10),
        },
      })
      .then(
        (returnObj) => {
          if (!returnObj.data || !returnObj.data.url) {
            setTransferEversignDocumentOpen(false);
            return;
          }

          // TODO: Creating MT funding session maybe?
          const { eversignDocumentId } = returnObj.data;

          eversign.open({
            url: returnObj.data.url,
            containerID: 'eversign-container-ira-to-ira-transfer',
            width: '100%',
            height: '100%',
            events: {
              loaded: () => {
                setTransferEversignDocumentLoaded(true);
              },
              signed: () => {
                notify('Document created and emailed to investor', {
                  type: 'success',
                });
                setTransferEversignDocumentLoaded(true);
                setTransferEversignDocumentOpen(false);

                dataProvider.create('MillenniumTrustFundingSession', {
                  data: {
                    subAccountId: parseInt(id, 10),
                    offlineTransferDocumentEversignId: eversignDocumentId,
                    fundingType: 'offline ira to ira transfer',
                    transferStatus: 'Offline',
                  },
                });
              },
              declined: () => {},
              error: () => {},
            },
          });
        },
        (e) => {
          setTransferEversignDocumentLoaded(true);
          setTransferEversignDocumentOpen(false);
          console.error('Error drafting MT IRA to IRA transfer form', e);
          notify(
            'Apologies! There was an error drafting transfer form. Please try again later.',
            'error'
          );
        }
      );
  };

  const handleEntrustInKindTransferForm = () => {
    dataProvider
      .create('MillenniumTrustEntrustInKindTransferForm', {
        data: {
          destinationSubAccountId: parseInt(id, 10),
          sourceSubAccountId: sourceSubAccountId
            ? parseInt(sourceSubAccountId, 10)
            : null,
        },
      })
      .then(
        (returnObj) => {
          if (!returnObj.data || !returnObj.data.url) {
            setTransferEversignDocumentOpen(false);
            return;
          }

          // TODO: Creating MT funding session maybe?
          const { eversignDocumentId } = returnObj.data;

          eversign.open({
            url: returnObj.data.url,
            containerID: 'eversign-container-ira-to-ira-transfer',
            width: '100%',
            height: '100%',
            events: {
              loaded: () => {
                setTransferEversignDocumentLoaded(true);
              },
              signed: () => {
                notify('Document created and emailed to investor', {
                  type: 'success',
                });
                setTransferEversignDocumentLoaded(true);
                setTransferEversignDocumentOpen(false);

                dataProvider.create('MillenniumTrustFundingSession', {
                  data: {
                    subAccountId: parseInt(id, 10),
                    offlineTransferDocumentEversignId: eversignDocumentId,
                    fundingType: 'offline ira to ira transfer',
                    transferStatus: 'Offline',
                  },
                });
              },
              declined: () => {},
              error: () => {},
            },
          });
        },
        (e) => {
          setTransferEversignDocumentLoaded(true);
          setTransferEversignDocumentOpen(false);
          console.error('Error drafting MT IRA to IRA transfer form', e);
          notify(
            'Apologies! There was an error drafting transfer form. Please try again later.',
            'error'
          );
        }
      );
  };

  const handleRolloverForm = () => {
    dataProvider
      .create('MillenniumTrustQualifiedRetirementRolloverForm', {
        data: {
          subAccountId: parseInt(id, 10),
        },
      })
      .then(
        (returnObj) => {
          if (!returnObj.data || !returnObj.data.url) {
            setRolloverEversignDocumentOpen(false);
            return;
          }

          // TODO: Creating MT funding session maybe?
          const { eversignDocumentId } = returnObj.data;

          eversign.open({
            url: returnObj.data.url,
            containerID: 'eversign-container-rollover-form',
            width: '100%',
            height: '100%',
            events: {
              loaded: () => {
                setRolloverEversignDocumentLoaded(true);
              },
              signed: () => {
                notify('Document created and emailed to investor', {
                  type: 'success',
                });
                setRolloverEversignDocumentLoaded(true);
                setRolloverEversignDocumentOpen(false);

                dataProvider.create('MillenniumTrustFundingSession', {
                  data: {
                    subAccountId: parseInt(id, 10),
                    offlineTransferDocumentEversignId: eversignDocumentId,
                    fundingType: 'qualified retirement plan rollover',
                    transferStatus: 'Offline',
                  },
                });
              },
              declined: () => {},
              error: () => {},
            },
          });
        },
        (e) => {
          setRolloverEversignDocumentLoaded(true);
          setRolloverEversignDocumentOpen(false);
          console.error('Error drafting MT rollover form', e);
          notify(
            'Apologies! There was an error drafting rollover form. Please try again later.',
            'error'
          );
        }
      );
  };

  const submitMTCAccountCloseRequest = () => {
    setAccountCloseLoading(true);
    dataProvider
      .update('MillenniumTrustRequestAccountClose', {
        id: parseInt(id, 10),
      })
      .then(
        (res) => {
          setAccountCloseLoading(false);
          notify('Request sent to MTC', { type: 'success' });
          refresh();
        },
        (err) => {
          setAccountCloseLoading(false);
          console.error(err);
          notify(
            'There was an error requesting to close this account. Please try again later.',
            'error'
          );
        }
      );
  };

  const handleCompleteInKindTransfer = () => {
    dataProvider
      .update('CompleteInKindTransfer', {
        data: {
          sourceSubAccountId: parseInt(id, 10),
          destinationSubAccountId: parseInt(
            completeInKindDestinationSubAccountId,
            10
          ),
        },
      })
      .then(
        (res) => {
          notify('Successfully complete in-kind transfer');
          setCompleteInKindDialogOpen(false);
        },
        (e) => {
          console.error(e);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          notify(
            `There was an error completing in-kind transfer. Error: ${errorMsg}`,
            'error'
          );
        }
      );
  };

  const renderEntrustInKindTransferSection = (formData) => {
    if (
      formData.sourceSubAccountId &&
      formData.sourceSubAccountId !== sourceSubAccountId
    ) {
      setSourceSubAccountId(formData.sourceSubAccountId);
    }

    return (
      <Grid item xs={12}>
        <Typography gutterBottom>
          The below buttons will open a new 'Entrust In Kind IRA to IRA Transfer
          Form'. <b>Fill out sections A and D.</b>
        </Typography>
        <Typography gutterBottom>
          Once you click finish, you will receive a filled out form via email to
          mail or email to the client for a wet signature.{' '}
          <b>Investor only needs to fill out section E.</b>
        </Typography>
        <Typography gutterBottom style={{ fontWeight: 'bold' }}>
          Make sure that the Dwolla Label balance for the Entrust subaccount is
          registered with Entrust so it can be transferred over to MTC legally.
        </Typography>
        <SelectInput
          source="sourceSubAccountId"
          choices={
            (formData.user &&
              formData.user.subAccounts &&
              formData.user.subAccounts
                .filter(
                  (s) => s.id !== parseInt(id, 10) && s.subAccountType?.id === 1
                )
                .map((s) => ({
                  id: s.id,
                  name: s.name,
                }))) ||
            []
          }
          fullWidth
          helperText={
            'Select the sub account we are transferring from in-kind.'
          }
        />
        <Button
          onClick={() => {
            setEntrustInKindTransferEversignDocumentOpen(true);
          }}
          color="primary"
          variant="contained"
          fullWidth
          disabled={!sourceSubAccountId}
        >
          Start Entrust In-kind transfer form
        </Button>
      </Grid>
    );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm margin="none">
        <FormTab margin="none" label="summary">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <Grid item xs={12} md={6}>
                <Labeled fullWidth>
                  <LinkField
                    label="User"
                    linkSource="user.id"
                    labelSource="user.fullName"
                    reference="User"
                  />
                </Labeled>
              </Grid>
              <Grid item xs={12} md={6}>
                <Labeled fullWidth>
                  <LinkField
                    label="Sub Account Type"
                    linkSource="subAccountType.id"
                    labelSource="subAccountType.name"
                    reference="SubAccountType"
                  />
                </Labeled>
              </Grid>
              <TextInput source="name" disabled fullWidth />
              <TextInput source="accountId" label="Account ID" fullWidth />
              {/* <ReferenceInput
                source="subAccountType.id"
                reference="SubAccountType"
              >
                <SelectInput
                  label="Sub Account Type"
                  required
                  // disabled NOTE: Causes error: Cannot update a component (`FormTabHeader`) while rendering a different component (`SelectInput`). To locate the bad setState() call inside `SelectInput`
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput> */}
              {/* <ReferenceInput
                perPage={10000}
                source="user.id"
                reference="UserLite"
              >
                <AutocompleteInput
                  label="User"
                  required
                  // disabled NOTE: Causes error: Cannot update a component (`FormTabHeader`) while rendering a different component (`AutocompleteInput`). To locate the bad setState() call inside `AutocompleteInput`
                  fullWidth
                  optionText="fullName"
                  shouldRenderSuggestions={(value) => value.trim().length > 0}
                />
              </ReferenceInput> */}
              <SelectInput
                source="iraType"
                required
                fullWidth
                choices={[
                  { id: 'roth', name: 'roth' },
                  { id: 'traditional', name: 'traditional' },
                ]}
              />
              <TextInput source="dwollaLabelId" fullWidth />
              {/* <BooleanInput
                source="understandsSubAccountFlg"
                // disabled NOTE: For some reason, this input being disabled caused 'Maximum update depth exceeded.' errors locally. Removing disabled prop fixes
                fullWidth
              /> */}
              <DateInput
                source="custodianCloseAcctRequestDt"
                fullWidth
                helperText="This is typically set by clicking the 'Request Account Close' button below which emails MTC"
              />
              <FormDataConsumer>
                {({ formData }) => (
                  <DateInput
                    disabled={formData.nav > 0}
                    source="closedDt"
                    helperText={`Before setting the closedDt, make sure it doesn't own any shares, have recurring investments set up, or have any pending transfers. Setting this will hide the subaccount from the investor. Current NAV: ${formData.nav}`}
                  />
                )}
              </FormDataConsumer>
              <FormDataConsumer>
                {({ formData }) => {
                  if (
                    formData.subAccountType &&
                    parseInt(formData.subAccountType.id, 10) === 37
                  ) {
                    return (
                      <>
                        <Grid
                          container
                          component={Card}
                          elevation={0}
                          style={{
                            padding: '1rem',
                            marginBottom: '1rem',
                            backgroundColor: '#eee',
                          }}
                        >
                          <Grid item>
                            <Typography style={{ fontWeight: 'bold' }}>
                              Millennium Trust account details
                            </Typography>
                          </Grid>
                          <TextInput
                            fullWidth
                            label="Account ID"
                            disabled
                            source="millenniumTrustAccountDetails.id"
                          />
                          <TextInput
                            fullWidth
                            label="FBO"
                            disabled
                            source="millenniumTrustAccountDetails.fbo"
                          />
                          <TextInput
                            fullWidth
                            label="Account # last 4"
                            disabled
                            source="millenniumTrustAccountDetails.last4"
                          />
                          <TextInput
                            fullWidth
                            label="Account type"
                            disabled
                            source="millenniumTrustAccountDetails.accountType"
                          />
                          <TextInput
                            fullWidth
                            label="Account status"
                            disabled
                            source="millenniumTrustAccountDetails.status"
                          />
                          <TextInput
                            fullWidth
                            label="Owner ID"
                            disabled
                            source="millenniumTrustAccountDetails.ownerId"
                          />
                          <Grid item xs={12}>
                            <Alert severity="info">
                              <Grid container direction="column">
                                <Grid item>
                                  <Typography variant="body1">
                                    <b>
                                      Account Balance shown on energea.com:{' '}
                                      {numeral(
                                        formData.mtcEstimatedInvestableBalance
                                      ).format('$0,0.00')}
                                    </b>
                                  </Typography>
                                  <Typography variant="caption">
                                    This is the value they see on energea.com
                                    for their balance. This value is calculated
                                    by summing all contributions, ACATs, in-kind
                                    transfers, other offline funding transfer,
                                    with non-reinvested dividends, and
                                    subtracting principal investments.
                                    Eventually we will need to include share
                                    transfers, distributions, etc. but for now
                                    this is enough.
                                  </Typography>
                                </Grid>
                                <Grid item style={{ marginTop: '1rem' }}>
                                  <Typography variant="body1">
                                    <b>
                                      Account Balance at Inspira:{' '}
                                      {numeral(
                                        formData.accountCashBalanceAtCustodian
                                      ).format('$0,0.00')}
                                    </b>
                                  </Typography>
                                  <Typography variant="caption">
                                    This balance is the actual IRA account cash
                                    balance on Inspira's platform. This may
                                    equal what we show as the MTC investable
                                    balance on our platform to investors but
                                    often differs for many reasons including:
                                    <br />
                                    <b>1)</b> offline funding transfers that
                                    Energea was not notified of (these may be
                                    the client investing in non-energea assets
                                    or may be something we should include in
                                    their estimated mtc investable balance),
                                    <br />
                                    <b>2)</b> dividends that were not
                                    automatically reinvested and are in transit
                                    via check to MTC,
                                    <br />
                                    <b>3)</b> MTC pays clients interest when
                                    their funds are on hold I believe
                                    <br />
                                    <b>4)</b> MTC charged any fees due to their
                                    cash balance
                                    <br />
                                    <b>5)</b> other
                                  </Typography>
                                </Grid>
                              </Grid>
                            </Alert>
                          </Grid>
                          <Grid item xs={12} style={{ marginTop: '1rem' }}>
                            {!formData.custodianCloseAcctRequestDt ||
                            (formData.custodianCloseAcctRequestDt &&
                              moment(
                                formData.custodianCloseAcctRequestDt
                              ).isBefore(moment().add(-2, 'days'))) ? (
                              <Button
                                color="primary"
                                variant="contained"
                                disabled={accountCloseLoading}
                                onClick={() => {
                                  if (
                                    window.confirm(
                                      `Clicking 'OK' will send a request to Millennium Trust Company to close this account. Are you sure you wish to close this account?`
                                    )
                                  ) {
                                    submitMTCAccountCloseRequest();
                                  }
                                }}
                              >
                                {accountCloseLoading ? (
                                  <CircularProgress
                                    style={{ position: 'absolute' }}
                                  />
                                ) : null}
                                Request Close Account
                              </Button>
                            ) : (
                              <Typography>
                                {`(Account Closing Requested: ${moment(
                                  formData.custodianCloseAcctRequestDt
                                ).format('MMM D, YYYY HH:mm:ss')})`}
                              </Typography>
                            )}
                          </Grid>
                        </Grid>
                        <Grid
                          container
                          component={Card}
                          elevation={0}
                          style={{
                            padding: '1rem',
                            marginBottom: '1rem',
                            backgroundColor: '#eee',
                          }}
                        >
                          <Grid
                            container
                            justifyContent="space-between"
                            alignItems="center"
                          >
                            <Grid item>
                              <Typography style={{ fontWeight: 'bold' }}>
                                Millennium Trust fee payment method
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Button
                                color="primary"
                                onClick={() =>
                                  setUploadPaymentMethodDialogOpen(true)
                                }
                                variant="contained"
                              >
                                Update payment method
                              </Button>
                            </Grid>
                          </Grid>
                          <TextInput
                            fullWidth
                            label="Id"
                            disabled
                            source="millenniumTrustFeePaymentMethod.id"
                          />
                          <TextInput
                            fullWidth
                            label="Client name"
                            disabled
                            source="millenniumTrustFeePaymentMethod.clientName"
                          />
                          <TextInput
                            fullWidth
                            label="Bank name"
                            disabled
                            source="millenniumTrustFeePaymentMethod.bankName"
                          />
                          <TextInput
                            fullWidth
                            label="Bank account number"
                            disabled
                            source="millenniumTrustFeePaymentMethod.bankAccountNumber"
                          />
                          <TextInput
                            fullWidth
                            label="Bank account type"
                            disabled
                            source="millenniumTrustFeePaymentMethod.bankAccountType"
                          />
                          <TextInput
                            fullWidth
                            label="Bank routing number"
                            disabled
                            source="millenniumTrustFeePaymentMethod.bankAccountABANumber"
                          />
                          <Dialog
                            open={uploadPaymentMethodDialogOpen}
                            onClose={() =>
                              setUploadPaymentMethodDialogOpen(false)
                            }
                            fullWidth
                          >
                            <DialogTitle>
                              Millennium Trust Payment Method Upload
                            </DialogTitle>
                            <DialogContent>
                              <MuiTextField
                                label="Dwolla funding source id"
                                value={dwollaFundingSourceId || ''}
                                onChange={(event) => {
                                  setDwollaFundingSourceId(event.target.value);
                                }}
                                fullWidth
                                required
                              />
                            </DialogContent>
                            <DialogActions>
                              <Button
                                onClick={() =>
                                  setUploadPaymentMethodDialogOpen(false)
                                }
                                color="primary"
                              >
                                Cancel
                              </Button>
                              <Button
                                onClick={
                                  handleUploadMillenniumTrustPaymentMethod
                                }
                                color="primary"
                                variant="contained"
                                disabled={!dwollaFundingSourceId}
                              >
                                Submit
                              </Button>
                            </DialogActions>
                          </Dialog>
                        </Grid>
                        <Grid
                          container
                          spacing={3}
                          component={Card}
                          elevation={0}
                          style={{
                            padding: '1rem',
                            backgroundColor: '#eee',
                            margin: '.5rem',
                          }}
                        >
                          <Divider style={{ width: '100%', padding: '1rem' }} />
                          <Grid item xs={12}>
                            <Typography gutterBottom>
                              The below button will open a new 'IRA to IRA
                              Transfer Form'. <b>Fill out sections A and D.</b>
                            </Typography>
                            <Typography gutterBottom>
                              Once you click finish, it will be sent to{' '}
                              <b>{formData.user.email}</b> to complete via
                              Eversign.
                            </Typography>
                            <Typography gutterBottom>
                              <b>If a wet signature is required,</b> dont use
                              this button.
                            </Typography>
                            <Button
                              onClick={() => {
                                setTransferEversignDocumentOpen(true);
                              }}
                              color="primary"
                              variant="contained"
                              fullWidth
                            >
                              Send IRA to IRA Transfer Form (Electronic
                              Signature)
                            </Button>
                          </Grid>
                          <Divider style={{ width: '100%', padding: '1rem' }} />
                          <Grid item xs={12}>
                            <Typography gutterBottom>
                              The below button will open download an IRA to IRA
                              Transform form for you to email to the client for
                              a <b>wet signature</b>. Pre-fill for the client
                              what we know. Sections A, B, C, D, and E must be
                              completed.
                            </Typography>
                            <Button
                              color="primary"
                              fullWidth
                              component="a"
                              variant="contained"
                              href="/documents/IRA_to_IRA_Account_Transfer_Authorization.pdf"
                              download
                            >
                              Download IRA to IRA Transfer Form (Wet Signature)
                            </Button>
                          </Grid>
                          <Divider style={{ width: '100%', padding: '1rem' }} />
                          <Grid item xs={12}>
                            <Typography gutterBottom>
                              The below buttons will open a new 'Qualified
                              Retirement Account Rollover Form'.{' '}
                              <b>Fill out sections A and D.</b>
                            </Typography>
                            <Typography gutterBottom>
                              Once you click finish, it will be sent to{' '}
                              <b>{formData.user.email}</b> to complete.
                            </Typography>
                            <Button
                              onClick={() => {
                                setRolloverEversignDocumentOpen(true);
                              }}
                              color="primary"
                              variant="contained"
                              fullWidth
                            >
                              Send Qual. Retirement Plan rollover form
                            </Button>
                          </Grid>
                          <Dialog
                            TransitionProps={{
                              onEnter: handleIRAtoIRATransferForm,
                            }}
                            fullWidth
                            fullScreen
                            maxWidth="lg"
                            open={!!transferEversignDocumentOpen}
                          >
                            <DialogContent
                              id="eversign-container-ira-to-ira-transfer"
                              style={{ padding: 0, height: '1000px' }}
                            />
                            <DialogActions>
                              <Grid
                                container
                                style={{ width: '100%', padding: '0 1rem' }}
                                justifyContent="center"
                              >
                                <Grid item style={{ padding: 0 }}>
                                  <Button
                                    size="large"
                                    onClick={() => {
                                      setTransferEversignDocumentOpen(false);
                                      setTransferEversignDocumentLoaded(false);
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                </Grid>
                              </Grid>
                            </DialogActions>
                          </Dialog>
                          <Backdrop
                            style={{
                              zIndex: 1700,
                              background: 'rgba(255,255,255,1)',
                            }}
                            open={
                              !!(
                                transferEversignDocumentOpen &&
                                !transferEversignDocumentLoaded
                              )
                            }
                          >
                            <Grid
                              style={{
                                padding: '4rem',
                                textAlign: 'center',
                                alignItems: 'center',
                              }}
                              container
                              direction="column"
                              justifyContent="center"
                            >
                              <Grid item lg={6} md={10} xs={12}>
                                <Alert variant="outlined" severity="info">
                                  <AlertTitle gutterBottom variant="h5">
                                    Drafting Transfer Request Form
                                  </AlertTitle>
                                  <Typography>
                                    Please wait while we draft the transfer
                                    form.
                                  </Typography>
                                </Alert>
                                <CircularProgress
                                  style={{ marginTop: '2em' }}
                                  color="primary"
                                />
                              </Grid>
                            </Grid>
                          </Backdrop>
                          <Dialog
                            TransitionProps={{
                              onEnter: handleEntrustInKindTransferForm,
                            }}
                            fullWidth
                            fullScreen
                            maxWidth="lg"
                            open={!!entrustInKindTransferEversignDocumentOpen}
                          >
                            <DialogContent
                              id="eversign-container-ira-to-ira-transfer"
                              style={{ padding: 0, height: '1000px' }}
                            />
                            <DialogActions>
                              <Grid
                                container
                                style={{ width: '100%', padding: '0 1rem' }}
                                justifyContent="center"
                              >
                                <Grid item style={{ padding: 0 }}>
                                  <Button
                                    size="large"
                                    onClick={() => {
                                      setEntrustInKindTransferEversignDocumentOpen(
                                        false
                                      );
                                      setEntrustInKindTransferEversignDocumentLoaded(
                                        false
                                      );
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                </Grid>
                              </Grid>
                            </DialogActions>
                          </Dialog>
                          <Backdrop
                            style={{
                              zIndex: 1700,
                              background: 'rgba(255,255,255,1)',
                            }}
                            open={
                              !!(
                                entrustInKindTransferEversignDocumentOpen &&
                                !entrustInKindTransferEversignDocumentLoaded
                              )
                            }
                          >
                            <Grid
                              style={{
                                padding: '4rem',
                                textAlign: 'center',
                                alignItems: 'center',
                              }}
                              container
                              direction="column"
                              justifyContent="center"
                            >
                              <Grid item lg={6} md={10} xs={12}>
                                <Alert variant="outlined" severity="info">
                                  <AlertTitle gutterBottom variant="h5">
                                    Drafting Entrust In-Kind Transfer Request
                                    Form
                                  </AlertTitle>
                                  <Typography>
                                    Please wait while we draft the transfer
                                    form.
                                  </Typography>
                                </Alert>
                                <CircularProgress
                                  style={{ marginTop: '2em' }}
                                  color="primary"
                                />
                              </Grid>
                            </Grid>
                          </Backdrop>
                          <Dialog
                            TransitionProps={{ onEnter: handleRolloverForm }}
                            fullWidth
                            fullScreen
                            maxWidth="lg"
                            open={!!rolloverEversignDocumentOpen}
                          >
                            <DialogContent
                              id="eversign-container-rollover-form"
                              style={{ padding: 0, height: '1000px' }}
                            />
                            <DialogActions>
                              <Grid
                                container
                                style={{ width: '100%', padding: '0 1rem' }}
                                justifyContent="center"
                              >
                                <Grid item style={{ padding: 0 }}>
                                  <Button
                                    size="large"
                                    onClick={() => {
                                      setRolloverEversignDocumentOpen(false);
                                      setRolloverEversignDocumentLoaded(false);
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                </Grid>
                              </Grid>
                            </DialogActions>
                          </Dialog>
                          <Backdrop
                            style={{
                              zIndex: 1700,
                              background: 'rgba(255,255,255,1)',
                            }}
                            open={
                              !!(
                                rolloverEversignDocumentOpen &&
                                !rolloverEversignDocumentLoaded
                              )
                            }
                          >
                            <Grid
                              style={{
                                padding: '4rem',
                                textAlign: 'center',
                                alignItems: 'center',
                              }}
                              container
                              direction="column"
                              justifyContent="center"
                            >
                              <Grid item lg={6} md={10} xs={12}>
                                <Alert variant="outlined" severity="info">
                                  <AlertTitle gutterBottom variant="h5">
                                    Drafting Rollover Request Form
                                  </AlertTitle>
                                  <Typography>
                                    Please wait while we draft the rollover
                                    form.
                                  </Typography>
                                </Alert>
                                <CircularProgress
                                  style={{ marginTop: '2em' }}
                                  color="primary"
                                />
                              </Grid>
                            </Grid>
                          </Backdrop>
                        </Grid>
                      </>
                    );
                  }
                  return null;
                }}
              </FormDataConsumer>
              <BooleanInput
                source="offlineDividendsFlg"
                fullWidth
                helperText="When turned on, a transfer will not automatically fire when dividends are created, and instead an email with the below payment details will be sent to Marta."
              />
              <TextInput
                source="offlineAccountName"
                fullWidth
                helperText="This is the 'destination' account name that will be sent in the investors dividend email."
              />
              <RichTextInput multiline source="offlinePaymentDetails" />
              <FunctionField
                label="Complete In-Kind Transfer"
                render={(record) => {
                  if (record.subAccountType?.id !== 1) return null;
                  return (
                    <>
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => setCompleteInKindDialogOpen(true)}
                      >
                        Complete In-Kind Transfer
                      </Button>
                      <Dialog
                        open={completeInKindDialogOpen}
                        onClose={() => {
                          setCompleteInKindDialogOpen(false);
                          setCompleteInKindDestinationSubAccountId(null);
                        }}
                        fullWidth
                      >
                        <DialogTitle>Complete In-Kind Transfer</DialogTitle>
                        <DialogContent>
                          <Typography gutterBottom>
                            This action cannot be undone. Clicking 'Submit' will
                            change the subAccountId for all investments,
                            sellOrders, and dividends associated with this
                            subaccount to the destination sub account id input
                            below. It will also set the closedDt on this sub
                            account and the investor will no longer have access
                            to this account unless we reset the closedDt to
                            null.
                          </Typography>
                          <Typography
                            gutterBottom
                            style={{ fontWeight: 'bold' }}
                          >
                            In addition to clicking this button you need to:
                            <br />
                            1) Make sure dwolla label balance is empty. If the
                            amount is more than a couple dollars, it should have
                            been registered with Entrust and been included in
                            the in-kind transfer.
                            <br />
                            2) Update the amount of the offline millennium trust
                            funding transfer so that the estimated mtc
                            investable balance is correct.
                          </Typography>
                          <MuiTextField
                            type="number"
                            label="Destination Sub Account ID"
                            value={completeInKindDestinationSubAccountId || ''}
                            onChange={(event) => {
                              setCompleteInKindDestinationSubAccountId(
                                event.target.value
                              );
                            }}
                            fullWidth
                          />
                        </DialogContent>
                        <DialogActions>
                          <Button
                            onClick={() => setCompleteInKindDialogOpen(false)}
                            color="primary"
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={() => {
                              if (
                                window.confirm(
                                  `Are you sure? This cannot be undone.`
                                )
                              ) {
                                handleCompleteInKindTransfer();
                              }
                            }}
                            color="primary"
                            variant="contained"
                            disabled={!completeInKindDestinationSubAccountId}
                          >
                            Submit
                          </Button>
                        </DialogActions>
                      </Dialog>
                    </>
                  );
                }}
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab margin="none" label="Contributions/Transfers">
          <FunctionField
            label="Funding sessions"
            render={(record) => {
              if (
                !record.millenniumTrustFundingSessions ||
                record.millenniumTrustFundingSessions.length === 0
              ) {
                return (
                  <Alert severity="info">
                    There are no contributions/transfers for this subAccount
                  </Alert>
                );
              }
              return (
                <>
                  <MuiList>
                    {record.millenniumTrustFundingSessions.map(
                      (millenniumTrustFundingSession) => {
                        return (
                          <>
                            <ListItem>
                              <Link
                                href={`../../MillenniumTrustFundingSession/${millenniumTrustFundingSession.id}`}
                              >
                                <ListItemText
                                  primary={
                                    millenniumTrustFundingSession.cmsLabel
                                  }
                                  secondary={`${`Initiated: ${moment(
                                    millenniumTrustFundingSession.createdAt
                                  ).format('MMM D, YYYY')}. `}${
                                    millenniumTrustFundingSession.toMillenniumTransferCompletedDt
                                      ? `Completed: ${moment(
                                          millenniumTrustFundingSession.toMillenniumTransferCompletedDt
                                        ).format('MMM D, YYYY')}. `
                                      : ''
                                  }${
                                    millenniumTrustFundingSession.contributionYear
                                      ? `Contribution Year: ${millenniumTrustFundingSession.contributionYear}. `
                                      : ''
                                  }`}
                                />
                              </Link>
                            </ListItem>
                            <Divider style={{ width: '100%' }} />
                          </>
                        );
                      }
                    )}
                  </MuiList>
                </>
              );
            }}
          />
        </FormTab>
        <FormTab margin="none" label="investments">
          <FunctionField
            label="investments"
            render={(record) => {
              if (!record.investments || record.investments.length === 0) {
                return (
                  <Alert severity="info">
                    There are no investments for this subAccount
                  </Alert>
                );
              }
              return (
                <>
                  <MuiList>
                    {record.investments.map((investment) => {
                      return (
                        <>
                          <ListItem>
                            <Link href={`../../Investment/${investment.id}`}>
                              <ListItemText
                                primary={investment.label}
                                secondary={`${
                                  investment.portfolio &&
                                  investment.portfolio.name
                                } - ${
                                  investment.portfolio &&
                                  investment.portfolio.subtitle
                                }`}
                              />
                            </Link>
                          </ListItem>
                          <Divider style={{ width: '100%' }} />
                        </>
                      );
                    })}
                  </MuiList>
                </>
              );
            }}
          />
        </FormTab>
        <FormTab margin="none" label="dividends">
          <FunctionField
            label="dividends"
            render={(record) => {
              if (!record.dividends || record.dividends.length === 0) {
                return (
                  <Alert severity="info">
                    There are no dividends for this subAccount
                  </Alert>
                );
              }
              return (
                <>
                  <MuiList>
                    {record.dividends.map((dividend) => {
                      return (
                        <>
                          <ListItem>
                            <Link href={`../../Dividend/${dividend.id}`}>
                              <ListItemText
                                primary={dividend.label}
                                secondary={
                                  dividend.portfolio &&
                                  `${dividend.portfolio.name} - ${dividend.portfolio.subtitle}`
                                }
                              />
                            </Link>
                          </ListItem>
                          <Divider style={{ width: '100%' }} />
                        </>
                      );
                    })}
                  </MuiList>
                </>
              );
            }}
          />
        </FormTab>
        <FormTab margin="none" label="buy directions">
          <FunctionField
            label="buy directions"
            render={(record) => {
              if (!record.buyDirections || record.buyDirections.length === 0) {
                return (
                  <Alert severity="info">
                    There are no buy directions for this user
                  </Alert>
                );
              }
              return (
                <>
                  <MuiList>
                    {record.buyDirections.map((buyDirection) => {
                      return (
                        <>
                          <ListItem>
                            <Link
                              href={`../../BuyDirection/${buyDirection.id}`}
                            >
                              <ListItemText
                                primary={buyDirection.label}
                                secondary={`Status: ${
                                  buyDirection.subAccount &&
                                  buyDirection.subAccount.status.statusText
                                }`}
                              />
                            </Link>
                          </ListItem>
                          <Divider style={{ width: '100%' }} />
                        </>
                      );
                    })}
                  </MuiList>
                </>
              );
            }}
          />
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const SubAccountFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by investor first name, last name, or email"
      source="q"
      alwaysOn
    />
    <ReferenceInput
      label="SubAccount Type"
      source="subAccountType.id"
      reference="SubAccountType"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="SubAccountType" optionText="name" />
    </ReferenceInput>
    <SelectInput
      style={{ minWidth: '200px' }}
      label="Status"
      source="status"
      choices={[
        { id: 'open', name: 'Open' },
        { id: 'closed', name: 'Closed' },
        { id: 'all', name: 'All' },
      ]}
    />
  </Filter>
);

const styleRow = (record, index) => {
  const issues = getIssues(record);

  const errorStyle = {
    backgroundColor: 'rgba(255,0,0,.2)',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (issues.filter((el) => el.severity === 'error').length > 0) {
    return errorStyle;
  }
  if (issues.filter((el) => el.severity === 'warning').length > 0) {
    return warningStyle;
  }
  return {};
};

const getIssues = (record) => {
  if (!record) {
    return [];
  }
  const { mtcEstimatedInvestableBalance, accountCashBalanceAtCustodian } =
    record;
  const issues = [];
  if (
    (mtcEstimatedInvestableBalance || mtcEstimatedInvestableBalance === 0) &&
    (accountCashBalanceAtCustodian || accountCashBalanceAtCustodian === 0) &&
    Math.abs(mtcEstimatedInvestableBalance - accountCashBalanceAtCustodian) > 1
  ) {
    issues.push({
      reason:
        'Balance shown on energea.com is different than the balance at Inspira',
      severity: 'error',
    });
  }
  return issues;
};

export const SubAccountList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<SubAccountFilter />}
      perPage={10}
    >
      <Datagrid
        rowStyle={styleRow}
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="accountStatus" />
        <DateField source="closedDt" />
        <LinkField
          label="Sub Account Type"
          linkSource="subAccountType.id"
          labelSource="subAccountType.name"
          reference="SubAccountType"
        />
        {/* <NumberField
          source="status.balance"
          label="Balance"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="status.pendingBalance"
          label="Pending Balance"
          options={{ style: 'currency', currency: 'USD' }}
        /> */}
        <NumberField
          source="accountCashBalanceAtCustodian"
          label="Cash balance according to Inspira"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="mtcEstimatedInvestableBalance"
          label="Estimated cash balance shown on energea.com"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          source="nav"
          label="NAV"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <DateField source="estimatedNextFeePaymentDt" />
        <BooleanField source="hasUncancelledFundingTransfer" sortable={false} />
        <ArrayField source="investments" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField color={() => 'primary'} source="label" />
          </SingleFieldList>
        </ArrayField>
        <ArrayField source="dividends" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField color={() => 'primary'} source="label" />
          </SingleFieldList>
        </ArrayField>
        <ArrayField source="millenniumTrustFundingSessions" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField color={() => 'primary'} source="cmsLabel" />
          </SingleFieldList>
        </ArrayField>
        <ArrayField source="buyDirections" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField color={() => 'primary'} source="label" />
          </SingleFieldList>
        </ArrayField>
        <BooleanField source="understandsSubAccountFlg" />
        <DateField source="updatedAt" showTime />
        <DateField source="createdAt" showTime />
        <FunctionField
          label="Issue Descriptions"
          render={(record) => {
            const issues = getIssues(record);
            return (
              <ol>
                {issues.map((el, i) => (
                  <li key={`subAccount-validation-${i}`}>{el.reason}</li>
                ))}
              </ol>
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const SubAccountCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={20000}
            source="user.id"
            sortable={false}
            reference="UserLite"
          >
            <AutocompleteInput
              label="User"
              required
              fullWidth
              optionText="label"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <ReferenceInput
            source="subAccountType.id"
            sortable={false}
            reference="SubAccountType"
          >
            <SelectInput
              label="Sub Account Type"
              required
              fullWidth
              optionText="name"
            />
          </ReferenceInput>
          <SelectInput
            source="iraType"
            required
            fullWidth
            choices={[
              { id: 'roth', name: 'roth' },
              { id: 'traditional', name: 'traditional' },
            ]}
          />
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData.subAccountType?.id) {
                return null;
              }
              if (
                formData.subAccountType &&
                formData.subAccountType.id === 37
              ) {
                return (
                  <TextInput
                    source="millenniumTrustAccountLast4"
                    required
                    fullWidth
                    label="MTC Account Last 4"
                  />
                );
              }
              if (formData.subAccountType && formData.subAccountType.id === 1) {
                return (
                  <TextInput
                    source="entrustAccountId"
                    required
                    fullWidth
                    label="Entrust Account ID"
                  />
                );
              }
              return (
                <TextInput
                  source="otherAccountId"
                  required
                  fullWidth
                  label="Other Account ID"
                />
              );
            }}
          </FormDataConsumer>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
