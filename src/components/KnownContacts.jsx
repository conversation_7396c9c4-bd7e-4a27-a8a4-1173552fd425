import React, { Component } from 'react';
import { useParams } from 'react-router-dom';
import { Alert } from '@mui/lab';

import {
  Create,
  Datagrid,
  DateField,
  Edit,
  List,
  Pagination,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

import { CheckCircle, Error, GetApp } from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import ExcelReader from './ExcelReader';

const entityName = 'KnownContact';

export const KnownContactEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="firstName" fullWidth />
            <TextInput multiline source="lastName" fullWidth />
            <TextInput source="email" fullWidth />
          </Grid>
        </Grid>
        <DateField source="updatedAt" />
        <DateField source="createdAt" />
      </SimpleForm>
    </Edit>
  );
};

const ContactsPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500]} />
);

export const KnownContactList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title="Known Contacts"
      perPage={25}
      pagination={<ContactsPagination />}
      sort={{ field: 'firstName', order: 'ASC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="firstName" />
        <TextField source="lastName" />
        <TextField source="email" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

const attrs = [
  {
    name: 'firstName',
    format: (val) => (val ? val.toLowerCase() : ''),
    label: 'First Name',
    align: 'left',
  },
  {
    name: 'lastName',
    format: (val) => (val ? val.toLowerCase() : ''),
    label: 'Last Name',
    align: 'left',
  },
  {
    name: 'email',
    format: (val) => (val ? val.toLowerCase() : ''),
    label: 'Email',
    align: 'left',
  },
];

export class KnownContactCreate extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = { data: null, loading: false };
    this.handleData = this.handleData.bind(this);
    this.save = this.save.bind(this);
  }

  handleData(data) {
    // const lintedData = lintData(data);
    this.setState({ data });
  }

  save() {
    this.setState({ loading: true });
    const { data } = this.state;
    const {
      options: { dataProvider },
    } = this.props;
    dataProvider
      .create('KnownContact', {
        data,
      })
      .catch((e) => {
        this.setState({ loading: false });
        console.log('ERROR', e);
      })
      .then(() => {
        window.location.href = '/KnownContact';
        this.setState({ loading: false });
      });
  }

  renderData() {
    const { state } = this;
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {state.data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell align={attr.align || 'center'}>
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  }

  renderSubmit() {
    const { reviewed, loading } = this.state;
    return (
      <>
        <Alert severity={!reviewed ? 'warning' : 'success'}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!reviewed}
                onChange={() => this.setState({ reviewed: !reviewed })}
              />
            }
            label="I have checked and the list looks good (this will override the existing list!)"
          />
          <Button
            onClick={this.save}
            disabled={!reviewed || loading}
            variant="contained"
            size="large"
            color="secondary"
          >
            {loading ? <CircularProgress /> : 'Save'}
          </Button>
        </Alert>
      </>
    );
  }

  render() {
    const { data } = this.state;
    return (
      <Create
        title={`Create ${entityName}`}
        {...this.props}
        helperText="This can be edited at any time so no need to be perfect."
      >
        <Grid container>
          <Grid xs={12} item style={{ padding: '1em' }}>
            <Button
              component="a"
              variant="contained"
              href="/csv-templates/knownContacts.xlsx"
              download
            >
              <GetApp />
              Click to download the csv template
            </Button>
          </Grid>
          <ExcelReader handleData={this.handleData} />
          <Grid item style={{ margin: 'auto' }}>
            {data ? this.renderSubmit() : null}
          </Grid>
          <Grid item xs={12}>
            {data ? this.renderData() : null}
          </Grid>
        </Grid>
      </Create>
    );
  }
}
