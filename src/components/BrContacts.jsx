import React, { Suspense } from 'react';
import { useParams } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';
import { Image, Transformation } from 'cloudinary-react';
import {
  ArrayField,
  ArrayInput,
  AutocompleteInput,
  BooleanField,
  Create,
  Datagrid,
  Edit,
  Filter,
  List,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  SimpleFormIterator,
  SingleFieldList,
  TextField,
  TextInput,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRedirect,
  useRefresh,
  useResourceDefinition,
  useRecordContext,
  BooleanInput,
  FunctionField,
  ReferenceArrayInput,
  SelectArrayInput,
  FormDataConsumer,
  TabbedForm,
  FormTab,
} from 'react-admin';

import {
  Alert,
  Button,
  Card,
  Divider,
  Grid,
  IconButton,
  ListItem,
  ListItemText,
  Paper,
  Typography,
} from '@mui/material';
import { Add, GetApp, OpenInNew } from '@mui/icons-material';
import { List as MuiList } from '@mui/material';

import { openUploadWidget, url } from '../utils/CloudinaryService';
import Config from '../config/config';
import { CustomReferenceField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import theme from '../theme';
import moment from 'moment';
import UserMap from './UserMap';

const entityName = 'Contact';

const definitionJsx = (
  <Alert severity="info" style={{ marginBottom: '1rem' }}>
    <Typography variant="body2">
      A 'Contact' is a person who has an email, password, and other contact
      information. A 'Contact' should always be associated with one or multiple
      'Customers'. A contact has a role associated with each customer they have
      a relationship with that will give them different abilities to manage the
      customer's account as well as determine which communications Energea will
      send to them.
    </Typography>
    <Divider style={{ margin: '1rem 0' }} />
    <Typography variant="body2">
      Um 'Contato' é uma pessoa que possui um e-mail, senha e outras informações
      de contato. Um 'Contato' deve estar sempre associado a um ou vários
      'Clientes'. Um contato tem um papel associado com cada cliente com o qual
      possui uma relação, o que lhe dará diferentes habilidades para gerenciar a
      conta do cliente, bem como determinar quais comunicações a Energea enviará
      para eles.
    </Typography>
  </Alert>
);

const CreateAuth0UserButton = () => {
  const record = useRecordContext();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const createAuth0User = () => {
    dataProvider
      .update('BrContact', {
        data: { id: parseInt(record.id, 10), createAuth0User: true },
      })
      .then(
        () => {
          notify(
            'Auth0 User created and brContact has been emailed with instructions on how to set password.',
            { type: 'success' }
          );
          refresh();
        },
        () => notify(`Error creating Auth0 User`, { type: 'error' })
      );
  };

  return (
    <Grid item>
      <Button
        color="primary"
        variant="contained"
        disabled={!record || !!(record.oktaId || record.authId)}
        onClick={createAuth0User}
      >
        {record.oktaId || record.authId
          ? 'Already has Login Access'
          : 'Create Login Access'}
      </Button>
    </Grid>
  );
};

export const BrContactEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;
  const isJulia =
    ['<EMAIL>', '<EMAIL>'].indexOf(
      permissions?.email
    ) > -1;

  const setAsTestUser = () => {
    return () => {
      const updatedRecord = {
        id: 6,
        value: String(id),
      };
      dataProvider
        .update('Constant', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Successfully set contact as test user.');
          const newWindow = window.open(
            'https://www.energea.com.br/login',
            '_blank'
          );
          if (newWindow) {
            newWindow.focus(); // Ensure the new window is brought to focus
          }
        })
        .catch((err) => {
          console.error(err);
          notify('Error setting contact as test user.', 'error');
        });
    };
  };

  const onPhotosUploaded = (photo) => {
    dataProvider
      .create('BrContactDocument', {
        data: {
          cloudinaryPublicId: photo.public_id,
          brContactId: parseInt(id, 10),
          title: photo.original_filename,
        },
      })
      .then((record) => {
        notify('Image successfully uploaded');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading image.', { type: 'error' });
      });
  };

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['sales-partner-id'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.br_documents,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded(resp.info);
      }
    });
  };

  const handleDownloadImage = (resource) => {
    return () => {
      // Generate full resolution Cloudinary URL (without attachment flag)
      const imageUrl = url(resource.cloudinaryPublicId, {
        quality: 'auto:best',
      });

      // Open in new tab
      window.open(imageUrl, '_blank');
    };
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12}>
              {definitionJsx}
            </Grid>
            {/* {isIT && ( */}
            <Grid item xs={12}>
              <Paper style={{ padding: '1rem' }}>
                <Typography variant="h6">
                  <b>Energea.com.br Access</b>
                </Typography>
                <Typography variant="body2" style={{ marginBottom: '1rem' }}>
                  Clicking this button will email the contact with instructions
                  on how to set their password and access energea.com.br
                </Typography>
                <Grid container spacing={2} style={{ marginBottom: '2rem' }}>
                  <CreateAuth0UserButton />
                </Grid>
                <Typography variant="h6">
                  Sales Partner Dashboard Access
                </Typography>
                <Typography variant="body2" style={{ marginBottom: '1rem' }}>
                  If this contact is a sales person, then assign them to the
                  sales partner whose dashboard they can view below. To enable
                  them to login, make sure to also grant login access above.
                </Typography>
                <ReferenceInput
                  perPage={10_000}
                  source="brSalesPerson.id"
                  reference="BrSalesPerson"
                >
                  <AutocompleteInput
                    allowEmpty={true}
                    optionText="name"
                    label="Sales Partner Dashboard Access"
                    required
                    fullWidth
                  />
                </ReferenceInput>
                <FormDataConsumer>
                  {({ formData }) => {
                    if (formData.brSalesPerson?.id) {
                      return (
                        <ReferenceArrayInput
                          source="brPowerPlanIds"
                          reference="BrPowerPlan"
                          fullWidth
                          sort={{ field: 'name', order: 'ASC' }}
                          perPage={10_000}
                        >
                          <SelectArrayInput
                            optionText="name"
                            fullWidth
                            label="Power Plans"
                            helperText="These are the power plans this sales person has access to"
                          />
                        </ReferenceArrayInput>
                      );
                    }
                  }}
                </FormDataConsumer>
              </Paper>
            </Grid>
            {/* )} */}
            <Grid item xs={12} md={6}>
              <TextInput
                source="firstName"
                fullWidth
                required
                label="First name / Primeiro nome"
              />
              <TextInput
                source="lastName"
                fullWidth
                required
                label="Last name / Sobrenome"
              />
              <FunctionField
                label="E-mail"
                render={(record) => (
                  <TextInput
                    source="email"
                    fullWidth
                    label="E-mail"
                    disabled={record.oktaId || record.authId} // NOTE: If we want to remove this line, we need to update the backend to properly change the email in okta as well.
                  />
                )}
              />
              <TextInput
                source="phone"
                fullWidth
                label="Phone number / Número de telefone"
              />
              <TextInput source="cpf" label="CPF" fullWidth />
              {isIT && (
                <>
                  <TextInput
                    source="oktaId"
                    fullWidth
                    label="Okta ID (This is only visible to IT)"
                  />
                  <TextInput
                    source="authId"
                    fullWidth
                    label="Auth0 ID (This is only visible to IT)"
                  />
                  <TextInput
                    source="hubSpotContactId"
                    fullWidth
                    label="HubSpot Contact ID (This is only visible to IT)"
                  />
                </>
              )}
            </Grid>
            {(isIT || isJulia) && (
              <Grid item xs={12}>
                <Paper style={{ padding: '1rem' }}>
                  <Typography variant="h6">
                    <b>Log in as Contact</b>
                  </Typography>
                  <Divider style={{ width: '100%', margin: '.5rem 0' }} />
                  <Typography variant="body2" gutterBottom>
                    This will allow you to login as this contact. This is useful
                    for troubleshooting issues that contacts are experiencing or
                    checking on the user experience for different types of
                    users.
                    <br />
                    <br />
                    Once you click the button below, a new tab should open at
                    energea.com.br where you will then need to login with the
                    email <b><EMAIL></b>. If you need the password,
                    please contact IT. If you were previously logged in to
                    energea.com.br as yourself or a different user, you may need
                    to log out first before logging back in as the test user.
                    <br />
                    <br />
                    <div style={{ color: theme.palette.error.main }}>
                      <b>
                        Be warned that any actions taken while logged in as the
                        user will be executed including things such as creating
                        consumer units, sending ToAs, etc.
                      </b>
                    </div>
                  </Typography>
                  <FunctionField
                    label="Test User"
                    style={{ marginBottom: '1rem' }}
                    render={(record) => (
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={setAsTestUser()}
                      >
                        Log in as contact
                      </Button>
                    )}
                  />
                </Paper>
              </Grid>
            )}
          </Grid>
        </FormTab>
        <FormTab label="Customers" path="customers">
          <Grid container style={{ width: '100%' }}>
            <FormDataConsumer>
              {({ formData }) => {
                if (formData.brSalesPerson?.id) {
                  return (
                    <Alert severity="info">
                      This tab is not applicable to sales people.
                    </Alert>
                  );
                }
                return (
                  <Grid item xs={12} md={6}>
                    <ArrayInput
                      source="brContactsBrCustomers"
                      fullWidth
                      label="Customer relationships / Relacionamentos com clientes"
                    >
                      <SimpleFormIterator
                        fullWidth
                        TransitionProps={{ enter: false, exit: false }}
                      >
                        <ReferenceInput
                          perPage={10_000}
                          source="brCustomer.id"
                          reference="BrCustomer"
                        >
                          <AutocompleteInput
                            allowEmpty={true}
                            optionText="name"
                            label="Customer / Cliente"
                            required
                            fullWidth
                          />
                        </ReferenceInput>
                        <SelectInput
                          source="role"
                          fullWidth
                          choices={[{ name: 'Admin', id: 'Admin' }]}
                          label="Role / Função"
                        />
                        <BooleanInput
                          source="primaryContactFlg"
                          label="Primary Contact"
                          helperText="Turn this on if this contact is the primary contact. This is important for customers who can or will access energea.com.br"
                        />
                        <BooleanInput
                          source="sendInvoiceFlg"
                          label="Send Invoice Emails"
                          helperText="Turn this on if this contact will receive the invoice emails for this customer."
                        />
                      </SimpleFormIterator>
                    </ArrayInput>
                  </Grid>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </FormTab>
        <FormTab label="Documents" path="documents">
          <Grid container style={{ width: '100%' }}>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (!formData?.documents || formData?.documents?.length === 0)
                  return <Alert severity="info">No documents</Alert>;
                return (
                  <Grid container>
                    {formData.documents.map((record) => (
                      <Grid item style={{ padding: '1rem' }}>
                        <Image
                          cloud_name={Config.cloud_name}
                          publicId={record.cloudinaryPublicId}
                        >
                          <Transformation width="240" crop="scale" />
                        </Image>
                        <Typography>{record.title}</Typography>
                        <IconButton
                          onClick={handleDownloadImage(record)}
                          title="View full resolution image"
                        >
                          <GetApp />
                        </IconButton>
                      </Grid>
                    ))}
                  </Grid>
                );
              }}
            </FormDataConsumer>
            <div className="actions">
              <Button
                endIcon={<Add />}
                variant="outlined"
                onClick={uploadImageWithCloudinary}
              >
                Add photo
              </Button>
            </div>
          </Grid>
        </FormTab>
        <FormTab label="Sales Person Details" path="salesperson">
          <Grid container style={{ width: '100%' }}>
            <FormDataConsumer>
              {({ formData }) => {
                if (!formData.brSalesPerson?.id) {
                  return (
                    <Alert severity="info">
                      This tab is not applicable to non-sales people.
                    </Alert>
                  );
                }
                return (
                  <Grid item xs={12} md={6}>
                    <Typography variant="h5">Address fields</Typography>
                    <TextInput source="address1" fullWidth />
                    <TextInput source="address2" fullWidth />
                    <TextInput source="district" fullWidth />
                    <TextInput source="city" fullWidth />
                    <TextInput source="state" fullWidth />
                    <TextInput source="postalCode" fullWidth />
                    <Typography variant="h5">Commission fields</Typography>
                    <TextInput
                      source="spCommissionBankName"
                      fullWidth
                      label="Bank name"
                    />
                    <TextInput
                      source="spCommissionBankAccountNumber"
                      fullWidth
                      label="CC Conta Bancária"
                    />
                    <TextInput
                      source="spCommissionBankAgency"
                      fullWidth
                      label="Agência"
                    />
                    <TextInput
                      source="spCommissionBankAccountTaxNumber"
                      fullWidth
                      label="CPF/ CNPJ "
                    />
                    <TextInput
                      source="spCommissionPix"
                      fullWidth
                      label="Chave PIX"
                    />
                  </Grid>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </FormTab>
        <FormTab label="Logins" path="logins">
          <Typography variant="h6">
            <b>Logins</b>
          </Typography>
          {/* TODO: Add a map with locations of every login */}
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <FunctionField
                fullWidth
                label="Recent IP Address"
                render={(record) => {
                  if (!record.userLogins || record.userLogins.length === 0) {
                    return (
                      <Alert severity="info">
                        This user hasn't logged in since we began tracking
                        logins ourself at the end of July 2025.
                      </Alert>
                    );
                  }
                  const mapMarkers = [];
                  if (record.latitude && record.longitude) {
                    mapMarkers.push({
                      onMarkerClick: () => {},
                      latitude: parseFloat(record.latitude),
                      longitude: parseFloat(record.longitude),
                      color: theme.palette.green.dark,
                      sizePx: '80px',
                    });
                  }
                  record.userLogins
                    .filter((userLogin) => userLogin.ipAddress)
                    .forEach((userLogin) => {
                      mapMarkers.push({
                        onMarkerClick: () => {},
                        latitude: userLogin.ipAddress.latitude,
                        longitude: userLogin.ipAddress.longitude,
                        color: theme.palette.green.main,
                        sizePx: '40px',
                      });
                    });

                  return (
                    <Grid container style={{ width: '100%' }}>
                      <Grid item>
                        <MuiList dense>
                          {record.userLogins.map((userLogin, index) => {
                            const { id, loginDt, ipAddress, device } =
                              userLogin;
                            let ipJsx = 'No IP address recorded.';
                            if (ipAddress) {
                              const {
                                ipAddress: ip,
                                city,
                                state,
                                postalCode,
                                country,
                                latitude,
                                longitude,
                              } = ipAddress;
                              ipJsx = (
                                <Grid style={{ marginBottom: '.5rem' }}>
                                  <Typography
                                    variant="body2"
                                    style={{ fontWeight: 'bold' }}
                                  >
                                    IP Address: {ip}
                                  </Typography>
                                  <Typography variant="body2">
                                    Location: {city}, {state} {postalCode},{' '}
                                    {country} ({latitude}, {longitude})
                                  </Typography>
                                </Grid>
                              );
                            }
                            let deviceJsx = 'No device recorded.';
                            if (device) {
                              const {
                                visitorId,
                                os,
                                browser,
                                device: deviceName,
                                firstSeenDt,
                                lastSeenDt,
                              } = device;
                              deviceJsx = (
                                <Grid>
                                  <Typography
                                    variant="body2"
                                    style={{ fontWeight: 'bold' }}
                                  >
                                    Device: {visitorId}
                                  </Typography>
                                  <Typography variant="body2">
                                    OS: {os}
                                  </Typography>
                                  <Typography variant="body2">
                                    Browser: {browser}
                                  </Typography>
                                  <Typography variant="body2">
                                    Device: {deviceName} (First seen:{' '}
                                    {moment(firstSeenDt).format('MMM D, YYYY')},
                                    Last seen:{' '}
                                    {moment(lastSeenDt).format('MMM D, YYYY')})
                                  </Typography>
                                </Grid>
                              );
                            }

                            return (
                              <ListItem key={`user-login-${id}`}>
                                <ListItemText
                                  primary={
                                    <Typography
                                      variant="body1"
                                      style={{ fontWeight: 'bold' }}
                                      gutterBottom
                                    >
                                      {moment(loginDt).format(
                                        'MMM D, YYYY HH:mm:ss'
                                      )}
                                    </Typography>
                                  }
                                  secondary={
                                    <>
                                      {ipJsx} {deviceJsx}
                                    </>
                                  }
                                />
                              </ListItem>
                            );
                          })}
                        </MuiList>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Card
                          style={{
                            borderRadius: theme.shape.borderRadius,
                          }}
                        >
                          <Suspense fallback={<div>Loading map...</div>}>
                            {location.pathname.includes('logins') && (
                              <UserMap markers={mapMarkers} />
                            )}
                          </Suspense>
                        </Card>
                      </Grid>
                    </Grid>
                  );
                }}
              />
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name, Last Name, or Email"
      source="q"
      alwaysOn
    />
    <BooleanInput source="isSalesPerson" alwaysOn label="Is Sales Person?" />
  </Filter>
);

export const BrContactList = () => {
  const { permissions } = usePermissions();
  const isIT = permissions?.roles?.map((el) => el.name)?.indexOf('ITRead') > -1;
  return (
    <>
      <Grid container style={{ marginTop: '1rem' }}>
        {definitionJsx}
      </Grid>
      <List
        title={entityName}
        perPage={25}
        filters={<CustomFilter />}
        sort={{ field: 'id', order: 'DESC' }}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <TextField source="firstName" label="First name / Primeiro nome" />
          <TextField source="lastName" label="Last name / Sobrenome" />
          <TextField source="email" label="E-mail" />
          {/* <TextField source="phone" label="Phone number / Número de telefone" /> */}
          <FunctionField
            label="WhatsApp phone"
            render={(record) => {
              return (
                <a
                  href={record.whatsAppPhoneLink}
                  target="_blank"
                  onClick={(event) => {
                    event.stopPropagation();
                  }}
                >
                  {record.phone}
                </a>
              );
            }}
          />
          <TextField source="cpf" label="CPF" />
          <BooleanField source="hasLoginAccess" label="Has Login Access" />
          {isIT ? <TextField source="oktaId" label="Okta ID" /> : null}
          {isIT ? <TextField source="authId" label="Auth0 ID" /> : null}
          <FunctionField
            label="HubSpot Profile"
            textAlign="center"
            render={({ hubSpotContactUrl }) => {
              if (!hubSpotContactUrl) return null;
              return (
                <a
                  target="_blank"
                  onClick={(e) => e.stopPropagation()}
                  href={hubSpotContactUrl}
                >
                  <OpenInNew />
                </a>
              );
            }}
          />
          <ArrayField
            source="brContactsBrCustomers"
            label="Customers / Clientes (NOT for sales people)"
          >
            <SingleFieldList>
              <CustomReferenceField
                source="brCustomerLabel"
                linkOverride={(record) => `/BrCustomer/${record.brCustomer.id}`}
              />
            </SingleFieldList>
          </ArrayField>
          <ArrayField
            source="brPowerPlans"
            label="Power Plans (Sales People ONLY)"
          >
            <SingleFieldList>
              <CustomReferenceField source="name" />
            </SingleFieldList>
          </ArrayField>
          <ArrayField
            source="brConsumerUnits"
            label="Consumer Units (Sales People ONLY)"
          >
            <SingleFieldList>
              <CustomReferenceField source="name" />
            </SingleFieldList>
          </ArrayField>
        </Datagrid>
      </List>
    </>
  );
};

export const BrContactCreate = (props) => {
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const redirect = useRedirect();
  const notify = useNotify();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;
  const isComponentWithinDialog = !!props?.withinDialog;

  const MyCreateButton = () => {
    const { getValues } = useFormContext();
    const { id, ...data } = getValues();

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      dataProvider.create('BrContact', { data }).then(
        (res) => {
          notify('Element created');
          if (props?.onSuccess) {
            props.onSuccess(res?.data?.id);
          } else {
            redirect('/BrContact');
          }
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={!data.firstName || !data.lastName || !data.email}
        />
      </Toolbar>
    );
  };

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm toolbar={<MyCreateButton />}>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={isComponentWithinDialog ? 12 : 6}>
            {definitionJsx}
            <TextInput
              source="firstName"
              fullWidth
              required
              label="First name / Primeiro nome"
            />
            <TextInput
              source="lastName"
              fullWidth
              required
              label="Last name / Sobrenome"
            />
            <TextInput source="email" fullWidth label="E-mail" />
            <TextInput
              source="phone"
              fullWidth
              label="Phone number / Número de telefone"
            />
            <TextInput source="cpf" label="CPF" fullWidth />
            {isIT && (
              <TextInput
                source="oktaId"
                fullWidth
                label="Okta ID (This is only visible to IT)"
              />
            )}
            {isIT && (
              <TextInput
                source="authId"
                fullWidth
                label="Auth0 ID (This is only visible to IT)"
              />
            )}
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
