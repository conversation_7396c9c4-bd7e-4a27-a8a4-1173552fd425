import React from 'react';
import { useParams } from 'react-router-dom';
import {
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  ReferenceInput,
  required,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';
import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'Notifications';

export const NotificationEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={8}>
            <ReferenceInput
              perPage={10000}
              source="user.id"
              reference="UserLite"
              validate={[required()]}
            >
              <AutocompleteInput
                label="User"
                fullWidth
                optionText="fullName"
                shouldRenderSuggestions={(value) => value.trim().length > 0}
              />
            </ReferenceInput>
            <TextInput fullWidth disabled source="type" />
            <TextInput fullWidth disabled source="title" />
            <TextInput fullWidth disabled source="summary" />
            <TextInput fullWidth disabled source="iconClass" />
            <BooleanInput fullWidth source="deletedFlg" />
            <DateTimeInput fullWidth source="readDt" />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const NotificationFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const NotificationList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<NotificationFilter />}
      sort={{ field: 'createdAt', order: 'DESC' }}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="type" sortable={false} />
        <TextField source="title" sortable={false} />
        <TextField source="summary" sortable={false} />
        <FunctionField
          label="Icon"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <span>
                <i
                  style={{ marginRight: '6px' }}
                  className={record.iconClass}
                />
                {record.iconClass}
              </span>
            );
          }}
        />
        <TextField source="url" sortable={false} />
        <BooleanField source="deletedFlg" />
        <DateField source="readDt" showTime />
        <DateField source="createdAt" showTime />
        <DateField source="updatedAt" showTime />
      </Datagrid>
    </List>
  );
};

export const NotificationCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={8}>
          <ReferenceInput
            validate={[required()]}
            perPage={10000}
            source="user.id"
            reference="UserLite"
          >
            <AutocompleteInput
              label="User (Investor)"
              fullWidth
              optionText="fullName"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <SelectInput
            source="type"
            required
            fullWidth
            choices={[
              { id: 'event', name: 'Event' },
              { id: 'userMilestone', name: 'User Milestone' },
              { id: 'transfer', name: 'Transfer' },
              { id: 'sellOrder', name: 'Sell Order' },
              { id: 'investment', name: 'Auto Reinvestment' },
            ]}
          />
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (formData.type) {
                const capitalizedResourceName =
                  formData.type.charAt(0).toUpperCase() +
                  formData.type.slice(1);
                const labelField =
                  formData.type === 'transfer' ||
                  formData.type === 'sellOrder' ||
                  formData.type === 'investment'
                    ? 'label'
                    : 'title';
                return (
                  // TODO: Clean up label in resolver to include user name, investment id, value, date
                  // TODO: Make autocomplete
                  // TODO: For transfers, only show the user who is selected's (may be easier if use autocompleteinput)
                  <ReferenceInput
                    perPage={25}
                    source={`${formData.type}.id`}
                    reference={capitalizedResourceName}
                  >
                    <SelectInput
                      label={capitalizedResourceName}
                      // required
                      fullWidth
                      optionText={labelField}
                    />
                  </ReferenceInput>
                );
              }
            }}
          </FormDataConsumer>
          <DateTimeInput
            source="readDt"
            helperText="Leave this blank for it to show up as an unread notification"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
