import React from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanInput,
  BooleanField,
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  SimpleForm,
  TextField,
  TextInput,
  UrlField,
  usePermissions,
  Filter,
  FunctionField,
  useResourceDefinition,
} from 'react-admin';

import { Avatar, Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, DetailField } from './CustomFields';

const entityName = 'Board Member';

export const BoardMemberEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="firstName"
              fullWidth
              helperText="The first name of the board member that will be displayed on the About Us page"
            />
            <TextInput
              source="lastName"
              fullWidth
              helperText="The last name of the board member that will be displayed on the About Us page"
            />
            {/* <TextInput source="jobTitle" fullWidth /> */}
            <TextInput source="description" multiline fullWidth />
            <TextInput source="imgUrl" label="Cloudinary img url" fullWidth />
            <CustomNumberInput source="orderNo" fullWidth />
            <BooleanInput
              source="inactiveFlg"
              defaultValue={false}
              helperText="When on, this hides the employee from energea.com"
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const BoardMemberFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
    <BooleanInput
      label="Inactive Board Members"
      source="inactiveFlg"
      alwaysOn
    />
  </Filter>
);

export const BoardMemberList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'orderNo', order: 'ASC' }}
      filters={<BoardMemberFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <FunctionField
          label="Main Image"
          sortable={false}
          render={(record) => {
            if (!record.imgUrl) {
              return null;
            }
            return (
              <Avatar
                style={{ width: '4em', height: '4em' }}
                alt={`${record.firstName} ${record.lastName}`}
                src={record.imgUrl}
              />
            );
          }}
        />
        <TextField source="firstName" />
        <TextField source="lastName" />
        <TextField source="email" />
        <TextField source="jobTitle" />
        <DetailField source="description" />
        <UrlField source="imgUrl" />
        <NumberField source="orderNo" />
        <BooleanField source="inactiveFlg" />
      </Datagrid>
    </List>
  );
};

export const BoardMemberCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="jobTitle" fullWidth required />
          <TextInput source="description" multiline fullWidth required />
          <TextInput
            source="imgUrl"
            label="Cloudinary img url"
            fullWidth
            helperText="For IT use only. Leave blank."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
