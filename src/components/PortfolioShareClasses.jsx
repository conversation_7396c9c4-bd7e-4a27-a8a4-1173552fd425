import { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  Edit,
  Filter,
  FormDataConsumer,
  FunctionField,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  BooleanField,
  BooleanInput,
  usePermissions,
  useResourceDefinition,
  useDataProvider,
  useNotify,
  useRefresh,
} from 'react-admin';
import { Grid, Button, CircularProgress } from '@mui/material';
import { Calculate } from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Portfolio Share Class';

const PortfolioShareClassFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      source="portfolioId"
      reference="PortfolioLite"
      perPage={1000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput
        label="Portfolio"
        optionText="name"
        helperText="Filter by portfolio"
      />
    </ReferenceInput>
    <ReferenceInput
      source="shareClassId"
      reference="ShareClass"
      perPage={1000}
      sort={{ field: 'name', order: 'ASC' }}
      alwaysOn
    >
      <SelectInput
        label="Share Class"
        optionText="name"
        helperText="Filter by share class"
      />
    </ReferenceInput>
  </Filter>
);

export const PortfolioShareClassEdit = () => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const handleUpdateDiscount = (formData) => {
    if (!formData.portfolioId || !formData.shareClassId) {
      notify('Portfolio and Share Class must be selected', { type: 'error' });
      return;
    }

    setLoading(true);
    dataProvider
      .update('PortfolioShareClass', {
        id: formData.id,
        data: {
          action: 'updateDiscount',
          portfolioId: formData.portfolioId,
          shareClassId: formData.shareClassId,
        },
      })
      .then(
        () => {
          notify('Discount updated successfully', { type: 'success' });
          refresh();
          setLoading(false);
        },
        (error) => {
          console.error('Error updating discount:', error);
          notify('Error updating discount', { type: 'error' });
          setLoading(false);
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="portfolioId"
              reference="PortfolioLite"
              perPage={1000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                label="Portfolio"
                optionText="name"
                fullWidth
                required
                helperText="Select the portfolio to associate with this share class"
              />
            </ReferenceInput>
            <ReferenceInput
              source="shareClassId"
              reference="ShareClass"
              perPage={1000}
              sort={{ field: 'name', order: 'ASC' }}
            >
              <SelectInput
                label="Share Class"
                optionText="name"
                fullWidth
                required
                helperText="Select the share class"
              />
            </ReferenceInput>
            <CustomNumberInput
              source="sharePriceDiscount"
              label="Share Price Discount (%)"
              fullWidth
              helperText="Discount percentage for this share class (e.g., 5 for 5%)"
              step={0.01}
              min={0}
              max={100}
            />
            <BooleanInput
              source="isClosed"
              label="Is Closed"
              helperText="Whether this share class is closed for new investments"
            />

            <FormDataConsumer>
              {({ formData }) => (
                <Grid container spacing={2} style={{ marginTop: '1rem' }}>
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={
                        loading ? <CircularProgress size={20} /> : <Calculate />
                      }
                      onClick={() => handleUpdateDiscount(formData)}
                      disabled={
                        loading ||
                        !formData.portfolioId ||
                        !formData.shareClassId
                      }
                      style={{ textTransform: 'none' }}
                    >
                      {loading ? 'Updating...' : 'Update Discount'}
                    </Button>
                  </Grid>
                </Grid>
              )}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const PortfolioShareClassList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      filters={<PortfolioShareClassFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolioId"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <LinkField
          reference="ShareClass"
          linkSource="shareClassId"
          labelSource="shareClass.name"
          label="Share Class"
        />
        <FunctionField
          label="Discount %"
          render={(record) => {
            if (record.sharePriceDiscount == null) return '';
            return `${record.sharePriceDiscount}%`;
          }}
        />
        <FunctionField
          label="Front End Load Rate %"
          render={(record) => {
            if (record.shareClass?.frontEndLoadRate == null) return '';
            return `${record.shareClass.frontEndLoadRate}%`;
          }}
        />
        <FunctionField
          label="Trail Fee Rate %"
          render={(record) => {
            if (record.shareClass?.trailFeeRate == null) return '';
            return `${record.shareClass.trailFeeRate}%`;
          }}
        />
        <BooleanField source="isClosed" />
        <DateField source="createdAt" showTime={true} />
        <DateField source="updatedAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

export const PortfolioShareClassCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="portfolioId"
            reference="PortfolioLite"
            perPage={1000}
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput
              label="Portfolio"
              optionText="name"
              fullWidth
              required
              helperText="Select the portfolio to associate with this share class"
            />
          </ReferenceInput>
          <ReferenceInput
            source="shareClassId"
            reference="ShareClass"
            perPage={1000}
            sort={{ field: 'name', order: 'ASC' }}
          >
            <SelectInput
              label="Share Class"
              optionText="name"
              fullWidth
              required
              helperText="Select the share class"
            />
          </ReferenceInput>
          <CustomNumberInput
            source="sharePriceDiscount"
            label="Share Price Discount (%)"
            fullWidth
            helperText="Discount percentage for this share class (e.g., 5 for 5%)"
            step={0.01}
            min={0}
            max={100}
          />
          <BooleanInput
            source="isClosed"
            label="Is Closed"
            helperText="Whether this share class is closed for new investments"
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
