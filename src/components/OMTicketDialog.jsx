import React, { useState } from 'react';
import { useDataProvider, useNotify, usePermissions } from 'react-admin';
import {
  Button,
  Checkbox,
  Collapse,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputLabel,
  OutlinedInput,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';

import moment from 'moment';

const hydroCMTicketTitleOptions = [
  'AC ELECTRICAL UNAVAILABILITY',
  'ADPS OR GROUDING ISSUE',
  'AIR COMPRESSOR ISSUE',
  'ASSET WARRANTY CLAIM',
  'BACKUP GENERATOR ISSUE',
  'CCTV ISSUES',
  'COMMUNICATION ISSUES',
  'DRAINAGE SYSTEM ISSUE',
  'EXTERNAL AREAS ISSUES',
  'GRID INSTABILITY ISSUES',
  'HSEQ STARDARD ADJUSTMENT',
  'HYDRAULIC UNIT ISSUE',
  'INDUSTRIAL WATER FILTERING FAILURE',
  'MOWING EQUIPMENT UNAVAILABILITY',
  'O&M BUILDING ISSUE',
  'PRESSURE ABNORMAL',
  'POOR CONTACT OR CONNECTIONS',
  'POWER HOUSE BUILDING ISSUE',
  'PROTECTION GRIDS ISSUES',
  'PRTVA PANEL ISSUE',
  "SCADA'S COMPUTER ISSUES",
  'SENSOR/TRANSDUCTOR MALFUNCTION',
  'SS BUILDING ISSUE',
  'TRANSFORMER ISSUES',
  'TRANSFORMER OIL ANALYSIS',
  'TURBINE AND/OR GENERATOR ISSUES',
  'UPS ISSUES',
  'VEGETATION CONTROL',
  'WATER INTAKE SYSTEM FAILURE',
];

const solarCMTicketTitleOptions = [
  'AC Electrical Unavailability',
  'Asset Warranty Claim',
  'CCTV Issues',
  'Communication Issues',
  'DC Electrical Unavailability',
  'Degradation / Soiling',
  'Grid Instability Issues',
  'HSEQ Stardard Adjustment',
  'Inverter Underperforming',
  'Meteo Station Issues',
  'Mowing Equipment Unavailability',
  'O&M Building Issue',
  'Poor Contact or Connections',
  'Protections Replacement',
  'Punchlist Item Realization',
  'SS Building Issue',
  'Tracking System Unavailability',
  'Transformer Issues',
  'Transformer Oil Analysis',
  'UPS Issues',
  'Vegetation Control',
];

export const OMTicketDialog = (props) => {
  const [ticketTypeId, setTicketTypeId] = useState(
    props.omTicket?.omTicketType?.id
  );
  const [ticketOwnerId, setTicketOwnerId] = useState(
    props.omTicket?.ticketOwner?.id
  );
  const [equipmentItemId, setEquipmentItemId] = useState(
    props.omTicket?.equipmentItem?.id
  );
  const [internalOnlyFlg, setInternalOnlyFlg] = useState(
    !!props.omTicket?.internalOnlyFlg
  );
  const [loss, setLoss] = useState(props.omTicket?.estimatedPercentageLoss);
  const [priorityNo, setPriorityNo] = useState(props.omTicket?.priorityNo);
  const [notes, setNotes] = useState(props.omTicket?.notes);

  const [statusNotes, setStatusNotes] = useState(props.omTicket?.statusNotes);
  const [title, setTitle] = useState(props.omTicket?.title);
  const [deviceName, setDeviceName] = useState(props.omTicket?.deviceName);
  const [clientNotificationRequiredFlg, setClientNotificationRequiredFlg] =
    useState(!!props.omTicket?.clientNotificationRequiredFlg);
  const [clientNotificationType, setClientNotificationType] = useState(
    props.omTicket?.clientNotificationType
  );
  const [clientNotifiedDt, setClientNotifiedDt] = useState(
    props.omTicket?.clientNotifiedDt
      ? moment(props.omTicket.startDt).format('yyyy-MM-DDTHH:mm')
      : null
  );
  const [startDt, setStartDt] = useState(
    props.omTicket?.startDt
      ? moment(props.omTicket.startDt).format('yyyy-MM-DDTHH:mm')
      : null
  );
  const [endDt, setEndDt] = useState(
    props.omTicket?.endDt
      ? moment(props.omTicket.endDt).format('yyyy-MM-DDTHH:mm')
      : null
  );
  const [newTicketTypeDialogOpen, setNewTicketTypeDialogOpen] = useState(false);
  const [manualTitleEntryMode, setManualTitleEntryMode] = useState(false);
  const [allOMTicketTypes, setAllOMTicketTypes] = useState(null);
  const [allEquipmentItems, setAllEquipmentItems] = useState(null);
  const [projects, setProjects] = useState(null);
  const [employees, setEmployees] = useState(null);
  const [loading, setLoading] = useState(false);
  const [projectId, setProjectId] = useState(props.projectId);
  const [projectInstallationTypeId, setProjectInstallationTypeId] = useState(
    props.projectInstallationTypeId
  );

  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  const roleNames = permissions?.roles?.map((r) => r.name) || [];
  const isThirdPartyI9 = roleNames.includes('OMPartnerI9');
  const isThirdPartyRun = roleNames.includes('OMPartnerRun');
  const isThirdPartySolRen = roleNames.includes('OMPartnerSolRen');
  const hasTitleOverrideAccess =
    roleNames.includes('ITWrite') ||
    [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ].indexOf(permissions.email) > -1;

  const fetchOMTicketTypes = () => {
    setLoading(true);
    dataProvider
      .getMany('OMTicketType', {
        sort: { field: 'name', order: 'ASC' },
        filter: {},
      })
      .then((res) => {
        setAllOMTicketTypes(res.data);
        setLoading(false);
      });
  };

  const fetchEquipmentItems = () => {
    setLoading(true);
    dataProvider
      .getList('EquipmentItem', {
        sort: { field: 'id', order: 'asc' },
        filter: {
          project: { id: projectId },
        },
        pagination: { page: 1, perPage: 10000 },
      })
      .then((res) => {
        const sortedData = res.data;
        sortedData.sort((a, b) => (a.label < b.label ? -1 : 1));
        setAllEquipmentItems(sortedData);
        setLoading(false);
      });
  };
  const fetchProjects = () => {
    setLoading(true);
    const filter = {};
    if (isThirdPartyI9) {
      filter.omTruckId = 5;
    } else if (isThirdPartySolRen) {
      filter.omTruckId = 8;
    } else if (isThirdPartyRun) {
      filter.omTruckId = 12;
    }
    dataProvider
      .getList('ProjectLite', {
        sort: { field: 'name', order: 'asc' },
        filter,
        pagination: { page: 1, perPage: 10000 },
      })
      .then((res) => {
        setProjects(res.data);
        setLoading(false);
      });
  };
  const fetchEmployees = () => {
    setLoading(true);
    const filter = {
      inactiveFlg: false,
    };
    if (isThirdPartyI9) {
      filter.omTruckId = 5;
    } else if (isThirdPartySolRen) {
      filter.omTruckId = 8;
    } else if (isThirdPartyRun) {
      filter.omTruckId = 12;
    }
    dataProvider
      .getList('Employee', {
        sort: { field: 'firstName', order: 'asc' },
        filter,
        pagination: { page: 1, perPage: 10000 },
      })
      .then((res) => {
        // sort again on full name incase we have duplicate first names
        const sortedData = res.data;
        sortedData.sort((a, b) => (a.fullName < b.fullName ? -1 : 1));
        setEmployees(sortedData);
        setLoading(false);
      });
  };

  if (!allOMTicketTypes && !loading) {
    fetchOMTicketTypes();
  }
  if (!projects && !loading && !projectId) {
    fetchProjects();
  }
  if (!employees && !loading) {
    fetchEmployees();
  }
  if (!allEquipmentItems && !loading && projectId) {
    fetchEquipmentItems();
  }

  const createOMTicket = () => {
    setLoading(true);
    dataProvider
      .create('OMTicket', {
        data: {
          clientNotificationRequiredFlg,
          clientNotifiedDt,
          clientNotificationType,
          deviceName,
          notes,
          statusNotes,
          priorityNo,
          title,
          startDt: startDt
            ? moment(startDt).format('YYYY-MM-DD HH:mm:ss')
            : null,
          endDt: endDt ? moment(endDt).format('YYYY-MM-DD HH:mm:ss') : null,
          estimatedPercentageLoss: parseFloat(loss),
          projectId,
          internalOnlyFlg,
          userId: permissions?.id || null,
          omTicketTypeId: ticketTypeId ? parseInt(ticketTypeId, 10) : null,
          ticketOwnerId: ticketOwnerId ? parseInt(ticketOwnerId, 10) : null,
          equipmentItemId: equipmentItemId
            ? parseInt(equipmentItemId, 10)
            : null,
        },
      })
      .then(
        (res) => {
          notify('Ticket created', { type: 'success' });
          setLoading(false);
          props.onClose();
        },
        (err) => {
          notify('Error creating ticket', { type: 'error' });
          setLoading(false);
        }
      );
  };

  const updateOMTicket = () => {
    if (!props.omTicket?.id) {
      console.warn('No ticket id. Skipping update');
      return null;
    }
    setLoading(true);
    dataProvider
      .update('OMTicket', {
        data: {
          id: parseFloat(props.omTicket?.id, 10),
          clientNotificationRequiredFlg,
          clientNotifiedDt,
          clientNotificationType,
          deviceName,
          notes,
          statusNotes,
          priorityNo,
          title,
          startDt: startDt
            ? moment(startDt).format('YYYY-MM-DD HH:mm:ss')
            : null,
          endDt: endDt ? moment(endDt).format('YYYY-MM-DD HH:mm:ss') : null,
          estimatedPercentageLoss: parseFloat(loss),
          internalOnlyFlg,
          userId: permissions?.id || null,
          omTicketTypeId: ticketTypeId ? parseInt(ticketTypeId, 10) : null,
          ticketOwnerId: ticketOwnerId ? parseInt(ticketOwnerId, 10) : null,
          equipmentItemId: equipmentItemId
            ? parseInt(equipmentItemId, 10)
            : null,
        },
      })
      .then(
        (res) => {
          notify('Ticket updated', { type: 'success' });
          setLoading(false);
          props.onClose();
        },
        (err) => {
          notify('Error updating ticket', { type: 'error' });
          setLoading(false);
        }
      );
  };

  let dialogTitle = 'Create Ticket';
  let submit = () => {};
  if (props.action === 'create') {
    dialogTitle = 'Create Ticket';
    submit = createOMTicket;
  } else if (props.action === 'update') {
    dialogTitle = 'Update Ticket';
    submit = updateOMTicket;
  } else {
    console.error(`Unrecognized ticket action ${props.action}`);
    return null;
  }

  const minStartDt = moment('2020-01-01', 'YYYY-MM-DD');
  return (
    <Dialog open>
      <DialogTitle>{dialogTitle}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          {props.action === 'update' && (
            <Grid item xs={12}>
              <Typography variant="caption">
                Created By : <b>{props.omTicket?.author?.fullName}</b>
              </Typography>
              <br />
              <Typography variant="caption">
                Created On :{' '}
                <b>{moment(props.omTicket?.acknowledgedDt).format('lll')}</b>
              </Typography>
            </Grid>
          )}
          <Grid item xs={12}>
            <FormControl variant="outlined" required fullWidth>
              <InputLabel id="ticket-type">Ticket Type</InputLabel>
              <Select
                labelId="ticket-type"
                placeholder="Ticket Type"
                labelWidth={200}
                value={ticketTypeId || ''}
                onChange={(event) => {
                  if (event.target.value === 'new-type') {
                    setNewTicketTypeDialogOpen(true);
                  } else {
                    setTicketTypeId(event.target.value);
                  }
                  setManualTitleEntryMode(false);
                }}
                style={{ width: '100%' }}
                required
                input={<OutlinedInput label="Ticket Type" />}
              >
                {allOMTicketTypes?.map((ticketType) => (
                  <MenuItem
                    key={`ticket-type-${ticketType.id}`}
                    value={ticketType.id}
                  >
                    {ticketType.name}
                  </MenuItem>
                ))}
                {isThirdPartyI9 ||
                isThirdPartySolRen ||
                isThirdPartyRun ? null : (
                  <>
                    <Divider />
                    <MenuItem
                      value="new-type"
                      onClick={() => {
                        setNewTicketTypeDialogOpen(true);
                      }}
                    >
                      Create new type
                    </MenuItem>
                  </>
                )}
              </Select>
            </FormControl>
          </Grid>
          {!props.projectId && (
            <Grid item xs={12}>
              <FormControl variant="outlined" fullWidth>
                <InputLabel id="equipment-item">Project</InputLabel>
                <Select
                  labelId="project"
                  placeholder="Project"
                  value={projectId || ''}
                  onChange={(event) => {
                    setProjectId(event.target.value);
                    const project = projects?.filter(
                      (p) => String(p.id) === String(event.target.value)
                    )?.[0];
                    setProjectInstallationTypeId(project?.installationTypeId);
                  }}
                  style={{ width: '100%' }}
                  input={<OutlinedInput label="Project" />}
                >
                  {projects?.map((project) => (
                    <MenuItem
                      key={`equipment-item-${project.id}`}
                      value={project.id}
                    >
                      {project.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}
          <Grid item xs={12}>
            {[1 /* corrective maintenance */, 39 /* fault */].indexOf(
              ticketTypeId
            ) > -1 &&
            projectId &&
            !manualTitleEntryMode ? (
              <FormControl variant="outlined" required fullWidth>
                <InputLabel id="ticket-title">Title</InputLabel>
                <Select
                  labelId="ticket-title"
                  placeholder="Title"
                  labelWidth={200}
                  value={title || ''}
                  onChange={(event) => {
                    if (event.target.value === 'custom-title') {
                      // Do nothing
                    } else {
                      setTitle(event.target.value);
                    }
                  }}
                  style={{ width: '100%' }}
                  required
                  input={<OutlinedInput label="Title" />}
                >
                  {(projectInstallationTypeId === 2 // hydro
                    ? hydroCMTicketTitleOptions
                    : solarCMTicketTitleOptions
                  ).map((titleOption, index) => (
                    <MenuItem
                      key={`ticket-title-option-${index}`}
                      value={titleOption.toUpperCase()}
                    >
                      {titleOption.toUpperCase()}
                    </MenuItem>
                  ))}
                  {hasTitleOverrideAccess && (
                    <>
                      <Divider />
                      <MenuItem
                        value="custom-title"
                        onClick={() => {
                          setManualTitleEntryMode(true);
                        }}
                      >
                        Manually enter title
                      </MenuItem>
                    </>
                  )}
                </Select>
              </FormControl>
            ) : (
              <TextField
                label="Title"
                value={title || ''}
                required
                onChange={(event) => {
                  setTitle(event.target.value);
                }}
                disabled={!ticketTypeId || !projectId}
                helperText={
                  ticketTypeId && projectId
                    ? null
                    : `You must select a ${
                        !ticketTypeId ? 'ticket type' : 'project'
                      } before entering a title.`
                }
                fullWidth
              />
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                id="startDt"
                label="Start Date"
                type="datetime-local"
                required
                value={startDt || ''}
                onChange={(event) => {
                  setStartDt(
                    event.target.value
                      ? moment(event.target.value).format('yyyy-MM-DDTHH:mm')
                      : null
                  );
                }}
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  inputProps: {
                    max: moment().add(30, 'years').format('yyyy-MM-DDTHH:mm'),
                    min: minStartDt.format('yyyy-MM-DDTHH:mm'),
                  },
                }}
                error={
                  (endDt && startDt && endDt <= startDt) ||
                  moment(startDt).isBefore(minStartDt)
                }
                helperText={
                  (endDt && startDt && endDt <= startDt) ||
                  moment(startDt).isBefore(minStartDt)
                    ? `Start date must precede end date and be after ${minStartDt.format(
                        'YYYY-MM-DD'
                      )}`
                    : "Beginning of the time period for this issue (in the project's timezone))"
                }
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                id="endDt"
                label="End Date"
                type="datetime-local"
                value={endDt || ''}
                onChange={(event) => {
                  setEndDt(
                    event.target.value
                      ? moment(event.target.value).format('yyyy-MM-DDTHH:mm')
                      : null
                  );
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  inputProps: {
                    max: moment().add(30, 'years').format('yyyy-MM-DDTHH:mm'),
                    min: startDt || minStartDt.format('yyyy-MM-DDTHH:mm'),
                  },
                }}
                fullWidth
                error={
                  (endDt && startDt && endDt <= startDt) ||
                  moment(endDt).isBefore(minStartDt)
                }
                helperText={
                  (endDt && startDt && endDt <= startDt) ||
                  moment(endDt).isBefore(minStartDt)
                    ? `Start date must precede end date and be after ${minStartDt.format(
                        'YYYY-MM-DD'
                      )}`
                    : 'End of the time period for this issue if issue is closed (in the project timezone).'
                }
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl variant="outlined" fullWidth>
              <InputLabel id="ticket-owner">Ticket Owner</InputLabel>
              <Select
                labelId="ticket-owner"
                placeholder="Ticket Owner"
                value={ticketOwnerId || ''}
                onChange={(event) => {
                  setTicketOwnerId(event.target.value);
                }}
                style={{ width: '100%' }}
                input={<OutlinedInput label="Ticket Owner" />}
              >
                {employees?.map((employee) => (
                  <MenuItem
                    key={`ticket-owner-${employee.id}`}
                    value={employee.id}
                  >
                    {employee.fullName}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Responsible party for the ticket.</FormHelperText>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Ticket Notes"
              value={notes || ''}
              required
              multiline
              helperText="Explain the issue in detail for the field techs."
              onChange={(event) => {
                setNotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Status Notes"
              value={statusNotes || ''}
              multiline
              helperText="Notes for any updates on ticket progress."
              onChange={(event) => {
                setStatusNotes(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Priority"
              helperText="Priority of the ticket. 1 is the highest priority."
              type="number"
              value={priorityNo || priorityNo === 0 ? priorityNo : ''}
              inputProps={{ min: 1 }}
              onChange={(event) => {
                setPriorityNo(
                  event.target.value ? parseFloat(event.target.value) : null
                );
              }}
              error={priorityNo !== null && priorityNo < 1}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Estimated Percentage Loss"
              helperText="Percentage of site generation that the issue is affecting (Ex: 25)"
              type="number"
              value={loss || loss === 0 ? loss : ''}
              onChange={(event) => {
                setLoss(
                  event.target.value ? parseFloat(event.target.value) : null
                );
              }}
              inputProps={{ min: 0, max: 100 }}
              error={loss !== null && (loss < 0 || loss > 100)}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl variant="outlined" fullWidth>
              <InputLabel id="equipment-item">Equipment Item</InputLabel>
              <Select
                labelId="equipment-item"
                placeholder="Equipment Item"
                labelWidth={200}
                value={equipmentItemId || ''}
                onChange={(event) => {
                  setEquipmentItemId(event.target.value);
                }}
                style={{ width: '100%' }}
                input={<OutlinedInput label="Equipment Item" />}
              >
                {allEquipmentItems?.map((equipmentItem) => (
                  <MenuItem
                    key={`equipment-item-${equipmentItem.id}`}
                    value={equipmentItem.id}
                  >
                    <ListItemText
                      primary={equipmentItem.label}
                      secondary={equipmentItem.equipmentType?.name}
                    />
                  </MenuItem>
                ))}
              </Select>
              {projectId && (
                <FormHelperText>
                  <span>
                    If you don't see the equipment item you need, add inventory
                    for this project{' '}
                    <a href={`Project/${projectId}/Inventory`}>here</a>.
                  </span>
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Device Name"
              value={deviceName || ''}
              onChange={(event) => {
                setDeviceName(event.target.value);
              }}
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              label="Does this issue require an immediate client notification?"
              control={
                <Checkbox
                  checked={clientNotificationRequiredFlg}
                  onChange={(event) => {
                    setClientNotificationRequiredFlg(
                      !clientNotificationRequiredFlg
                    );
                  }}
                  fullWidth
                />
              }
            />
          </Grid>
          <Grid item xs={12}>
            <Collapse in={clientNotificationRequiredFlg}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextField
                    id="clientNotifiedDt"
                    label="Client Notified Date"
                    type="datetime-local"
                    helperText="Timestamp of when the client was notified of the issue (if applicable)"
                    value={clientNotifiedDt || ''}
                    onChange={(event) => {
                      setClientNotifiedDt(
                        event.target.value
                          ? moment(event.target.value).format(
                              'yyyy-MM-DDTHH:mm'
                            )
                          : null
                      );
                    }}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                    InputProps={{
                      inputProps: {
                        max: moment().format('yyyy-MM-DDTHH:mm'),
                        min: minStartDt.format('yyyy-MM-DDTHH:mm'),
                      },
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl variant="outlined" fullWidth>
                  <InputLabel id="client-notification-type">
                    Client Notification Type
                  </InputLabel>
                  <Select
                    labelId="client-notification-type"
                    placeholder="Client Notification Type"
                    labelWidth={200}
                    value={clientNotificationType || ''}
                    onChange={(event) => {
                      setClientNotificationType(event.target.value);
                    }}
                    style={{ width: '100%' }}
                  >
                    <MenuItem
                      key="client-notification-type-email"
                      value="Email"
                    >
                      Email
                    </MenuItem>
                    <MenuItem
                      key="client-notification-type-phone"
                      value="Phone"
                    >
                      Phone
                    </MenuItem>
                    <MenuItem key="client-notification-type-text" value="Text">
                      Text
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Collapse>
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={internalOnlyFlg}
                  label="Is Internal Only?"
                  onChange={(event) => {
                    setInternalOnlyFlg(!internalOnlyFlg);
                  }}
                  fullWidth
                />
              }
              label="Hide this note from external reports"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button disabled={loading} onClick={() => props.onClose()}>
          Cancel
        </Button>
        <Button
          onClick={() => submit()}
          variant="contained"
          color="primary"
          disabled={
            loading ||
            !title ||
            !notes ||
            !startDt ||
            (endDt && endDt < startDt) ||
            moment(startDt).isBefore(minStartDt) ||
            !ticketTypeId
          }
        >
          Save
          {loading ? (
            <CircularProgress style={{ position: 'absolute' }} />
          ) : null}
        </Button>
      </DialogActions>
      {newTicketTypeDialogOpen ? (
        <CreateOMTicketTypeDialog
          onClose={() => {
            setNewTicketTypeDialogOpen(false);
            fetchOMTicketTypes();
          }}
          onSuccess={(newTicketTypeId) => {
            setTicketTypeId(newTicketTypeId);
          }}
        />
      ) : null}
    </Dialog>
  );
};

function withMobileDialog(WrappedComponent) {
  return (props) => (
    <WrappedComponent
      {...props}
      fullScreen={useMediaQuery(useTheme().breakpoints.down('sm'))}
    />
  );
}

const CreateOMTicketTypeDialog = withMobileDialog((props) => {
  const [name, setName] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();
  const { fullScreen } = props;

  const createTicketType = () => {
    setLoading(true);
    dataProvider
      .create('OMTicketType', {
        data: {
          name,
        },
      })
      .then((res) => {
        setLoading(false);
        props.onSuccess(res.data.id);
        props.onClose();
      });
  };

  return (
    <Dialog open fullScreen={fullScreen}>
      <DialogContent>
        <Grid item xs={12}>
          <TextField
            label="Ticket Type"
            value={name || ''}
            required
            onChange={(event) => {
              setName(event.target.value);
            }}
            fullWidth
          />
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button disabled={loading} onClick={() => props.onClose()}>
          Cancel
        </Button>
        <Button
          onClick={() => createTicketType()}
          variant="contained"
          color="primary"
          disabled={loading || !name || name?.length < 2}
        >
          Save
          {loading ? (
            <CircularProgress style={{ position: 'absolute' }} />
          ) : null}
        </Button>
      </DialogActions>
    </Dialog>
  );
});
