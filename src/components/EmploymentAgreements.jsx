import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';

import {
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  FormDataConsumer,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  Toolbar,
  useDataProvider,
  useNotify,
  usePermissions,
  useRecordContext,
  useRedirect,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Button,
  Chip,
  CircularProgress,
  Collapse,
  Grid,
  Typography,
} from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CheckCircle, CloudDownload, CloudUpload } from '@mui/icons-material';
import { uploadObjectToS3 } from '../utils/aws';
import moment from 'moment';
import theme from '../theme';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Employment Agreement';

export const EmploymentAgreementEdit = () => {
  const [documentUploading, setDocumentUploading] = useState(false);
  const [offerDocumentUploading, setOfferDocumentUploading] = useState(false);
  const { id } = useParams();
  const refresh = useRefresh();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  const uploadToS3 = (event, formData, offerDoc) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Document must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `EmploymentAgreements/${formData.employee.id}_${
          offerDoc ? 'OfferLetter_' : ''
        }${formData.startDt}_${moment().valueOf()}_${file.name}`
      );
      offerDoc ? setOfferDocumentUploading(true) : setDocumentUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_BUCKET_HUMAN_RESOURCES
      ).then(
        () => {
          const data = offerDoc
            ? {
                id: parseInt(id, 10),
                offerAwsObjectKey: awsObjectKey,
              }
            : {
                id: parseInt(id, 10),
                agreementAwsObjectKey: awsObjectKey,
              };
          dataProvider
            .update('EmploymentAgreement', {
              data,
            })
            .then(
              (res) => {
                notify('Document uploaded', { type: 'success' });
                setDocumentUploading(false);
                setOfferDocumentUploading(false);
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading document', { type: 'error' });
                setDocumentUploading(false);
                setOfferDocumentUploading(false);
                refresh();
              }
            );
        },
        (e) => {
          notify('Error uploading document', { type: 'error' });
        }
      );
    }
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="employee.id"
              reference="EmployeeLite"
              perPage={10000}
              sort={{ field: 'firstName', order: 'ASC' }}
            >
              <SelectInput
                optionText="fullName"
                label="Employee"
                fullWidth
                required
              />
            </ReferenceInput>
            <BooleanInput
              source="employmentAgreementFlg"
              helperText="Check if this is an initial employment agreement (leave off if an employment 'plan')"
            />
            <DateInput
              source="startDt"
              fullWidth
              required
              label="Effective dt"
            />
            <DateInput
              source="endDt"
              fullWidth
              helperText="Leave blank if no end date is specified in the employment agreement."
            />
            <CustomNumberInput source="totalSalary" fullWidth required />
            <CustomNumberInput source="cashSalary" fullWidth required />
            <CustomNumberInput
              source="equityCompensation"
              fullWidth
              required
              helperText="Amount of salary taken in the form of equity (do not factor in multiplier)."
            />
            <CustomNumberInput
              source="equityMultiplier"
              label="Equity Compensation Multiplier"
              helperText="Amount to multiply the equity compensation by (ie: 1.4, 1, etc.)"
            />
            <SelectInput
              placeholder="Select Salary Basis"
              source="salaryBasis"
              helperText="The time period the salary is based on."
              style={{ width: '100%' }}
              choices={[
                { id: 'hourly', name: 'hourly' },
                { id: 'monthly', name: 'monthly' },
                { id: 'yearly', name: 'yearly' },
              ]}
            />
            <SelectInput
              placeholder="Select Salary Currency"
              source="currencyCode"
              style={{ width: '100%' }}
              choices={[
                { id: 'USD', name: 'USD' },
                { id: 'BRL', name: 'BRL' },
              ]}
            />
            <FunctionField
              label="Download"
              render={(record) => {
                return (
                  <Grid container spacing={2}>
                    <Grid item>
                      <Button
                        disabled={!record.agreementDownloadUrl}
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{ textTransform: 'none' }}
                        onClick={() =>
                          window.location.assign(record.agreementDownloadUrl)
                        }
                      >
                        Download Employment Agreement
                      </Button>
                    </Grid>
                    <Grid item>
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          return (
                            <Collapse
                              in={
                                formData.employee?.id &&
                                formData.startDt &&
                                (formData.totalSalary ||
                                  formData.totalSalary === 0) &&
                                (formData.cashSalary ||
                                  formData.cashSalary === 0) &&
                                (formData.equityCompensation ||
                                  formData.equityCompensation === 0)
                              }
                            >
                              <Button
                                color="primary"
                                variant="contained"
                                component="label" // https://stackoverflow.com/a/54043619
                                startIcon={<CloudUpload />}
                                style={{ textTransform: 'none' }}
                                disabled={documentUploading}
                              >
                                {documentUploading ? (
                                  <CircularProgress
                                    style={{ position: 'absolute' }}
                                  />
                                ) : null}
                                {record.agreementDownloadUrl
                                  ? 'Overwrite Employment Agreement'
                                  : 'Upload Employment Agreement'}
                                <input
                                  type="file"
                                  hidden
                                  onChange={(event) =>
                                    uploadToS3(event, formData)
                                  }
                                  accept="application/pdf"
                                />
                              </Button>
                              <Typography variant="body2">
                                This should be a PDF
                              </Typography>
                            </Collapse>
                          );
                        }}
                      </FormDataConsumer>
                    </Grid>
                  </Grid>
                );
              }}
            />
            <FunctionField
              label="Download"
              render={(record) => {
                return (
                  <Grid container spacing={2}>
                    <Grid item>
                      <Button
                        disabled={!record.offerDownloadUrl}
                        variant="contained"
                        startIcon={<CloudDownload />}
                        style={{ textTransform: 'none' }}
                        onClick={() =>
                          window.location.assign(record.offerDownloadUrl)
                        }
                      >
                        Download Offer Letter
                      </Button>
                    </Grid>
                    <Grid item>
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          return (
                            <Collapse
                              in={
                                formData.employee?.id &&
                                formData.startDt &&
                                (formData.totalSalary ||
                                  formData.totalSalary === 0) &&
                                (formData.cashSalary ||
                                  formData.cashSalary === 0) &&
                                (formData.equityCompensation ||
                                  formData.equityCompensation === 0)
                              }
                            >
                              <Button
                                color="primary"
                                variant="contained"
                                component="label" // https://stackoverflow.com/a/54043619
                                startIcon={<CloudUpload />}
                                style={{ textTransform: 'none' }}
                                disabled={offerDocumentUploading}
                              >
                                {offerDocumentUploading ? (
                                  <CircularProgress
                                    style={{ position: 'absolute' }}
                                  />
                                ) : null}
                                {record.offerDownloadUrl
                                  ? 'Overwrite Offer Letter'
                                  : 'Upload Offer Letter'}
                                <input
                                  type="file"
                                  hidden
                                  onChange={(event) =>
                                    uploadToS3(event, formData, true)
                                  }
                                  accept="application/pdf"
                                />
                              </Button>
                              <Typography variant="body2">
                                This should be a PDF
                              </Typography>
                            </Collapse>
                          );
                        }}
                      </FormDataConsumer>
                    </Grid>
                  </Grid>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CurrencyNumberField = ({ source }) => {
  const record = useRecordContext();
  return (
    <NumberField
      options={{ style: 'currency', currency: record.currencyCode || 'USD' }}
      source={source}
    />
  );
};

export const EmploymentAgreementList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <FunctionField
          label="Type"
          render={(record) => {
            return record.employmentAgreementFlg ? (
              <Chip
                label="Agreement"
                clickable={false}
                color="primary"
                style={{ background: theme.palette.primary.main }}
              />
            ) : (
              <Chip
                label="Plan"
                clickable={false}
                // color="primary"
                // style={{ background: theme.palette.primary.main }}
              />
            );
          }}
        />
        <LinkField
          reference="Employee"
          linkSource="employee.id"
          labelSource="employee.fullName"
          label="Employee"
        />
        <DateField source="startDt" label="Effective dt" />
        <DateField source="endDt" />
        <CurrencyNumberField textAlign="right" source="totalSalary" />
        <CurrencyNumberField textAlign="right" source="cashSalary" />
        <CurrencyNumberField textAlign="right" source="equityCompensation" />
        <NumberField
          source="equityMultiplier"
          label="Equity Compensation Multiplier"
        />
        <CurrencyNumberField
          textAlign="right"
          source="totalEquityCompensation"
        />
        <TextField source="salaryBasis" label="Salary Basis" />
        <FunctionField
          label="Download"
          render={(record) => (
            <Button
              variant="contained"
              color="primary"
              startIcon={<CloudDownload />}
              onClick={(e) => {
                e.stopPropagation();
                window.location.assign(record.agreementDownloadUrl);
              }}
              disabled={!record.agreementDownloadUrl}
              style={{ textTransform: 'none' }}
            >
              Download
            </Button>
          )}
        />
        <FunctionField
          label="Download"
          render={(record) =>
            record.offerDownloadUrl && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<CloudDownload />}
                onClick={(e) => {
                  e.stopPropagation();
                  window.location.assign(record.offerDownloadUrl);
                }}
                disabled={!record.offerDownloadUrl}
                style={{ textTransform: 'none' }}
              >
                Download
              </Button>
            )
          }
        />
      </Datagrid>
    </List>
  );
};

export const EmploymentAgreementCreate = (props) => {
  const [awsObjectKey, setAwsObjectKey] = useState(null);
  const [offerAwsObjectKey, setOfferAwsObjectKey] = useState(null);
  const [documentUploading, setDocumentUploading] = useState(false);
  const [offerDocumentUploading, setOfferDocumentUploading] = useState(false);

  const notify = useNotify();

  const MyCreateButton = () => {
    const dataProvider = useDataProvider();
    const { getValues } = useFormContext();
    const redirect = useRedirect();
    const { id, ...data } = getValues();

    const handleClick = (e) => {
      e.preventDefault(); // necessary to prevent default SaveButton submit logic
      data.agreementAwsObjectKey = awsObjectKey;
      data.offerAwsObjectKey = offerAwsObjectKey;
      dataProvider.create('EmploymentAgreement', { data }).then(
        () => {
          notify('Element created');
          if (props?.onSuccess) {
            props.onSuccess();
          } else {
            redirect('/EmploymentAgreement');
          }
        },
        (e) => {
          notify(e.message, { type: 'error' });
        }
      );
    };

    return (
      <Toolbar>
        <SaveButton
          type="button"
          onClick={handleClick}
          disabled={
            !awsObjectKey ||
            !data.startDt ||
            !data.employee?.id ||
            (!data.totalSalary && data.totalSalary !== 0) ||
            (!data.equityCompensation && data.equityCompensation !== 0) ||
            (!data.cashSalary && data.cashSalary !== 0)
          }
        />
      </Toolbar>
    );
  };

  const uploadToS3 = (event, formData, offerDoc) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Document must be less than 10MB', { type: 'error' });
        return;
      }
      // const aFileName = file.name.split('.');
      // const fileExtension =
      //   aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `EmploymentAgreements/${formData.employee.id}_${
          offerDoc ? 'OfferLetter_' : ''
        }${formData.startDt}_${moment().valueOf()}_${file.name}`
      );
      offerDoc ? setOfferDocumentUploading(true) : setDocumentUploading(true);
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_BUCKET_HUMAN_RESOURCES
      ).then(
        () => {
          if (offerDoc) {
            setOfferDocumentUploading(false);
            setOfferAwsObjectKey(awsObjectKey);
          } else {
            setAwsObjectKey(awsObjectKey);
            setDocumentUploading(false);
          }
        },
        (e) => {
          console.error(e);
          notify('Error uploading invoice to S3', { type: 'error' });
          setDocumentUploading(false);
        }
      );
    }
  };

  const isComponentWithinDialog = !!props?.withinDialog;

  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm toolbar={<MyCreateButton />}>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={isComponentWithinDialog ? 12 : 6}>
            <ReferenceInput
              source="employee.id"
              reference="EmployeeLite"
              perPage={10000}
              sort={{ field: 'firstName', order: 'ASC' }}
              filter={
                props?.employeeId ? { id: parseInt(props.employeeId, 10) } : {}
              }
            >
              <SelectInput
                optionText="fullName"
                label="Employee"
                fullWidth
                required
              />
            </ReferenceInput>
            <BooleanInput
              source="employmentAgreementFlg"
              helperText="Check if this is an initial employment agreement (leave off if an employment 'plan')"
            />
            <DateInput
              source="startDt"
              required
              fullWidth
              label="Effective dt"
            />
            <DateInput
              source="endDt"
              fullWidth
              helperText="Leave blank if no end date is specified in the employment agreement."
            />
            <CustomNumberInput source="totalSalary" required fullWidth />
            <CustomNumberInput source="cashSalary" required fullWidth />

            <CustomNumberInput
              source="equityCompensation"
              required
              fullWidth
              helperText="Amount of salary taken in the form of equity (do not factor in multiplier)."
            />
            <CustomNumberInput
              source="equityMultiplier"
              required
              fullWidth
              helperText="Amount to multiply the equity compensation by (ie: 1.4, 1, etc.)"
            />
            <SelectInput
              placeholder="Select Salary Basis"
              source="salaryBasis"
              helperText="The time period the salary is based on."
              style={{ width: '100%' }}
              required
              choices={[
                { id: 'hourly', name: 'hourly' },
                { id: 'monthly', name: 'monthly' },
                { id: 'yearly', name: 'yearly' },
              ]}
            />
            <SelectInput
              placeholder="Select Salary Currency"
              source="currencyCode"
              style={{ width: '100%' }}
              required
              choices={[
                { id: 'USD', name: 'USD' },
                { id: 'BRL', name: 'BRL' },
              ]}
            />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Collapse
                    in={
                      !!(
                        formData.employee?.id &&
                        formData.startDt &&
                        (formData.totalSalary || formData.totalSalary === 0) &&
                        (formData.equityCompensation ||
                          formData.equityCompensation === 0) &&
                        (formData.cashSalary || formData.cashSalary === 0)
                      )
                    }
                  >
                    {awsObjectKey ? (
                      <Grid container alignItems="center">
                        <Grid item>
                          <CheckCircle
                            style={{ color: theme.palette.success.main }}
                          />
                        </Grid>
                        <Grid item>
                          <Typography
                            style={{
                              color: theme.palette.success.main,
                              paddingLeft: '.5rem',
                            }}
                          >
                            Click 'Save' below to complete upload.
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <Button
                        color="primary"
                        disabled={awsObjectKey || documentUploading}
                        variant="contained"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        {documentUploading ? (
                          <CircularProgress style={{ position: 'absolute' }} />
                        ) : null}
                        Upload Employment Agreement Document
                        <input
                          type="file"
                          hidden
                          onChange={(event) => uploadToS3(event, formData)}
                          accept="application/pdf"
                        />
                      </Button>
                    )}
                  </Collapse>
                );
              }}
            </FormDataConsumer>
            <br />
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                return (
                  <Collapse
                    in={
                      !!(
                        formData.employee?.id &&
                        formData.startDt &&
                        (formData.totalSalary || formData.totalSalary === 0) &&
                        (formData.equityCompensation ||
                          formData.equityCompensation === 0) &&
                        (formData.cashSalary || formData.cashSalary === 0)
                      )
                    }
                  >
                    {offerAwsObjectKey ? (
                      <Grid container alignItems="center">
                        <Grid item>
                          <CheckCircle
                            style={{ color: theme.palette.success.main }}
                          />
                        </Grid>
                        <Grid item>
                          <Typography
                            style={{
                              color: theme.palette.success.main,
                              paddingLeft: '.5rem',
                            }}
                          >
                            Click 'Save' below to complete upload.
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <Button
                        color="primary"
                        disabled={offerAwsObjectKey || offerDocumentUploading}
                        variant="contained"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        {offerDocumentUploading ? (
                          <CircularProgress style={{ position: 'absolute' }} />
                        ) : null}
                        Upload Offer Letter
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            uploadToS3(event, formData, true)
                          }
                          accept="application/pdf"
                        />
                      </Button>
                    )}
                  </Collapse>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
