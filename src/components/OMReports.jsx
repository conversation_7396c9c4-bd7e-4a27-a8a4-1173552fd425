import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>yField,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberInput,
  ReferenceArrayInput,
  ReferenceInput,
  SelectArrayInput,
  SelectInput,
  SimpleForm,
  SingleFieldList,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Button, CircularProgress, Grid } from '@mui/material';

import { CustomReferenceField, DetailField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { CloudDownload } from '@mui/icons-material';

const entityName = 'O&M Dispatch Report';

export const OMReportEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceArrayInput
              source="employeeIds"
              reference="EmployeeLite"
              helperText="These are the employees present at this service report"
              fullWidth
              sort={{ field: 'firstName', order: 'ASC' }}
              perPage={10000}
            >
              <SelectArrayInput optionText="fullName" fullWidth />
            </ReferenceArrayInput>
            <DateTimeInput source="startDt" fullWidth required />
            <DateTimeInput source="endDt" fullWidth />
            <ReferenceInput
              source="omTicket.id"
              reference="OMTicket"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
              required
            >
              <SelectInput
                optionText="title"
                label="OM Ticket"
                fullWidth
                required
              />
            </ReferenceInput>
            <TextInput
              source="notes"
              label="Diagnostic Procedure Notes"
              fullWidth
              multiline
            />
            <TextInput
              source="ppeNotes"
              label="PPE Level and Gear Notes"
              fullWidth
              multiline
            />
            <TextInput
              source="partsUsedNotes"
              label="Parts Used Notes"
              fullWidth
              multiline
            />
            <TextInput
              source="partsOrderedNotes"
              label="Parts Ordered Notes"
              fullWidth
              multiline
            />
            <TextInput
              source="rmaNotes"
              label="RMA Notes"
              fullWidth
              multiline
            />
            <ReferenceInput
              source="omReportType.id"
              reference="OMReportType"
              perPage={10000}
              sort={{ field: 'id', order: 'ASC' }}
              required
            >
              <SelectInput
                optionText="name"
                label="OM Report Type"
                fullWidth
                required
              />
            </ReferenceInput>
            <BooleanInput source="resolvedFlg" fullWidth />
            <ReferenceInput
              source="submittedByEmployee.id"
              reference="EmployeeLite"
              perPage={10000}
              sort={{ field: 'firstName', order: 'ASC' }}
              required
            >
              <SelectInput
                optionText="fullName"
                label="Submitted by Employee"
                fullWidth
              />
            </ReferenceInput>
            <DateTimeInput source="submittedDt" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <NumberInput source="omTicketId" alwaysOn label="Ticket #" />
  </Filter>
);

export const OMReportList = () => {
  const { permissions } = usePermissions();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const [loading, setLoading] = useState(false);

  const styleRow = (record, index) => {
    const { omReportType, omTicket } = record;
    const warningStyle = {
      backgroundColor: 'lightyellow',
    };
    if (
      omReportType?.name &&
      omTicket?.omTicketType?.name &&
      omReportType?.name !== omTicket?.omTicketType?.name
    ) {
      return warningStyle;
    }
    return {};
  };

  const downloadReportPdf = async (omReportId) => {
    setLoading(true);
    dataProvider.create('OMReportPdf', { data: { id: omReportId } }).then(
      (res) => {
        window.location.assign(res.data.downloadUrl);
        setLoading(false);
      },
      (err) => {
        console.error(err);
        notify(`Error downloading report`, { type: 'error' });
        setLoading(false);
      }
    );
  };

  return (
    <List title={entityName} perPage={25} filters={<CustomFilter />}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
        rowStyle={styleRow}
      >
        <TextField source="id" />
        <LinkField
          label="Ticket #"
          linkSource="omTicket.id"
          labelSource="omTicket.ticketNumber"
          reference="OMTicket"
          sortable={false}
        />
        <LinkField
          label="OM Ticket"
          linkSource="omTicket.id"
          labelSource="omTicket.title"
          reference="OMTicket"
          sortable={false}
        />
        <LinkField
          label="Report Type"
          linkSource="omReportType.id"
          labelSource="omReportType.name"
          reference="OMReportType"
          sortable={false}
        />
        <LinkField
          label="Project"
          linkSource="omTicket.project.id"
          labelSource="omTicket.project.name"
          reference="Project"
          sortable={false}
        />
        <ArrayField source="employees" sortable={false}>
          <SingleFieldList>
            <CustomReferenceField
              // color={(resource) => (resource.isPublic ? 'primary' : 'default')}
              source="fullName"
            />
          </SingleFieldList>
        </ArrayField>
        <DetailField
          source="notes"
          label="Diagnostic Procedure Notes"
          sortable={false}
        />
        <DetailField
          source="ppeNotes"
          label="PPE Level and Gear Notes"
          sortable={false}
        />
        <DetailField source="partsUsedNotes" sortable={false} />
        <DetailField source="partsOrderedNotes" sortable={false} />
        <DetailField source="rmaNotes" label="RMA Notes" sortable={false} />
        <DateField source="startDt" showTime />
        <DateField source="endDt" showTime />
        <BooleanField source="resolvedFlg" />
        <BooleanField source="thirdPartyFlg" />
        <TextField source="thirdPartyCompanyName" />
        <BooleanField source="hasThirdPartyEnglishReport" sortable={false} />
        <BooleanField source="hasThirdPartyPortugueseReport" sortable={false} />
        <LinkField
          label="Submitted By Employee"
          linkSource="submittedByEmployee.id"
          labelSource="submittedByEmployee.fullName"
          reference="Employee"
        />
        <DateField source="submittedDt" showTime />
        <FunctionField
          label="Download Report"
          render={(record) => {
            const { id } = record;
            return (
              <Button
                color="primary"
                variant="contained"
                style={{ textTransform: 'none' }}
                onClick={(event) => {
                  event.stopPropagation();
                  downloadReportPdf(id);
                }}
                disabled={loading}
                startIcon={<CloudDownload />}
              >
                Download
                {loading ? (
                  <CircularProgress style={{ position: 'absolute' }} />
                ) : null}
              </Button>
            );
          }}
        />
      </Datagrid>
    </List>
  );
};

export const OMReportCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <DateTimeInput source="startDt" fullWidth required />
          <DateTimeInput source="endDt" fullWidth />
          <ReferenceArrayInput
            source="employeeIds"
            reference="EmployeeLite"
            helperText="These are the employees present at this service report"
            fullWidth
            sort={{ field: 'firstName', order: 'ASC' }}
            perPage={10000}
          >
            <SelectArrayInput optionText="fullName" fullWidth />
          </ReferenceArrayInput>
          <TextInput
            source="notes"
            label="Diagnostic Procedure Notes"
            fullWidth
            multiline
          />
          <TextInput
            source="ppeNotes"
            label="PPE Level and Gear Notes"
            fullWidth
            multiline
          />
          <TextInput
            source="partsUsedNotes"
            label="Parts Used Notes"
            fullWidth
            multiline
          />
          <TextInput
            source="partsOrderedNotes"
            label="Parts Ordered Notes"
            fullWidth
            multiline
          />
          <TextInput source="rmaNotes" label="RMA Notes" fullWidth multiline />
          <ReferenceInput
            source="omTicket.id"
            reference="OMTicket"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <SelectInput
              optionText="title"
              label="OM Ticket"
              fullWidth
              required
            />
          </ReferenceInput>
          <ReferenceInput
            source="omReportType.id"
            reference="OMReportType"
            perPage={10000}
            sort={{ field: 'id', order: 'ASC' }}
            required
          >
            <SelectInput
              optionText="name"
              label="OM Report Type"
              fullWidth
              required
            />
          </ReferenceInput>
          <BooleanInput source="resolvedFlg" fullWidth />
          <ReferenceInput
            source="submittedByEmployee.id"
            reference="EmployeeLite"
            perPage={10000}
            sort={{ field: 'firstName', order: 'ASC' }}
            required
          >
            <SelectInput
              optionText="fullName"
              label="Submitted by Employee"
              fullWidth
              required
            />
          </ReferenceInput>
          <DateTimeInput source="submittedDt" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
