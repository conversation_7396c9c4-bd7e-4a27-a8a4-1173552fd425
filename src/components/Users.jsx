import React, { useState, lazy, Suspense } from 'react';
import numeral from 'numeral';
import { Link as ReactRouterLink, useParams } from 'react-router-dom';

import {
  ArrayField,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  EmailField,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  List,
  NumberField,
  Pagination,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TabbedFormTabs,
  TextField,
  TextInput,
  SingleFieldList,
  UrlField,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useRecordContext,
  useResourceDefinition,
  // CreateButton,
  ExportButton,
  useListContext,
  TopToolbar,
  // ArrayInput,
  // SimpleFormIterator,
  // ReferenceInput,
} from 'react-admin';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActionArea,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Fab,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  Link,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  TableHead,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import MuiList from '@mui/material/List';
import MuiTextField from '@mui/material/TextField';
import {
  AccountBalance,
  Check,
  Clear,
  CheckCircle,
  Close,
  Delete,
  Email,
  ExpandMore,
  Help,
  Person,
  PhoneCallback,
  PhoneForwarded,
  RadioButtonUnchecked,
  Work,
  Launch,
  ArrowForward,
  AccountBalanceWallet,
  OpenInNew,
} from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import { Image, Transformation } from 'cloudinary-react';

import {
  CustomNumberInput,
  CustomReferenceField,
  LinkField,
} from './CustomFields';
import { CustomTopToolbar } from './CustomTopToolbar';
// import { UserNotificationPreferences } from './UserNotificationPreferences';
import { UserAuthenticationDocuments } from './UserAuthenticationDocuments';
import { UserDocuments } from './UserDocuments';
import { UserBeneficiaries } from './UserBeneficiaries';
import { UserTransfers } from './UserTransfers';
import { UserSellOrders } from './UserSellOrders';
import { getEditable } from '../utils/applyRoleAuth';
import { openUploadWidget } from '../utils/CloudinaryService';
import { uploadObjectToS3 } from '../utils/aws';

import Config from '../config/config';
import theme from '../theme';
import { getUUID, lintAwsObjectKey } from '../utils/global';
import moment from 'moment';
import UserPerformanceSummary from './UserPerformanceSummary';
import { downloadSimpleExcelFromRows } from '../utils/excel';

const UserMap = lazy(() => import('./UserMap'));
const entityName = 'User';

const Title = () => {
  const record = useRecordContext();
  return (
    <span>
      {entityName}
      {record?.id ? ` #${record.id} - ${record.fullName}` : ''}
    </span>
  );
};

const getCommIcon = (event) => {
  const { eventSource } = event;
  let jsx;
  switch (eventSource) {
    case 'incomingCall':
      jsx = <PhoneCallback />;
      break;
    case 'outgoingCall':
      jsx = <PhoneForwarded />;
      break;
    case 'emailSent':
      jsx = <Email />;
      break;
    default:
      jsx = <Help />;
      break;
  }
  return jsx;
};

const UserFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name, Last Name, or Email"
      source="q"
      alwaysOn
    />
    <SelectInput
      style={{ minWidth: '200px' }}
      label="User Account Status"
      source="userAccountStatus"
      choices={[
        { id: 'all', name: 'All' },
        { id: 'completed', name: 'Completed' },
        { id: 'notCompleted', name: 'Not Completed' },
      ]}
    />
    <SelectInput
      style={{ minWidth: '200px' }}
      label="User Type"
      source="type"
      choices={[
        { id: 'personal', name: 'personal' },
        { id: 'business', name: 'business' },
      ]}
    />
  </Filter>
);
const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);

export const UserList = () => {
  const { permissions } = usePermissions();
  const { name } = useResourceDefinition();
  return (
    <List
      sort={{ field: 'id', order: 'DESC' }}
      perPage={25}
      pagination={<CustomPagination />}
      filters={<UserFilter />}
      actions={<CustomTopToolbar hideExportButton={true} />}
    >
      <Datagrid rowClick={getEditable(name, permissions) ? 'edit' : 'show'}>
        <TextField source="id" />
        <TextField source="type" />
        <TextField source="intendedFundingSrcType" />
        <LinkField
          label="Lead Source"
          linkSource="hubSpotLeadSource.id"
          labelSource="hubSpotLeadSource.name"
          reference="HubSpotLeadSource"
        />
        <TextField label="First Name" source="firstName" />
        <TextField label="Last Name" source="lastName" />
        <EmailField sortable={false} label="Email" source="email" />
        <BooleanField
          label="Is Permanently Suspended"
          source="permSuspendedFlg"
        />
        <FunctionField
          label="Dwolla Status"
          sortable={false}
          render={(record) => {
            let color;
            if (!record.dwollaCustomer) {
              color = null;
            } else if (
              record.requiresBeneficialOwnership &&
              record.beneficialOwnershipIncomplete
            ) {
              color = 'brown';
            } else if (record.dwollaCustomer.status === 'verified') {
              color = 'green';
            } else if (record.dwollaCustomer.status === 'document') {
              color = 'orange';
            } else if (record.dwollaCustomer.status === 'retry') {
              color = 'orange';
            } else if (record.dwollaCustomer.status === 'suspended') {
              color = 'red';
            }
            return (
              <span style={{ color, fontWeight: 'bold' }}>
                {record.dwollaCustomer && record.dwollaCustomer.status}
                {record.requiresBeneficialOwnership &&
                record.beneficialOwnershipIncomplete
                  ? ' - Pending Ownership'
                  : ''}
              </span>
            );
          }}
        />
        <NumberField
          label="Total Invested"
          source="investmentSum"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Initial Investment"
          source="initialInvestmentValue"
          sortable={false}
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Total Dividends"
          source="dividendSum"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Total Sold"
          source="soldShareValue"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          sortable={false}
          label="Cost Basis"
          source="costBasis"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          sortable={false}
          label="NAV"
          source="nav"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <FunctionField
          label="IRR (w/ NAV)"
          sortable={false}
          textAlign="right"
          render={(record) => (
            <Typography
              variant="body2"
              style={{
                color: record.showIRR ? '#000' : 'rgba(0,0,0,.4)',
              }}
            >
              {record.navBasedIRR}%
            </Typography>
          )}
        />
        <FunctionField
          label="Est. IRR"
          sortable={false}
          textAlign="right"
          render={(record) => (
            <Typography
              variant="body2"
              style={{
                color: record.showIRR ? '#000' : 'rgba(0,0,0,.4)',
              }}
            >
              {record.estimatedIRR}%
            </Typography>
          )}
        />
        <FunctionField
          label="Annualized COC Yield"
          sortable={false}
          textAlign="right"
          render={(record) => (
            <Typography
              variant="body2"
              style={{
                color: record.showIRR ? '#000' : 'rgba(0,0,0,.4)',
              }}
            >
              {record.annualizedCOCYield}%
            </Typography>
          )}
        />
        <FunctionField
          label="COC Yield"
          sortable={false}
          textAlign="right"
          render={(record) => (
            <Typography
              variant="body2"
              style={{
                color: record.showIRR ? '#000' : 'rgba(0,0,0,.4)',
              }}
            >
              {record.COCYield}%
            </Typography>
          )}
        />
        <FunctionField
          label="Dwolla Balance"
          sortable={false}
          sortBy="value"
          textAlign="right"
          render={(record) =>
            numeral(record.dwollaBalance && record.dwollaBalance.value).format(
              '$0,0.00'
            )
          }
        />
        <FunctionField
          label="HubSpot Profile"
          textAlign="center"
          render={({ hubSpotContactUrl }) => {
            if (!hubSpotContactUrl) return null;
            return (
              <a
                target="_blank"
                onClick={(e) => e.stopPropagation()}
                href={hubSpotContactUrl}
              >
                <OpenInNew />
              </a>
            );
          }}
        />
        <FunctionField
          label="Dwolla Profile"
          textAlign="center"
          render={({ dwollaCustomer }) => {
            if (!dwollaCustomer?.id) return null;
            return (
              <a
                target="_blank"
                onClick={(e) => e.stopPropagation()}
                href={dwollaCustomer.customerUrl}
              >
                <OpenInNew />
              </a>
            );
          }}
        />
        <NumberField
          label="Environmental (0) - Returns (100)"
          source="environmentalImpactVsReturns"
        />
        <BooleanField
          sortable={false}
          label="Has Funding Source?"
          source="hasBankAccount"
        />
        <BooleanField
          label="Substantive Relationship"
          source="substantiveRelationshipFlg"
        />
        <BooleanField label="Advisory Council" source="advisoryCouncilFlg" />
        <DateField label="Verified" source="verifiedDt" />
        <DateField label="Email Confirmed" source="confirmationDt" />
        <DateField label="Customer Since" source="createdAt" />
        <BooleanField
          label="Has Corporate Shares"
          source="hasCorporateShares"
          sortable={false}
        />
      </Datagrid>
    </List>
  );
};

const userMerchExporter = (rows) => {
  const keys = [];
  const columns = [
    {
      label: 'Nickname',
      value: (row) => row.firstName,
    },
    {
      label: 'FullName',
      value: (row) => `${row.firstName} ${row.lastName}`,
    },
    {
      label: 'FirstName',
      value: (row) => row.firstName,
    },
    {
      label: 'LastName',
      value: (row) => row.lastName,
    },
    {
      label: 'Title',
      value: (row) => '',
    },
    {
      label: 'Company',
      value: (row) => '',
    },
    {
      label: 'Department',
      value: (row) => '',
    },
    {
      label: 'AddressOne',
      value: (row) => row.merchAddress1,
    },
    {
      label: 'AddressTwo',
      value: (row) => row.merchAddress2,
    },
    {
      label: 'City',
      value: (row) => row.merchCity,
    },
    {
      label: 'State',
      value: (row) => row.merchState,
    },
    {
      label: 'Zip',
      value: (row) => row.merchPostalCode,
    },
    {
      label: 'PhoneNumber',
      value: (row) => '',
    },
    {
      label: 'ExtensionNumber',
      value: (row) => '',
    },
    {
      label: 'FAXNumber',
      value: (row) => '',
    },
    {
      label: 'PagerNumber',
      value: (row) => '',
    },
    {
      label: 'MobilePhoneNumber',
      value: (row) => '',
    },
    {
      label: 'CountryCode',
      value: (row) => row.merchCountryCode,
    },
    {
      label: 'EmailAddress',
      value: (row) => row.email,
    },
    {
      label: 'Net Asset Value',
      value: (row) => row.nav,
    },
    { label: 'Shirt Size', value: (row) => row.shirtSize },
  ];
  const rowsForExport = rows.map((row) => {
    const returnRow = {};
    columns.forEach((column) => {
      returnRow[column.label] = column.value(row);
    });
    return returnRow;
  });
  return downloadSimpleExcelFromRows(
    rowsForExport,
    columns.map((col) => col.label),
    `FedEx Merch Export.xlsx`
  );
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <ExportButton label="FedEx Export" />
    </TopToolbar>
  );
};

export const UserMerchList = () => {
  const { permissions } = usePermissions();
  return (
    <List exporter={userMerchExporter} actions={<ListActions />}>
      <Datagrid>
        {/* <TextField source="id" /> */}
        <TextField source="firstName" />
        <TextField source="lastName" />
        <TextField source="fullName" sortable={false} />
        <TextField source="merchAddress1" />
        <TextField source="merchAddress2" />
        <TextField source="merchCity" />
        <TextField source="merchPostalCode" />
        <TextField source="merchState" />
        <TextField source="merchCountryCode" />
        <TextField source="shirtSize" sortable={false} />
        <BooleanField source="merchOptOutFlg" />
        {/* <NumberField
          source="investmentSum"
          options={{ style: 'currency', currency: 'USD' }}
        /> */}
        <NumberField
          source="nav"
          label="Net asset value"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
      </Datagrid>
    </List>
  );
};

export const UserMarketingList = () => {
  const { permissions } = usePermissions();
  const { name } = useResourceDefinition();
  return (
    <List
      sort={{ field: 'id', order: 'DESC' }}
      perPage={25}
      pagination={<CustomPagination />}
      filters={<UserFilter />}
    >
      <Datagrid
        rowClick={(id, resource, record) => {
          const editable = getEditable(name, permissions);
          return editable ? `/User/${id}` : `/User/${id}/show`;
        }}
      >
        <TextField source="id" />
        <TextField source="type" />
        <TextField label="First Name" source="firstName" />
        <TextField label="Last Name" source="lastName" />
        <NumberField
          label="Total Invested"
          source="investmentSum"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <NumberField
          label="Total Dividends"
          source="dividendSum"
          options={{ style: 'currency', currency: 'USD' }}
        />
        <UrlField
          target="_blank"
          onClick={(e) => e.stopPropagation()}
          sortable={false}
          label="HubSpot Contact URL"
          source="hubSpotContactUrl"
        />
        <NumberField
          label="Environmental (0) - Returns (100)"
          source="environmentalImpactVsReturns"
        />
        <BooleanField
          sortable={false}
          label="Has Funding Source?"
          source="hasBankAccount"
        />
        <BooleanField
          label="Substantive Relationship"
          source="substantiveRelationshipFlg"
        />
        <ArrayField
          sortable={false}
          label="Portfolios Invested"
          source="userPortfoliosInvested"
        >
          <SingleFieldList>
            <CustomReferenceField
              linkOverride={({ id }) => `/portfolio/${id}`}
              source="name"
            />
          </SingleFieldList>
        </ArrayField>
        <DateField label="Email Confirmed" source="confirmationDt" />
        <DateField label="Customer Since" source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const UserEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const [documentType, setDocumentType] = useState('license');
  const [withdrawalFundsDialogOpen, setWithdrawalFundsDialogOpen] =
    useState(false);
  const [undoDividendId, setUndoDividendId] = useState(null);
  const [withdrawFundsDialogOpen, setWithdrawFundsDialogOpen] = useState(false);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [loading, setLoading] = useState(false);
  const [controllerPassportNumber, setControllerPassportNumber] =
    useState(null);
  const [controllerPassportCountryCode, setControllerPassportCountryCode] =
    useState(null);
  const [controllerPassportDialogOpen, setControllerPassportDialogOpen] =
    useState(false);
  const { permissions } = usePermissions();
  const { id } = useParams();
  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;
  const [amountTransferFound, setAmountTransferFound] = useState(0);
  const [toAccountId, setToAccountId] = useState('');

  const onPhotosUploaded = (aPhotos) => {
    const photos = aPhotos.map((photo) => {
      return {
        public_id: photo.public_id,
        userId: parseInt(id, 10),
      };
    });
    dataProvider
      .create('UserImage', {
        input: photos,
      })
      .then((record) => {
        notify('Image(s) successfully uploaded');
        refresh();
      })
      .catch((e) => {
        console.error(e);
        notify('Error updating images.', { type: 'error' });
      });
  };

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['users'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.user_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };
  const uploadBeneficialOwnerImageWithAWS = (
    event,
    beneficialOwnerId,
    userId
  ) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      const aFileName = file.name.split('.');
      const fileExtension =
        aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `${userId}/authDocs/${getUUID()}.${fileExtension}`
      );
      uploadObjectToS3(file, awsObjectKey).then(() =>
        onBeneficialOwnerPhotoUploaded(beneficialOwnerId, userId, awsObjectKey)
      );
    }
  };

  const undoDividend = () => {
    dataProvider
      .update('Dividend', {
        data: {
          undoDividend: true,
          id: undoDividendId,
        },
      })
      .then(
        (resp) => {
          notify('Dividend transfer reversed, and dividend deleted', {
            type: 'success',
          });
          refresh();
        },
        (err) => {
          const errorMsg = err?.body?.graphQLErrors?.[0]?.message;
          notify(errorMsg, { type: 'error' });
        }
      );
  };

  const onBeneficialOwnerPhotoUploaded = (
    beneficialOwnerDwollaId,
    userId,
    awsObjectKey
  ) => {
    dataProvider
      .update('DwollaDocument', {
        data: {
          awsObjectKey,
          documentType,
          beneficialOwnerDwollaId,
          userId,
        },
      })
      .then((record) => {
        notify('Image(s) successfully uploaded', { type: 'success' });
        refresh();
      })
      .catch((e) => {
        console.error(e);
        let errorMsg = e;
        if (e.graphQLErrors && e.graphQLErrors[0]) {
          errorMsg = e.graphQLErrors[0].message;
        }
        notify(`Error updating images. ${errorMsg}`, { type: 'error' });
      });
  };

  const uploadDwollaAuthImageWithAWS = (event, dwollaId, userId) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      const aFileName = file.name.split('.');
      const fileExtension =
        aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `${userId}/authDocs/${getUUID()}.${fileExtension}`
      );
      uploadObjectToS3(file, awsObjectKey).then(
        () => onDwollaAuthPhotosUploaded(dwollaId, userId, awsObjectKey),
        (e) => {
          console.error(e);
          let errorMsg = e;
          if (e.graphQLErrors && e.graphQLErrors[0]) {
            errorMsg = e.graphQLErrors[0].message;
          }
          notify(`Error uploading documents. ${errorMsg}`, { type: 'error' });
        }
      );
    }
  };

  const uploadMillenniumAuthImageWithAWS = (
    event,
    userId,
    documentType,
    authSessionId
  ) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('Image must be less than 10MB', { type: 'error' });
        return;
      }
      const aFileName = file.name.split('.');
      const fileExtension =
        aFileName.length > 1 ? aFileName[aFileName.length - 1] : 'png';
      const awsObjectKey = lintAwsObjectKey(
        `${userId}/authDocs/${getUUID()}.${fileExtension}`
      );
      uploadObjectToS3(file, awsObjectKey).then(() =>
        onMillenniumAuthPhotosUploaded(
          documentType,
          authSessionId,
          awsObjectKey
        )
      );
    }
  };

  const onDwollaAuthPhotosUploaded = (dwollaId, userId, awsObjectKey) => {
    dataProvider
      .update('DwollaDocument', {
        data: {
          awsObjectKey,
          documentType,
          dwollaId,
          userId,
        },
      })
      .then(() => {
        notify('Image(s) successfully uploaded', { type: 'success' });
        refresh();
      })
      .catch((e) => {
        console.error(e);
        let errorMsg = e;
        if (e.graphQLErrors && e.graphQLErrors[0]) {
          errorMsg = e.graphQLErrors[0].message;
        }
        notify(`Error updating images. ${errorMsg}`, { type: 'error' });
      });
  };

  const onMillenniumAuthPhotosUploaded = (
    documentType,
    authSessionId,
    awsObjectKey
  ) => {
    dataProvider
      .update('MillenniumTrustVerificationDocument', {
        data: {
          awsObjectKey,
          documentType,
          authSessionId,
        },
      })
      .then(() => {
        notify('Verification document successfully uploaded', {
          type: 'success',
        });
        refresh();
      })
      .catch((e) => {
        console.error(e);
        let errorMsg = e;
        if (e.graphQLErrors && e.graphQLErrors[0]) {
          errorMsg = e.graphQLErrors[0].message;
        }
        notify(`Error updating MT verification document. ${errorMsg}`, {
          type: 'error',
        });
      });
  };

  const deleteBeneficialOwner = (id) => {
    return () => dataProvider.delete('DwollaBeneficialOwner', { id });
  };
  const syncBeneficialOwner = (data) => {
    return () =>
      dataProvider
        .update('DwollaBeneficialOwner', {
          data,
        })
        .then(() => {
          notify('Successfully synced beneficial owner.');
        })
        .catch(() => {
          notify('Error syncing beneficial owner', { type: 'error' });
        });
  };

  const sendRegistrationEmail = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        email: resource.email,
      };
      dataProvider
        .update('Mail', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Successfully sent registration email.');
        })
        .catch(() => {
          notify(
            'Error sending registration email. User may already be confirmed.',
            'error'
          );
        });
    };
  };

  const setAsTestUser = () => {
    return () => {
      const updatedRecord = {
        id: 1,
        value: String(id),
      };
      dataProvider
        .update('Constant', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Successfully set user as test user.');
          const newWindow = window.open(
            'https://www.energea.com/login',
            '_blank'
          );
          if (newWindow) {
            newWindow.focus(); // Ensure the new window is brought to focus
          }
        })
        .catch((err) => {
          console.error(err);
          notify('Error setting user as test user.', 'error');
        });
    };
  };

  const suspendUserPermanently = () => {
    return () => {
      dataProvider
        .update('SuspendUser', { id: parseInt(id, 10) })
        .then(() => {
          notify('Successfully suspended user.', { type: 'success' });
          // Give dwolla database time to update
          setTimeout(() => refresh(), 1000);
        })
        .catch((error) => {
          notify(`Error suspending user. ${error}`, { type: 'error' });
        });
    };
  };

  const deactivateUser = () => {
    return () => {
      dataProvider
        .update('DeactivateUser', { id: parseInt(id, 10) })
        .then(() => {
          notify('Successfully deactivated user.', { type: 'success' });
          // Give dwolla database time to update
          setTimeout(() => refresh(), 1000);
        })
        .catch((error) => {
          notify(`Error deactivating user. ${error}`, { type: 'error' });
        });
    };
  };

  const reactivateUser = () => {
    return () => {
      dataProvider
        .update('ReactivateUser', { id: parseInt(id, 10) })
        .then(() => {
          notify('Successfully reactivated user.', { type: 'success' });
          // Give dwolla database time to update
          setTimeout(() => refresh(), 1000);
        })
        .catch((error) => {
          notify(`Error reactivating user. ${error}`, { type: 'error' });
        });
    };
  };

  const createSyncUserHubSpotContact = () => {
    return () => {
      dataProvider
        .update('HubSpotContact', {
          data: { userId: parseInt(id, 10), createSyncHubSpotContact: true },
        })
        .then(() => {
          notify(
            'Successfully created/synced HubSpot contact properties with user.'
          );
        })
        .catch((error) => {
          notify(
            `Error creating/syncing HubSpot contact properties for user. ${error}`,
            'error'
          );
        });
    };
  };

  const createSyncUserHubSpotContactDeals = () => {
    return () => {
      dataProvider
        .update('HubSpotContact', {
          data: {
            userId: parseInt(id, 10),
            createSyncHubSpotContactDeals: true,
          },
        })
        .then(() => {
          notify(
            'Successfully created/synced HubSpot contact deals with user investments.'
          );
        })
        .catch((error) => {
          notify(
            `Error creating/syncing HubSpot contact deals for user investments. ${error}`,
            'error'
          );
        });
    };
  };

  const createSyncUserHubSpotSubAccounts = () => {
    return () => {
      dataProvider
        .update('HubSpotContact', {
          data: {
            userId: parseInt(id, 10),
            createSyncHubSpotContactSubAccounts: true,
          },
        })
        .then(() => {
          notify('Successfully created/synced HubSpot subAccounts with user.');
        })
        .catch((error) => {
          notify(
            `Error creating/syncing HubSpot subAccounts for user. ${error}`,
            'error'
          );
        });
    };
  };

  const updateEmailAddress = (email, sendConfirmationEmails) => {
    const updatedRecord = {
      updateEmailAddress: true,
      id: parseInt(id, 10),
      email,
      sendConfirmationEmails,
    };
    dataProvider
      .update('Mail', {
        data: updatedRecord,
      })
      .then(() => {
        notify('Successfully updated email address.');
        refresh();
      })
      .catch(() => {
        notify('Error updating email address.', { type: 'error' });
      });
  };

  const manuallyConfirmEmailAddress = (record) => {
    dataProvider
      .update('UserManuallyConfirmEmail', {
        id: record.id,
      })
      .then(() => {
        notify('Successfully confirmed email address.');
        refresh();
      })
      .catch(() => {
        notify('Error updating email address.', { type: 'error' });
      });
  };

  const resetMFA = () => {
    setLoading(true);
    const updatedRecord = {
      resetMFA: true,
      id: parseInt(id, 10),
    };
    dataProvider
      .update('User', {
        data: updatedRecord,
      })
      .then(() => {
        notify(
          "Successfully reset user's MFA. The user will be emailed of this reset, and will be required to confirm the new phone number next time they log in.",
          'success'
        );
        setLoading(false);
        refresh();
      })
      .catch((e) => {
        notify(e.message, { type: 'error' });
        setLoading(false);
      });
  };

  const removeMFA = () => {
    setLoading(true);
    const updatedRecord = {
      removeMFA: true,
      id: parseInt(id, 10),
    };
    dataProvider
      .update('User', {
        data: updatedRecord,
      })
      .then(() => {
        notify(
          "Successfully removed user's MFA. The user will be emailed of this removal, and will be required to confirm the new phone number next time they log in.",
          'success'
        );
        setLoading(false);
        refresh();
      })
      .catch(() => {
        notify('Error removing MFA.', { type: 'error' });
        setLoading(false);
      });
  };

  const cancelUpdateEmailRequest = () => {
    dataProvider
      .delete('Mail', {
        id: parseInt(id, 10),
      })
      .then(() => {
        notify('Successfully cancelled email update request.');
        refresh();
      })
      .catch(() => {
        notify('Error cancelling request to update email address.', {
          type: 'error',
        });
      });
  };

  const handleGetIdentity = (plaidItemId) => {
    return () => {
      dataProvider
        .create('PlaidItemIdentity', {
          data: {
            id: plaidItemId,
          },
        })
        .then(
          (resp) => {
            notify('Identity Successfully Retrieved');
            refresh();
          },
          (e) => {
            console.error(e);
            notify('Error retrieving plaid item identity', { type: 'error' });
          }
        );
    };
  };

  const handleMakePhotoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryImage: resource.id,
      };
      dataProvider
        .update('User', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Images successfully updated');
          refresh();
        })
        .catch(() => {
          notify('Error updating images', { type: 'error' });
        });
    };
  };

  const handleRemovePhoto = (resource) => {
    return () => {
      dataProvider
        .delete('UserImage', {
          userId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Image successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error(e);
          notify('Error removing image', { type: 'error' });
        });
    };
  };
  const handleSetOwnerAsBeneficiary = () => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        addCurrentUserAsBeneficiary: true,
      };
      dataProvider
        .update('User', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Beneficial owner successfully set');
          refresh();
        })
        .catch(() => {
          notify('Error setting beneficial owner', { type: 'error' });
        });
    };
  };
  const handleCertifyBeneficialOwners = () => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        certifyBeneficialOwners: true,
      };
      dataProvider
        .update('User', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Beneficial owners successfully certified');
          refresh();
        })
        .catch(() => {
          notify('Error certifying beneficial owners', { type: 'error' });
        });
    };
  };

  const handleAddControllerPassport = () => {
    const updatedRecord = {
      id: parseInt(id, 10),
      controllerPassportNumber,
      controllerPassportCountryCode,
    };
    dataProvider
      .update('User', {
        data: updatedRecord,
      })
      .then(() => {
        notify('Controller passport successfully saved to Dwolla User');
        refresh();
      })
      .catch(() => {
        notify('Error saving Controller passport to Dwolla user', {
          type: 'error',
        });
      });
  };

  const deleteFundingSource = (fundingSourceId) => {
    dataProvider
      .delete('UserDwollaFundingSource', {
        id: fundingSourceId,
      })
      .then(() => {
        notify('Funding source successfully removed');
        refresh();
      })
      .catch(() => {
        notify('Error removing funding source', { type: 'error' });
      });
  };

  const handleSetProcessTokenAccount = (entity, record, account) => {
    return () => {
      const userId = record.id;
      dataProvider
        .update('PlaidItemProcessorToken', {
          data: {
            accessToken: entity.access_token,
            dwollaId: record.dwollaId,
            account,
          },
        })
        .then(
          (resp) => {
            console.log('HIT IT', resp.data.getPlaidLinkToken);
          },
          (e) => {
            console.log(e);
          }
        );
    };
  };

  const handleVerifyAccount = (entity, record) => {
    return () => {
      const userId = record.id;
      dataProvider
        .getOne('PlaidItem', {
          data: {
            input: {
              access_token: entity.access_token,
              verifyMicrodepositsMode: true,
            },
          },
        })
        .then(
          (resp) => {
            const linkHandler = Plaid.create({
              token: resp.data.getPlaidLinkToken,
              onLoad() {
                // The Link module finished loading.
              },
              onSuccess(public_token, metadata) {
                // The onSuccess function is called when the user has
                // successfully authenticated and selected an account to use.
                //
                // When called, you will send the public_token and the selected
                // account ID, metadata.account_id, to your backend app server.
                //
                setLoading(true);
                const { accounts, institution } = metadata;
                dataProvider
                  .update('PlaidItem', {
                    data: {
                      userId,
                      public_token,
                      accounts,
                      institution: institution.institution_id,
                    },
                  })
                  .then(() => {
                    setLoading(true);

                    notify('Beneficial owners successfully certified');
                    refresh();
                  })
                  .catch(() => {
                    setLoading(true);

                    notify('Error certifying beneficial owners', {
                      type: 'error',
                    });
                  });
                // mutate({
                //   variables: {
                //     input: {
                //       userId,
                //       public_token,
                //       accounts,
                //       institution: institution.institution_id,
                //     },
                //   },
                //   refetchQueries: [
                //     {
                //       query: bankAccountsQuery,
                //     },
                //     {
                //       query: currentUserQuery,
                //     },
                //     { query: userDashboardQuery, variables: { userId } },
                //   ],
                // })
                //   .catch((e) => {
                //     that.setState({ loading: false });
                //     snackbar.setState({
                //       snackbarMessage: `Error linking bank account. Please try again later.`,
                //       snackbarOpen: true,
                //       snackbarVariant: 'error',
                //     });
                //     throw new Error('Error linking bank account.', e);
                //   })
                //   .then(() => {
                //     that.setState({ loading: false });
                //     snackbar.setState({
                //       snackbarMessage: `Successfully linked bank account.`,
                //       snackbarOpen: true,
                //       snackbarVariant: 'success',
                //     });
                //   });
              },
              onExit(err /* metadata */) {
                // The user exited the Link flow.
                if (err != null) {
                  // The user encountered a Plaid API error prior to exiting.
                  console.error('Error from plaid link', err);
                }
                // setTimeout(() => data.refetch(), 3000);

                // metadata contains information about the institution
                // that the user selected and the most recent API request IDs.
                // Storing this information can be helpful for support.
              },
            });
            linkHandler.open();
          },
          (e) => {
            console.log(e);
          }
        );
    };
  };

  const withdrawalFunds = (record) => {
    if (
      parseFloat(amountTransferFound) === 0 ||
      record.dwollaBalance.value < amountTransferFound
    ) {
      notify(
        'The withdrawal amount must be greater than zero and less than the wallet balance.',
        { type: 'error' }
      );
      setAmountTransferFound(0);
      setToAccountId('');
      setWithdrawalFundsDialogOpen(false);
      return;
    }

    dataProvider
      .create('UserTransferFunds', {
        data: {
          userId: parseInt(id, 10),
          type: 'withdrawal',
          fromAccountId: record.dwollaBalanceFundingSource.id,
          toAccountId,
          amount: parseFloat(amountTransferFound),
        },
      })
      .then(
        () => {
          setWithdrawalFundsDialogOpen(false);
          notify('The withdrawal requested with success.', { type: 'success' });
          refresh();
        },
        (e) => {
          setAmountTransferFound(0);
          setToAccountId('');
          setWithdrawalFundsDialogOpen(false);
          notify(e.message, { type: 'error' });
        }
      );
  };

  const disableWithdrawalBtn = (record) => {
    if (parseFloat(amountTransferFound) <= 0) {
      return true;
    }
    if (record.dwollaBalanceFundingSource?.id === null) {
      return true;
    }
    if (!toAccountId) {
      return true;
    }
    return false;
  };

  const clientAllowedFormats =
    documentType === 'other'
      ? 'image/png, image/jpeg, image/jpg, application/pdf'
      : 'image/png, image/jpeg, image/jpg';

  return (
    <>
      <Edit title={<Title />} undoable={false}>
        <TabbedForm
          margin="none"
          tabs={<TabbedFormTabs variant="scrollable" />}
        >
          <FormTab label="profile">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <TextInput source="firstName" fullWidth />
                <TextInput source="lastName" fullWidth />
                <DateInput
                  label="Date of Birth"
                  source="dateOfBirth"
                  fullWidth
                />
                <TextInput
                  id="editable.ssn"
                  label="SSN (last 4 digits)"
                  source="ssn"
                  helperText="No dashes, just the numbers"
                  fullWidth
                  // format={(val) => {
                  //   const str = val;
                  //   if (!val || val === '') return '';
                  //   const result = str.replace(
                  //     /^(\D*\d\D*){5}/gm,
                  //     function (match) {
                  //       return match.replace(/\d/g, '*');
                  //     }
                  //   );
                  //   return result;
                  // }}
                />
                <TextInput source="address1" fullWidth />
                <TextInput source="address2" fullWidth />
                <TextInput source="city" fullWidth />
                <TextInput source="postalCode" fullWidth />
                <TextInput source="state" fullWidth />
              </Grid>
            </Grid>
            <Typography variant="h4" gutterBottom>
              Images
            </Typography>
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (!formData?.images || formData?.images.length === 0)
                  return 'No photos';
                return formData.images.map((record) => (
                  <Grid
                    style={{ paddingBottom: '1rem' }}
                    key={`user-profile-images-${record.id}`}
                  >
                    <Image
                      style={{}}
                      cloud_name={Config.cloud_name}
                      publicId={record.public_id}
                    >
                      <Transformation width="200" crop="scale" />
                    </Image>
                    <Grid>
                      <Button
                        color="primary"
                        style={
                          record.primaryFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        disabled={!!record.primaryFlg}
                        variant={record.primaryFlg ? 'contained' : 'outlined'}
                        onClick={handleMakePhotoPrimary(record)}
                      >
                        Make Primary Photo
                      </Button>
                      <Button
                        style={{ float: 'right' }}
                        onClick={handleRemovePhoto(record)}
                      >
                        <Delete />
                      </Button>
                    </Grid>
                  </Grid>
                ));
              }}
            </FormDataConsumer>
            <div className="actions">
              <Button variant="outlined" onClick={uploadImageWithCloudinary}>
                Add photo
              </Button>
            </div>
          </FormTab>
          <FormTab margin="none" label="connected accts">
            <Grid container style={{ width: '100%' }} spacing={2}>
              <Grid item xs={12} md={6}>
                <FormDataConsumer>
                  {({ formData, ...rest }) => {
                    const { fullName, type } = formData;
                    return (
                      <Grid container style={{ width: '100%' }}>
                        {type === 'business' ? (
                          <Work fontSize="large" style={{ height: '.9em' }} />
                        ) : (
                          <Person fontSize="large" style={{ height: '.9em' }} />
                        )}
                        <Typography
                          variant="h6"
                          style={{ marginLeft: '.5rem' }}
                        >
                          <b>{fullName}</b> is a <b>{type}</b> user
                        </Typography>
                      </Grid>
                    );
                  }}
                </FormDataConsumer>
              </Grid>
            </Grid>
            <Grid style={{ width: '100%' }} container spacing={5}>
              <Grid item md={7} xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography gutterBottom variant="h6">
                      Dwolla User Profile
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid>
                      <Grid item style={{ paddingBottom: '1rem' }}>
                        <FormDataConsumer>
                          {({ formData, ...rest }) => {
                            if (
                              formData.dwollaCustomer &&
                              formData.dwollaCustomer.customerUrl
                            ) {
                              return (
                                <a
                                  href={formData.dwollaCustomer.customerUrl}
                                  target="_blank"
                                >
                                  Click here to view customer on Dwolla
                                  dashboard
                                </a>
                              );
                            }
                          }}
                        </FormDataConsumer>
                      </Grid>
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="Dwolla Id"
                        source="dwollaCustomer.id"
                      />
                      <TextInput
                        margin="none"
                        label="First Name"
                        fullWidth
                        disabled
                        source="dwollaCustomer.firstName"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="Last Name"
                        source="dwollaCustomer.lastName"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="Email"
                        source="dwollaCustomer.email"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="Status"
                        source="dwollaCustomer.status"
                      />
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          if (formData.dwollaCustomer) {
                            return (
                              <Grid
                                container
                                alignItems="flex-end"
                                style={{ paddingBottom: '1.5rem' }}
                              >
                                <Grid item md={6} xs={12}>
                                  <InputLabel id="select-label">
                                    Document Type
                                  </InputLabel>
                                  <Select
                                    required
                                    variant="outlined"
                                    value={documentType || ''}
                                    name="type"
                                    labelWidth={118}
                                    onChange={(event) =>
                                      setDocumentType(event.target.value)
                                    }
                                    fullWidth
                                  >
                                    <MenuItem value="license">
                                      U.S. Driver&apos;s License
                                    </MenuItem>
                                    <MenuItem value="passport">
                                      U.S. Passport
                                    </MenuItem>
                                    <MenuItem value="idCard">ID Card</MenuItem>
                                    <MenuItem value="other">Other</MenuItem>
                                  </Select>
                                </Grid>{' '}
                                <Grid item md={6} xs={12}>
                                  <Button
                                    color="primary"
                                    disabled={!documentType}
                                    variant="contained"
                                    style={{
                                      marginLeft: '1rem',
                                      height: '3.5rem',
                                    }}
                                    component="label" // https://stackoverflow.com/a/54043619
                                  >
                                    Upload Dwolla Auth Image (business or
                                    individual)
                                    <input
                                      type="file"
                                      hidden
                                      onChange={(event) =>
                                        uploadDwollaAuthImageWithAWS(
                                          event,
                                          formData.dwollaCustomer.id,
                                          formData.id
                                        )
                                      }
                                      accept={clientAllowedFormats}
                                    />
                                  </Button>
                                </Grid>
                              </Grid>
                            );
                          }
                          return null;
                        }}
                      </FormDataConsumer>
                      <TextInput
                        margin="none"
                        disabled
                        fullWidth
                        label="Type"
                        source="dwollaCustomer.type"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="Address 1"
                        source="dwollaCustomer.address1"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="Address 2"
                        source="dwollaCustomer.address2"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="city"
                        source="dwollaCustomer.city"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="state"
                        source="dwollaCustomer.state"
                      />
                      <TextInput
                        margin="none"
                        fullWidth
                        disabled
                        label="postalCode"
                        source="dwollaCustomer.postalCode"
                      />
                      <DateInput
                        margin="none"
                        disabled
                        fullWidth
                        label="Created"
                        source="dwollaCustomer.created"
                      />
                      <FunctionField
                        label="Deactivate User"
                        render={(record) => {
                          const isEnabled =
                            record.dwollaCustomer &&
                            record.dwollaCustomer.status !== 'deactivated' &&
                            record.dwollaCustomer.status !== 'suspended';
                          return (
                            <Button
                              onClick={deactivateUser()}
                              disabled={!isEnabled}
                              variant="contained"
                              size="large"
                              style={{
                                backgroundColor: !isEnabled
                                  ? null
                                  : theme.palette.error.main,
                                color: !isEnabled ? null : '#fff',
                                marginRight: '1rem',
                              }}
                            >
                              Deactivate User
                            </Button>
                          );
                        }}
                      />
                      <FunctionField
                        label="Re-activate User"
                        render={(record) => {
                          const isEnabled =
                            record.dwollaCustomer &&
                            record.dwollaCustomer.status === 'deactivated';
                          if (!isEnabled) return null;
                          return (
                            <Button
                              onClick={reactivateUser()}
                              disabled={!isEnabled}
                              variant="contained"
                              size="large"
                              style={{
                                backgroundColor: !isEnabled
                                  ? null
                                  : theme.palette.error.main,
                                color: !isEnabled ? null : '#fff',
                                marginRight: '1rem',
                              }}
                            >
                              Re-activate User
                            </Button>
                          );
                        }}
                      />
                      <FunctionField
                        label="Suspend User"
                        render={(record) => (
                          <Button
                            onClick={suspendUserPermanently()}
                            disabled={record.permSuspendedFlg}
                            variant="contained"
                            size="large"
                            style={{
                              backgroundColor: record.permSuspendedFlg
                                ? null
                                : theme.palette.error.main,
                              color: record.permSuspendedFlg ? null : '#fff',
                            }}
                          >
                            {record.permSuspendedFlg
                              ? 'User Permanently Suspended'
                              : 'Suspend User Permanently'}
                          </Button>
                        )}
                      />
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Grid>
              <Grid item md={7} xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography gutterBottom variant="h6">
                      HubSpot Profile
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid>
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          if (formData.hubSpotContactUrl) {
                            return (
                              <Grid
                                container
                                alignItems="center"
                                style={{ paddingBottom: '1rem' }}
                              >
                                <Grid item>
                                  <Typography>
                                    <a
                                      href={formData.hubSpotContactUrl}
                                      target="_blank"
                                    >
                                      HubSpot Link
                                    </a>
                                  </Typography>
                                </Grid>
                                <Grid item>
                                  <Launch
                                    fontSize="medium"
                                    style={{ marginBottom: '-2px' }}
                                  />
                                </Grid>
                              </Grid>
                            );
                          }
                          return null;
                        }}
                      </FormDataConsumer>
                      <TextInput
                        margin="none"
                        fullWidth
                        source="hubSpotContactId"
                      />
                      <Button
                        onClick={createSyncUserHubSpotContact()}
                        variant="contained"
                        size="large"
                        color="primary"
                        style={{ margin: '.5rem' }}
                      >
                        Create/Sync HubSpot Contact Properties
                      </Button>
                      <Button
                        onClick={createSyncUserHubSpotContactDeals()}
                        variant="contained"
                        size="large"
                        color="primary"
                        style={{ margin: '.5rem' }}
                      >
                        Create/Sync HubSpot Contact Deals
                      </Button>
                      <FormDataConsumer>
                        {({ formData, ...rest }) => {
                          if (!formData.subAccounts?.length) return null;
                          return (
                            <Button
                              onClick={createSyncUserHubSpotSubAccounts()}
                              variant="contained"
                              size="large"
                              color="primary"
                              style={{ margin: '.5rem' }}
                            >
                              Create/Sync HubSpot SubAccounts
                            </Button>
                          );
                        }}
                      </FormDataConsumer>
                      <Typography
                        variant="body2"
                        style={{ color: theme.palette.warning.dark }}
                      >
                        Warning: Syncing HubSpot contacts can potentially enroll
                        or re-enroll contacts in HubSpot workflows.
                      </Typography>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Grid>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (formData.type === 'business') {
                    return (
                      <Grid item md={7} xs={12}>
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMore />}>
                            <Typography gutterBottom variant="h6">
                              Business Profile
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Grid>
                              <TextInput fullWidth source="businessName" />
                              <TextInput fullWidth source="doingBusinessAs" />
                              <SelectInput
                                source="businessType"
                                choices={[
                                  { id: 'llc', name: 'llc' },
                                  {
                                    id: 'soleProprietorship',
                                    name: 'soleProprietorship',
                                  },
                                  { id: 'corporation', name: 'corporation' },
                                  { id: 'partnership', name: 'partnership' },
                                ]}
                                fullWidth
                              />
                              <TextInput fullWidth source="website" />
                              {/* <TextInput
                              fullWidth
                              source="dwollaCustomer.controller.firstName"
                            />
                            <TextInput
                              fullWidth
                              source="dwollaCustomer.controller.lastName"
                            /> */}
                              <TextInput
                                disabled
                                label="Controller Name"
                                fullWidth
                                source="dwollaCustomer.controller.fullName"
                              />
                              <TextInput
                                fullWidth
                                source="industryClassification"
                              />
                              <TextInput fullWidth source="ein" />
                              <Button
                                color="primary"
                                variant="contained"
                                onClick={handleSetOwnerAsBeneficiary()}
                                style={{ marginRight: '1rem' }}
                              >
                                Set owner as beneficial owner
                              </Button>
                              <Button
                                color="primary"
                                variant="contained"
                                onClick={handleCertifyBeneficialOwners()}
                                style={{ marginRight: '1rem' }}
                              >
                                Certify Beneficial Owners
                              </Button>
                              <Button
                                color="primary"
                                variant="contained"
                                onClick={() =>
                                  setControllerPassportDialogOpen(true)
                                }
                              >
                                Add Controller Passport
                              </Button>
                              <Dialog
                                open={!!controllerPassportDialogOpen}
                                onClose={() =>
                                  setControllerPassportDialogOpen(false)
                                }
                              >
                                <DialogTitle>
                                  <Grid
                                    container
                                    justifyContent="space-between"
                                    alignItems="center"
                                  >
                                    <Grid item>Add Controller Passport</Grid>
                                    <Grid item>
                                      <IconButton
                                        onClick={() =>
                                          setControllerPassportDialogOpen(false)
                                        }
                                        size="large"
                                      >
                                        <Close />
                                      </IconButton>
                                    </Grid>
                                  </Grid>
                                </DialogTitle>
                                <DialogContent>
                                  <Grid container direction="column">
                                    <Grid item>
                                      <MuiTextField
                                        value={controllerPassportNumber}
                                        helperText="Passport #"
                                        onChange={(event) => {
                                          setControllerPassportNumber(
                                            event.target.value
                                          );
                                        }}
                                        fullWidth
                                      />
                                    </Grid>
                                    <Grid item>
                                      <MuiTextField
                                        // label="Passport Country Code"
                                        value={controllerPassportCountryCode}
                                        helperText={
                                          'Country of issued passport. Two digit ISO code, e.g. US.'
                                        }
                                        onChange={(event) => {
                                          setControllerPassportCountryCode(
                                            event.target.value
                                          );
                                        }}
                                        fullWidth
                                      />
                                    </Grid>
                                  </Grid>
                                </DialogContent>
                                <DialogActions>
                                  <Button
                                    color="primary"
                                    variant="contained"
                                    disabled={
                                      !controllerPassportCountryCode ||
                                      !controllerPassportNumber
                                    }
                                    onClick={() => {
                                      setControllerPassportDialogOpen(false);
                                      handleAddControllerPassport();
                                    }}
                                    style={{ margin: '1rem' }}
                                  >
                                    Submit
                                  </Button>
                                </DialogActions>
                              </Dialog>
                              {formData.dwollaBeneficialOwners &&
                                formData.dwollaBeneficialOwners.map((owner) => {
                                  return (
                                    <Card key={`beneficial-owner-${owner.id}`}>
                                      <CardContent>
                                        <Grid item>
                                          <Typography>
                                            <b>ID : </b>
                                            {owner.id}
                                          </Typography>

                                          <Typography>
                                            <b>NAME : </b>
                                            {owner.firstName} {owner.lastName}
                                          </Typography>
                                          <Typography>
                                            <b>STATUS : </b>
                                            {owner.verificationStatus}
                                          </Typography>
                                          <Button
                                            color="primary"
                                            variant="contained"
                                            style={{ marginRight: '1rem' }}
                                            component="label"
                                          >
                                            Upload Auth Document
                                            <input
                                              type="file"
                                              hidden
                                              onChange={(event) =>
                                                uploadBeneficialOwnerImageWithAWS(
                                                  event,
                                                  owner.id,
                                                  formData.id
                                                )
                                              }
                                              accept={clientAllowedFormats}
                                            />
                                          </Button>
                                          <Button
                                            color="primary"
                                            variant="contained"
                                            onClick={deleteBeneficialOwner(
                                              owner.id
                                            )}
                                            style={{ marginRight: '1rem' }}
                                          >
                                            Delete
                                          </Button>
                                          <Button
                                            color="primary"
                                            variant="contained"
                                            onClick={syncBeneficialOwner({
                                              id: owner.id,
                                              firstName:
                                                formData.dwollaCustomer
                                                  .firstName,
                                              lastName:
                                                formData.dwollaCustomer
                                                  .lastName,
                                              ssn: formData.ssn,
                                              dateOfBirth: formData.dateOfBirth,
                                              address:
                                                formData.dwollaCustomer
                                                  .controller?.address,
                                            })}
                                            style={{ marginRight: '1rem' }}
                                          >
                                            Sync owner with controller
                                          </Button>
                                        </Grid>
                                      </CardContent>
                                    </Card>
                                  );
                                })}
                            </Grid>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>
                    );
                  }
                }}
              </FormDataConsumer>
              <Grid item md={7} xs={12}>
                <FunctionField
                  label="test"
                  render={(record) => {
                    if (record.millenniumTrustContactId) {
                      const authSessionsWStatus =
                        record.millenniumTrustAuthSessions.filter(
                          (authSession) => authSession.status
                        );
                      return (
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMore />}>
                            <Typography gutterBottom variant="h6">
                              Millennium Trust Profile
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Grid container style={{ width: '100%' }}>
                              <Grid xs={12} md={6}>
                                <TextInput
                                  margin="none"
                                  fullWidth
                                  disabled
                                  label="Millennium Trust Contact Id"
                                  source="millenniumTrustContactId"
                                />
                              </Grid>
                              <Grid xs={12}>
                                {authSessionsWStatus.length > 0 ? (
                                  <>
                                    <Typography>
                                      Contact verification sessions:
                                    </Typography>
                                    <Grid container style={{ width: '100%' }}>
                                      {authSessionsWStatus.map(
                                        (authSession) => {
                                          // NOTE: For some reason, sometimes the status is null but I have only seen this when its actually awaiting documents
                                          const status =
                                            authSession.status.status ||
                                            'Awaiting Documentation';
                                          return (
                                            <Grid
                                              item
                                              xs={12}
                                              key={`auth-session-list-${authSession.id}`}
                                            >
                                              <ListItemText
                                                primary={`Status: ${status}`}
                                                secondary={`Verification ID: ${authSession.status.verificationId}`}
                                              />
                                              {status ===
                                                'Awaiting Documentation' ||
                                              status === 'OTP Incorrect' ? (
                                                <Grid
                                                  container
                                                  style={{ width: '100%' }}
                                                >
                                                  <Grid item xs={12}>
                                                    <MuiList>
                                                      {authSession.status.requiredDocuments.map(
                                                        (requiredAuthDoc) => (
                                                          <ListItem>
                                                            <ListItemText
                                                              primary={`Document type: '${requiredAuthDoc.docType}' - Status: '${requiredAuthDoc.status}'`}
                                                              secondary={`Acceptable docs: ${requiredAuthDoc.acceptableDocuments.join(
                                                                ', '
                                                              )}`}
                                                            />
                                                            <ListItemSecondaryAction>
                                                              <Button
                                                                color="primary"
                                                                disabled={
                                                                  [
                                                                    'Awaiting Documentation',
                                                                    'Rejected',
                                                                  ].indexOf(
                                                                    requiredAuthDoc.status
                                                                  ) === -1
                                                                }
                                                                variant="contained"
                                                                style={{
                                                                  marginLeft:
                                                                    '1rem',
                                                                  height:
                                                                    '3.5rem',
                                                                }}
                                                                component="label" // https://stackoverflow.com/a/54043619
                                                              >
                                                                Upload
                                                                Verification
                                                                Document
                                                                <input
                                                                  type="file"
                                                                  hidden
                                                                  onChange={(
                                                                    event
                                                                  ) =>
                                                                    uploadMillenniumAuthImageWithAWS(
                                                                      event,
                                                                      record.id,
                                                                      requiredAuthDoc.docType,
                                                                      authSession.id
                                                                    )
                                                                  }
                                                                  accept={
                                                                    clientAllowedFormats
                                                                  }
                                                                />
                                                              </Button>
                                                            </ListItemSecondaryAction>
                                                          </ListItem>
                                                        )
                                                      )}
                                                    </MuiList>
                                                  </Grid>
                                                </Grid>
                                              ) : null}
                                            </Grid>
                                          );
                                        }
                                      )}
                                    </Grid>
                                  </>
                                ) : null}
                              </Grid>
                            </Grid>
                          </AccordionDetails>
                        </Accordion>
                      );
                    }
                    return null;
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="investor fields">
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12} md={6}>
                <BooleanInput
                  label="Substantive Relationship"
                  helperText="Flags a user as someone we have a 'substantive relationship' with as per SEC Regulation D guidelines"
                  source="substantiveRelationshipFlg"
                  fullWidth
                />
                <BooleanInput
                  label="Deactivate Referral Code"
                  helperText="Deactivates referral code but only prevents future referrals from being rewarded."
                  source="referralCodeDeactivatedFlg"
                  fullWidth
                />
                <BooleanInput
                  label="Advisory Council"
                  helperText="Flags a user if they are on our advisory council"
                  source="advisoryCouncilFlg"
                  fullWidth
                />
                <BooleanInput
                  label="Ignore Portfolio Max Sell Order Value Limit"
                  helperText="This will allow the user to place a sell order for all of their eligible shares and they will not be subject to the max sell order value limit that is enforced on the user per portfolio."
                  source="maxSellOrderValueLimitBypassFlg"
                  fullWidth
                />
                <BooleanInput
                  label="Account Frozen"
                  helperText="Prevents user from withdrawing cash, editing / adding new bank accounts, updating auto-invests, and making investments. Use this if you are suspicious of an account and want to return the money to the original bank account."
                  source="accountFrozenFlg"
                  fullWidth
                />
                <BooleanInput
                  label="Ignore 'Frontend-Closed' Portfolio Status"
                  helperText="When portfolios are 'frontend-closed', this allows the user to still see the portfolios as investable and invest on the platform."
                  source="ignoreCloseFrontendFlg"
                  fullWidth
                />

                <BooleanInput
                  label="Understands Wallet"
                  helperText="Flags a user as someone who has been presented and acknowledged the wallet functionality"
                  source="understandsWalletFlg"
                  fullWidth
                />
                <BooleanInput
                  label="Wealth Channel User"
                  helperText="Flag used to determine if a user is a wealth channel user"
                  source="wealthChannelFlg"
                  fullWidth
                />
                <TextInput
                  multiline
                  fullWidth
                  label="Relationship Description"
                  helperText="Internal use only so we remember how/why we know someone"
                  source="substantiveRelationshipDescription"
                />
                <BooleanInput label="Is Accredited" source="isAccredited" />
                <CustomNumberInput
                  label="Annual Income"
                  source="annualIncome"
                  helperText="**NO DECIMALS** Only relevant for non-accredited investors. This will determine their investment max."
                  fullWidth
                />
                <CustomNumberInput
                  step={1}
                  label="Net Worth"
                  source="netWorth"
                  helperText="**NO DECIMALS** Only relevant for non-accredited investors. This will determine their investment max."
                  fullWidth
                />
                <Typography
                  variant="h6"
                  gutterBottom
                  style={{ marginTop: '1rem' }}
                >
                  Beneficiaries
                </Typography>
                <FunctionField
                  label="Beneficiaries"
                  render={(record) => {
                    if (
                      !record.beneficiaries ||
                      record.beneficiaries.length === 0
                    ) {
                      return (
                        <Alert severity="info">
                          There are no beneficiaries for this user
                        </Alert>
                      );
                    }
                    return (
                      <MuiList>
                        {record.beneficiaries.map((beneficiary) => {
                          let title = '';
                          let secondary = '';
                          if (
                            beneficiary.type.id === 1 ||
                            beneficiary.type.id === 2
                          ) {
                            title = `${beneficiary.firstName} ${beneficiary.lastName}`;
                            secondary = `${beneficiary.email} - ${beneficiary.phone} - ${beneficiary.dateOfBirth}`;
                          } else if (beneficiary.type.id === 3) {
                            title = `${beneficiary.entityName}`;
                            secondary = `${beneficiary.email} - ${beneficiary.phone} - ${beneficiary.formationDt}`;
                          } else {
                            title = `${beneficiary.trustName}`;
                            secondary = `${beneficiary.email} - ${beneficiary.phone} - ${beneficiary.formationDt}`;
                          }
                          return (
                            <ListItem
                              key={`user-beneficiary-list-${beneficiary.id}`}
                            >
                              <ListItemText
                                primary={`${title} (${beneficiary.type.name})${
                                  beneficiary.primaryFlg ? ' - Primary' : ''
                                }`}
                                secondary={secondary}
                              />
                            </ListItem>
                          );
                        })}
                      </MuiList>
                    );
                  }}
                />
                {/* <ArrayInput
                  source="beneficiaries"
                  fullWidth
                  label="Beneficiaries"
                >
                  <SimpleFormIterator
                    fullWidth
                    TransitionProps={{ enter: false, exit: false }}
                  >
                    <ReferenceInput
                      perPage={10000}
                      source="type.id"
                      reference="BeneficiaryType"
                      sort={{ field: 'name', order: 'ASC' }}
                    >
                      <SelectInput
                        label="Type"
                        required
                        fullWidth
                        optionText="name"
                      />
                    </ReferenceInput>
                    <TextInput
                      source="firstName"
                      required
                      label="First Name"
                      fullWidth
                    />
                    <FunctionField
                      render={(record) => {
                        return (
                          <TextInput
                            source="firstName"
                            required
                            label="First Name"
                            fullWidth
                          />
                        );
                      }}
                    />
                    <FormDataConsumer>
                      {({ scopedFormData, ...rest }) => {
                        if (!scopedFormData.type) return null;
                        if (
                          scopedFormData.type.id === 1 ||
                          scopedFormData.type.id === 2
                        ) {
                          // spouse or non-spouse individual
                          return (
                            <>
                            <TextInput
                              source="firstName"
                              required
                              label="First Name"
                              fullWidth
                            />
                            <TextInput
                              source="lastName"
                              required
                              label="Last Name"
                              fullWidth
                            />
                            <TextInput
                              source="email"
                              required
                              label="Email"
                              fullWidth
                            />
                            <TextInput
                              source="phone"
                              required
                              label="Phone"
                              fullWidth
                            />
                            <DateInput
                              source="dateOfBirth"
                              required
                              label="Date of Birth"
                              fullWidth
                            />
                            <TextInput
                              source="ssn"
                              required
                              label="SSN"
                              fullWidth
                            />
                            </>
                          );
                        }
                        if (scopedFormData.type.id === 3) {
                          // entity
                          return (
                            <>
                              <TextInput
                                source="entityName"
                                required
                                label="Entity Name"
                                fullWidth
                              />
                              <TextInput
                                source="email"
                                required
                                label="Email"
                                fullWidth
                              />
                              <TextInput
                                source="phone"
                                required
                                label="Phone"
                                fullWidth
                              />
                              <DateInput
                                source="formationDt"
                                required
                                label="Formation Date"
                                fullWidth
                              />
                              <TextInput
                                source="taxIdentificationNumber"
                                required
                                label="TIN"
                                fullWidth
                              />
                            </>
                          );
                        }
                        if (scopedFormData.type.id === 4) {
                          // trust
                          return (
                            <>
                              <TextInput
                                source="trustName"
                                required
                                label="Trust Name"
                                fullWidth
                              />
                              <TextInput
                                source="email"
                                required
                                label="Email"
                                fullWidth
                              />
                              <TextInput
                                source="phone"
                                required
                                label="Phone"
                                fullWidth
                              />
                              <DateInput
                                source="formationDt"
                                required
                                label="Formation Date"
                                fullWidth
                              />
                              <TextInput
                                source="taxIdentificationNumber"
                                required
                                label="TIN"
                                fullWidth
                              />
                            </>
                          );
                        }
                      }}
                    </FormDataConsumer>
                    <BooleanInput
                      source="primaryFlg"
                      label="Primary"
                      helperText="If not enabled, this beneficiary is a 'Contingent Beneficiary'"
                    />
                  </SimpleFormIterator>
                </ArrayInput> */}
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="Performance">
            <Grid container style={{ width: '100%' }}>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  return (
                    <>
                      <Grid item xs={12} style={{ padding: '1rem' }}>
                        <UserPerformanceSummary
                          userId={formData.id}
                          subAccount={null}
                        />
                      </Grid>
                      {formData?.subAccounts?.map((subAccount) => (
                        <Grid
                          item
                          xs={12}
                          style={{ padding: '1rem' }}
                          key={`sub-account-performance-summary-${subAccount.id}`}
                        >
                          <UserPerformanceSummary
                            userId={formData.id}
                            subAccount={subAccount}
                          />
                        </Grid>
                      ))}
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
          </FormTab>
          <FormTab label="investments">
            <FunctionField
              label="investments"
              render={(record) => {
                if (!record.investments || record.investments.length === 0) {
                  return (
                    <Alert severity="info">
                      There are no investments for this user
                    </Alert>
                  );
                }
                return (
                  <>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>
                            <b>Date</b>
                          </TableCell>
                          <TableCell>
                            <b>Sub Account</b>
                          </TableCell>
                          <TableCell>
                            <b>Portfolio</b>
                          </TableCell>
                          <TableCell>
                            <b>Amount</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Cancelled?</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Reinvestment?</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Referral?</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Promo?</b>
                          </TableCell>
                          <TableCell>
                            <b>Edit</b>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.investments.map((investment) => {
                          return (
                            <TableRow
                              key={`user-investment-list-${investment.id}`}
                            >
                              <TableCell>
                                {moment(investment.startDt).format(
                                  'MMM D, YYYY'
                                )}
                              </TableCell>
                              <TableCell>
                                {investment.subAccount?.id ? (
                                  <a
                                    href={`/SubAccount/${investment.subAccount?.id}`}
                                  >
                                    {investment.subAccount?.name}
                                  </a>
                                ) : null}
                              </TableCell>
                              <TableCell>
                                <a
                                  href={`/Portfolio/${investment.portfolio?.id}`}
                                >
                                  {investment.portfolio?.name}
                                </a>
                              </TableCell>
                              <TableCell>
                                {numeral(investment.value).format('$0,0.00')}
                              </TableCell>
                              <TableCell>
                                {investment.cancelledDt ? <Check /> : null}
                              </TableCell>
                              <TableCell>
                                {investment.isReinvestment ? <Check /> : null}
                              </TableCell>
                              <TableCell>
                                {investment.isReferral ? <Check /> : null}
                              </TableCell>
                              <TableCell>
                                {investment.isPromo ? <Check /> : null}
                              </TableCell>
                              <TableCell>
                                <a href={`/Investment/${investment.id}`}>
                                  <EditIcon />
                                </a>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </FormTab>
          <FormTab label="dividends">
            <FunctionField
              label="dividends"
              render={(record) => {
                if (!record.dividends || record.dividends.length === 0) {
                  return (
                    <Alert severity="info">
                      There are no dividends for this user
                    </Alert>
                  );
                }
                return (
                  <>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>
                            <b>Date</b>
                          </TableCell>
                          <TableCell>
                            <b>Sub Account</b>
                          </TableCell>
                          <TableCell>
                            <b>Portfolio</b>
                          </TableCell>
                          <TableCell>
                            <b>Amount</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Reinvested?</b>
                          </TableCell>
                          <TableCell>
                            <b>COC Yield</b>
                          </TableCell>
                          <TableCell>
                            <b>Edit</b>
                          </TableCell>
                          {isIT && <TableCell />}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.dividends.map((dividend) => {
                          return (
                            <TableRow key={`user-dividend-list-${dividend.id}`}>
                              <TableCell>
                                {moment(dividend.date).format('MMM D, YYYY')}
                              </TableCell>
                              <TableCell>
                                {dividend.subAccount?.id ? (
                                  <a
                                    href={`/SubAccount/${dividend.subAccount?.id}`}
                                  >
                                    {dividend.subAccount?.name}
                                  </a>
                                ) : null}
                              </TableCell>
                              <TableCell>
                                <a
                                  href={`/Portfolio/${dividend.portfolio?.id}`}
                                >
                                  {dividend.portfolio?.subtitle}
                                </a>
                              </TableCell>
                              <TableCell>
                                {numeral(dividend.value).format('$0,0.00')}
                              </TableCell>
                              <TableCell>
                                {dividend.investment ? <Check /> : <Clear />}
                              </TableCell>
                              <TableCell>
                                {numeral(dividend.annualizedCOCYield).format(
                                  '%0,0.00'
                                )}
                              </TableCell>
                              <TableCell>
                                <a href={`/Dividend/${dividend.id}`}>
                                  <EditIcon />
                                </a>
                              </TableCell>
                              {isIT && !dividend.investment && (
                                <TableCell>
                                  <Tooltip
                                    arrow
                                    title="This will attempt to send these funds from the user's wallet to the portfolio that it came from. If successful, it will delete the dividend. If the dividend transfer has not been completed yet, it will fail."
                                  >
                                    <Button
                                      onClick={() => {
                                        setUndoDividendId(dividend.id);
                                      }}
                                      style={{
                                        background: theme.palette.error.main,
                                      }}
                                      variant="contained"
                                    >
                                      Undo Dividend
                                    </Button>
                                  </Tooltip>
                                </TableCell>
                              )}
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </FormTab>
          <FormTab label="sell orders">
            <UserSellOrders />
          </FormTab>
          <FormTab label="transfers">
            <UserTransfers />
          </FormTab>
          <FormTab label="auto-reinvest">
            <FunctionField
              label="auto reinvest indicators"
              render={(record) => {
                if (
                  !record.autoReinvestIndicators ||
                  record.autoReinvestIndicators.length === 0
                ) {
                  return (
                    <Alert severity="info">
                      There are no auto reinvest indicators for this user
                    </Alert>
                  );
                }
                return (
                  <>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>
                            <b>Sub Account</b>
                          </TableCell>
                          <TableCell>
                            <b>Portfolio</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Active?</b>
                          </TableCell>
                          <TableCell>
                            <b>Percentage</b>
                          </TableCell>
                          <TableCell>
                            <b>Last Updated</b>
                          </TableCell>
                          <TableCell>
                            <b>Edit</b>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.autoReinvestIndicators.map(
                          (autoReinvestIndicator) => {
                            return (
                              <TableRow
                                key={`user-auto-reinvest-indicator-list-${autoReinvestIndicator.id}`}
                              >
                                <TableCell>
                                  {autoReinvestIndicator.subAccount?.id ? (
                                    <a
                                      href={`/SubAccount/${autoReinvestIndicator.subAccount?.id}`}
                                    >
                                      {autoReinvestIndicator.subAccount?.name}
                                    </a>
                                  ) : null}
                                </TableCell>
                                <TableCell>
                                  <a
                                    href={`/Portfolio/${autoReinvestIndicator.portfolio?.id}`}
                                  >
                                    {autoReinvestIndicator.portfolio?.subtitle}
                                  </a>
                                </TableCell>
                                <TableCell>
                                  {autoReinvestIndicator.isActive ? (
                                    <Check />
                                  ) : (
                                    <Clear />
                                  )}
                                </TableCell>
                                <TableCell>
                                  {numeral(
                                    autoReinvestIndicator.percentage / 100
                                  ).format('%0')}
                                </TableCell>
                                <TableCell>
                                  {moment(
                                    autoReinvestIndicator.updatedAt
                                  ).format('MMM D, YYYY')}
                                </TableCell>
                                <TableCell>
                                  <a
                                    href={`/AutoReinvestIndicator/${autoReinvestIndicator.id}`}
                                  >
                                    <EditIcon />
                                  </a>
                                </TableCell>
                              </TableRow>
                            );
                          }
                        )}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </FormTab>
          <FormTab label="auto-invest">
            <FunctionField
              label="auto invest subscriptions"
              render={(record) => {
                if (
                  !record.autoInvestSubscriptions ||
                  record.autoInvestSubscriptions.length === 0
                ) {
                  return (
                    <Alert severity="info">
                      There are no auto invest subscriptions for this user
                    </Alert>
                  );
                }
                return (
                  <>
                    <Table>
                      <TableHead>
                        <TableRow>
                          {/* <TableCell>
                        <b>Sub Account</b>
                      </TableCell> */}
                          <TableCell>
                            <b>Portfolio</b>
                          </TableCell>
                          <TableCell>
                            <b>Amount</b>
                          </TableCell>
                          <TableCell>
                            <b>Is Active?</b>
                          </TableCell>
                          <TableCell>
                            <b>Next Investment Dt</b>
                          </TableCell>
                          <TableCell>
                            <b>Edit</b>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.autoInvestSubscriptions.map(
                          (autoInvestSubscription) => {
                            return (
                              <TableRow
                                key={`user-auto-invest-subscription-list-${autoInvestSubscription.id}`}
                              >
                                <TableCell>
                                  <a
                                    href={`/Portfolio/${autoInvestSubscription.portfolio?.id}`}
                                  >
                                    {autoInvestSubscription.portfolio?.subtitle}
                                  </a>
                                </TableCell>
                                <TableCell>
                                  {numeral(autoInvestSubscription.value).format(
                                    '$0,0[.]00'
                                  )}
                                </TableCell>
                                <TableCell>
                                  {!autoInvestSubscription.inactive ? (
                                    <Check />
                                  ) : (
                                    <Clear />
                                  )}
                                </TableCell>
                                <TableCell>
                                  {moment(
                                    autoInvestSubscription.nextInvestmentDt
                                  ).format('MMM D, YYYY')}
                                </TableCell>
                                <TableCell>
                                  <a
                                    href={`/AutoInvestSubscription/${autoInvestSubscription.id}`}
                                  >
                                    <EditIcon />
                                  </a>
                                </TableCell>
                              </TableRow>
                            );
                          }
                        )}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </FormTab>
          <FormTab label="funding sources">
            <FunctionField
              label="Dwolla Wallet Balance"
              style={{ width: '100%' }}
              render={(record) => {
                return (
                  <>
                    <Grid
                      container
                      spacing={3}
                      alignItems="center"
                      style={{ marginBottom: '1rem' }}
                    >
                      <Grid item>
                        <Typography
                          style={{ fontWeight: 'bold', marginLeft: '1rem' }}
                        >
                          <AccountBalanceWallet
                            style={{
                              fontSize: '1.2rem',
                              marginBottom: '-2px',
                              marginRight: '6px',
                            }}
                          />
                          Energea Wallet
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography>
                          Current balance:{' '}
                          {numeral(record.dwollaBalance?.value).format(
                            '$0,0.00'
                          )}
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography>
                          <i>
                            Pending balance:{' '}
                            {numeral(record.pendingDwollaBalance?.value).format(
                              '$0,0.00'
                            )}
                          </i>
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Button
                          variant="contained"
                          disabled={!record.dwollaBalance?.value}
                          onClick={() => setWithdrawalFundsDialogOpen(true)}
                        >
                          Withdraw Funds
                        </Button>
                      </Grid>
                    </Grid>
                    <Dialog
                      severity="warn"
                      open={undoDividendId}
                      onClose={() => setUndoDividendId(null)}
                    >
                      <DialogTitle>Undo Dividend?</DialogTitle>
                      <DialogContent>
                        <Typography>
                          Are you sure you want to undo this dividend? This will
                          create a transfer from the user's wallet to the
                          portfolio that the dividend came from. If the dividend
                          transfer has not been completed yet, it will fail. If
                          successful, it will delete the dividend.
                        </Typography>
                      </DialogContent>
                      <DialogActions>
                        <Button
                          variant="contained"
                          style={{ background: theme.palette.error.main }}
                          onClick={() => {
                            undoDividend(undoDividendId);
                            setUndoDividendId(null);
                          }}
                        >
                          Yes
                        </Button>
                        <Button onClick={() => setUndoDividendId(null)}>
                          No
                        </Button>
                      </DialogActions>
                    </Dialog>
                    <Dialog open={withdrawalFundsDialogOpen}>
                      <DialogTitle>Withdraw Funds</DialogTitle>
                      <Alert severity="info">
                        Use this form to withdraw funds from their wallet on
                        behalf of the user. The user will be notified
                        automatically via email.
                      </Alert>
                      <DialogContent>
                        <Grid
                          container
                          justifyContent="space-between"
                          alignItems="center"
                        >
                          <Grid item>
                            <CustomNumberInput
                              source="amount"
                              label="Amount"
                              fullWidth
                              min={0}
                              value={amountTransferFound}
                              onChange={(e) =>
                                setAmountTransferFound(e.target.value)
                              }
                            />
                          </Grid>
                          <Grid item>
                            <SelectInput
                              style={{ minWidth: '300px' }}
                              label="To Account Id"
                              source="toAccountId"
                              onChange={(e) => setToAccountId(e.target.value)}
                              choices={record.dwollaBankFundingSources?.map(
                                (source) => {
                                  if (source.status === 'verified') {
                                    return {
                                      id: source.id,
                                      name: `${source.name} - ${source.id}`,
                                    };
                                  }
                                }
                              )}
                            />
                          </Grid>
                        </Grid>
                      </DialogContent>
                      <DialogActions>
                        {/* TODO: disable this button if the value is not between
                        0 and the wallet balance. */}
                        {/* TODO: disable this button
                        if a valid destination funding source isn't present. */}
                        <Button
                          onClick={() => setWithdrawalFundsDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={() => withdrawalFunds(record)}
                          disabled={disableWithdrawalBtn(record)}
                        >
                          Withdraw
                        </Button>
                      </DialogActions>
                    </Dialog>
                  </>
                );
              }}
            />
            <FunctionField
              label="Dwolla funding sources"
              style={{ width: '100%' }}
              render={(record) => {
                if (
                  record.dwollaBankFundingSources &&
                  !record.dwollaBankFundingSources.length > 0 &&
                  record.plaidItems &&
                  record.plaidItems.length === 0
                ) {
                  return (
                    <Alert severity="info">
                      No funding sources for this user
                    </Alert>
                  );
                }
                const handleSendMicroDeposit = (fundingSourceId) => {
                  dataProvider
                    .update('UserDwollaFundingSource', {
                      data: {
                        id: 1,
                        fundingSourceId: fundingSourceId,
                        sendMicroDeposits: true,
                      },
                    })
                    .then(() => {
                      notify('Successfully sent Micro-Deposit');
                      refresh();
                    })
                    .catch(() => {
                      notify('Error sending Micro-Deposit', { type: 'error' });
                    });
                };

                const handleMakePreferredReceivingAccount = (
                  fundingSourceId
                ) => {
                  return () => {
                    const updatedRecord = {
                      id: parseInt(id, 10),
                      preferredReceivingAccount: fundingSourceId,
                    };
                    dataProvider
                      .update('User', {
                        data: updatedRecord,
                      })
                      .then(() => {
                        notify(
                          'Preferred receiving account successfully updated'
                        );
                        refresh();
                      })
                      .catch(() => {
                        notify(
                          'Error updating preferred receiving account',
                          'error'
                        );
                      });
                  };
                };

                return (
                  <>
                    {(record.dwollaCustomer &&
                      record.preferredReceivingAccount ===
                        record.dwollaCustomer.id) ||
                    record.preferredReceivingAccount === null ? (
                      <Alert severity="info">
                        {record.fullName}'s preferred receiving account is
                        currently their <b>Energea Wallet</b>.
                      </Alert>
                    ) : null}
                    {/* <MuiList> */}
                    <Table
                      /*className={classes.table}*/ aria-label="simple table"
                    >
                      <TableBody>
                        {record.plaidItems &&
                          record.plaidItems.map((plaidItem) => {
                            if (
                              !plaidItem ||
                              !plaidItem.id ||
                              !plaidItem.accounts
                            )
                              return null;
                            return plaidItem.accounts.map((plaidAccount) => {
                              if (
                                plaidAccount.verification_status ===
                                'pending_manual_verification'
                              )
                                return (
                                  <TableRow
                                    key={`pending-plaid-account-row-${plaidItem.id}-${plaidAccount.account_id}`}
                                  >
                                    <TableCell component="th" scope="row">
                                      <AccountBalance
                                        style={{
                                          fontSize: '1.2rem',
                                          marginBottom: '-2px',
                                          marginRight: '6px',
                                        }}
                                      />
                                      {plaidAccount.name}
                                    </TableCell>
                                    <TableCell component="th" scope="row" />
                                    <TableCell component="th" scope="row">
                                      <Chip
                                        avatar={<Help />}
                                        color="default"
                                        label="Pending Verification"
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <Fab
                                        onClick={handleVerifyAccount(
                                          plaidItem,
                                          record
                                        )}
                                        variant="extended"
                                        color="secondary"
                                      >
                                        Verify Micro-Deposits
                                      </Fab>
                                    </TableCell>
                                    {/* <TableCell
                                  component="th"
                                  scope="row"
                                  align="right"
                                >
                                  <IconButton
                                    onClick={this.handleRemovePlaidAccount(
                                      plaidItem
                                    )}
                                  >
                                    <DeleteForever color="error" />
                                  </IconButton>
                                </TableCell> */}
                                  </TableRow>
                                );
                              return null;
                            });
                          })}

                        {record.plaidItems &&
                          record.plaidItems.map((plaidItem) => {
                            if (
                              !plaidItem ||
                              !plaidItem.id ||
                              !plaidItem.accounts
                            )
                              return null;
                            return plaidItem.accounts.map((plaidAccount) => {
                              if (
                                plaidAccount.verification_status ===
                                  'manually_verified' &&
                                !plaidAccount.processor_token
                              )
                                return (
                                  <TableRow
                                    key={`pending-plaid-account-row-${plaidItem.id}-${plaidAccount.account_id}`}
                                  >
                                    <TableCell component="th" scope="row">
                                      <AccountBalance
                                        style={{
                                          fontSize: '1.2rem',
                                          marginBottom: '-2px',
                                          marginRight: '6px',
                                        }}
                                      />
                                      {plaidAccount.name}
                                    </TableCell>
                                    <TableCell component="th" scope="row" />
                                    <TableCell component="th" scope="row">
                                      <Chip
                                        avatar={<Help />}
                                        color="default"
                                        label="Pending Verification"
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <Fab
                                        onClick={handleSetProcessTokenAccount(
                                          plaidItem,
                                          record,
                                          plaidAccount
                                        )}
                                        variant="extended"
                                        color="secondary"
                                      >
                                        Set Processor Token
                                      </Fab>
                                    </TableCell>
                                    {/* <TableCell
                                  component="th"
                                  scope="row"
                                  align="right"
                                >
                                  <IconButton
                                    onClick={this.handleRemovePlaidAccount(
                                      plaidItem
                                    )}
                                  >
                                    <DeleteForever color="error" />
                                  </IconButton>
                                </TableCell> */}
                                    <TableCell></TableCell>
                                  </TableRow>
                                );
                              return null;
                            });
                          })}

                        {record.dwollaBankFundingSources &&
                          record.dwollaBankFundingSources.map(
                            (fundingSource, index) => {
                              if (!fundingSource) {
                                return (
                                  <TableRow
                                    key={`funding-source-row-error-${index}`}
                                  >
                                    <TableCell component="th" scope="row">
                                      Error retrieving funding Src
                                    </TableCell>
                                  </TableRow>
                                );
                              }
                              const isPreferredReceivingAcct =
                                record.preferredReceivingAccount ===
                                fundingSource.id;
                              return (
                                <TableRow
                                  key={`funding-source-row-${fundingSource.id}`}
                                >
                                  <TableCell component="th" scope="row">
                                    <AccountBalance
                                      style={{
                                        fontSize: '1.2rem',
                                        marginBottom: '-2px',
                                        marginRight: '6px',
                                      }}
                                    />
                                    {fundingSource.bankName}
                                  </TableCell>
                                  <TableCell component="th" scope="row">
                                    <b>{fundingSource.name}</b>
                                  </TableCell>
                                  <TableCell component="th" scope="row">
                                    <Chip
                                      style={{
                                        backgroundColor:
                                          fundingSource.status === 'verified'
                                            ? theme.palette.success.main
                                            : null,
                                      }}
                                      avatar={
                                        fundingSource.status === 'verified' ? (
                                          <CheckCircle
                                            style={{
                                              backgroundColor:
                                                fundingSource.status ===
                                                'verified'
                                                  ? theme.palette.success.main
                                                  : null,
                                            }}
                                          />
                                        ) : (
                                          <Close />
                                        )
                                      }
                                      color={
                                        fundingSource.status === 'verified'
                                          ? 'primary'
                                          : 'default'
                                      }
                                      label={fundingSource.status}
                                    />
                                  </TableCell>
                                  <TableCell>
                                    <Chip
                                      avatar={
                                        isPreferredReceivingAcct ? (
                                          <CheckCircle
                                            style={{
                                              backgroundColor: 'primary',
                                            }}
                                          />
                                        ) : (
                                          <RadioButtonUnchecked />
                                        )
                                      }
                                      color={
                                        isPreferredReceivingAcct
                                          ? 'primary'
                                          : 'default'
                                      }
                                      label={
                                        isPreferredReceivingAcct
                                          ? 'Current receiving account'
                                          : 'Set as receiving account'
                                      }
                                      onClick={
                                        isPreferredReceivingAcct
                                          ? null
                                          : handleMakePreferredReceivingAccount(
                                              fundingSource.id
                                            )
                                      }
                                    />
                                  </TableCell>
                                  <TableCell
                                    component="th"
                                    scope="row"
                                    align="right"
                                    // style={{ width: fullScreen ? '72px' : null }}
                                  >
                                    <IconButton
                                      onClick={() => {
                                        if (
                                          window.confirm(
                                            'Are you sure you wish to delete this account?'
                                          )
                                        ) {
                                          deleteFundingSource(fundingSource.id);
                                        }
                                      }}
                                      size="large"
                                    >
                                      <Delete color="error" />
                                    </IconButton>
                                  </TableCell>
                                  <TableCell
                                    component="th"
                                    scope="row"
                                    align="right"

                                    // style={{ width: fullScreen ? '72px' : null }}
                                  >
                                    <Button
                                      disabled={
                                        fundingSource.status !== 'unverified'
                                      }
                                      onClick={() => {
                                        handleSendMicroDeposit(
                                          fundingSource.id
                                        );
                                      }}
                                    >
                                      Issue Micro-Deposits
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              );
                            }
                          )}
                      </TableBody>
                    </Table>
                    {record.preferredReceivingAccount &&
                    record.dwollaCustomer &&
                    record.preferredReceivingAccount !==
                      record.dwollaCustomer.id ? (
                      <Button
                        style={{ marginTop: '1em' }}
                        color="primary"
                        variant="contained"
                        onClick={handleMakePreferredReceivingAccount(null)}
                      >
                        <Typography variant="body2">
                          Set <b>Energea Wallet</b> as Receiving Account
                        </Typography>
                      </Button>
                    ) : null}
                  </>
                );
              }}
            />
            <FunctionField
              label="Dwolla funding sources"
              style={{ width: '100%' }}
              render={(record) => {
                return (
                  <>
                    <Typography>Plaid Item list (internal use only)</Typography>
                    <Table
                      /*className={classes.table}*/ aria-label="simple table"
                    >
                      <TableBody>
                        {record.plaidItems &&
                          record.plaidItems.map((plaidItem) => {
                            if (!plaidItem || !plaidItem.id) {
                              return null;
                            }
                            return (
                              <TableRow
                                key={`internal-plaid-item-row-${plaidItem.id}`}
                              >
                                <TableCell component="th" scope="row">
                                  {plaidItem.id}
                                </TableCell>
                                <TableCell component="th" scope="row">
                                  {plaidItem.item_id}
                                </TableCell>
                                <TableCell
                                  component="th"
                                  scope="row"
                                  align="right"

                                  // style={{ width: fullScreen ? '72px' : null }}
                                >
                                  <Button
                                    disabled={!!plaidItem.identity}
                                    onClick={handleGetIdentity(plaidItem.id)}
                                  >
                                    Get Identity ($1.75/request)
                                  </Button>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                      </TableBody>
                    </Table>
                    <Table
                      /*className={classes.table}*/ aria-label="simple table"
                    >
                      <TableBody>
                        {record.plaidItems &&
                          record.plaidItems.map((plaidItem) => {
                            if (
                              !plaidItem ||
                              !plaidItem.id ||
                              !plaidItem.identity
                            )
                              return null;
                            return plaidItem.identity.accounts.map(
                              (plaidAccount) => {
                                return (
                                  <TableRow
                                    key={`internal-plaid-account-row-${plaidItem.id}-${plaidAccount.account_id}`}
                                  >
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.name}
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      <b>{plaidAccount.account_id}</b>
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      <b>
                                        {numeral(
                                          plaidAccount.balances.available
                                        ).format('$0,0')}
                                      </b>
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.owners.map((owner) => {
                                        return (
                                          <>
                                            <b>Emails</b>
                                            <br />

                                            <span>
                                              {owner.emails
                                                .map((email) => email.data)
                                                .join(', ')}
                                            </span>
                                            <br />
                                            <b>Names</b>
                                            <br />
                                            <span>
                                              {owner.names.join(', ')}
                                            </span>
                                            <br />
                                            <b>Addresses</b>
                                            <br />
                                            {owner.addresses.map((address) => {
                                              return (
                                                <>
                                                  <span>
                                                    {address.street}{' '}
                                                    {address.city}{' '}
                                                    {address.region},{' '}
                                                    {address.postal_code}{' '}
                                                    {address.country}
                                                  </span>
                                                  <br />
                                                </>
                                              );
                                            })}
                                          </>
                                        );
                                      })}
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.mask}
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.name}
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.official_name}
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.subtype}
                                    </TableCell>
                                    <TableCell component="th" scope="row">
                                      {plaidAccount.type}
                                    </TableCell>
                                  </TableRow>
                                );
                              }
                            );
                          })}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </FormTab>
          <FormTab label="Sub Accounts">
            <FormDataConsumer>
              {({ formData, ...rest }) => {
                if (!formData?.subAccounts?.[0]) {
                  return (
                    <Alert severity="info">
                      There are no sub accounts for this user
                    </Alert>
                  );
                }
                return formData?.subAccounts?.map((subAccount) => {
                  if (!subAccount.status) {
                    return <CircularProgress />;
                  }
                  return (
                    <Card
                      key={`sub-account-card-${subAccount.id}`}
                      style={{ margin: '.5rem' }}
                    >
                      <CardActionArea
                        style={{ padding: '1rem' }}
                        component={ReactRouterLink}
                        to={`/subaccount/${subAccount.id}`}
                      >
                        <Grid container>
                          <Grid item>
                            {subAccount.closedDt ? (
                              <Typography style={{ color: 'red' }}>
                                <b>
                                  Account CLOSED{' '}
                                  {moment(subAccount.closedDt).format(
                                    'M/D/YYYY'
                                  )}
                                </b>
                              </Typography>
                            ) : null}
                            <Typography>
                              <b>Name: </b>
                              {subAccount.name}
                            </Typography>
                            <Typography>
                              <b>Status: </b>
                              {subAccount.status.statusText === 'pending'
                                ? 'Pending Transfers'
                                : subAccount.status.statusText}
                            </Typography>
                            {subAccount.subAccountType.id === 37 ? (
                              <Typography>
                                <b>Estimated MTC Investable Balance: </b>
                                {numeral(
                                  subAccount.mtcEstimatedInvestableBalance
                                ).format('$0,0.00')}
                              </Typography>
                            ) : (
                              <>
                                <Typography>
                                  <b>Pending Balance: </b>
                                  {numeral(
                                    subAccount.status.pendingBalance
                                  ).format('$0,0.00')}
                                </Typography>
                                <Typography>
                                  <b>Avail. Balance: </b>
                                  {numeral(subAccount.status.balance).format(
                                    '$0,0.00'
                                  )}
                                </Typography>
                              </>
                            )}
                          </Grid>
                          <Grid item container justifyContent="flex-end">
                            <Button
                              variant="text"
                              endIcon={<ArrowForward />}
                              component={Link}
                              to={`/SubAccount/${subAccount.id}`}
                            >
                              See more
                            </Button>
                          </Grid>
                        </Grid>
                      </CardActionArea>
                    </Card>
                  );
                });
              }}
            </FormDataConsumer>
          </FormTab>
          <FormTab label="beneficiaries" path="beneficiaries">
            <UserBeneficiaries />
          </FormTab>
          <FormTab label="referral">
            <FunctionField
              label="referral url"
              render={(record) => {
                return <Typography>{record.referralUrl}</Typography>;
              }}
            />
            <FunctionField
              label="referrer"
              render={(record) => {
                if (!record.referrer) {
                  return (
                    <Alert severity="info">This user was not referred</Alert>
                  );
                }
                return (
                  <Typography>
                    <a href={`/User/${record.referrer?.id}`}>
                      {record.referrer?.fullName}
                    </a>
                  </Typography>
                );
              }}
            />
            <FunctionField
              label="referrals"
              render={(record) => {
                if (!record.referrals || record.referrals.length === 0) {
                  return (
                    <Alert severity="info">
                      There are no referrals for this user
                    </Alert>
                  );
                }
                return (
                  <>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>
                            <b>Referee</b>
                          </TableCell>
                          <TableCell>
                            <b>Completed Dt</b>
                          </TableCell>
                          <TableCell>
                            <b>Deactivated Dt</b>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {record.referrals.map((referral) => {
                          return (
                            <TableRow key={`user-referral-list-${referral.id}`}>
                              <TableCell>
                                <a href={`/User/${referral.referee?.id}`}>
                                  {referral.referee?.fullName}
                                </a>
                              </TableCell>
                              <TableCell>
                                {referral.referralCompletedDt
                                  ? moment(referral.referralCompletedDt).format(
                                      'MMM D, YYYY'
                                    )
                                  : null}
                              </TableCell>
                              <TableCell>
                                {referral.deactivatedDt
                                  ? moment(referral.deactivatedDt).format(
                                      'MMM D, YYYY'
                                    )
                                  : null}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </>
                );
              }}
            />
          </FormTab>
          <FormTab label="auth documents" path="authdocs">
            <UserAuthenticationDocuments />
          </FormTab>
          <FormTab label="documents">
            <UserDocuments />
          </FormTab>
          <FormTab label="Logins" path="logins">
            <Typography variant="h6">
              <b>Logins</b>
            </Typography>
            {/* TODO: Add a map with locations of every login */}
            <Grid container style={{ width: '100%' }}>
              <Grid item xs={12}>
                <FunctionField
                  fullWidth
                  label="Recent IP Address"
                  render={(record) => {
                    if (!record.userLogins || record.userLogins.length === 0) {
                      return (
                        <Alert severity="info">
                          This user hasn't logged in since we began tracking
                          logins ourself at the end of April 2023.
                        </Alert>
                      );
                    }
                    const mapMarkers = [];
                    if (record.latitude && record.longitude) {
                      mapMarkers.push({
                        onMarkerClick: () => {},
                        latitude: parseFloat(record.latitude),
                        longitude: parseFloat(record.longitude),
                        color: theme.palette.green.dark,
                        sizePx: '80px',
                      });
                    }
                    record.userLogins
                      .filter((userLogin) => userLogin.ipAddress)
                      .forEach((userLogin) => {
                        mapMarkers.push({
                          onMarkerClick: () => {},
                          latitude: userLogin.ipAddress.latitude,
                          longitude: userLogin.ipAddress.longitude,
                          color: theme.palette.green.main,
                          sizePx: '40px',
                        });
                      });

                    return (
                      <Grid container style={{ width: '100%' }}>
                        <Grid item>
                          <MuiList dense>
                            {record.userLogins.map((userLogin, index) => {
                              const { id, loginDt, ipAddress, device } =
                                userLogin;
                              let ipJsx = 'No IP address recorded.';
                              if (ipAddress) {
                                const {
                                  ipAddress: ip,
                                  city,
                                  state,
                                  postalCode,
                                  country,
                                  latitude,
                                  longitude,
                                } = ipAddress;
                                ipJsx = (
                                  <Grid style={{ marginBottom: '.5rem' }}>
                                    <Typography
                                      variant="body2"
                                      style={{ fontWeight: 'bold' }}
                                    >
                                      IP Address: {ip}
                                    </Typography>
                                    <Typography variant="body2">
                                      Location: {city}, {state} {postalCode},{' '}
                                      {country} ({latitude}, {longitude})
                                    </Typography>
                                  </Grid>
                                );
                              }
                              let deviceJsx = 'No device recorded.';
                              if (device) {
                                const {
                                  visitorId,
                                  os,
                                  browser,
                                  device: deviceName,
                                  firstSeenDt,
                                  lastSeenDt,
                                } = device;
                                deviceJsx = (
                                  <Grid>
                                    <Typography
                                      variant="body2"
                                      style={{ fontWeight: 'bold' }}
                                    >
                                      Device: {visitorId}
                                    </Typography>
                                    <Typography variant="body2">
                                      OS: {os}
                                    </Typography>
                                    <Typography variant="body2">
                                      Browser: {browser}
                                    </Typography>
                                    <Typography variant="body2">
                                      Device: {deviceName} (First seen:{' '}
                                      {moment(firstSeenDt).format(
                                        'MMM D, YYYY'
                                      )}
                                      , Last seen:{' '}
                                      {moment(lastSeenDt).format('MMM D, YYYY')}
                                      )
                                    </Typography>
                                  </Grid>
                                );
                              }

                              return (
                                <ListItem key={`user-login-${id}`}>
                                  <ListItemText
                                    primary={
                                      <Typography
                                        variant="body1"
                                        style={{ fontWeight: 'bold' }}
                                        gutterBottom
                                      >
                                        {moment(loginDt).format(
                                          'MMM D, YYYY HH:mm:ss'
                                        )}
                                      </Typography>
                                    }
                                    secondary={
                                      <>
                                        {ipJsx} {deviceJsx}
                                      </>
                                    }
                                  />
                                </ListItem>
                              );
                            })}
                          </MuiList>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Card
                            style={{
                              borderRadius: theme.shape.borderRadius,
                            }}
                          >
                            <Suspense fallback={<div>Loading map...</div>}>
                              {location.pathname.includes('logins') && (
                                <UserMap markers={mapMarkers} />
                              )}
                            </Suspense>
                          </Card>
                        </Grid>
                      </Grid>
                    );
                  }}
                />
              </Grid>
            </Grid>
          </FormTab>
          <FormTab label="utilities">
            <Typography variant="h6">
              <b>Test User</b>
            </Typography>
            <Divider style={{ width: '100%', margin: '.5rem 0' }} />
            <Typography variant="body2" gutterBottom>
              Setting a user as the test user will allow you to login as them.
              This is useful for troubleshooting issues that users are
              experiencing.
              <br />
              <br />
              Once you click the button below, a new tab should open at
              energea.com where you will then need to login with the email{' '}
              <b><EMAIL></b>. If you need the password, please contact
              IT. If you were previously logged in to energea.com as yourself or
              a different user, you may need to log out first before logging
              back in as the test user.
              <br />
              <br />
              <div style={{ color: theme.palette.error.main }}>
                <b>
                  Be warned that any actions taken while logged in as the user
                  will be executed including things such as making investments,
                  changing personal information, reading notifications, etc.
                </b>
              </div>
            </Typography>
            <FunctionField
              label="Test User"
              style={{ marginBottom: '1rem' }}
              render={(record) => (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={setAsTestUser()}
                >
                  Set as Test User
                </Button>
              )}
            />
            <Typography variant="h6">
              <b>Email</b>
            </Typography>
            <Divider style={{ width: '100%', margin: '.5rem 0' }} />
            <Typography paragraph>
              Current Email Address:{' '}
              <EmailField source="email" style={{ fontWeight: 'bold' }} />
            </Typography>
            <Typography paragraph>
              Email Confirmed On:{' '}
              <DateField
                source="confirmationDt"
                showTime
                style={{ fontWeight: 'bold' }}
              />
            </Typography>
            <FunctionField
              label="Registration Email"
              style={{ marginBottom: '1rem' }}
              render={(record) => (
                <Button
                  onClick={sendRegistrationEmail(record)}
                  disabled={!!record.confirmationDt}
                  variant="contained"
                  color="primary"
                >
                  Send Registration Email
                </Button>
              )}
            />
            <FunctionField
              label="Manually Confirm Email"
              style={{ marginBottom: '1rem' }}
              render={(record) => (
                <Button
                  onClick={() => manuallyConfirmEmailAddress(record)}
                  disabled={!!record.confirmationDt}
                  variant="contained"
                  color="primary"
                >
                  Manually Confirm Email
                </Button>
              )}
            />
            <BooleanInput
              helperText="Turn this on when emails have bounced. This will prompt the user to update their email on next login. If they update their email, this field will automatically be turned off again."
              source="invalidEmailFlg"
              label="Invalid Email"
            />
            <Typography variant="h6" style={{ marginTop: '1em' }}>
              <b>Update Email</b>
            </Typography>
            <Divider style={{ width: '100%', margin: '.5rem 0' }} />
            <Grid container style={{ width: '50%' }}>
              <Grid item xs={12} md={10}>
                <FormDataConsumer>
                  {({ formData, ...rest }) => {
                    if (formData.emailUpdateRequest) {
                      return (
                        <Alert
                          severity="warning"
                          action={
                            <Tooltip arrow title="Cancel update request">
                              <IconButton
                                size="small"
                                onClick={cancelUpdateEmailRequest}
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          }
                        >
                          Waiting for confirmation of{' '}
                          <b> {formData.emailUpdateRequest}</b>
                        </Alert>
                      );
                    }
                  }}
                </FormDataConsumer>
                <TextInput source="newEmail" fullWidth />
                <FormDataConsumer>
                  {({ formData, ...rest }) => {
                    return (
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Button
                            fullWidth
                            onClick={() =>
                              updateEmailAddress(formData.newEmail, false)
                            }
                            variant="contained"
                            disabled={!formData.newEmail}
                            color="primary"
                          >
                            Update Email Directly
                          </Button>
                          <FormHelperText>
                            Users email address is updated immediately and
                            marked as confirmed. No emails are sent to the user.
                          </FormHelperText>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Button
                            fullWidth
                            onClick={() =>
                              updateEmailAddress(formData.newEmail, true)
                            }
                            variant="contained"
                            disabled={!formData.newEmail}
                            color="primary"
                          >
                            Update Email & Send Confirmation Emails
                          </Button>
                          <FormHelperText>
                            Users email address will not be updated until they
                            click the link in the confirmation email that
                            clicking this button will send them.
                          </FormHelperText>
                        </Grid>
                      </Grid>
                    );
                  }}
                </FormDataConsumer>
              </Grid>{' '}
            </Grid>
            <Typography variant="h6" style={{ marginTop: '1em' }}>
              <b>Update Phone or MFA</b>
            </Typography>
            <Divider style={{ width: '100%', margin: '.5rem 0' }} />
            <Grid container style={{ width: '50%' }}>
              <Grid item xs={12} md={10}>
                {/* <TextInput
                  label="Primary Phone"
                  source="primaryPhone"
                  fullWidth
                /> */}
                <FormDataConsumer>
                  {({ formData, ...rest }) => {
                    return (
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Button
                            fullWidth
                            onClick={() => resetMFA()}
                            variant="contained"
                            disabled={loading}
                            color="primary"
                          >
                            Reset MFA
                          </Button>
                          <FormHelperText>
                            This will reset the user's multi-factor
                            authentication. If the user is changing their phone
                            number, change it in the above field and{' '}
                            <b>MAKE SURE TO CLICK SAVE BELOW</b>. Then click
                            this button and the new number will be set to their
                            MFA. They will receive an email with the title
                            "Security method reset"
                          </FormHelperText>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Button
                            fullWidth
                            onClick={() => removeMFA()}
                            variant="contained"
                            disabled={
                              !formData.auth0User?.user_metadata?.mfa_opt_in ||
                              loading
                            }
                            color="primary"
                          >
                            Remove MFA
                          </Button>
                          <FormHelperText>
                            {!loading &&
                            !formData.auth0User?.user_metadata?.mfa_opt_in ? (
                              <>This user is not currently enrolled in MFA.</>
                            ) : (
                              <>
                                This will remove the MFA requirement from a
                                user's login. This will not "reset" the MFA
                                however, so if the user had a change in phone
                                number or an issue with Google Authenticator,
                                then you will need to reset them. Please don't
                                remove MFA unless you are sure that the user
                                that requested the removal is the user
                                associated with the account...it may be worth
                                manually sending them a unique 6 digit pin to
                                their email before removing mfa.
                              </>
                            )}
                          </FormHelperText>
                        </Grid>
                      </Grid>
                    );
                  }}
                </FormDataConsumer>
              </Grid>
            </Grid>
          </FormTab>
          {/* <FormTab label="Notification Preferences">
          <FunctionField
            label="Notification Preferences"
            render={(record) => (
              <UserNotificationPreferences  data={record} />
            )}
          />
        </FormTab> */}
        </TabbedForm>
      </Edit>
    </>
  );
};

export const UserCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <Grid container style={{ marginBottom: '2rem' }}>
            <BooleanInput
              source="employeeFlg"
              fullWidth
              helperText="Turning this on will grant basic CMS access and send an email with directions on setting up their CMS account."
            />
          </Grid>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (formData.employeeFlg) {
                return (
                  <TextInput
                    source="employeeTitle"
                    label="Employee title"
                    fullWidth
                    required
                  />
                );
              }
              return null;
            }}
          </FormDataConsumer>
          <TextInput source="firstName" fullWidth required />
          <TextInput source="lastName" fullWidth required />
          <TextInput source="email" fullWidth required />
          <TextInput
            source="oktaId"
            label="Okta ID (not required for employees)"
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
