import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Datagrid,
  DateField,
  FunctionField,
  List,
  NumberField,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { Typography } from '@mui/material';

const entityName = 'Pending Dwolla Dividend Payment';

export const PendingDwollaDividendPaymentList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="Dividend"
          linkSource="dividend.id"
          labelSource="dividend.label"
          label="Dividend"
        />
        <TextField source="fromAccountId" label="From account ID" />
        <TextField source="toAccountId" label="To account ID" />
        <NumberField source="amount" />
        <FunctionField
          label="Dwolla mass payment IDs"
          render={(record) => (
            <Typography variant="body2">
              {record?.dwollaMassPaymentIds?.join(', ')}
            </Typography>
          )}
        />
      </Datagrid>
    </List>
  );
};
