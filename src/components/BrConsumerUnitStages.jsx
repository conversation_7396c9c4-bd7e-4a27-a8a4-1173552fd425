import React from 'react';
import { useParams } from 'react-router-dom';

import {
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Chip, Grid, Icon } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Consumer Unit Stage';

export const BrConsumerUnitStageEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="name"
              fullWidth
              required
              helperText="This is displayed to the customer on energea.com.br"
            />
            <TextInput
              source="description"
              fullWidth
              multiline
              helperText="This may be displayed to the customer on energea.com.br"
            />
            <TextInput
              source="color"
              fullWidth
              disabled={!isIT}
              helperText="Hex color (format: #121212) that is displayed to the customer on energea.com.br"
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrConsumerUnitStageList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <TextField source="description" />
        <FunctionField
          label="Appearance"
          sortable={false}
          render={(record) => (
            <Chip
              variant="caption"
              style={{
                backgroundColor: record.color,
                color: '#fff',
              }}
              label={record.name}
            />
          )}
        />
      </Datagrid>
    </List>
  );
};

export const BrConsumerUnitStageCreate = () => {
  const { permissions } = usePermissions();
  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;
  return (
    <Create title={`Create ${entityName}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              source="name"
              required
              fullWidth
              helperText="These are displayed on the frontend in energea.com.br"
            />
            <TextInput
              source="description"
              fullWidth
              multiline
              helperText="This may be displayed to the customer on energea.com.br"
            />
            <TextInput
              source="color"
              fullWidth
              disabled={!isIT}
              helperText="Hex color (format: #121212) this is displayed to the customer on energea.com.br"
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
