import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Button,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useDataProvider, useNotify, usePermissions } from 'react-admin';
import moment from 'moment';
import numeral from 'numeral';
import { AddCircle, Edit, OpenInNew, Image } from '@mui/icons-material';
import { OMReportDialog } from './OMReportDialog';
import { OMReportImageDialog } from './OMReportImageDialog';
import { ThirdPartyReportDialog } from './ThirdPartyReportDialog';

export const OMTicketExpansionPanel = (props) => {
  const {
    open,
    ticket,
    fullScreen,
    setSelectedTicketEdit,
    setUpdateTicketOpen,
  } = props;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const [createReportOpen, setCreateReportOpen] = useState(false);
  const [updateReportOpen, setUpdateReportOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [thirdPartyReportOpen, setThirdPartyReportOpen] = useState(false);
  const [selectedThirdPartyReport, setSelectedThirdPartyReport] =
    useState(null);

  const { permissions } = usePermissions();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  const fetchData = () => {
    setLoading(true);
    setError(null);
    dataProvider
      .getOne('OMFieldTicket', {
        id: ticket.id,
      })
      .then(
        (res) => {
          setData(res.data);
          setLoading(false);
        },
        (e) => {
          console.error('Error retrieving ticket data', e);
          setError(e);
          setLoading(false);
        }
      );
  };

  if (!open) return null;

  if (!error && !loading && !data) {
    fetchData();
  }

  const roleNames = permissions?.roles?.map((r) => r.name) || [];
  const isThirdPartyI9 = roleNames.includes('OMPartnerI9');
  const isThirdPartyRun = roleNames.includes('OMPartnerRun');
  const isThirdPartySolRen = roleNames.includes('OMPartnerSolRen');

  let isTicketEditable = false;
  // TODO: update this to allow OMRead guys to be able to edit tickets
  if (roleNames.includes('OMWrite') || roleNames.includes('ITWrite')) {
    isTicketEditable = true;
  }
  if (isThirdPartyI9 || isThirdPartySolRen || isThirdPartyRun) {
    if (data?.ticketOwner?.user?.roles) {
      const roles = data.ticketOwner.user.roles.map((r) => r.name);
      if (isThirdPartyI9 && roles.includes('OMPartnerI9')) {
        isTicketEditable = true;
      } else if (isThirdPartySolRen && roles.includes('OMPartnerSolRen')) {
        isTicketEditable = true;
      } else if (isThirdPartyRun && roles.includes('OMPartnerRun')) {
        isTicketEditable = true;
      }
    }
  }

  if (!data) {
    return (
      <TableRow style={{ position: 'relative' }}>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={10}>
          <Grid
            container
            style={{ width: '100%', minHeight: '12rem' }}
            justifyContent="center"
            alignItems="center"
          >
            <Grid item>
              <CircularProgress />
            </Grid>
          </Grid>
        </TableCell>
      </TableRow>
    );
  }

  const dataList = [
    {
      label: 'Project',
      value: data.project.name,
    },
    {
      label: 'Title',
      value: data.title,
    },
    {
      label: 'Ticket Owner',
      value: data.ticketOwner?.fullName,
    },
    {
      label: 'Ticket Author',
      value: data.author?.fullName,
    },
    {
      label: 'Priority',
      value: data.priorityNo,
    },
    {
      label: 'Truck',
      value: data.project.omTruck?.name,
    },
    {
      label: 'Equipment Item',
      value: data.equipmentItem?.label,
    },
    {
      label: 'Device Name',
      value: data.deviceName,
    },
    {
      label: 'Internal Notes',
      value: data.notes,
    },
    {
      label: 'Status Notes',
      value: data.statusNotes,
    },
    {
      label: 'Ticket Type',
      value: data.omTicketType?.name,
    },
    {
      label: 'Est. Percentage Generation Loss',
      value: `${numeral(data.estimatedPercentageLoss || 0).format('0[.]0')}%`,
    },
    {
      label: 'Est. Generation Loss (kWh)',
      value: numeral(data.estimatedImpact?.estimatedGenerationLoss).format(
        '0,0'
      ),
    },
    {
      label: 'Est. Revenue Loss (USD)',
      value: numeral(data.estimatedImpact?.estimatedRevenueLossUSD).format(
        '$0,0'
      ),
    },
    {
      label: 'Acknowledged Date',
      value: moment(data.acknowledgedDt).format('MMM D, YYYY h:mma'),
    },
    {
      label: 'Start Date',
      value: moment(data.startDt).format('MMM D, YYYY h:mma'),
    },
    {
      label: 'End Date',
      value: data.endDt ? moment(data.endDt).format('MMM D, YYYY h:mma') : null,
    },
    {
      label: 'Client Notification Required',
      value: data.clientNotificationRequiredFlg ? 'Yes' : 'No',
    },
    {
      label: 'Client Notified Date',
      value: data.clientNotifiedDt
        ? moment(data.clientNotifiedDt).format('MMM D, YYYY h:mma')
        : null,
      hidden: !data.clientNotificationRequiredFlg,
    },
    {
      label: 'Client Notification Type',
      value: data.clientNotificationType,
      hidden: !data.clientNotificationRequiredFlg,
    },
  ];

  const handleApproveReport = (report) => {
    setLoading(true);
    const employeeIds = report.employees.map((employee) => employee.id) || [];

    // TODO: FetchData again

    // Update Report
    dataProvider
      .update('OMReport', {
        data: {
          id: report.id,
          employeeIds,
          approvedDt: moment().toDate(),
        },
      })
      .then(
        () => {
          notify('OM Report Approved successfully', { type: 'success' });
          fetchData();
          setLoading(false);
        },
        (e) => {
          console.error('Error approving OM Report', e);
          setLoading(false);
        }
      );

    if (report.resolvedFlg) {
      // Update Ticket
      dataProvider
        .update('OMTicket', {
          data: {
            id: ticket.id,
            endDt: moment(report.endDt).format('YYYY-MM-DD HH:mm:ss'),
          },
        })
        .then(
          () => {
            setLoading(false);
          },
          (e) => {
            console.error('Error updating OM Ticket', e);
            setLoading(false);
          }
        );
    }
  };

  return (
    <TableRow>
      <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={10}>
        <Collapse in={open} timeout="auto" unmountOnExit>
          <Grid container style={{ padding: fullScreen ? '2rem 0' : '2rem' }}>
            <Grid item xs={12}>
              <Grid container justifyContent="space-between">
                <Grid item>
                  <Typography variant="h5">Ticket Details:</Typography>
                </Grid>
                {isTicketEditable && (
                  <Grid item>
                    <Button
                      color="primary"
                      variant="contained"
                      // component={Link}
                      endIcon={<Edit />}
                      // to={`/OMTicket/${data.id}`}
                      onClick={() => {
                        setSelectedTicketEdit(ticket);
                        setUpdateTicketOpen(true);
                      }}
                    >
                      {fullScreen ? 'Edit' : 'Edit Ticket'}
                    </Button>
                  </Grid>
                )}
              </Grid>
              <Table size="small">
                <TableBody>
                  {dataList
                    .filter((dataPoint) => !dataPoint.hidden)
                    .map((dataPoint) => (
                      <TableRow key={`ticket-${data.id}-${dataPoint.label}`}>
                        <TableCell>
                          <b>{dataPoint.label}</b>
                        </TableCell>
                        <TableCell>{dataPoint.value}</TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </Grid>
            <Grid item xs={12} style={{ marginTop: '1rem' }}>
              <Grid
                container
                justifyContent="space-between"
                alignItems="center"
              >
                <Grid item>
                  <Typography variant="h5">Service Reports:</Typography>
                </Grid>
                {isTicketEditable && (
                  <Grid item>
                    <Button
                      color="primary"
                      variant="contained"
                      style={{ margin: '.5rem' }}
                      endIcon={<AddCircle />}
                      onClick={() => setCreateReportOpen(true)}
                    >
                      {fullScreen ? 'Create' : 'Create Report'}
                    </Button>
                  </Grid>
                )}
              </Grid>
              <Grid item xs={12}>
                {data.omReports?.length > 0 ? (
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Start Date</TableCell>
                        {!fullScreen ? <TableCell>End Date</TableCell> : null}
                        {!fullScreen ? (
                          <TableCell>Diagnostic Procedure Notes</TableCell>
                        ) : null}
                        {!fullScreen ? (
                          <TableCell>PPE Level and Gear Notes</TableCell>
                        ) : null}
                        {!fullScreen ? (
                          <TableCell>Parts Used Notes</TableCell>
                        ) : null}
                        {!fullScreen ? (
                          <TableCell>Parts Ordered Notes</TableCell>
                        ) : null}
                        {!fullScreen ? <TableCell>RMA Notes</TableCell> : null}
                        {!fullScreen ? (
                          <TableCell>Report Type</TableCell>
                        ) : null}
                        <TableCell>Resolves Ticket?</TableCell>
                        <TableCell>Report Status</TableCell>
                        <TableCell>Images</TableCell>
                        <TableCell>3rd Party Report</TableCell>
                        {!fullScreen && isTicketEditable ? <TableCell /> : null}
                        {!fullScreen && isTicketEditable ? <TableCell /> : null}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {data.omReports?.map((report) => (
                        <TableRow
                          key={`om-report-${report.id}-ticket-${ticket.id}`}
                          onClick={
                            fullScreen
                              ? () => {
                                  setSelectedReport(report);
                                  setUpdateReportOpen(true);
                                }
                              : null
                          }
                        >
                          <TableCell>
                            {report.startDt
                              ? moment(report.startDt).format(
                                  'MMM D, YYYY h:mma'
                                )
                              : null}
                          </TableCell>
                          {!fullScreen ? (
                            <TableCell>
                              {report.endDt
                                ? moment(report.endDt).format(
                                    'MMM D, YYYY h:mma'
                                  )
                                : null}
                            </TableCell>
                          ) : null}
                          {!fullScreen ? (
                            <TableCell>{report.notes}</TableCell>
                          ) : null}
                          {!fullScreen ? (
                            <TableCell>{report.ppeNotes}</TableCell>
                          ) : null}
                          {!fullScreen ? (
                            <TableCell>{report.partsUsedNotes}</TableCell>
                          ) : null}
                          {!fullScreen ? (
                            <TableCell>{report.partsOrderedNotes}</TableCell>
                          ) : null}
                          {!fullScreen ? (
                            <TableCell>{report.rmaNotes}</TableCell>
                          ) : null}
                          {!fullScreen ? (
                            <TableCell>{report.omReportType?.name}</TableCell>
                          ) : null}
                          <TableCell>
                            {report.resolvedFlg ? 'Yes' : 'No'}
                          </TableCell>
                          <TableCell>
                            {report.submittedDt
                              ? `Submitted by: ${
                                  report.submittedByEmployee?.fullName ||
                                  'unknown'
                                } (${moment(report.submittedDt).format(
                                  'MMM D, YYYY h:mma'
                                )})`
                              : 'Not yet submitted'}
                          </TableCell>
                          <TableCell>
                            <Badge
                              badgeContent={report.omReportImages?.length}
                              color="primary"
                              overlap="circular"
                              showZero
                            >
                              <IconButton
                                onClick={(event) => {
                                  if (fullScreen) {
                                    event.preventDefault();
                                    event.stopPropagation();
                                  }
                                  setSelectedReport(report);
                                  setImageDialogOpen(true);
                                }}
                              >
                                <Image />
                              </IconButton>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {report.thirdPartyFlg ? (
                              <IconButton
                                onClick={(event) => {
                                  if (fullScreen) {
                                    event.preventDefault();
                                    event.stopPropagation();
                                  }
                                  setSelectedThirdPartyReport(report);
                                  setThirdPartyReportOpen(true);
                                }}
                              >
                                <OpenInNew />
                              </IconButton>
                            ) : null}
                          </TableCell>
                          {!fullScreen && isTicketEditable ? (
                            <TableCell align="right">
                              <IconButton
                                onClick={() => {
                                  setSelectedReport(report);
                                  setUpdateReportOpen(true);
                                }}
                              >
                                <Edit />
                              </IconButton>
                            </TableCell>
                          ) : null}
                          {!fullScreen && isTicketEditable
                            ? !report.approvedDt && (
                                <TableCell align="right">
                                  <Button
                                    color="primary"
                                    variant="contained"
                                    onClick={() => handleApproveReport(report)}
                                  >
                                    Approve
                                  </Button>
                                </TableCell>
                              )
                            : null}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <Alert severity="info">
                    No reports have been created yet for this ticket.
                  </Alert>
                )}
              </Grid>
            </Grid>
          </Grid>
          {updateReportOpen && selectedReport ? (
            <OMReportDialog
              action="update"
              omTicketId={ticket.id}
              omTruckId={data.project.omTruck?.id}
              omReport={selectedReport}
              onClose={() => {
                setUpdateReportOpen(false);
                setSelectedReport(null);
                fetchData();
              }}
              fullScreen={fullScreen}
            />
          ) : null}
          {createReportOpen ? (
            <OMReportDialog
              action="create"
              omTicketId={ticket.id}
              omTruckId={data.project.omTruck?.id}
              onClose={() => {
                setCreateReportOpen(false);
                fetchData();
              }}
            />
          ) : null}
          {selectedReport ? (
            <OMReportImageDialog
              isTicketEditable={isTicketEditable}
              reportId={selectedReport.id}
              handleClose={() => {
                setCreateReportOpen(false);
                setSelectedReport(null);
                setImageDialogOpen(false);
                fetchData();
              }}
              handleRefreshParent={() => {
                fetchData();
              }}
              open={imageDialogOpen}
              setOpen={setImageDialogOpen}
              fullScreen={fullScreen}
            />
          ) : null}
          {thirdPartyReportOpen ? (
            <ThirdPartyReportDialog
              isTicketEditable={isTicketEditable}
              reportId={selectedThirdPartyReport.id}
              handleClose={() => {
                setSelectedThirdPartyReport(null);
                setThirdPartyReportOpen(false);
                fetchData();
              }}
              handleRefreshParent={() => {
                fetchData();
              }}
              open={thirdPartyReportOpen}
              setOpen={setThirdPartyReportOpen}
              fullScreen={fullScreen}
            />
          ) : null}
        </Collapse>
      </TableCell>
    </TableRow>
  );
};
