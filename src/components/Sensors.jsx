import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  Filter,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { <PERSON>ert, Grid, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';
import LegendItem from './LegendItem';

const entityName = 'Sensor';

export const SensorEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <ReferenceInput source="sensorType.id" reference="SensorType">
              <SelectInput
                label="Sensor Type"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <TextInput
              label="Data key"
              source="dataAttribute"
              fullWidth
              helperText="Information used to fetch the correct sensor from the corresponding service. (SCADA ex: 317) (SolarEdge ex: ambientTemperature)"
            />
            <TextInput source="serialNumber" fullWidth />
            <ReferenceInput source="equipmentItem.id" reference="EquipmentItem">
              <SelectInput
                label="Equipment Item"
                fullWidth
                allowEmpty
                optionText="label"
                helperText={
                  <span>
                    If you don't see the equipment type you need, create it{' '}
                    <a href="/EquipmentItem/create">here</a>.
                  </span>
                }
              />
            </ReferenceInput>
            <ReferenceInput source="solarEdgeSite.id" reference="SolarEdgeSite">
              <SelectInput
                label="SolarEdge Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="scadaSystem.id" reference="ScadaSystemLite">
              <SelectInput
                label="SCADA System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="sgdSystem.id" reference="SgdSystem">
              <SelectInput
                label="SGD System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="smaSite.id" reference="SmaSite">
              <SelectInput
                label="SMA Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="flexOmSite.id" reference="FlexOmSite">
              <SelectInput
                label="FlexOM Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="sungrowSystem.id" reference="SungrowSystem">
              <SelectInput
                label="Sungrow System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput source="solisSystem.id" reference="SolisSystem">
              <SelectInput
                label="Solis System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="sunExchangeSite.id"
              reference="SunExchangeSite"
            >
              <SelectInput
                label="AMMP Site"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
            <ReferenceInput
              source="powerFactorSystem.id"
              reference="PowerFactorSystem"
            >
              <SelectInput
                label="PowerFactor System"
                fullWidth
                allowEmpty
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const SensorFilter = (props) => (
  <Filter {...props}>
    <ReferenceInput
      label="Project"
      source="project.id"
      reference="Project"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Project" optionText="name" />
    </ReferenceInput>
    <ReferenceInput
      label="Sensor Type"
      source="sensorType.id"
      reference="SensorType"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <SelectInput label="Sensor Type" optionText="name" />
    </ReferenceInput>
  </Filter>
);

const styleRow = (record) => {
  const { sensorType, serialNumber, equipmentItem } = record;
  const errorStyle = {
    backgroundColor: 'rgba(255,0,0,.2)',
    color: '#fff',
  };
  const warningStyle = {
    backgroundColor: 'lightyellow',
  };
  if (!sensorType || !serialNumber || !equipmentItem) {
    return {
      ...errorStyle,
    };
  }
};

export const SensorList = () => {
  const { permissions } = usePermissions();
  return (
    <>
      <Alert severity="info">
        <Grid container>
          <Grid item xs={12} style={{ padding: '1rem 1rem 0rem 1rem' }}>
            <Typography gutterBottom>
              <b>Sensor Notes:</b>
            </Typography>
            <Typography>
              - <b>SolarEdge</b> sensors should be automatically created for you
              when the SolarEdge Site is created.
            </Typography>
            <Typography>
              - <b>SCADA</b> sensors need to be created manually after the SCADA
              System is created. Use the SCADA database id mapping excel doc to
              find the ID (data key) for the sensor.
            </Typography>
            <Typography>
              - <b>AMMP</b> sensors need to be created manually after the AMMP
              Site is created. Check the response object of the weather station
              data and grab the data key
            </Typography>
            <Typography>
              - <b>SGD</b> sensors will be created automatically, however, the
              sensor names needs to be changed and sensor type IDs need to be
              assigned for all those that are missing one.
            </Typography>
            <Typography>
              - <b>Solis</b> sensors need to be created manually however sensors
              for all Solis projects use the same data (fetched from Hobolink).
              Add new sensors that match the other Solis sites.
            </Typography>
            <Typography>
              - <b>SMA</b> sensors are not yet available for the SMA site we
              have configured
            </Typography>
            <Typography>
              - <b>FlexOM</b> No API exists for FlexOM yet. Manually create
              sensors if we have data to upload for them
            </Typography>
            <Typography>
              - <b>Sungrow</b> sensors will be created automatically.
            </Typography>
            <Typography>
              - <b>PowerFactor</b> sensors need to be created manually using the
              attribute names from the PowerFactor dashboard found in the Data &
              Trends section as of 11/20/2023.
            </Typography>
            <Typography gutterBottom style={{ marginTop: '1rem' }}>
              <b>Table Key:</b>
            </Typography>
            <LegendItem
              label="Missing sensor type, serial number, and/or equipment item"
              color="rgba(255,0,0,.2)"
            />
          </Grid>
        </Grid>
      </Alert>
      <List title={entityName} perPage={25} filters={<SensorFilter />}>
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
          rowStyle={styleRow}
        >
          <TextField source="id" />
          <TextField source="name" />
          <LinkField
            label="Sensor Type"
            linkSource="sensorType.id"
            labelSource="sensorType.name"
            reference="SensorType"
          />
          <TextField label="Data key" source="dataAttribute" fullWidth />
          <TextField source="serialNumber" />
          <LinkField
            label="Equipment Item"
            linkSource="equipmentItem.id"
            labelSource="equipmentItem.label"
            reference="EquipmentItem"
          />
          <LinkField
            label="SolarEdge Site"
            linkSource="solarEdgeSite.id"
            labelSource="solarEdgeSite.name"
            reference="SolarEdgeSite"
          />
          <LinkField
            label="SCADA System"
            linkSource="scadaSystem.id"
            labelSource="scadaSystem.name"
            reference="ScadaSystem"
          />
          <LinkField
            label="SGD System"
            linkSource="sgdSystem.id"
            labelSource="sgdSystem.name"
            reference="SgdSystem"
          />
          <LinkField
            label="SMA Site"
            linkSource="smaSite.id"
            labelSource="smaSite.name"
            reference="SmaSite"
          />
          <LinkField
            label="FlexOM Site"
            linkSource="flexOmSite.id"
            labelSource="flexOmSite.name"
            reference="FlexOmSite"
          />
          <LinkField
            label="Sungrow System"
            linkSource="sungrowSystem.id"
            labelSource="sungrowSystem.name"
            reference="SungrowSystem"
          />
          <LinkField
            label="Solis System"
            linkSource="solisSystem.id"
            labelSource="solisSystem.name"
            reference="SolisSystem"
          />
          <LinkField
            label="AMMP Site"
            linkSource="sunExchangeSite.id"
            labelSource="sunExchangeSite.name"
            reference="SunExchangeSite"
          />
          <LinkField
            label="PowerFactor System"
            linkSource="powerFactorSystem.id"
            labelSource="powerFactorSystem.name"
            reference="PowerFactorSystem"
          />
        </Datagrid>
      </List>
    </>
  );
};

export const SensorCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <ReferenceInput source="sensorType.id" reference="SensorType">
            <SelectInput label="Sensor Type" fullWidth optionText="name" />
          </ReferenceInput>
          <TextInput
            label="Data key"
            source="dataAttribute"
            fullWidth
            helperText="Information used to fetch the correct sensor from the corresponding service. (SCADA ex: 317) (SolarEdge ex: ambientTemperature)"
          />
          <TextInput source="serialNumber" fullWidth />
          <ReferenceInput source="equipmentItem.id" reference="EquipmentItem">
            <SelectInput
              label="Equipment Item"
              fullWidth
              allowEmpty
              optionText="label"
              helperText={
                <span>
                  If you don't see the equipment type you need, create it{' '}
                  <a href="/EquipmentItem/create">here</a>.
                </span>
              }
            />
          </ReferenceInput>
          <ReferenceInput source="solarEdgeSite.id" reference="SolarEdgeSite">
            <SelectInput
              label="SolarEdge Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="scadaSystem.id" reference="ScadaSystemLite">
            <SelectInput
              label="SCADA System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="sgdSystem.id" reference="SgdSystem">
            <SelectInput
              label="SGD System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="smaSite.id" reference="SmaSite">
            <SelectInput
              label="SMA Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="flexOmSite.id" reference="FlexOmSite">
            <SelectInput
              label="FlexOM Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="sungrowSystem.id" reference="SungrowSystem">
            <SelectInput
              label="Sungrow System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput source="solisSystem.id" reference="SolisSystem">
            <SelectInput
              label="Solis System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput
            source="sunExchangeSite.id"
            reference="SunExchangeSite"
          >
            <SelectInput
              label="AMMP Site"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
          <ReferenceInput
            source="powerFactorSystem.id"
            reference="PowerFactorSystem"
          >
            <SelectInput
              label="PowerFactor System"
              fullWidth
              allowEmpty
              optionText="name"
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
