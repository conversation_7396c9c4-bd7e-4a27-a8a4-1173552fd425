import React, { useState } from 'react';
import numeral from 'numeral';
import moment from 'moment';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  ArrayField,
  ArrayInput,
  BooleanField,
  BooleanInput,
  Create,
  CreateButton,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  ExportButton,
  Filter,
  FormDataConsumer,
  FormTab,
  FunctionField,
  TabbedForm,
  List,
  Show,
  SimpleFormIterator,
  SingleFieldList,
  required,
  NumberField,
  Pagination,
  ReferenceArrayInput,
  ReferenceField,
  ReferenceInput,
  SelectInput,
  SelectArrayInput,
  SimpleForm,
  TabbedShowLayout,
  TextField,
  TextInput,
  TopToolbar,
  useDataProvider,
  useListContext,
  useNotify,
  useRefresh,
  usePermissions,
  useRecordContext,
  useResourceDefinition,
  useShowContext,
} from 'react-admin';

import {
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  Collapse,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  LinearProgress,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Popover,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import MatList from '@mui/material/List';
import {
  Add,
  CancelOutlined,
  CheckCircle,
  CheckCircleOutlineOutlined,
  Close,
  Delete,
  Description,
  Error,
  ExpandMore,
  ExpandLess,
  Folder,
  GetApp,
  Help,
  PictureAsPdf,
  Public,
  Subject,
  ArrowForward,
} from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import { Alert } from '@mui/lab';
import { Image, Video, Transformation } from 'cloudinary-react';

import Config from '../config/config';
import ExcelReader from './ExcelReader';
import {
  CustomReferenceField,
  DetailField,
  CustomBooleanField,
  CustomNumberInput,
  LinkField,
} from './CustomFields';

import { openUploadWidget, url } from '../utils/CloudinaryService';
import { getEditable } from '../utils/applyRoleAuth';
import theme from '../theme';
import EPCConversionGrid from './EPCConversionGrid';
import { RichTextInput } from 'ra-input-rich-text';
import Map from './Map';

const entityName = 'Project';

const Title = () => {
  const record = useRecordContext();
  return (
    <span>
      {entityName}
      {record ? ` #${record.id} - ${record.name}` : ''}
    </span>
  );
};

export const ProjectEdit = () => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const [editDropboxLocation, setEditDropboxLocation] = useState(false);
  const [isPublicDropdownOpen, setIsPublicDropdownOpen] = useState(false);

  const { id } = useParams();
  const { permissions } = usePermissions();

  const isIT =
    permissions?.roles?.map((el) => el.name)?.indexOf('ITWrite') > -1;

  const uploadImageWithCloudinary = () => {
    const uploadOptions = {
      tags: ['projects'],
      showPoweredBy: false,
      multiple: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.project_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotosUploaded([resp.info]);
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.project_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideosUploaded([resp.info]);
      }
    });
  };

  const uploadDocumentWithCloudinary = (id, type) => {
    return () => {
      const uploadOptions = {
        tags: [type, id],
        showPoweredBy: false,
        multiple: false,
        cloudName: Config.cloud_name,
        sources: ['local', 'url'],
        // dropboxAppKey: '1y1wsyzgzfb5f2g',
        uploadPreset: Config.project_document_upload_preset,
      };
      openUploadWidget(uploadOptions, (error, resp) => {
        if (!error && resp.event === 'success') {
          onDocumentUploaded(resp.info, type);
        }
      });
    };
  };

  const handleMakeVideoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryVideo: resource.id,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Videos successfully updated');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error updating videos', { type: 'error' });
        });
    };
  };

  const handleCalculateAvgModuleTemp = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        calculateAvgModuleTemp: true,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then((resp) => {
          notify('Module Temperatures successfully backfilled');
          refresh();
        })
        .catch((e) => {
          notify(
            e.body?.graphQLErrors?.[0]?.message ||
              'Error updating module temperature!',
            { type: 'error' }
          );
        });
    };
  };
  const handleModuleSpecBackfill = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        backfillModuleSpecs: true,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then((resp) => {
          notify('Module Specs successfully backfilled');
          refresh();
        })
        .catch((e) => {
          notify(
            e.body?.graphQLErrors?.[0]?.message ||
              'Error updating module specs!',
            { type: 'error' }
          );
        });
    };
  };

  const handleMakePhotoPrimary = (resource) => {
    return () => {
      const updatedRecord = {
        id: parseInt(id, 10),
        primaryImage: resource.id,
      };
      dataProvider
        .update('Project', {
          data: updatedRecord,
        })
        .then(() => {
          notify('Images successfully updated');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error updating images', { type: 'error' });
        });
    };
  };

  const handleRemovePhoto = (resource) => {
    return () => {
      dataProvider
        .delete('ProjectImage', {
          projectId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Image successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing image', { type: 'error' });
        });
    };
  };

  const handleDownloadImage = (resource) => {
    return () => {
      // Generate full resolution Cloudinary URL (without attachment flag)
      const imageUrl = url(resource.public_id, {
        quality: 'auto:best',
      });

      // Open in new tab
      window.open(imageUrl, '_blank');
    };
  };

  const handleRemoveDocument = (documentId) => {
    return () => {
      dataProvider
        .delete('ProjectDocument', {
          id: documentId,
        })
        .then(() => {
          notify('Document successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing document', { type: 'error' });
        });
    };
  };

  const handleRemoveVideo = (resource) => {
    return () => {
      dataProvider
        .delete('ProjectVideo', {
          projectId: parseInt(id, 10),
          id: resource.id,
        })
        .then(() => {
          notify('Video successfully removed');
          refresh();
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing video', { type: 'error' });
        });
    };
  };

  const handleDownloadVideo = (resource) => {
    return () => {
      // Generate full resolution Cloudinary video URL (without attachment flag)
      const videoUrl = url(resource.public_id, {
        resource_type: 'video',
        quality: 'auto:best',
      });

      // Open in new tab
      window.open(videoUrl, '_blank');
    };
  };

  const onPhotosUploaded = (aPhotos) => {
    const photos = aPhotos.map((photo) => {
      return {
        public_id: photo.public_id,
        projectId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('ProjectImage', {
        input: photos,
      })
      .then((record) => {
        notify('Image successfully uploaded');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading image.', { type: 'error' });
      });
  };

  const onVideosUploaded = (videos) => {
    videos = videos.map((video) => {
      return {
        public_id: video.public_id,
        projectId: parseInt(id, 10),
        primaryFlg: false,
      };
    });
    dataProvider
      .create('ProjectVideo', {
        input: videos,
      })
      .then((record) => {
        notify('Video uploaded successfully');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error uploading video.', { type: 'error' });
      });
  };

  const onDocumentUploaded = (document, type) => {
    const docObj = document;
    delete docObj.id;
    docObj.title = `${type}-(${id})`;
    dataProvider
      .update('Project', {
        data: { id: parseInt(id, 10), [type]: docObj },
      })
      .then(() => {
        notify('Document successfully added');
        refresh();
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error adding document', { type: 'error' });
      });
  };

  const handleDownloadDropboxFile = (fileLocation) => {
    dataProvider
      .getOne('DropboxFile', {
        fileLocation,
      })
      .then((res) => {
        if (!res.data) {
          notify('Error getting dropbox download url', { type: 'error' });
          return null;
        }
        const link = document.createElement('a');
        link.href = res.data.downloadUrl;
        link.download = true;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch((e) => {
        console.error(e);
        notify('Error getting dropbox download url', { type: 'error' });
      });
  };

  const handleDeleteDropboxFile = (fileLocation) => {
    dataProvider
      .delete('DropboxFile', {
        fileLocation,
      })
      .then((res) => {
        notify('File deleted from Dropbox', { type: 'success' });
        refresh();
      })
      .catch((e) => {
        console.error(e);
        notify('Error deleting file from Dropbox', { type: 'error' });
      });
  };

  const handleUploadFileToDropbox = (event, dropboxDirectory) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 100) {
        notify('Document must be less than 100MB', { type: 'error' });
        return;
      }
      dataProvider
        .create('DropboxFile', {
          fileLocation: `${dropboxDirectory}/${file.name}`,
        })
        .then((res) => {
          const uploadUrl = res.data;
          const formData = new FormData();
          formData.append('File', file);
          fetch(uploadUrl, {
            method: 'POST',
            body: formData,
            headers: { 'Content-type': 'application/octet-stream' },
          })
            .then((response) => response.json())
            .then((result) => {
              console.log('Success:', result);
              notify('File uploaded to Dropbox', { type: 'success' });
              refresh();
            })
            .catch((error) => {
              console.error('Error:', error);
              notify('Error uploading file to Dropbox', { type: 'error' });
              refresh();
            });
        })
        .catch((e) => {
          console.error(e);
          notify('Error getting file upload url from Dropbox', {
            type: 'error',
          });
        });
    }
  };

  const isPublicRequirements = [
    {
      label: 'Name',
      field: 'name',
      isComplete: (val) => !!val,
    },
    {
      label: 'Portfolio',
      field: 'portfolio',
      isComplete: (val) => !!val,
    },
    {
      label: 'Short Summary',
      field: 'shortSummary',
      isComplete: (val) => !!val,
    },
    {
      label: 'Installation Type',
      field: 'installationType',
      isComplete: (val) => !!val,
    },
    {
      label: 'Investment Status',
      field: 'projectInvestmentStatus',
      isComplete: (val) => !!val,
    },
    {
      label: 'Projected COD',
      field: 'projectedCOD',
      isComplete: (val) => !!val,
    },
    {
      label: 'NTP Date',
      field: 'ntpDt',
      isComplete: (val) => !!val,
    },
    {
      label: 'System Size AC',
      field: 'systemSizeAC',
      isComplete: (val) => !!val,
    },
    {
      label: 'System Size DC',
      field: 'systemSizeDC',
      isComplete: (val) => !!val,
    },
    {
      label: 'Percentage Ownership',
      field: 'percentageOwnership',
      isComplete: (val) => val || val === 0,
    },
    {
      label: 'Country',
      field: 'country',
      isComplete: (val) => !!val,
    },
    {
      label: 'Latitude',
      field: 'latitude',
      isComplete: (val) => val || val === 0,
    },
    {
      label: 'Longitude',
      field: 'longitude',
      isComplete: (val) => val || val === 0,
    },
    {
      label: 'All P50 Values',
      field: 'P50ProdCompleteFlg',
      isComplete: (val) => !!val,
    },
    {
      label: 'Degradation Constant',
      field: 'degradationConstant',
      isComplete: (val) => val || val === 0,
    },
    {
      label: 'Debt',
      field: 'debt',
      isComplete: (val) => val || val === 0,
    },
    {
      label: 'Sponsor Equity',
      field: 'sponsorEquity',
      isComplete: (val) => val || val === 0,
    },
    {
      label: 'Tax Equity',
      field: 'taxEquity',
      isComplete: (val) => val || val === 0,
    },
    // {
    //   label: 'IC Memo',
    //   field: 'ICMemo',
    //   isComplete: (val) => !!val,
    // },
  ];

  const validateRequiredFields = (record, requirements) => {
    const reqFieldStatus = {};
    requirements.forEach((requirement) => {
      reqFieldStatus[String(requirement.label)] = requirement.isComplete(
        record[requirement.field]
      );
    });
    return reqFieldStatus;
  };

  return (
    <Edit title={<Title />} undoable={false} redirect="edit">
      <TabbedForm redirect={false}>
        <FormTab label="Summary">
          <Grid container style={{ width: '100%' }} spacing={5}>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom>
                Details
              </Typography>
              <TextInput
                label="Name"
                validate={required()}
                source="name"
                fullWidth
              />
              <ReferenceInput
                source="portfolio.id"
                reference="PortfolioLite"
                perPage={10000}
              >
                <SelectInput optionText="name" label="Portfolio" fullWidth />
              </ReferenceInput>
              <ReferenceInput
                source="projectGroup.id"
                reference="ProjectGroup"
                perPage={10000}
              >
                <SelectInput
                  optionText="name"
                  label="ProjectGroup"
                  fullWidth
                  helperText="To be used for grouping in the frontend project lists. (ie: Micros I)"
                />
              </ReferenceInput>
              <TextInput
                // required
                source="shortSummary"
                fullWidth
                helperText="Shows up on project popup tile."
              />
              <TextInput
                source="shortSummaryPT"
                fullWidth
                helperText="Project description used only on the .br site (only required for consortium projects)."
              />
              <TextInput
                multiline
                source="summary"
                fullWidth
                helperText="This feeds the project popup on the map on the user dashboard"
              />
              <ReferenceInput
                source="installationType.id"
                reference="InstallationType"
              >
                <SelectInput
                  label="Installation Type"
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput>
              <CustomNumberInput
                // required
                source="debt"
                fullWidth
                helperText="Portion of total project cost funded by debt."
              />
              <CustomNumberInput
                // required
                source="sponsorEquity"
                fullWidth
                helperText="Portion of total project cost funded by sponsor equity (crowd funding)."
              />
              <CustomNumberInput
                // required
                source="taxEquity"
                fullWidth
                helperText="Portion of total project cost funded by tax equity."
              />
              {/* <CustomNumberInput
                source="projectedAnnualProduction"
                label="Projected Annual Production (kWh AC)"
                fullWidth
              /> */}
              <CustomNumberInput
                label="Proj. Sys Size (MW) AC"
                helperText="MW AC"
                source="systemSizeAC"
                fullWidth
              />
              <CustomNumberInput
                label="Proj. Sys Size (MW) DC"
                helperText="MW DC"
                source="systemSizeDC"
                fullWidth
              />
              <CustomNumberInput source="percentageOwnership" fullWidth />
              <DateInput
                label="NTP date"
                helperText="Notice to proceed date. If this is not set, or the date is prior to the current date, than the project will not be included in the portfolio's investment cap."
                source="ntpDt"
                fullWidth
              />
              <DateInput
                label="Acquisition Dt"
                source="acquisitionDt"
                fullWidth
                helperText="The date the project was acquired."
              />
              <DateInput
                label="Projected Construction Start Dt"
                source="projectedConstructionStartDt"
                fullWidth
                helperText="The date the project is projected to begin construction."
              />
              <DateInput
                label="Actual Construction Start Dt"
                source="actualConstructionStartDt"
                fullWidth
                helperText="The date the project began construction."
              />
              <DateInput
                label="Projected COD"
                source="projectedCOD"
                fullWidth
              />
              <DateInput
                label="Actual COD"
                source="actualCOD"
                fullWidth
                helperText="Must be *actual*. Should not be a future date."
              />
              <DateInput
                label="Projected End Dt"
                source="projectedEndDt"
                fullWidth
              />
              <TextInput
                label="Internal Notes"
                multiline
                source="internalNotes"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom>
                Status
              </Typography>
              <ReferenceInput
                source="projectInvestmentStatus.id"
                reference="ProjectInvestmentStatus"
                filter={{ activeOnly: true }}
                perPage={10000}
              >
                <SelectInput
                  label="Investment Status"
                  fullWidth
                  optionText="name"
                  helperText="Adds badge on project list in portfolio detail page"
                />
              </ReferenceInput>
              <BooleanInput
                fullWidth
                // TODO: reinstate this logic
                // disabled={
                //   !!(!record.isPublic && trueCount < requiredFields.length)
                // }
                helperText="Determines whether or not the project is included in it's parent portfolio...if it is not public, it essentially does not exist."
                source="isPublic"
                label="Is Public?"
              />
              <FunctionField
                label="Is Public?"
                render={(record) => {
                  const requiredFieldStatus = validateRequiredFields(
                    record,
                    isPublicRequirements
                  );
                  const requiredFields = Object.keys(requiredFieldStatus);
                  const trueCount = Object.values(requiredFieldStatus).filter(
                    (val) => val
                  ).length;
                  let alertText;
                  if (!record.isPublic && trueCount < requiredFields.length) {
                    alertText = 'This project is not ready to turn public!';
                  } else if (record.isPublic) {
                    alertText = 'This project is public.';
                  } else {
                    alertText = 'This project is ready to turn public!';
                  }
                  return (
                    <Alert
                      severity={
                        trueCount < requiredFields.length ? 'error' : 'success'
                      }
                    >
                      <Typography>{alertText}</Typography>
                      <Grid container style={{ width: '100%' }}>
                        <Grid item xs={10} md={7}>
                          <LinearProgress
                            style={{
                              height: '.5rem',
                              borderRadius: '.5rem',
                              marginTop: '1.25rem',
                              width: '100%',
                            }}
                            color="secondary"
                            variant="determinate"
                            value={
                              (trueCount * 100) /
                              Object.keys(requiredFieldStatus).length
                            }
                          />
                        </Grid>
                        <Grid item>
                          {isPublicDropdownOpen ? (
                            <IconButton size="large">
                              <ExpandLess
                                onClick={() => setIsPublicDropdownOpen(false)}
                              />
                            </IconButton>
                          ) : (
                            <IconButton size="large">
                              <ExpandMore
                                onClick={() => setIsPublicDropdownOpen(true)}
                              />
                            </IconButton>
                          )}
                        </Grid>
                      </Grid>
                      <Collapse in={isPublicDropdownOpen}>
                        <MatList style={{ width: '100%' }}>
                          {Object.keys(requiredFieldStatus).map((label) => {
                            return (
                              <ListItem
                                key={`li-${label}`}
                                style={{
                                  paddingTop: 0,
                                  paddingBottom: 0,
                                  width: '100%',
                                }}
                              >
                                <ListItemAvatar
                                  style={{
                                    minWidth: 30,
                                    marginBottom: '-5px',
                                  }}
                                >
                                  {requiredFieldStatus[String(label)] ? (
                                    <CheckCircleOutlineOutlined
                                      style={{
                                        color: theme.palette.green.main,
                                      }}
                                    />
                                  ) : (
                                    <CancelOutlined color="error" />
                                  )}
                                </ListItemAvatar>
                                <ListItemText>
                                  <Typography variant="body2">
                                    {label}
                                  </Typography>
                                </ListItemText>
                              </ListItem>
                            );
                          })}
                        </MatList>
                      </Collapse>
                    </Alert>
                  );
                }}
              />
              <BooleanInput
                source="newFlg"
                label="Is New?"
                helperText="Determines whether or not the project is marked as new in it's parent portfolio."
                fullWidth
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Address" path="address">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="address1" fullWidth />
              <TextInput source="address2" fullWidth />
              <TextInput source="city" fullWidth />
              <TextInput source="postalCode" fullWidth />
              <TextInput source="state" fullWidth />
              <ReferenceInput
                source="country.id"
                reference="Country"
                perPage={10000}
                sort={{ field: 'name', order: 'ASC' }}
              >
                <SelectInput
                  label="Country"
                  // required
                  fullWidth
                  optionText="name"
                />
              </ReferenceInput>
              <CustomNumberInput
                // required
                source="latitude"
                fullWidth
              />
              <CustomNumberInput
                // required
                source="longitude"
                fullWidth
              />
              <FormHelperText>
                To get longitude and latitude in decimal form{' '}
                <a
                  href="https://www.fcc.gov/media/radio/dms-decimal"
                  target="_blank"
                >
                  go here
                </a>
                .
              </FormHelperText>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Media" path="media">
          <Grid style={{ width: '100%', marginBottom: '1rem' }}>
            <Grid item xs={12}>
              <Alert severity="info">
                <Typography>
                  All images : 1600px X 1200px (landscape)
                </Typography>
              </Alert>
            </Grid>
          </Grid>
          <Typography variant="h5" gutterBottom>
            Images
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.images || formData?.images?.length === 0)
                return 'No photos';
              return (
                <Grid container>
                  {formData.images.map((record) => (
                    <Grid style={{ padding: '1rem' }}>
                      <Image
                        cloud_name={Config.cloud_name}
                        publicId={record.public_id}
                      >
                        <Transformation width="240" crop="scale" />
                      </Image>
                      <Grid>
                        <Button
                          color="primary"
                          style={
                            record.primaryFlg
                              ? {
                                  backgroundColor: 'green',
                                  color: 'white',
                                }
                              : {}
                          }
                          disabled={record.primaryFlg}
                          variant={record.primaryFlg ? 'contained' : 'outlined'}
                          onClick={handleMakePhotoPrimary(record)}
                        >
                          Make Primary
                        </Button>
                        <IconButton
                          onClick={handleDownloadImage(record)}
                          title="View full resolution image"
                        >
                          <GetApp />
                        </IconButton>
                        <IconButton
                          style={{ float: 'right' }}
                          onClick={handleRemovePhoto(record)}
                        >
                          <Delete />
                        </IconButton>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              );
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button
              endIcon={<Add />}
              variant="outlined"
              onClick={uploadImageWithCloudinary}
            >
              Add photo
            </Button>
          </div>
          <Divider style={{ width: '100%', margin: '2em 0' }} />
          <Typography variant="h5" gutterBottom>
            Videos
          </Typography>
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (!formData?.videos) return 'No videos';
              return formData.videos.map((record) => {
                if (!record?.public_id) return null;
                return (
                  <Grid style={{ paddingBottom: '1rem' }}>
                    <Video
                      cloud_name={Config.cloud_name}
                      publicId={record.public_id}
                      muted
                      width="240"
                      sourceTypes={['mp4']}
                      controls
                    >
                      {record.public_id ? (
                        <Transformation width={200} crop="scale" />
                      ) : null}
                    </Video>
                    <Grid>
                      <Button
                        color="primary"
                        style={
                          record.primaryFlg
                            ? {
                                backgroundColor: 'green',
                                color: 'white',
                              }
                            : {}
                        }
                        disabled={record.primaryFlg}
                        variant={record.primaryFlg ? 'contained' : 'outlined'}
                        onClick={handleMakeVideoPrimary(record)}
                      >
                        Make Primary
                      </Button>
                      <IconButton
                        onClick={handleDownloadVideo(record)}
                        title="View full resolution video"
                      >
                        <GetApp />
                      </IconButton>
                      <IconButton
                        style={{ float: 'right' }}
                        onClick={handleRemoveVideo(record)}
                      >
                        <Delete />
                      </IconButton>
                    </Grid>
                  </Grid>
                );
              });
            }}
          </FormDataConsumer>
          <div className="actions">
            <Button
              endIcon={<Add />}
              variant="outlined"
              onClick={uploadVideoWithCloudinary}
            >
              Add video
            </Button>
          </div>
        </FormTab>
        <FormTab label="Documents" path="documents">
          <Typography>IC Memo</Typography>
          <FunctionField
            source="ICMemo"
            label="ICMemo"
            render={(record) => {
              return (
                <>
                  <div>
                    {!record.ICMemo ? (
                      <>
                        <Button
                          // style={{ float: 'right' }}
                          variant="contained"
                          color="primary"
                          onClick={uploadDocumentWithCloudinary(
                            record.id,
                            'ICMemo'
                          )}
                        >
                          Add IC-Memo
                        </Button>
                        <FormHelperText>
                          IC-Memo must be in pdf form and don't forget to reduce
                          your file size before uploading (
                          <a
                            href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                            target="_blank"
                          >
                            Mac directions
                          </a>
                          ).
                        </FormHelperText>
                      </>
                    ) : (
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          // eslint-disable-next-line security/detect-non-literal-fs-filename
                          window.open(record.ICMemo.url, '_blank');
                        }}
                      >
                        <PictureAsPdf /> - View file
                      </Button>
                    )}
                    {record.ICMemo ? (
                      <IconButton
                        // style={{ float: 'right' }}
                        onClick={handleRemoveDocument(record.ICMemo.id)}
                      >
                        <Delete />
                      </IconButton>
                    ) : null}
                  </div>
                  {record.ICMemo ? (
                    <TextInput
                      label="Title"
                      helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - IC Memo')"
                      source="ICMemo.title"
                    />
                  ) : null}
                </>
              );
            }}
          />

          <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />

          <Typography>Project Model</Typography>
          <FunctionField
            source="projectModel"
            label="Project Model"
            render={(record) => {
              return (
                <>
                  <div>
                    {!record.projectModel ? (
                      <>
                        <Button
                          // style={{ float: 'right' }}
                          variant="contained"
                          color="primary"
                          onClick={uploadDocumentWithCloudinary(
                            record.id,
                            'projectModel'
                          )}
                        >
                          Add Project Model
                        </Button>

                        <FormHelperText>
                          Project Model must be in pdf form. Don't forget to
                          reduce your file size before uploading (
                          <a
                            href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                            target="_blank"
                          >
                            Mac directions
                          </a>
                          ).
                        </FormHelperText>
                      </>
                    ) : (
                      <Button
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          // eslint-disable-next-line security/detect-non-literal-fs-filename
                          window.open(record.projectModel.url, '_blank');
                        }}
                      >
                        <PictureAsPdf /> - View file
                      </Button>
                    )}
                    {record.projectModel ? (
                      <IconButton
                        // style={{ float: 'right' }}
                        onClick={handleRemoveDocument(record.projectModel.id)}
                      >
                        <Delete />
                      </IconButton>
                    ) : null}
                  </div>
                  {record.projectModel ? (
                    <TextInput
                      label="Title"
                      helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - Financial Model')"
                      source="projectModel.title"
                    />
                  ) : null}
                </>
              );
            }}
          />
        </FormTab>
        <FormTab label="Monitoring" path="monitoring">
          <Grid container style={{ width: '100%' }} spacing={2}>
            <Grid item xs={12} md={6}>
              <Grid container justifyContent="space-between">
                <Grid item>
                  <Typography variant="h5" gutterBottom>
                    Details
                  </Typography>
                </Grid>
                {isIT && (
                  <Grid item>
                    <FunctionField
                      label="Data polling configuration"
                      render={(record) => {
                        let link = null;
                        if (record.scadaSystem)
                          link = `/ScadaSystem/${record.scadaSystem.id}`;
                        if (record.sgdSystem)
                          link = `/SGDSystem/${record.sgdSystem.id}`;
                        if (record.solisSystem)
                          link = `/SolisSystem/${record.solisSystem.id}`;
                        else if (record.solarEdgeSites?.length > 0)
                          link = `/SolarEdgeSite/${record.solarEdgeSites[0].id}`;
                        else if (record.sunExchangeSites?.length > 0)
                          link = `/SunExchangeSite/${record.sunExchangeSites[0].id}`;
                        else if (record.greenAntMeters?.length > 0)
                          link = `/GreenAntMeter/${record.greenAntMeters[0].id}`;
                        else if (record.powerFactorSystems?.length > 0)
                          link = `/PowerFactorSystem/${record.powerFactorSystems[0].id}`;
                        else if (record.smaSites?.length > 0)
                          link = `/SMASite/${record.smaSites[0].id}`;
                        else if (record.flexOmSites?.length > 0)
                          link = `/FlexOmSite/${record.flexOmSites[0].id}`;
                        else if (record.sungrowSystems?.length > 0)
                          link = `/SungrowSystem/${record.sungrowSystems[0].id}`;

                        if (link) {
                          return (
                            <Button
                              component={Link}
                              to={link}
                              // style={{ marginBottom: '1rem' }}
                              endIcon={<ArrowForward fontSize="small" />}
                              variant="contained"
                            >
                              Data Polling Config
                            </Button>
                          );
                        } else return null;
                      }}
                    />
                  </Grid>
                )}
              </Grid>

              {isIT ? (
                <>
                  <ReferenceArrayInput
                    source="expectedIrradianceSensorTypeIds"
                    reference="SensorType"
                    label="SOMETHING ELSE"
                    // labelWidth={118}
                  >
                    <SelectArrayInput
                      label="Expected Generation Irradiance Sensor Type"
                      optionText="name"
                      fullWidth
                      // labelWidth={118}
                      allowEmpty
                    />
                  </ReferenceArrayInput>
                </>
              ) : null}
              <ReferenceInput
                source="projectMonitoringStatus.id"
                reference="ProjectMonitoringStatus"
                perPage={10000}
              >
                <SelectInput
                  label="Monitoring Status"
                  fullWidth
                  allowEmpty
                  optionText="name"
                />
              </ReferenceInput>
              <TextInput source="monitoringNotes" multiline fullWidth />
              <CustomNumberInput
                label="Degradation constant"
                source="degradationConstant"
                fullWidth
              />
              <CustomNumberInput
                source="transformerLoss"
                helperText="After changing, run 'Apply Transformer Loss to Production Periods' on the CMS Dashboard to update production values. Input the decimal form of the percentage of power loss for projects where the meter is before the transformer. Write 5% as 0.05."
                fullWidth
              />
              <CustomNumberInput
                source="revenuePerkWh"
                label="Revenue per kWh"
                helperText="Enter the price per kWh in the project's local currency. Ex: '0.2822' in USD for a U.S. project."
                fullWidth
              />
              <BooleanInput
                source="noTusdFlg"
                label="Ineligible for TUSD"
                helperText="Turn on for Brazil projects that are cash-flowing and we will not need to track TUSD invoices."
              />
              <ReferenceInput
                source="irradianceSensorTracker.id"
                reference="Sensor"
                perPage={10000}
                sort={{ field: 'id', order: 'ASC' }}
                filter={{
                  sensorTypeId: 7,
                  project: {
                    id: parseInt(id, 10),
                  },
                }}
              >
                <SelectInput
                  optionText="name"
                  label="Irradiance Sensor Tracker"
                  fullWidth
                  helperText="Choose the tracker that the POA irradiance sensor is mounted to if applicable."
                />
              </ReferenceInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom>
                Status
              </Typography>
              <DateInput label="Actual COD" source="actualCOD" fullWidth />
              <DateInput
                label="Projected COD"
                source="projectedCOD"
                fullWidth
              />
              <DateInput
                label="Projected End Dt"
                source="projectedEndDt"
                fullWidth
              />
              <DateInput
                label="Monitoring Start Dt"
                source="monitoringStartDt"
                helperText="This date is used to discount projected production when it is after the Actual COD on the Asset Management Dashboard"
                fullWidth
              />
              <DateInput
                label="Expected Generation Start Dt"
                source="expectedGenerationStartDt"
                helperText="This date is used to determine whether or not to calculate/display expected generation #s on the Asset Management Dashboard"
                fullWidth
              />
              <BooleanInput
                source="generationDataPublicFlg"
                fullWidth
                helperText="Turning this flag on will enable this project to display limited generation data to investors"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom>
                Module Specs
                <FunctionField
                  style={{ fontSize: '1.25rem' }}
                  render={(record) => (
                    <Button
                      onClick={handleModuleSpecBackfill(record)}
                      variant="contained"
                      size="small"
                      style={{ marginLeft: '1rem' }}
                    >
                      Calculate
                    </Button>
                  )}
                />
              </Typography>
              <CustomNumberInput
                label="Temp. Coefficient (PMax)"
                helperText="The temperature coefficient of Pmax for the site\'s modules. this should be a loss coefficient per degrees celsius. (ex: 0.0034)"
                source="tempCoeffPmax"
                fullWidth
              />
              <CustomNumberInput
                label="NOCT"
                helperText="The nominal operating cell temperature of the modules. (ex: 45)"
                source="noct"
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                System Modelled Assumptions
              </Typography>
              <Grid container spacing={2}>
                <Grid item lg={4} md={6} xs={12}>
                  <Typography variant="h6">P50 Production</Typography>
                  <Typography variant="caption">
                    This value should come from financial model
                  </Typography>
                  <CustomNumberInput
                    label="January P50 Production (kWh)"
                    source="janP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="February P50 Production (kWh)"
                    source="febP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="March P50 Production (kWh)"
                    source="marP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="April P50 Production (kWh)"
                    source="aprP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="May P50 Production (kWh)"
                    source="mayP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="June P50 Production (kWh)"
                    source="junP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="July P50 Production (kWh)"
                    source="julP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="August P50 Production (kWh)"
                    source="augP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="September P50 Production (kWh)"
                    source="sepP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="October P50 Production (kWh)"
                    source="octP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="November P50 Production (kWh)"
                    source="novP50Prod"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="December P50 Production (kWh)"
                    source="decP50Prod"
                    fullWidth
                  />
                </Grid>
                <Grid item lg={4} md={6} xs={12}>
                  <Typography variant="h6">Performance Ratio</Typography>
                  <Typography variant="caption">
                    This value should be between 0 and 1 (not a percentage).
                  </Typography>
                  <CustomNumberInput
                    label="January Performance Ratio"
                    source="janPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="February Performance Ratio"
                    source="febPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="March Performance Ratio"
                    source="marPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="April Performance Ratio"
                    source="aprPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="May Performance Ratio"
                    source="mayPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="June Performance Ratio"
                    source="junPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="July Performance Ratio"
                    source="julPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="August Performance Ratio"
                    source="augPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="September Performance Ratio"
                    source="sepPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="October Performance Ratio"
                    source="octPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="November Performance Ratio"
                    source="novPR"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="December Performance Ratio"
                    source="decPR"
                    fullWidth
                  />
                </Grid>
                <Grid item lg={4} md={6} xs={12}>
                  <Typography variant="h6">POA Irradiance</Typography>
                  <Typography variant="caption">
                    This value should come from PVSyst or Helioscope as-built.
                  </Typography>
                  <CustomNumberInput
                    label="January GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="janGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="February GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="febGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="March GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="marGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="April GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="aprGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="May GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="mayGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="June GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="junGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="July GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="julGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="August GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="augGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="September GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="sepGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="October GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="octGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="November GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="novGlobEff"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="December GlobInc (Plane of Array) Irradiance (kWh/m2)"
                    source="decGlobEff"
                    fullWidth
                  />
                </Grid>
                <Grid item lg={4} md={6} xs={12}>
                  <Grid container style={{ width: '100%' }}>
                    <Typography variant="h6">
                      <Grid item>
                        Avg Module Temp
                        <FunctionField
                          style={{ fontSize: '1.25rem' }}
                          render={(record) => (
                            <Button
                              onClick={handleCalculateAvgModuleTemp(record)}
                              variant="contained"
                              size="small"
                              style={{ marginLeft: '1rem' }}
                            >
                              Calculate
                            </Button>
                          )}
                        />
                      </Grid>
                    </Typography>
                  </Grid>
                  <Typography variant="caption">
                    This value should come from financial model
                  </Typography>
                  <CustomNumberInput
                    label="January Module Temperature Reference (celsius)"
                    source="janModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="February Module Temperature Reference (celsius)"
                    source="febModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="March Module Temperature Reference (celsius)"
                    source="marModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="April Module Temperature Reference (celsius)"
                    source="aprModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="May Module Temperature Reference (celsius)"
                    source="mayModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="June Module Temperature Reference (celsius)"
                    source="junModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="July Module Temperature Reference (celsius)"
                    source="julModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="August Module Temperature Reference (celsius)"
                    source="augModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="September Module Temperature Reference (celsius)"
                    source="sepModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="October Module Temperature Reference (celsius)"
                    source="octModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="November Module Temperature Reference (celsius)"
                    source="novModuleTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="December Module Temperature Reference (celsius)"
                    source="decModuleTempRef"
                    fullWidth
                  />
                </Grid>
                <Grid item lg={4} md={6} xs={12}>
                  <Typography variant="h6">
                    Average Ambient Temperature
                  </Typography>
                  <Typography variant="caption">
                    This value should come from financial model
                  </Typography>
                  <CustomNumberInput
                    label="January Ambient Temperature Reference (celsius)"
                    source="janAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="February Ambient Temperature Reference (celsius)"
                    source="febAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="March Ambient Temperature Reference (celsius)"
                    source="marAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="April Ambient Temperature Reference (celsius)"
                    source="aprAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="May Ambient Temperature Reference (celsius)"
                    source="mayAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="June Ambient Temperature Reference (celsius)"
                    source="junAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="July Ambient Temperature Reference (celsius)"
                    source="julAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="August Ambient Temperature Reference (celsius)"
                    source="augAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="September Ambient Temperature Reference (celsius)"
                    source="sepAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="October Ambient Temperature Reference (celsius)"
                    source="octAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="November Ambient Temperature Reference (celsius)"
                    source="novAmbientTempRef"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="December Ambient Temperature Reference (celsius)"
                    source="decAmbientTempRef"
                    fullWidth
                  />
                </Grid>
                <Grid item lg={4} md={6} xs={12}>
                  <Typography variant="h6">
                    Horizontal to POA Constant
                  </Typography>
                  <Typography variant="caption">
                    This value should come from financial model (POA / GHI)
                  </Typography>
                  <CustomNumberInput
                    label="January Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="janPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="February Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="febPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="March Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="marPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="April Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="aprPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="May Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="mayPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="June Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="junPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="July Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="julPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="August Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="augPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="September Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="sepPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="October Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="octPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="November Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="novPOAIrradianceFactor"
                    fullWidth
                  />
                  <CustomNumberInput
                    label="December Multiplier to get POA Irradiance from Horizontal Irradiance"
                    source="decPOAIrradianceFactor"
                    fullWidth
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="O&M" path={'O&M'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (!formData.omFlg) {
                    return (
                      <Alert severity="warning">
                        This project is not currently flagged for O&M. Monthly
                        reports will not be auto-generated.
                      </Alert>
                    );
                  }
                  return null;
                }}
              </FormDataConsumer>
              <Grid item xs={12} md={6}>
                <BooleanInput
                  source="omFlg"
                  label="O&M Flag"
                  helperText="Turning this on will enable monthly O&M reports to be generated for this project"
                  fullWidth
                />
                <ReferenceInput
                  source="omTruck.id"
                  reference="OMTruck"
                  perPage={10000}
                >
                  <SelectInput optionText="name" label="O&M Truck" fullWidth />
                </ReferenceInput>
                <ReferenceInput
                  source="projectManager.id"
                  reference="EmployeeLite"
                  sort={{ field: 'firstName', order: 'ASC' }}
                  perPage={10000}
                  // filter={{ employeeTypeId: 1 }} // TODO: This should be turned back on but it will require upkeep
                >
                  <SelectInput
                    label="Project Manager"
                    fullWidth
                    allowEmpty
                    optionText="fullName"
                  />
                </ReferenceInput>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Credentials</b>
              </Typography>
              <ArrayInput fullWidth source="omPlatformCredentials" label="">
                <SimpleFormIterator
                  TransitionProps={{ enter: false, exit: false }}
                  disableReordering
                  inline
                >
                  <FormDataConsumer>
                    {({ scopedFormData, getSource }) => {
                      const isLocked = scopedFormData?.isSharedCredential;
                      if (isLocked) {
                        return (
                          <Alert severity="info">
                            This credential is shared with other projects. Any
                            changes that you make to it, will not be saved.
                            Contact IT if you need to change any of the
                            information below.
                          </Alert>
                        );
                      }
                      return null;
                    }}
                  </FormDataConsumer>
                  <TextInput
                    fullWidth
                    required
                    label="Platform name"
                    source="name"
                  />
                  <TextInput label="Platform url" source="url" fullWidth />
                  <TextInput label="Username" source="username" fullWidth />
                  <TextInput label="Password" source="password" fullWidth />
                  <RichTextInput label="Notes" source="notes" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contacts</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectContacts"
                // id="project-contacts"
                label=""
              >
                <SimpleFormIterator
                  disableReordering
                  inline
                  TransitionProps={{ enter: false, exit: false }}
                >
                  <TextInput
                    fullWidth
                    required
                    label="Label"
                    helperText="ex: O&M Contractor"
                    source="name"
                  />
                  <TextInput
                    label="Primary Contact Name"
                    source="primaryContactName"
                    fullWidth
                  />
                  <TextInput label="Phone" source="phone" fullWidth />
                  <TextInput label="Email" source="email" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Insurance Policies</b>
              </Typography>
              <ArrayField source="projectInsurancePolicies" sortable={false}>
                <SingleFieldList>
                  <CustomReferenceField
                    source="label"
                    color={() => 'primary'}
                  />
                </SingleFieldList>
              </ArrayField>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/InsurancePolicy/create"
              >
                Add new insurance policy
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contracts</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  // if (!formData.omContractsDropboxLocation && !editDropboxLocation) {
                  //   setEditDropboxLocation(true)
                  // }
                  return (
                    <>
                      {!editDropboxLocation ? (
                        <Grid container>
                          <Grid item xs={11} style={{ paddingLeft: '1rem' }}>
                            <Typography variant="body2" gutterBottom>
                              <b>Dropbox directory:</b>{' '}
                              {formData.omContractsDropboxLocation ||
                                'None specified. Click the pencil to edit.'}
                            </Typography>
                          </Grid>
                          <Grid item xs={1}>
                            <Grid container item justifyContent="flex-end">
                              <IconButton
                                onClick={() => {
                                  setEditDropboxLocation(true);
                                }}
                                size="large"
                              >
                                <EditIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Grid>
                      ) : (
                        <TextInput
                          source="omContractsDropboxLocation"
                          fullWidth
                          helperText="The '8.1.1 O&M Agreement' directory for this project. Ex: '/Energea Global/Market II - USA/Portfolio/Projects/Portfolio 4 - MA - Waltham/8. Asset Management/8.1 O&M/8.1.1 O&M Agreement'"
                        />
                      )}
                      <Table>
                        <TableBody>
                          {formData.contracts &&
                          formData.contracts.length > 0 ? (
                            formData.contracts.map((contract, index) => (
                              <TableRow key={`contracts-row-${contract.id}`}>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none', width: '1rem' }
                                      : { width: '1rem' }
                                  }
                                >
                                  {contract.isDirectory ? (
                                    <Folder />
                                  ) : contract.isFile ? (
                                    <Description />
                                  ) : (
                                    <Help />
                                  )}
                                </TableCell>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <b>{contract.name}</b>
                                </TableCell>
                                <TableCell
                                  align="right"
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <Tooltip title="Download">
                                    <IconButton
                                      color="primary"
                                      disabled={!contract.isDownloadable}
                                      onClick={() =>
                                        handleDownloadDropboxFile(
                                          contract.location
                                        )
                                      }
                                      size="large"
                                    >
                                      <GetApp />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      style={{
                                        color: !contract.isFile
                                          ? null
                                          : theme.palette.error.main,
                                      }}
                                      disabled={!contract.isFile}
                                      onClick={() => {
                                        if (
                                          window.confirm(
                                            `Clicking 'OK' will PERMANENTLY DELETE this document from Dropbox. Are you sure you wish to delete '${contract.name}'?`
                                          )
                                        ) {
                                          handleDeleteDropboxFile(
                                            contract.location
                                          );
                                        }
                                      }}
                                      size="large"
                                    >
                                      <Delete />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <Alert severity="info">
                              No files found in Dropbox directory
                            </Alert>
                          )}
                        </TableBody>
                      </Table>
                      <Button
                        style={{ marginTop: '1rem' }}
                        variant="contained"
                        color="secondary"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        Upload Contract to Dropbox
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            handleUploadFileToDropbox(
                              event,
                              formData.omContractsDropboxLocation
                            )
                          }
                        />
                      </Button>
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Equipment Inventory" path={'Inventory'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Equipment Inventory</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectEquipmentItems"
                id="project-equipment"
              >
                <SimpleFormIterator disableReordering fullWidth>
                  <ReferenceInput
                    source="equipmentItem.id"
                    reference="EquipmentItem"
                    inline
                    fullWidth
                    sort={{ field: 'model', order: 'ASC' }}
                    TransitionProps={{ enter: false, exit: false }}
                    perPage={10000}
                  >
                    <SelectInput
                      label="Equipment"
                      required
                      fullWidth
                      optionText={(choice) => {
                        return (
                          <ListItemText
                            primary={choice.model}
                            secondary={`${choice.equipmentType?.name} - ${choice.manufacturer}`}
                          />
                        );
                      }}
                    />
                  </ReferenceInput>
                  <CustomNumberInput
                    required
                    label="Quantity"
                    source="quantity"
                    fullWidth
                  />
                  <BooleanInput
                    source="sparePartFlg"
                    label="Is Spare Part?"
                    fullWidth
                  />
                </SimpleFormIterator>
              </ArrayInput>
              <Alert severity="info">
                If you don't see the equipment your looking for in the dropdown,
                then it doesn't exist yet in our database. Click here to create
                it.
                <br />
                <Button
                  style={{ marginTop: '1rem' }}
                  component="a"
                  variant="contained"
                  color="secondary"
                  href="/EquipmentItem/create"
                >
                  Add new equipment
                </Button>
              </Alert>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Env. Impact">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Projected</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} will create{' '}
                      <b>
                        {numeral(
                          formData.lifetimeEnergyProjection / 1000
                        ).format('0,0')}{' '}
                        MWh
                      </b>{' '}
                      in its lifetime. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.lifetimeEnergyProjection / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
            <Grid item xs={12} style={{ padding: '2rem' }}>
              <Typography variant="h5" gutterBottom>
                <b>Actual</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Typography gutterBottom>
                      {formData.name} has created{' '}
                      <b>
                        {numeral(formData.allTimeActual / 1000).format('0,0.0')}{' '}
                        MWh
                      </b>{' '}
                      so far. For perspective, that&apos;s equal to:
                    </Typography>
                    <EPCConversionGrid
                      production={formData.allTimeActual / 1000}
                      columns={3}
                      dataPointKeys={[
                        'tonsReduced',
                        'homesPowered',
                        'treesPlanted',
                        'gallonsOfGasConsumed',
                        'poundsCoalBurned',
                        'smartPhonesCharged',
                      ]}
                    />
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

const ProjectShowView = () => {
  const showContext = useShowContext();
  if (!showContext) return null;
  const { record } = showContext;
  if (!record) {
    return null;
  }
  return (
    <TabbedShowLayout redirect={false}>
      <TabbedShowLayout.Tab label="Summary">
        <Typography variant="h6" gutterBottom>
          Details
        </Typography>
        <TextField source="name" />
        <ReferenceField
          source="portfolio.id"
          reference="PortfolioLite"
          label="Portfolio"
          // fullWidth
        />
        <TextField
          source="shortSummary"
          // fullWidth
          helperText="Shows up on project popup tile."
        />
        <TextField
          source="summary"
          label="Long Summary"
          // fullWidth
          helperText="This feeds the project popup on the map on the user dashboard"
        />
        <ReferenceField
          source="installationType.id"
          reference="InstallationType"
          label="Installation Type"
          // fullWidth
        />
        <Divider style={{ margin: '1rem 0' }} />

        <Typography variant="h6" gutterBottom>
          Project Cost
        </Typography>
        <NumberField
          // required
          source="debt"
          options={{ style: 'currency', currency: 'USD' }}
          // fullWidth
          helperText="Portion of total project cost funded by debt."
        />
        <NumberField
          // required
          source="sponsorEquity"
          options={{ style: 'currency', currency: 'USD' }}
          // fullWidth
          helperText="Portion of total project cost funded by sponsor equity (crowd funding)."
        />
        <NumberField
          // required
          source="taxEquity"
          options={{ style: 'currency', currency: 'USD' }}
          fullWidth
          helperText="Portion of total project cost funded by tax equity."
        />
        <Divider style={{ margin: '1rem 0' }} />

        <Typography variant="h6" gutterBottom>
          System
        </Typography>
        <NumberField
          label="Proj. Sys Size (MW) AC"
          helperText="MW AC"
          source="systemSizeAC"
          // fullWidth
        />
        <NumberField
          label="Proj. Sys Size (MW) DC"
          helperText="MW DC"
          source="systemSizeDC"
          // fullWidth
        />
        <NumberField source="percentageOwnership" fullWidth />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Milestones
        </Typography>
        <DateField
          label="NTP date"
          helperText="Notice to proceed date. If this is not set, or the date is prior to the current date, than the project will not be included in the portfolio's investment cap."
          source="ntpDt"
          // fullWidth
        />
        <DateField
          label="Acquisition Dt"
          source="acquisitionDt"
          // fullWidth
          helperText="The date the project was acquired."
        />
        <DateField
          label="Projected Construction Start Dt"
          source="projectedConstructionStartDt"
          fullWidth
          helperText="The date the project is projected to begin construction."
        />
        <DateField
          label="Actual Construction Start Dt"
          source="actualConstructionStartDt"
          // fullWidth
          helperText="The date the project began construction."
        />
        <DateField label="Projected COD" source="projectedCOD" fullWidth />
        <DateField
          label="Actual COD"
          source="actualCOD"
          fullWidth
          helperText="Must be *actual*. Should not be a future date."
        />
        <DateField label="Projected End Dt" source="projectedEndDt" fullWidth />
      </TabbedShowLayout.Tab>
      <TabbedShowLayout.Tab label="Address" path="address">
        <Typography variant="h6" gutterBottom>
          Address
        </Typography>
        <TextField source="address1" />
        <TextField source="address2" />
        <TextField source="city" />
        <TextField source="postalCode" />
        <TextField source="state" />
        <ReferenceField source="country.id" reference="Country" />
        <NumberField
          // required
          source="latitude"
          fullWidth
        />
        <NumberField
          // required
          source="longitude"
          fullWidth
        />
      </TabbedShowLayout.Tab>

      <TabbedShowLayout.Tab label="Media" path="media">
        <Typography variant="h6" gutterBottom>
          Images
        </Typography>
        {!record?.images || record?.images?.length === 0 ? (
          'No photos'
        ) : (
          <Grid container>
            {record.images.map((image) => (
              <Grid style={{ padding: '1rem' }}>
                <Image
                  cloud_name={Config.cloud_name}
                  publicId={image.public_id}
                >
                  <Transformation width="240" crop="scale" />
                </Image>
              </Grid>
            ))}
          </Grid>
        )}
        <Divider style={{ width: '100%', margin: '2em 0' }} />
        <Typography variant="h6" gutterBottom>
          Videos
        </Typography>
        {!record?.videos
          ? 'No videos'
          : record.videos.map((video) =>
              !video?.public_id ? null : (
                <Grid style={{ paddingBottom: '1rem' }}>
                  <Video
                    cloud_name={Config.cloud_name}
                    publicId={video.public_id}
                    muted
                    width="240"
                    sourceTypes={['mp4']}
                    controls
                  >
                    {video.public_id ? (
                      <Transformation width={200} crop="scale" />
                    ) : null}
                  </Video>
                </Grid>
              )
            )}
      </TabbedShowLayout.Tab>

      <TabbedShowLayout.Tab label="Documents" path="documents">
        <Typography>IC Memo</Typography>
        <FunctionField
          source="ICMemo"
          label="ICMemo"
          render={(record) => {
            return (
              <>
                <div>
                  {!record.ICMemo ? (
                    <>
                      <FormHelperText>
                        IC-Memo must be in pdf form and don't forget to reduce
                        your file size before uploading (
                        <a
                          href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                          target="_blank"
                        >
                          Mac directions
                        </a>
                        ).
                      </FormHelperText>
                    </>
                  ) : (
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        // eslint-disable-next-line security/detect-non-literal-fs-filename
                        window.open(record.ICMemo.url, '_blank');
                      }}
                    >
                      <PictureAsPdf /> - View file
                    </Button>
                  )}
                </div>
                {record.ICMemo ? (
                  <TextField
                    label="Title"
                    helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - IC Memo')"
                    source="ICMemo.title"
                  />
                ) : null}
              </>
            );
          }}
        />

        <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />

        <Typography>Project Model</Typography>
        <FunctionField
          source="projectModel"
          label="Project Model"
          render={(record) => {
            return (
              <>
                <div>
                  {!record.projectModel ? (
                    <>
                      <FormHelperText>
                        Project Model must be in pdf form. Don't forget to
                        reduce your file size before uploading (
                        <a
                          href="https://support.apple.com/guide/preview/compress-a-pdf-prvw1509/mac"
                          target="_blank"
                        >
                          Mac directions
                        </a>
                        ).
                      </FormHelperText>
                    </>
                  ) : (
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        // eslint-disable-next-line security/detect-non-literal-fs-filename
                        window.open(record.projectModel.url, '_blank');
                      }}
                    >
                      <PictureAsPdf /> - View file
                    </Button>
                  )}
                </div>
                {record.projectModel ? (
                  <TextField
                    label="Title"
                    helperText="This is the filename the user will see. (format example: 'Pedro Teixeira - Financial Model')"
                    source="projectModel.title"
                  />
                ) : null}
              </>
            );
          }}
        />
      </TabbedShowLayout.Tab>

      <TabbedShowLayout.Tab label="PVSyst" path="pvsyst">
        <Typography variant="h6" gutterBottom>
          Module Specs
        </Typography>
        <NumberField
          label="Degradation constant"
          source="degradationConstant"
          fullWidth
        />
        <NumberField
          label="Temp. Coefficient (PMax)"
          helperText="The temperature coefficient of Pmax for the site\'s modules. this should be a loss coefficient per degrees celsius. (ex: 0.0034)"
          source="tempCoeffPmax"
          fullWidth
        />
        <NumberField
          label="NOCT"
          helperText="The nominal operating cell temperature of the modules. (ex: 45)"
          source="noct"
          fullWidth
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          P50 Production
        </Typography>
        <Typography variant="caption">
          This value should come from financial model
        </Typography>
        <NumberField
          label="January P50 Production (kWh)"
          source="janP50Prod"
          fullWidth
        />
        <NumberField
          label="February P50 Production (kWh)"
          source="febP50Prod"
          fullWidth
        />
        <NumberField
          label="March P50 Production (kWh)"
          source="marP50Prod"
          fullWidth
        />
        <NumberField
          label="April P50 Production (kWh)"
          source="aprP50Prod"
          fullWidth
        />
        <NumberField
          label="May P50 Production (kWh)"
          source="mayP50Prod"
          fullWidth
        />
        <NumberField
          label="June P50 Production (kWh)"
          source="junP50Prod"
          fullWidth
        />
        <NumberField
          label="July P50 Production (kWh)"
          source="julP50Prod"
          fullWidth
        />
        <NumberField
          label="August P50 Production (kWh)"
          source="augP50Prod"
          fullWidth
        />
        <NumberField
          label="September P50 Production (kWh)"
          source="sepP50Prod"
          fullWidth
        />
        <NumberField
          label="October P50 Production (kWh)"
          source="octP50Prod"
          fullWidth
        />
        <NumberField
          label="November P50 Production (kWh)"
          source="novP50Prod"
          fullWidth
        />
        <NumberField
          label="December P50 Production (kWh)"
          source="decP50Prod"
          fullWidth
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Performance Ratio
        </Typography>
        <NumberField
          label="January Performance Ratio"
          source="janPR"
          fullWidth
        />
        <NumberField
          label="February Performance Ratio"
          source="febPR"
          fullWidth
        />
        <NumberField label="March Performance Ratio" source="marPR" fullWidth />
        <NumberField label="April Performance Ratio" source="aprPR" fullWidth />
        <NumberField label="May Performance Ratio" source="mayPR" fullWidth />
        <NumberField label="June Performance Ratio" source="junPR" fullWidth />
        <NumberField label="July Performance Ratio" source="julPR" fullWidth />
        <NumberField
          label="August Performance Ratio"
          source="augPR"
          fullWidth
        />
        <NumberField
          label="September Performance Ratio"
          source="sepPR"
          fullWidth
        />
        <NumberField
          label="October Performance Ratio"
          source="octPR"
          fullWidth
        />
        <NumberField
          label="November Performance Ratio"
          source="novPR"
          fullWidth
        />
        <NumberField
          label="December Performance Ratio"
          source="decPR"
          fullWidth
        />
        <Typography variant="h6" gutterBottom>
          POA Irradiance
        </Typography>
        <Typography variant="caption">
          This value should come from PVSyst or Helioscope as-built.
        </Typography>
        <NumberField
          label="January Global Effective (Inclined) Irradiance (kWh/m2)"
          source="janGlobEff"
          fullWidth
        />
        <NumberField
          label="February Global Effective (Inclined) Irradiance (kWh/m2)"
          source="febGlobEff"
          fullWidth
        />
        <NumberField
          label="March Global Effective (Inclined) Irradiance (kWh/m2)"
          source="marGlobEff"
          fullWidth
        />
        <NumberField
          label="April Global Effective (Inclined) Irradiance (kWh/m2)"
          source="aprGlobEff"
          fullWidth
        />
        <NumberField
          label="May Global Effective (Inclined) Irradiance (kWh/m2)"
          source="mayGlobEff"
          fullWidth
        />
        <NumberField
          label="June Global Effective (Inclined) Irradiance (kWh/m2)"
          source="junGlobEff"
          fullWidth
        />
        <NumberField
          label="July Global Effective (Inclined) Irradiance (kWh/m2)"
          source="julGlobEff"
          fullWidth
        />
        <NumberField
          label="August Global Effective (Inclined) Irradiance (kWh/m2)"
          source="augGlobEff"
          fullWidth
        />
        <NumberField
          label="September Global Effective (Inclined) Irradiance (kWh/m2)"
          source="sepGlobEff"
          fullWidth
        />
        <NumberField
          label="October Global Effective (Inclined) Irradiance (kWh/m2)"
          source="octGlobEff"
          fullWidth
        />
        <NumberField
          label="November Global Effective (Inclined) Irradiance (kWh/m2)"
          source="novGlobEff"
          fullWidth
        />
        <NumberField
          label="December Global Effective (Inclined) Irradiance (kWh/m2)"
          source="decGlobEff"
          fullWidth
        />
        <Typography variant="h6" gutterBottom>
          Avg Module Temp
        </Typography>
        <Typography variant="caption">
          This value should come from PVSyst or Helioscope as-built.
        </Typography>
        <NumberField
          label="January Module Temperature Reference (celsius)"
          source="janModuleTempRef"
          fullWidth
        />
        <NumberField
          label="February Module Temperature Reference (celsius)"
          source="febModuleTempRef"
          fullWidth
        />
        <NumberField
          label="March Module Temperature Reference (celsius)"
          source="marModuleTempRef"
          fullWidth
        />
        <NumberField
          label="April Module Temperature Reference (celsius)"
          source="aprModuleTempRef"
          fullWidth
        />
        <NumberField
          label="May Module Temperature Reference (celsius)"
          source="mayModuleTempRef"
          fullWidth
        />
        <NumberField
          label="June Module Temperature Reference (celsius)"
          source="junModuleTempRef"
          fullWidth
        />
        <NumberField
          label="July Module Temperature Reference (celsius)"
          source="julModuleTempRef"
          fullWidth
        />
        <NumberField
          label="August Module Temperature Reference (celsius)"
          source="augModuleTempRef"
          fullWidth
        />
        <NumberField
          label="September Module Temperature Reference (celsius)"
          source="sepModuleTempRef"
          fullWidth
        />
        <NumberField
          label="October Module Temperature Reference (celsius)"
          source="octModuleTempRef"
          fullWidth
        />
        <NumberField
          label="November Module Temperature Reference (celsius)"
          source="novModuleTempRef"
          fullWidth
        />
        <NumberField
          label="December Module Temperature Reference (celsius)"
          source="decModuleTempRef"
          fullWidth
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Avg Ambient Temperature (optional)
        </Typography>
        <Typography variant="caption">
          This value should come from PVSyst or Helioscope as-built.
        </Typography>
        <NumberField
          label="January Ambient Temperature Reference (celsius)"
          source="janAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="February Ambient Temperature Reference (celsius)"
          source="febAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="March Ambient Temperature Reference (celsius)"
          source="marAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="April Ambient Temperature Reference (celsius)"
          source="aprAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="May Ambient Temperature Reference (celsius)"
          source="mayAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="June Ambient Temperature Reference (celsius)"
          source="junAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="July Ambient Temperature Reference (celsius)"
          source="julAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="August Ambient Temperature Reference (celsius)"
          source="augAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="September Ambient Temperature Reference (celsius)"
          source="sepAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="October Ambient Temperature Reference (celsius)"
          source="octAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="November Ambient Temperature Reference (celsius)"
          source="novAmbientTempRef"
          fullWidth
        />
        <NumberField
          label="December Ambient Temperature Reference (celsius)"
          source="decAmbientTempRef"
          fullWidth
        />
        <Divider style={{ margin: '1rem 0' }} />
        <Typography variant="h6" gutterBottom>
          Horizontal to POA Constant (optional)
        </Typography>
        <Typography variant="caption">
          This value should come from PVSyst or Helioscope as-built.
        </Typography>
        <NumberField
          label="January Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="janPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="February Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="febPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="March Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="marPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="April Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="aprPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="May Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="mayPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="June Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="junPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="July Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="julPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="August Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="augPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="September Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="sepPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="October Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="octPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="November Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="novPOAIrradianceFactor"
          fullWidth
        />
        <NumberField
          label="December Multiplier to get POA Irradiance from Horizontal Irradiance"
          source="decPOAIrradianceFactor"
        />
      </TabbedShowLayout.Tab>

      {/* <TabbedShowLayout.Tab label="O&M" path={'O&M'}>
          <Grid container style={{ width: '100%' }} spacing={4}>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  if (!formData.omFlg) {
                    return (
                      <Alert severity="warning">
                        This project is not currently flagged for O&M. Monthly
                        reports will not be auto-generated.
                      </Alert>
                    );
                  }
                  return null;
                }}
              </FormDataConsumer>
              <Grid item xs={12} md={6}>
                <BooleanInput
                  source="omFlg"
                  label="O&M Flag"
                  helperText="Turning this on will enable monthly O&M reports to be generated for this project"
                  fullWidth
                />
                <ReferenceInput
                  source="omTruck.id"
                  reference="OMTruck"
                  perPage={10000}
                >
                  <SelectInput optionText="name" label="O&M Truck" fullWidth />
                </ReferenceInput>
                <ReferenceInput
                  source="projectManager.id"
                  reference="EmployeeLite"
                  sort={{ field: 'firstName', order: 'ASC' }}
                  perPage={10000}
                  // filter={{ employeeTypeId: 1 }} // TODO: This should be turned back on but it will require upkeep
                >
                  <SelectInput
                    label="Project Manager"
                    fullWidth
                    allowEmpty
                    optionText="fullName"
                  />
                </ReferenceInput>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Credentials</b>
              </Typography>
              <ArrayInput fullWidth source="omPlatformCredentials" label="">
                <SimpleFormIterator
                  TransitionProps={{ enter: false, exit: false }}
                  disableReordering
                  inline
                >
                  <TextInput
                    fullWidth
                    required
                    label="Platform name"
                    source="name"
                  />
                  <TextInput label="Platform url" source="url" fullWidth />
                  <TextInput label="Username" source="username" fullWidth />
                  <TextInput label="Password" source="password" fullWidth />
                  <RichTextInput label="Notes" source="notes" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contacts</b>
              </Typography>
              <ArrayInput
                fullWidth
                source="projectContacts"
                // id="project-contacts"
                label=""
              >
                <SimpleFormIterator
                  disableReordering
                  inline
                  TransitionProps={{ enter: false, exit: false }}
                >
                  <TextInput
                    fullWidth
                    required
                    label="Label"
                    helperText="ex: O&M Contractor"
                    source="name"
                  />
                  <TextInput
                    label="Primary Contact Name"
                    source="primaryContactName"
                    fullWidth
                  />
                  <TextInput label="Phone" source="phone" fullWidth />
                  <TextInput label="Email" source="email" fullWidth />
                </SimpleFormIterator>
              </ArrayInput>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Insurance Policies</b>
              </Typography>
              <ArrayField source="projectInsurancePolicies" sortable={false}>
                <SingleFieldList>
                  <CustomReferenceField
                    source="label"
                    color={() => 'primary'}
                  />
                </SingleFieldList>
              </ArrayField>
              <Button
                style={{ marginTop: '1rem' }}
                component="a"
                variant="contained"
                color="secondary"
                href="/InsurancePolicy/create"
              >
                Add new insurance policy
              </Button>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                <b>Contracts</b>
              </Typography>
              <FormDataConsumer>
                {({ formData, ...rest }) => {
                  // if (!formData.omContractsDropboxLocation && !editDropboxLocation) {
                  //   setEditDropboxLocation(true)
                  // }
                  return (
                    <>
                      {!editDropboxLocation ? (
                        <Grid container>
                          <Grid item xs={11} style={{ paddingLeft: '1rem' }}>
                            <Typography variant="body2" gutterBottom>
                              <b>Dropbox directory:</b>{' '}
                              {formData.omContractsDropboxLocation ||
                                'None specified. Click the pencil to edit.'}
                            </Typography>
                          </Grid>
                          <Grid item xs={1}>
                            <Grid container item justifyContent="flex-end">
                              <IconButton
                                onClick={() => {
                                  setEditDropboxLocation(true);
                                }}
                                size="large"
                              >
                                <EditIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Grid>
                      ) : (
                        <TextInput
                          source="omContractsDropboxLocation"
                          fullWidth
                          helperText="The '8.1.1 O&M Agreement' directory for this project. Ex: '/Energea Global/Market II - USA/Portfolio/Projects/Portfolio 4 - MA - Waltham/8. Asset Management/8.1 O&M/8.1.1 O&M Agreement'"
                        />
                      )}
                      <Table>
                        <TableBody>
                          {formData.contracts &&
                          formData.contracts.length > 0 ? (
                            formData.contracts.map((contract, index) => (
                              <TableRow key={`contracts-row-${contract.id}`}>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none', width: '1rem' }
                                      : { width: '1rem' }
                                  }
                                >
                                  {contract.isDirectory ? (
                                    <Folder />
                                  ) : contract.isFile ? (
                                    <Description />
                                  ) : (
                                    <Help />
                                  )}
                                </TableCell>
                                <TableCell
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <b>{contract.name}</b>
                                </TableCell>
                                <TableCell
                                  align="right"
                                  style={
                                    index === formData.contracts.length - 1
                                      ? { borderBottom: 'none' }
                                      : {}
                                  }
                                >
                                  <Tooltip title="Download">
                                    <IconButton
                                      color="primary"
                                      disabled={!contract.isDownloadable}
                                      onClick={() =>
                                        handleDownloadDropboxFile(
                                          contract.location
                                        )
                                      }
                                      size="large"
                                    >
                                      <GetApp />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      style={{
                                        color: !contract.isFile
                                          ? null
                                          : theme.palette.error.main,
                                      }}
                                      disabled={!contract.isFile}
                                      onClick={() => {
                                        if (
                                          window.confirm(
                                            `Clicking 'OK' will PERMANENTLY DELETE this document from Dropbox. Are you sure you wish to delete '${contract.name}'?`
                                          )
                                        ) {
                                          handleDeleteDropboxFile(
                                            contract.location
                                          );
                                        }
                                      }}
                                      size="large"
                                    >
                                      <Delete />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <Alert severity="info">
                              No files found in Dropbox directory
                            </Alert>
                          )}
                        </TableBody>
                      </Table>
                      <Button
                        style={{ marginTop: '1rem' }}
                        variant="contained"
                        color="secondary"
                        component="label" // https://stackoverflow.com/a/54043619
                      >
                        Upload Contract to Dropbox
                        <input
                          type="file"
                          hidden
                          onChange={(event) =>
                            handleUploadFileToDropbox(
                              event,
                              formData.omContractsDropboxLocation
                            )
                          }
                        />
                      </Button>
                    </>
                  );
                }}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </TabbedShowLayout.Tab> */}

      <TabbedShowLayout.Tab label="Equipment Inventory" path={'Inventory'}>
        <Typography variant="h6" gutterBottom>
          <b>Equipment Inventory</b>
        </Typography>
        <ArrayField
          fullWidth
          source="projectEquipmentItems"
          id="project-equipment-show"
        >
          <Datagrid disableReordering fullWidth>
            <ReferenceField
              source="equipmentItem.id"
              label="Equipment Item"
              reference="EquipmentItem"
            />
            <NumberField required label="Quantity" source="quantity" />
            <BooleanField source="sparePartFlg" label="Is Spare Part?" />
          </Datagrid>
        </ArrayField>
      </TabbedShowLayout.Tab>
      <TabbedShowLayout.Tab label="Env. Impact">
        <Typography variant="h5" gutterBottom>
          <b>Projected</b>
        </Typography>
        <Typography gutterBottom>
          {record?.name} will create{' '}
          <b>
            {numeral(record?.lifetimeEnergyProjection / 1000).format('0,0')} MWh
          </b>{' '}
          in its lifetime. For perspective, that&apos;s equal to:
        </Typography>
        <EPCConversionGrid
          production={record?.lifetimeEnergyProjection / 1000}
          columns={3}
          dataPointKeys={[
            'tonsReduced',
            'homesPowered',
            'treesPlanted',
            'gallonsOfGasConsumed',
            'poundsCoalBurned',
            'smartPhonesCharged',
          ]}
        />
        <Divider style={{ margin: '1rem 0' }} />

        <Typography variant="h5" gutterBottom>
          <b>Actual</b>
        </Typography>
        <Typography gutterBottom>
          {record?.name} has created{' '}
          <b>{numeral(record?.allTimeActual / 1000).format('0,0.0')} MWh</b> so
          far. For perspective, that&apos;s equal to:
        </Typography>
        <EPCConversionGrid
          production={record?.allTimeActual / 1000}
          columns={3}
          dataPointKeys={[
            'tonsReduced',
            'homesPowered',
            'treesPlanted',
            'gallonsOfGasConsumed',
            'poundsCoalBurned',
            'smartPhonesCharged',
          ]}
        />
      </TabbedShowLayout.Tab>
    </TabbedShowLayout>
  );
};

export const ProjectShow = () => {
  return (
    <Show title={<Title />}>
      <ProjectShowView />
      {/* <SimpleShowLayout> */}
    </Show>
  );
};
// export const ProjectShow = () => (
//   <Show title={<Title />}>
//     <SimpleShowLayout>
//       <TextField source="name" />
//       <TextField source="shortSummary" />
//       <TextField source="shortSummaryPT" />
//       <TextField source="summary" />
//       <CustomBooleanField source="isPublic" />
//       <CustomBooleanField source="newFlg" />
//       <NumberField source="debt" />
//       <NumberField source="sponsorEquity" />
//       <NumberField source="taxEquity" />
//       <NumberField source="costAssessment" />
//       {/* <NumberField source="projectedAnnualProduction" /> */}
//       <NumberField source="systemSizeAC" />
//       <NumberField source="systemSizeDC" />
//       <DateField label="Projected COD" source="projectedCOD" />
//       <DateField label="Actual COD" source="actualCOD" />
//       <DateField label="Acquisition Dt" source="acquisitionDt" />
//       <DateField label="Projected End Dt" source="projectedEndDt" />
//       <CustomBooleanField source="generationDataPublicFlg" />
//       <TextField multiline source="internalNotes" />
//       <TextField source="primaryImage" />
//       <FunctionField
//         label="Images"
//         render={(record) => {
//           const jsx = [];
//           (record.images || []).forEach((image) => {
//             jsx.push(
//               <Image
//                 style={{}}
//                 cloud_name={Config.cloud_name}
//                 publicId={image.public_id}
//               >
//                 <Transformation width="200" crop="scale" />
//               </Image>
//             );
//           });
//           return <>{jsx}</>;
//         }}
//       />
//       <TextField source="countryCode" />
//       <LinkField
//         reference="Portfolio"
//         linkSource="portfolio.id"
//         labelSource="portfolio.name"
//         label="Portfolio"
//       />
//       <LinkField
//         label="Installation Type"
//         linkSource="installationType.id"
//         labelSource="installationType.name"
//         reference="InstallationType"
//       />
//     </SimpleShowLayout>
//   </Show>
// );

const MultiProjectUpdateButton = () => {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [reviewed, setReviewed] = useState(false);
  const [unitsReviewed, setUnitsReviewed] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const attrs = [
    {
      name: 'id',
      format: (val) => val,
      label: 'Project ID',
      align: 'left',
    },
    {
      name: 'debt',
      format: (val) => numeral(val).format('$0,0[.]00'),
      label: 'Debt',
      align: 'left',
    },
    {
      name: 'sponsorEquity',
      format: (val) => numeral(val).format('$0,0[.]00'),
      label: 'Sponsor Equity',
      align: 'left',
    },
    {
      name: 'taxEquity',
      format: (val) => numeral(val).format('$0,0[.]00'),
      label: 'Tax Equity',
      align: 'left',
    },
    {
      name: 'projectedCOD',
      format: (val) => moment.utc(val).format('MMM D, YYYY'),
      label: 'Projected COD',
      align: 'left',
    },
    {
      name: 'actualCOD',
      format: (val) => moment.utc(val).format('MMM D, YYYY'),
      label: 'Actual COD',
      align: 'left',
    },
    {
      name: 'acquisitionDt',
      format: (val) => moment.utc(val).format('MMM D, YYYY'),
      label: 'Acquisition Dt',
      align: 'left',
    },
    {
      name: 'ntpDt',
      format: (val) => moment.utc(val).format('MMM D, YYYY'),
      label: 'NTP Dt',
      align: 'left',
    },
    {
      name: 'systemSizeAC',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'System Size (MW) AC',
      align: 'left',
    },
    {
      name: 'systemSizeDC',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'System Size (MW) DC',
      align: 'left',
    },
    {
      name: 'janP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'January P50 (kWh)',
      align: 'left',
    },
    {
      name: 'febP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'February P50 (kWh)',
      align: 'left',
    },
    {
      name: 'marP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'March P50 (kWh)',
      align: 'left',
    },
    {
      name: 'aprP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'April P50 (kWh)',
      align: 'left',
    },
    {
      name: 'mayP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'May P50 (kWh)',
      align: 'left',
    },
    {
      name: 'junP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'June P50 (kWh)',
      align: 'left',
    },
    {
      name: 'julP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'July P50 (kWh)',
      align: 'left',
    },
    {
      name: 'augP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'August P50 (kWh)',
      align: 'left',
    },
    {
      name: 'sepP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'September P50 (kWh)',
      align: 'left',
    },
    {
      name: 'octP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'October P50 (kWh)',
      align: 'left',
    },
    {
      name: 'novP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'November P50 (kWh)',
      align: 'left',
    },
    {
      name: 'decP50Prod',
      format: (val) => numeral(val).format('0,0[.]00'),
      label: 'December P50 (kWh)',
      align: 'left',
    },
  ];

  const handleData = (data) => {
    const lintedData = data.map((point) => ({
      id: parseInt(point.projectId, 10),
      debt: parseFloat(point.debt || 0),
      sponsorEquity: parseFloat(point.sponsorEquity || 0),
      taxEquity: parseFloat(point.taxEquity || 0),
      projectedCOD: moment(point.projectedCOD),
      actualCOD: moment(point.actualCOD),
      acquisitionDt: moment(point.acquisitionDt),
      ntpDt: moment(point.ntpDt),
      systemSizeDC: parseFloat(point.systemSizeMWDC || 0),
      systemSizeAC: parseFloat(point.systemSizeMWAC || 0),
      janP50Prod: parseFloat(point.janP50Prod || 0),
      febP50Prod: parseFloat(point.febP50Prod || 0),
      marP50Prod: parseFloat(point.marP50Prod || 0),
      aprP50Prod: parseFloat(point.aprP50Prod || 0),
      mayP50Prod: parseFloat(point.mayP50Prod || 0),
      junP50Prod: parseFloat(point.junP50Prod || 0),
      julP50Prod: parseFloat(point.julP50Prod || 0),
      augP50Prod: parseFloat(point.augP50Prod || 0),
      sepP50Prod: parseFloat(point.sepP50Prod || 0),
      octP50Prod: parseFloat(point.octP50Prod || 0),
      novP50Prod: parseFloat(point.novP50Prod || 0),
      decP50Prod: parseFloat(point.decP50Prod || 0),
    }));
    setData(lintedData);
  };

  const save = () => {
    setLoading(true);
    dataProvider
      .update('ProjectExcelUpdates', {
        data,
      })
      .catch((e) => {
        setLoading(false);
        notify(
          `Error uploading Project data to projects table. Error: ${e}`,
          'error'
        );
        refresh();
      })
      .then(() => {
        setLoading(false);
        setReviewed(false);
        setUnitsReviewed(false);
        setUploadDialogOpen(false);
        setData(null);
        notify('Project updates uploaded.');
        refresh();
      });
  };

  const renderSubmit = () => {
    return (
      <Alert severity={reviewed && unitsReviewed ? 'success' : 'warning'}>
        <Grid container>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={!!reviewed}
                  onChange={() => setReviewed(!reviewed)}
                />
              }
              label="I have checked the data and I want to overwrite the below fields for all projects listed below. This cannot be undone."
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={!!unitsReviewed}
                  onChange={() => setUnitsReviewed(!unitsReviewed)}
                />
              }
              label="I have confirmed that the system sizes are in MW and that the p50 values are in kWh."
            />
          </Grid>
          <Grid item container justifyContent="flex-end">
            <Button
              onClick={save}
              disabled={!reviewed || !unitsReviewed || loading}
              variant="contained"
              size="large"
              color="secondary"
            >
              {loading ? <CircularProgress /> : 'Save'}
            </Button>
          </Grid>
        </Grid>
      </Alert>
    );
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'} key={attr.label}>
                {attr.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell
                  align={attr.align || 'center'}
                  key={`project-attr-${attr.name}-row-${index}`}
                >
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow key={`project-${row.id}`}>
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  return (
    <>
      <Button
        label="Upload Project Updates"
        onClick={() => {
          setUploadDialogOpen(true);
        }}
        size="small"
        startIcon={<GetApp />}
      >
        Upload Project Updates
      </Button>
      <Dialog open={uploadDialogOpen} fullWidth maxWidth="xl">
        <DialogTitle>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>Project Updates Upload</Grid>
            <Grid item>
              <IconButton
                onClick={() => setUploadDialogOpen(false)}
                size="large"
              >
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>
          <Grid container style={{ minWidth: '450px' }}>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Button
                component="a"
                variant="contained"
                href="/csv-templates/projectsUpdate.xlsx"
                download
              >
                <GetApp />
                Click to download the excel template
              </Button>
            </Grid>
            <ExcelReader handleData={handleData} />
            <Grid item style={{ margin: 'auto' }}>
              {data ? renderSubmit() : null}
            </Grid>
            <Grid item xs={12}>
              {data ? renderData() : null}
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
    </>
  );
};

const ShowProjectMapButton = (props) => {
  const [projectMapDialogOpen, setProjectMapDialogOpen] = useState(false);
  const [portfolios, setPortfolios] = useState(null);
  const [portfoliosLoading, setPortfoliosLoading] = useState(false);
  const [projects, setProjects] = useState(null);
  const [projectsLoading, setProjectsLoading] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectPopoverOpen, setProjectPopoverOpen] = useState(false);
  const [projectPopoverAnchorEl, setProjectPopoverAnchorEl] = useState(null);
  const [selectedPortfolioIds, setSelectedPortfolioIds] = useState(null);
  const dataProvider = useDataProvider();

  if (projectMapDialogOpen && !portfoliosLoading && !portfolios) {
    dataProvider
      .getList('PortfolioLite', {
        filter: {},
        pagination: { page: 1, perPage: 10000 },
        sort: { field: 'name', order: 'ASC' },
      })
      .then((res) => {
        setPortfolios(res.data);
        setPortfoliosLoading(false);
        setSelectedPortfolioIds(res.data.map((portfolio) => portfolio.id));
      });
  }

  if (projectMapDialogOpen && !projectsLoading && !projects) {
    dataProvider
      .getList('ProjectLite', {
        // filter: { publicFlg: true },
        filter: {},
        pagination: { page: 1, perPage: 10000 },
        sort: { field: 'name', order: 'ASC' },
      })
      .then((res) => {
        setProjects(res.data);
        setProjectsLoading(false);
      });
  }

  let jsx = null;
  if (
    portfoliosLoading ||
    projectsLoading ||
    !portfolios ||
    !projects ||
    !selectedPortfolioIds
  ) {
    jsx = (
      <Grid container item justifyContent="center" alignItems="center">
        <CircularProgress />
      </Grid>
    );
  } else {
    const onMarkerClick = (project) => {
      return (event) => {
        setSelectedProject(project);
        setProjectPopoverOpen(true);
        setProjectPopoverAnchorEl(event.currentTarget);
      };
    };
    const handleClose = () => {
      setProjectPopoverOpen(false);
      setProjectPopoverAnchorEl(null);
    };
    const markers = projects
      .filter(
        (project) => selectedPortfolioIds.indexOf(project.portfolioId) > -1
      )
      .map((project) => ({
        onMarkerClick: onMarkerClick(project),
        latitude: project.latitude || 0,
        longitude: project.longitude || 0,
        scale: 0.75,
      }));

    jsx = (
      <>
        <Grid container item justifyContent="center" alignItems="center">
          {portfolios
            .filter(
              (portfolio) =>
                projects.filter((p) => p.portfolioId === portfolio.id).length >
                0
            )
            .map((portfolio) => {
              const portfolioSelected =
                selectedPortfolioIds.indexOf(portfolio.id) > -1;
              return (
                <Chip
                  clickable
                  label={portfolio.name}
                  variant={portfolioSelected ? 'contained' : 'outlined'}
                  style={{
                    margin: '0.25em',
                    backgroundColor: portfolioSelected
                      ? theme.palette.green.main
                      : 'white',
                    borderColor: portfolioSelected
                      ? theme.palette.green.main
                      : null,
                    padding: '4px',
                  }}
                  onClick={() => {
                    const newList = [...selectedPortfolioIds];
                    if (selectedPortfolioIds?.indexOf(portfolio.id) > -1) {
                      setSelectedPortfolioIds(
                        newList.filter((id) => id !== portfolio.id)
                      );
                    } else {
                      setSelectedPortfolioIds([...newList, portfolio.id]);
                    }
                  }}
                />
              );
            })}
        </Grid>
        <Grid
          item
          onClick={(e) => e.preventDefault()}
          style={{
            overflow: 'hidden',
            borderRadius: theme.shape.borderRadius,
          }}
        >
          <Map markers={markers} projection="mercator" />
        </Grid>
        {projectPopoverOpen ? (
          <Popover
            open={projectPopoverOpen}
            anchorEl={projectPopoverAnchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
          >
            <Typography variant="h6" style={{ padding: '1em' }}>
              {selectedProject.name}
            </Typography>
          </Popover>
        ) : null}
      </>
    );
  }

  return (
    <>
      <Button
        size="small"
        label="View Project Map"
        onClick={() => {
          setProjectMapDialogOpen(true);
        }}
        startIcon={<Public />}
      >
        View Project Map
      </Button>
      <Dialog open={projectMapDialogOpen} fullScreen maxWidth="xl">
        <DialogTitle>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>Project Map</Grid>
            <Grid item>
              <IconButton
                onClick={() => setProjectMapDialogOpen(false)}
                size="large"
              >
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>{jsx}</DialogContent>
      </Dialog>
    </>
  );
};

const ListActions = (props) => {
  const { resource, displayedFilters, filterValues, showFilter } =
    useListContext();
  return (
    <TopToolbar>
      {props.filters &&
        React.cloneElement(props.filters, {
          resource,
          showFilter,
          displayedFilters,
          filterValues,
          context: 'button',
        })}
      <CreateButton />
      <ExportButton maxResults={100000} />
      <MultiProjectUpdateButton {...props} />
      <ShowProjectMapButton {...props} />
    </TopToolbar>
  );
};

const ProjectFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by Project Name"
      source="q"
      alwaysOn
    />
  </Filter>
);
const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200]} />
);

const Aside = () => {
  const [missingProjectFields, setMissingProjectFields] = useState(null);
  const [loading, setLoading] = useState(false);
  const dataProvider = useDataProvider();

  if (!loading && !missingProjectFields) {
    setLoading(true);
    dataProvider.getOne('MissingProjectFields', {}).then(
      (res) => {
        setLoading(false);
        setMissingProjectFields(res.data);
      },
      (e) => console.error('HIT ERROR', e)
    );
  }
  let jsx;
  if (loading || !missingProjectFields) {
    jsx = <CircularProgress />;
  } else {
    const dataMap = [
      { endpoint: 'allProjectsMissingICMemos', label: 'Missing ICMemos' },
    ];
    jsx = dataMap.map((data) => (
      <Grid item xs={12} key={`missing-project-field-${data.endpoint}`}>
        <Typography gutterBottom variant="h6">
          {data.label} ({missingProjectFields[data.endpoint]?.length})
        </Typography>
        <Grid container spacing={0.5}>
          {missingProjectFields[data.endpoint]?.length === 0 ? (
            <Alert severity="success">You're all up to date!</Alert>
          ) : (
            missingProjectFields[data.endpoint]?.map((missingProjectField) => (
              <Grid
                item
                xs={12}
                key={`missing-field-chip-${data.endpoint}-${missingProjectField.id}`}
              >
                <Chip
                  style={{ cursor: 'pointer' }}
                  component={Link}
                  to={`/Project/${missingProjectField.id}/documents`}
                  label={`${missingProjectField.name}`}
                  color="error"
                />
              </Grid>
            ))
          )}
        </Grid>
      </Grid>
    ));
  }

  return (
    <div>
      <Grid container style={{ margin: '1rem', width: '260px' }} spacing={1}>
        {jsx}
      </Grid>
    </div>
  );
};
export const ProjectList = () => {
  const { permissions } = usePermissions();
  const errorStyling = { color: 'red', fontWeight: 'bold' };

  return (
    <List
      perPage={25}
      pagination={<CustomPagination />}
      actions={<ListActions />}
      filters={<ProjectFilter />}
      aside={<Aside />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <DetailField source="shortSummary" sortable={false} />
        {/* <DetailField source="shortSummaryPT" sortable={false} /> */}
        <DetailField source="summary" sortable={false} />
        <CustomBooleanField source="isPublic" />
        <CustomBooleanField hideOnFalse={true} source="newFlg" />
        <DateField label="NTP" source="ntpDt" />
        <DateField label="Acquisition Dt" source="acquisitionDt" />
        <FunctionField
          label="Projected Construction Start Dt"
          render={(record) => {
            if (!record.projectedConstructionStartDt) return null;
            const sDate = moment(record.projectedConstructionStartDt).format(
              'MM/DD/YYYY'
            );
            if (
              moment(record.projectedConstructionStartDt).isBefore(moment()) &&
              !record.actualConstructionStartDt
            ) {
              return (
                <span style={errorStyling}>
                  {moment(record.projectedConstructionStartDt).format(
                    'MM/DD/YYYY'
                  )}
                </span>
              );
            }
            return sDate;
          }}
        />
        <FunctionField
          label="Actual Construction Start Dt"
          render={(record) => {
            if (!record.actualConstructionStartDt) {
              return null;
            }
            const sDate = moment(record.actualConstructionStartDt).format(
              'MM/DD/YYYY'
            );
            if (moment(record.actualConstructionStartDt).isAfter(moment())) {
              return <span style={errorStyling}>{sDate}</span>;
            }
            return sDate;
          }}
        />
        <FunctionField
          label="Projected COD"
          render={(record) => {
            if (!record.projectedCOD) return null;
            const sDate = moment(record.projectedCOD).format('MM/DD/YYYY');
            if (
              moment(record.projectedCOD).isBefore(moment()) &&
              !record.actualCOD
            ) {
              return (
                <span style={errorStyling}>
                  {moment(record.projectedCOD).format('MM/DD/YYYY')}
                </span>
              );
            }
            return sDate;
          }}
        />
        <FunctionField
          label="Actual COD"
          render={(record) => {
            if (
              !record.actualCOD &&
              record?.projectInvestmentStatus?.id === 8
            ) {
              // Cash Flowing
              return <span style={errorStyling}>MISSING</span>;
            }
            if (!record.actualCOD) {
              return null;
            }
            const sDate = moment(record.actualCOD).format('MM/DD/YYYY');
            if (moment(record.actualCOD).isAfter(moment())) {
              return <span style={errorStyling}>{sDate}</span>;
            }
            if (
              moment(record.actualCOD).isBefore(moment()) &&
              !record.actualCOD
            ) {
              return <span style={errorStyling}>{sDate}</span>;
            }
            return sDate;
          }}
        />
        <DateField label="Projected End Dt" source="projectedEndDt" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <FunctionField
          label="Investment Status"
          render={(record) => {
            const resp = record.projectInvestmentStatus?.name;
            if (
              record.actualCOD &&
              [1, 2, 3, 9].indexOf(record.projectInvestmentStatus?.id) > -1
            ) {
              return <span style={errorStyling}>{resp}</span>;
            }
            return resp;
          }}
        />
        <LinkField
          label="Project Manager"
          linkSource="projectManager.id"
          labelSource="projectManager.fullName"
          reference="Employee"
        />
        <CustomBooleanField
          label="P50 Data Inputted"
          source="P50ProdCompleteFlg"
          sortable={false}
        />
        <NumberField source="revenuePerkWh" label="Revenue per kWh" />
        <NumberField
          source="revenuePerkWhUSD"
          label="Revenue per kWh in USD"
          options={{ style: 'currency', currency: 'USD' }}
          sortable={false}
        />
        <CustomBooleanField source="generationDataPublicFlg" sortable={false} />
        <CustomBooleanField
          label="Has primary video?"
          source="hasPrimaryVideo"
          sortable={false}
        />
        <FunctionField
          label="Main Image"
          render={(record) => {
            if (!record.primaryImage) return null; // TODO return placeholder
            return (
              <Image
                cloud_name={Config.cloud_name}
                publicId={record.primaryImage.public_id}
              >
                <Transformation width="120" crop="scale" />
              </Image>
            );
          }}
        />
        <FunctionField
          align="center"
          label="Cost Assessment"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record) return null;
            const data = [
              {
                label: 'Debt',
                value: numeral(record.debt).format('$0,0'),
              },
              {
                label: 'Sponsor Equity',
                value: numeral(record.sponsorEquity).format('$0,0'),
              },
              {
                label: 'Tax Equity',
                value: numeral(record.taxEquity).format('$0,0'),
              },
              {
                label: 'Cost Assessment',
                value: numeral(record.costAssessment).format('$0,0'),
              },
            ];
            const text = (
              <>
                <Grid container>
                  {data.map((dataPoint, i) => (
                    <Grid
                      key={`data-point-key-${dataPoint.label}`}
                      container
                      style={{
                        borderTop:
                          i === data.length - 1 ? 'thin solid #fff' : null,
                      }}
                      justifyContent="space-between"
                    >
                      <Grid item>
                        <Typography>
                          <b>{dataPoint.label} : </b>
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography>{dataPoint.value}</Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              </>
            );
            return (
              <Tooltip title={text}>
                <IconButton
                  style={{ color: !record.costAssessment ? 'red' : null }}
                  size="large"
                >
                  <Subject />
                </IconButton>
              </Tooltip>
            );
          }}
        />
        {/* <NumberField
        label="Proj. Production"
        source="projectedAnnualProduction"
      /> */}
        <NumberField label="Sys Size MWac" source="systemSizeAC" />
        <NumberField label="Sys Size MWdc" source="systemSizeDC" />
        <NumberField label="Owned Sys Size MWdc" source="ownedSystemSizeDC" />
        <LinkField
          label="Installation Type"
          linkSource="installationType.id"
          labelSource="installationType.name"
          reference="InstallationType"
        />
        <DetailField source="internalNotes" sortable={false} />
      </Datagrid>
    </List>
  );
};

export const ProjectCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
