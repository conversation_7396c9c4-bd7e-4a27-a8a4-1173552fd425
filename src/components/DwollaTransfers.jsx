import React from 'react';

import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Filter,
  List,
  NumberField,
  Pagination,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextField,
  TextInput,
  useNotify,
  useRedirect,
  useRefresh,
} from 'react-admin';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, <PERSON>rid, Tooltip, Typography } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { FileCopy } from '@mui/icons-material';

const entityName = 'Dwolla Transfer';

export const DwollaTransferShow = () => (
  <Show>
    <SimpleShowLayout>
      <TextField source="id" />
      <TextField source="status" />
      <TextField source="amount.value" />
      <DateField source="created" />
      <TextField source="correlationId" />
      <TextField source="individualAchId" />
    </SimpleShowLayout>
  </Show>
);

const CustomPagination = () => (
  <Pagination rowsPerPageOptions={[25, 50, 100, 200, 500, 1000]} />
);

const CustomFilter = (props) => (
  <Filter {...props}>
    <CustomNumberInput
      style={{ minWidth: '220px' }}
      label="Min Amount"
      source="startAmount"
    />
    <CustomNumberInput
      style={{ minWidth: '220px' }}
      label="Max Amount"
      source="endAmount"
    />
    <DateInput
      style={{ minWidth: '220px' }}
      label="Start Date Lower Bound"
      source="startDtLowerBound"
    />
    <DateInput
      style={{ minWidth: '220px' }}
      label="Start Date Upper Bound"
      source="startDtUpperBound"
    />
  </Filter>
);

export const DwollaTransferList = () => (
  <List
    perPage={25}
    pagination={<CustomPagination />}
    sort={{ field: 'id', order: 'DESC' }}
    filters={<CustomFilter />}
  >
    <Datagrid rowClick="show">
      <TextField source="id" sortable={false} />
      <TextField source="status" sortable={false} />
      <NumberField
        source="amount.value"
        options={{ style: 'currency', currency: 'USD' }}
        sortable={false}
      />
      <LinkField
        reference="Transfer"
        linkSource="transfer.id"
        labelSource="transfer.label"
        label="Transfer"
      />
      <DateField source="created" sortable={false} />
      <TextField source="correlationId" sortable={false} />
      <TextField source="individualAchId" sortable={false} />
    </Datagrid>
  </List>
);

export const DwollaTransferCreate = () => {
  const notify = useNotify();
  const refresh = useRefresh();
  const redirect = useRedirect();
  const onSuccess = () => {
    notify(`Dwolla Transfer Created`, { type: 'success' });
    redirect('/DwollaTransfer');
    refresh();
  };
  const renderFundingSourceIds = () => {
    const fundingSourceData = [
      {
        label: 'Project 1, Sao Fernando Hydro',
        value: 'ce7881fc-8039-4d40-afab-a16c9176a9b6',
      },
      {
        label: 'Portfolio 2, Community Solar in Brazil',
        value: '787cdaa9-b518-4d85-b766-839ff556d9cc',
      },
      {
        label: 'Portfolio 3, Solarize Africa',
        value: '61ea8f13-2aaf-4d26-8b64-ba2bf75a5849',
      },
      {
        label: 'Portfolio 4, Solar in the USA',
        value: '0449f586-b882-4871-a014-96c0af045e36',
      },
      {
        label: 'Energea Wallet Balance',
        value: 'c42c628d-afbf-4326-82cb-5919c53d5cdd',
      },
      {
        label: 'Investor Communications',
        value: 'b3e410d1-a150-475d-b087-6468091ec560',
      },
      {
        label: 'Energea Global LLC',
        value: 'cac75f51-8339-44ae-b53c-d70428276842',
      },
    ];
    const jsx = fundingSourceData.map((item) => (
      <Tooltip key={item.value} title="Click to copy to clipboard" arrow>
        <Typography key={item.value} gutterBottom variant="body2">
          {item.label} :{' '}
          <a
            onClick={() => {
              navigator.clipboard.writeText(item.value);
              notify('ID copied to clipboard', { type: 'success' });
            }}
            style={{ cursor: 'pointer', textDecoration: 'underline' }}
            role="button"
          >
            {item.value} <FileCopy fontSize="small" />
          </a>
        </Typography>
      </Tooltip>
    ));
    return jsx;
  };

  return (
    <Create
      title={`Create ${entityName}`}
      mutationOptions={{ onSuccess: onSuccess }}
      undoable={false}
    >
      <SimpleForm>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextInput
              fullWidth
              source="fromAccountId"
              label="From Account ID"
            />
            <TextInput fullWidth source="toAccountId" label="To Account ID" />
            <CustomNumberInput fullWidth source="amount" />
            <TextInput
              fullWidth
              source="correlationId"
              label="Correlation ID"
              helperText="Optional. The database id of the transfer that this Dwolla transfer is associated with."
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Alert severity="info">
              <AlertTitle>
                <strong>Funding Source ID Reference</strong>
              </AlertTitle>
              {renderFundingSourceIds()}
            </Alert>
          </Grid>
        </Grid>
      </SimpleForm>
    </Create>
  );
};
