import React from 'react';

import {
  ArrayField,
  Datagrid,
  FunctionField,
  List,
  NumberField,
  SingleFieldList,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import numeral from 'numeral';

import { Grid, Icon, Tooltip, Typography } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomReferenceField } from './CustomFields';
import theme from '../theme';

const entityName = 'Marketing Portfolio';

export const MarketingPortfolioList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="subtitle" />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          source="totalDebt"
        />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          source="totalTaxEquity"
        />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          source="totalSponsorEquity"
        />
        <NumberField
          options={{ style: 'currency', currency: 'USD' }}
          source="currentEquityData.totalNetRaised"
          label="Total Crowd Raised"
        />
        <NumberField
          source="percentageOfEquityContributingToDividends"
          options={{ style: 'percent' }}
        />
        <NumberField source="systemSizeSumDC" label="System Size (MW) DC" />
        <FunctionField
          label="Tons of carbon emissions reduced (lifetime)"
          render={(record) => {
            const p = record.lifetimeEnergyProjection / 1000;
            const tonsOfCarbon = p * 0.699;
            return (
              <Grid container direction="column" style={{ minWidth: '70px' }}>
                <Grid item>{numeral(tonsOfCarbon).format('0,0.[00]a')}</Grid>
                <Grid item>
                  <Typography variant="caption">
                    <i>
                      {numeral(
                        tonsOfCarbon /
                          (record.totalDebt +
                            record.totalTaxEquity +
                            record.totalSponsorEquity)
                      ).format('0,0.000')}{' '}
                      per $1
                    </i>
                  </Typography>
                </Grid>
              </Grid>
            );
          }}
        />
        <FunctionField
          label="Est. Lifetime Production (MWh)"
          render={(record) => {
            return (
              <Grid container direction="column" style={{ minWidth: '70px' }}>
                <Grid item>
                  {numeral(record.lifetimeEnergyProjection).format('0,0.[00]a')}
                </Grid>
                <Grid item>
                  <Typography variant="caption">
                    <i>
                      {numeral(
                        record.lifetimeEnergyProjection /
                          (record.totalDebt +
                            record.totalTaxEquity +
                            record.totalSponsorEquity)
                      ).format('0,0.[00]a')}{' '}
                      per $1
                    </i>
                  </Typography>
                </Grid>
              </Grid>
            );
          }}
        />
        <FunctionField
          sortable={false}
          label="Actual IRR (Crowd Only)"
          render={(record) =>
            `${numeral(record.crowdOnlyActualIRR).format('0,0.000')}%`
          }
        />
        <FunctionField
          label="Total Project Costs"
          render={(record) => (
            <span>
              {numeral(
                record.totalDebt +
                  record.totalTaxEquity +
                  record.totalSponsorEquity
              ).format('$0,0.00')}
            </span>
          )}
        />
        <FunctionField
          label="Timeline Events Status"
          render={(record) => {
            if (!record.missingProjectEvents) return null;
            const sText = (
              <>
                {record.missingProjectEvents.length === 0 ? (
                  <span>No missing events!</span>
                ) : (
                  record.missingProjectEvents.map((el) => (
                    <>
                      <span>{el}</span>
                      <br />
                    </>
                  ))
                )}
              </>
            );
            return (
              <Tooltip title={sText}>
                <span>
                  {' '}
                  <Icon
                    style={{
                      color:
                        record.missingProjectEvents.length === 0
                          ? theme.palette.green.main
                          : theme.palette.error.main,
                    }}
                    className={
                      record.missingProjectEvents.length === 0
                        ? 'fas fa-circle-check'
                        : 'fas fa-circle-xmark'
                    }
                  />
                </span>
              </Tooltip>
            );
          }}
        />
        <ArrayField source="projects">
          <SingleFieldList>
            <CustomReferenceField
              color={(resource) => (resource.isPublic ? 'primary' : 'default')}
              source="name"
            />
          </SingleFieldList>
        </ArrayField>
      </Datagrid>
    </List>
  );
};
