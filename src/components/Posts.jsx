import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  useRefresh,
  usePermissions,
  DateTimeInput,
  DateField,
  useResourceDefinition,
} from 'react-admin';
import { RichTextInput } from 'ra-input-rich-text';

import {
  Button,
  Card,
  CardActionArea,
  CardMedia,
  CardContent,
  FormHelperText,
  Grid,
  Typography,
} from '@mui/material';

import { Image, Video, Transformation } from 'cloudinary-react';
import { Delete, ArrowRight } from '@mui/icons-material';
import { getEditable } from '../utils/applyRoleAuth';

import { openUploadWidget } from '../utils/CloudinaryService';
import { CustomNumberInput, DetailField, LinkField } from './CustomFields';
import theme from '../theme';

import Config from '../config/config';

const entityName = 'Post';

export const PostEdit = () => {
  const [postPreviewTitle, setPostPreviewTitle] = useState(null);
  const [postPreviewEstimatedReadTime, setPostPreviewEstimatedReadTime] =
    useState(null);

  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const onPhotoUploaded = (photo) => {
    dataProvider
      .create('PostBannerImage', {
        input: {
          postId: parseInt(id, 10),
          image: { public_id: photo.public_id },
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
      })
      .then(() => {
        notify('Successfully uploaded image');
        refresh();
      });
  };

  const onVideoUploaded = (video) => {
    dataProvider
      .create('PostVideo', {
        input: {
          postId: parseInt(id, 10),
          video: { public_id: video.public_id },
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
      })
      .then(() => {
        notify('Successfully uploaded video');
        refresh();
      });
  };

  const handleAddImage = () => {
    const uploadOptions = {
      tags: ['post-banner-image'],
      multiple: false,
      resourceType: 'image',
      showPoweredBy: false,
      cloudName: Config.cloud_name,
      uploadPreset: Config.post_banner_image_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onPhotoUploaded(resp.info);
      }
    });
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      eager: [
        { width: 200, crop: 'scale' },
        { width: 960, crop: 'scale' },
        { width: 1280, crop: 'scale' },
        { width: 1920, crop: 'scale' },
      ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.post_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideoUploaded(resp.info);
      }
    });
  };

  const handleRemoveVideo = (resource) => {
    return () => {
      dataProvider
        .delete('PostVideo', {
          postId: parseInt(id, 10),
          id: resource.video.id,
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing video', { type: 'error' });
        })
        .then(() => {
          notify('Video successfully removed');
          refresh();
        });
    };
  };

  const handleRemoveImage = (imageId) => {
    return () => {
      dataProvider
        .delete('PostBannerImage', {
          postId: parseInt(id, 10),
          id: imageId,
        })
        .catch((e) => {
          console.error('ERROR', e);
          notify('Error removing image', { type: 'error' });
        })
        .then(() => {
          notify('Image successfully removed');
          refresh();
        });
    };
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              multiline
              source="title"
              helperText="Title text of post. Title's to be capitalized."
              fullWidth
              onChange={(event) => setPostPreviewTitle(event.target.value)}
            />
            <TextInput
              multiline
              fullWidth
              source="summary"
              helperText="1 to 2 sentences max. This shows below the article tile on the investment 101 page."
            />
            <TextInput
              source="readTime"
              helperText="Estimated article read time (ex: '5 min read'"
              fullWidth
              onChange={(event) =>
                setPostPreviewEstimatedReadTime(event.target.value)
              }
            />
            <DateTimeInput source="postedDt" />
            <TextInput
              multiline
              fullWidth
              source="externalUrl"
              helperText="If present, 'read more' link will go to this url"
            />
            <CustomNumberInput source="orderNo" fullWidth />
            <BooleanInput source="inactive" />
            <RichTextInput
              multiline
              fullWidth
              source="article"
              helperText="This will show if the user clicks on the tile."
            />
            <ReferenceInput source="category.id" reference="PostCategory">
              <SelectInput
                label="Category"
                fullWidth
                helperText="If no category is selected, article will not appear on frontend"
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
        </Grid>
        <FunctionField
          align="center"
          label="Banner Image"
          style={{ width: '100%' }}
          render={(record) => {
            if (!postPreviewTitle && !postPreviewEstimatedReadTime) {
              setPostPreviewTitle(record.title);
              setPostPreviewEstimatedReadTime(record.readTime);
            }
            return (
              <>
                <Image
                  style={{}}
                  cloud_name={Config.cloud_name}
                  publicId={record.bannerImage && record.bannerImage.public_id}
                >
                  <Transformation width="200" crop="scale" />
                </Image>
                {record.bannerImage ? (
                  <Button onClick={handleRemoveImage(record.bannerImage.id)}>
                    <Delete /> Delete
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={handleAddImage}
                  >
                    Add Image
                  </Button>
                )}
                <FormHelperText>
                  Image should be landscape (16:9 or 4:3), at least
                  1200px-2400px in width, and should avoid the use of text
                  (unless a company logo).
                </FormHelperText>
              </>
            );
          }}
        />
        <FunctionField
          align="center"
          label="Video"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record.video || !record.video.public_id) return null;
            return (
              <>
                <Video
                  cloud_name={Config.cloud_name}
                  publicId={record.video.public_id}
                  muted
                  width="200"
                  crop="scale"
                  sourceTypes={['mp4']}
                  controls
                >
                  {/* <Transformation width={200} crop="scale" /> */}
                </Video>
                <Button
                  style={{ float: 'right' }}
                  onClick={handleRemoveVideo(record)}
                >
                  <Delete />
                </Button>
              </>
            );
          }}
        />
        <div className="actions">
          <Button
            variant="contained"
            color="primary"
            onClick={uploadVideoWithCloudinary}
          >
            Add video
          </Button>
        </div>
        <FunctionField
          align="center"
          label="Live Preview"
          style={{ width: '100%' }}
          render={(record) => {
            return (
              <Grid container style={{ width: '100%' }} justifyContent="center">
                <Grid item xs={12}>
                  <Typography variant="h5">Live Preview</Typography>
                </Grid>
                <Grid item style={{ textAlign: 'center' }}>
                  <Card
                    component="a"
                    href={record.externalUrl || null}
                    style={{
                      flexGrow: 1,
                      width: '308px',
                      backgroundColor: '#fff',
                      display: 'flex',
                    }}
                    rel="noopener noreferrer"
                    target="_blank"
                    elevation={4}
                  >
                    <CardActionArea>
                      <CardMedia
                        style={{ height: '200px' }}
                        // image={constants.missingImageUrl}
                        title={`${record.title} banner image`}
                      >
                        <Image
                          style={{
                            objectFit: 'cover',
                            width: '100%',
                            height: '100%',
                            // maxHeight: '285px'
                          }}
                          title={`${record.title} banner image`}
                          width="500"
                          crop="scale"
                          cloud_name={Config.cloud_name}
                          publicId={
                            record.bannerImage && record.bannerImage.public_id
                          } // eslint-disable-line camelcase
                        />
                      </CardMedia>
                      <CardContent>
                        <Typography
                          style={{
                            minHeight: '78px',
                            textAlign: 'left',
                            fontSize: '1.1rem',
                            fontWeight: 'bold',
                          }}
                          variant="body1"
                        >
                          <b>{postPreviewTitle}</b>
                        </Typography>
                        <Typography
                          variant="body2"
                          style={{
                            width: '100%',
                            minHeight: '21px',
                            textAlign: 'left',
                          }}
                          color="textSecondary"
                          component="p"
                        >
                          <em>{postPreviewEstimatedReadTime}</em>
                        </Typography>
                        <Grid
                          container
                          style={{ width: '100%' }}
                          justifyContent={
                            record.video && record.article
                              ? 'space-between'
                              : 'flex-end'
                          }
                        >
                          {record.video ? (
                            <Grid item>
                              <Typography
                                variant="body2"
                                style={{
                                  textAlign: 'right',
                                  textDecoration: 'none',
                                  fontWeight: 'bold',
                                  color: theme.palette.primary.light,
                                  '&:hover': {
                                    cursor: 'pointer',
                                    color: theme.palette.primary.main,
                                  },
                                }}
                              >
                                {record.video ? 'Watch Video ' : 'Read More '}
                                <ArrowRight
                                  style={{
                                    marginBottom: '-7px',
                                    marginLeft: '-6px',
                                  }}
                                />
                              </Typography>
                            </Grid>
                          ) : null}
                          {record.article || record.externalUrl ? (
                            <Grid item>
                              <Typography
                                onClick={
                                  record.article
                                    ? (event) => {
                                        event.stopPropagation();
                                      }
                                    : null
                                }
                                variant="body2"
                                style={{
                                  textAlign: 'right',
                                  textDecoration: 'none',
                                  fontWeight: 'bold',
                                  color: theme.palette.primary.light,
                                  '&:hover': {
                                    cursor: 'pointer',
                                    color: theme.palette.primary.main,
                                  },
                                }}
                              >
                                {'Read More '}
                                <ArrowRight
                                  style={{
                                    marginBottom: '-7px',
                                    marginLeft: '-6px',
                                  }}
                                />
                              </Typography>
                            </Grid>
                          ) : null}
                        </Grid>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              </Grid>
            );
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

export const PostList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <NumberField source="orderNo" />
        <FunctionField
          label="Main Image"
          render={(record) => {
            if (!record.bannerImage) return null; // TODO return placeholder
            return (
              <Image
                cloud_name={Config.cloud_name}
                publicId={record.bannerImage.public_id}
              >
                <Transformation width="120" crop="scale" />
              </Image>
            );
          }}
        />
        <TextField source="title" />
        <TextField source="summary" />
        <TextField source="readTime" sortable={false} />
        <DateField source="postedDt" />
        <TextField source="externalUrl" sortable={false} />
        <BooleanField source="inactive" />
        <DetailField source="article" sortable={false} />
        <LinkField
          label="Category"
          linkSource="category.id"
          labelSource="postCategory.name"
          reference="PostCategory"
        />
      </Datagrid>
    </List>
  );
};

export const PostCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="title"
            helperText="Title text of post. Title's to be capitalized. This can be updated at any time."
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
