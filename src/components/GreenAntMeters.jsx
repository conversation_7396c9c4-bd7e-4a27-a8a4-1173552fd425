import React, { useState } from 'react';
import moment from 'moment';
import numeral from 'numeral';
import { useParams } from 'react-router-dom';
import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FormDataConsumer,
  FormTab,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { CheckCircle, Error, GetApp } from '@mui/icons-material';
import { Alert } from '@mui/lab';

import { getEditable } from '../utils/applyRoleAuth';
import ExcelReader from './ExcelReader';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'GreenAnt Meter';

const attrs = [
  {
    name: 'periodStartDt',
    format: (val) => val,
    label: 'Time',
    align: 'left',
  },
  {
    name: 'production',
    format: (val) => (val ? numeral(val).format('0,0') : ''),
    label: 'Production (kWh)',
    align: 'left',
  },
  {
    name: 'unit',
    format: (val) => val,
    label: 'Unit',
    align: 'right',
  },
  {
    name: 'timeUnit',
    format: (val) => val,
    label: 'Time Unit',
    align: 'right',
  },
];

export const GreenAntMeterEdit = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [reviewed, setReviewed] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const backfillMeterHistoryFromGreenAnt = (startDt, endDt) => {
    if (startDt > endDt) {
      notify('Backfill start date must precede backfill end date.', {
        type: 'error',
      });
      setLoading(false);
      return;
    }
    dataProvider
      .create('ProductionPeriod', {
        input: {
          backfillFromAPI: true,
          greenAntMeterId: parseInt(id, 10),
          startDt,
          endDt,
        },
      })
      .then(() => {
        setLoading(false);
        notify('GreenAnt meter production history successfully backfilled');
        refresh();
      })
      .catch((e) => {
        setLoading(false);
        console.error('ERROR', e);
        notify('Error backfilling meter production history', { type: 'error' });
      });
  };

  const handleData = (data) => {
    const lintedData = data
      .filter((point, index) => index !== 0)
      .map((point) => {
        const values = Object.values(point);
        const datetime = values[0];
        const periodStartDt = moment
          .utc(datetime, '(DD/MM) HH:mm')
          .format('YYYY-MM-DD HH:mm:ss');
        const production = values[1] * 1000;
        return {
          greenAntMeterId: parseInt(id, 10),
          periodStartDt,
          production,
          unit: 'Wh',
          timeUnit: 'HOUR',
        };
      });
    setData(lintedData);
  };

  const save = () => {
    setLoading(true);
    dataProvider
      .create('UploadProductionPeriod', {
        data: {
          greenAntMeterId: parseInt(id, 10),
          data,
        },
      })
      .catch((e) => {
        setLoading(false);
        notify(
          `Error uploading GreenAnt production data. Error: ${e}`,
          'error'
        );
        refresh();
      })
      .then(() => {
        setLoading(false);
        notify('GreenAnt Production data uploaded.');
        refresh();
      });
  };

  const renderData = () => {
    return (
      <Table aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell style={{ width: '1em' }} align="center">
              Status
            </TableCell>
            {attrs.map((attr) => (
              <TableCell align={attr.align || 'center'}>{attr.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row) => {
            let errors = false;
            const cellJsx = attrs.map((attr) => {
              const val = row[String(attr.name)];
              const validated = attr.validate
                ? attr.validate(val)
                : val === 0 || !!val;
              if (!errors && !validated) errors = true;
              return (
                <TableCell align={attr.align || 'center'}>
                  {attr.format ? attr.format(val) : val}
                </TableCell>
              );
            });
            return (
              <TableRow
                onClick={() => {}}
                // style={{ cursor: 'pointer' }}
                key={`${row.email}`}
              >
                <TableCell style={{ paddingRight: 0 }} align="right">
                  {errors ? (
                    <Error style={{ color: 'red' }} />
                  ) : (
                    <CheckCircle style={{ color: 'green' }} />
                  )}
                </TableCell>
                {cellJsx}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  };

  const renderSubmit = () => {
    return (
      <Alert severity={!reviewed ? 'warning' : 'success'}>
        <FormControlLabel
          control={
            <Checkbox
              checked={!!reviewed}
              onChange={() => setReviewed(!reviewed)}
            />
          }
          label="I have checked the list and all dates and values look good (this will override the existing records!)"
        />
        <Button
          onClick={save}
          disabled={!reviewed || loading}
          variant="contained"
          size="large"
          color="secondary"
        >
          {loading ? <CircularProgress /> : 'Save'}
        </Button>
      </Alert>
    );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <TabbedForm redirect={false}>
        <FormTab label="Details">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12} md={6}>
              <TextInput source="name" disabled fullWidth />
              <TextInput source="timezone" disabled fullWidth />
              <CustomNumberInput source="meterId" disabled fullWidth />
              <ReferenceInput
                source="project.id"
                reference="Project"
                sort={{ field: 'name', order: 'ASC' }}
                perPage={10000}
              >
                <SelectInput
                  label="Project"
                  required
                  fullWidth
                  helperText="Select the project associated with this GreenAnt meter."
                  optionText="name"
                />
              </ReferenceInput>
              <BooleanInput
                source="isLivePollingEnabled"
                fullWidth
                helperText="Toggling this switch will enable/disable automatic production data fetching."
              />
              <CustomNumberInput
                source="noCommsLimitMins"
                fullWidth
                label="No Comms Alert Limit (minutes)"
                helperText="If you want to disable the 'No Comms' alert for this site, set to null."
              />
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Backfill">
          <Grid container style={{ width: '100%' }}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                Backfill Meter Data
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextInput
                label="From"
                source="meterBackfillStartDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                style={{ marginRight: '2rem' }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextInput
                label="To"
                source="meterBackfillEndDt"
                type="date"
                helperText="Enter date in the timezone of the site"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormDataConsumer>
                {({ formData, ...rest }) => (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      disabled={
                        loading ||
                        !(
                          formData.meterBackfillStartDt &&
                          formData.meterBackfillEndDt
                        )
                      }
                      onClick={() => {
                        setLoading(true);
                        backfillMeterHistoryFromGreenAnt(
                          formData.meterBackfillStartDt,
                          formData.meterBackfillEndDt
                        );
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        'Backfill Meter History'
                      )}
                    </Button>
                  </>
                )}
              </FormDataConsumer>
            </Grid>
          </Grid>
        </FormTab>
        <FormTab label="Production Upload">
          <Grid container style={{ minWidth: '450px' }}>
            <Typography gutterBottom>
              To upload data from CSV,{' '}
              <a href="/ProductionPeriod/create">click here</a>.
            </Typography>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Typography>or</Typography>
            </Grid>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Alert severity="info">
                Make sure the excel document you are uploading matches the
                (date, value, column, etc.) formatting of the downloadable excel
                below.
              </Alert>
            </Grid>
            <Grid xs={12} item style={{ padding: '1em' }}>
              <Button
                component="a"
                variant="contained"
                href="/csv-templates/greenAntProductionData.xlsx"
                download
              >
                <GetApp />
                Click to download the excel template
              </Button>
            </Grid>
            <ExcelReader handleData={handleData} />
            <Grid item style={{ margin: 'auto' }}>
              {data ? renderSubmit() : null}
            </Grid>
            <Grid item xs={12}>
              {data ? renderData() : null}
            </Grid>
          </Grid>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

export const GreenAntMeterList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="name" />
        <LinkField
          reference="Project"
          linkSource="project.id"
          labelSource="project.name"
          label="Project"
        />
        <TextField source="meterId" label="Meter ID" />
        <BooleanField source="isLivePollingEnabled" />
        <TextField source="timezone" />
        <NumberField source="noCommsLimitMins" fullWidth />
      </Datagrid>
    </List>
  );
};

export const GreenAntMeterCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            source="project.id"
            reference="Project"
            sort={{ field: 'name', order: 'ASC' }}
            perPage={10000}
          >
            <SelectInput
              label="Project"
              required
              fullWidth
              helperText="Select the project associated with this GreenAnt meter."
              optionText="name"
            />
          </ReferenceInput>
          <CustomNumberInput
            required
            source="meterId"
            label="GreenAnt meter id"
            helperText="This should be the ID of the meter in the GreenAnt system. You can find this by going to the GreenAnt dashboard and grabbing the number in the URL after 'meters/'"
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
