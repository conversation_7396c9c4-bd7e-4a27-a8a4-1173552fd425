import React from 'react';
import { Grid, Typography } from '@mui/material';

// Define an interface for the component's props
interface LegendItemProps {
  label: string;
  color: string;
}

// Convert the component to use TypeScript functionality
const LegendItem: React.FC<LegendItemProps> = ({ label, color }) => {
  if (!label || !color) {
    throw new Error('Missing required props (label or color)');
  }
  return (
    <Grid container alignItems="center">
      <Grid item style={{ background: '#fff', marginRight: '4px' }}>
        <span
          style={{
            background: color,
            border: '1px solid lightgrey',
            width: '16px',
            height: '16px',
            display: 'block',
          }}
        />
      </Grid>
      <Grid item>
        <Typography variant="caption">= {label}</Typography>
      </Grid>
    </Grid>
  );
};

export default LegendItem;
