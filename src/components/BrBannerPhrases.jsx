import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  NumberInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';
import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput } from './CustomFields';

const entityName = 'Br Banner Phrase';

export const BrBannerPhraseEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput multiline source="entryEN" fullWidth />
            <TextInput multiline source="entryPT" fullWidth />
            <CustomNumberInput source="orderNo" step={1} fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrBannerPhraseList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <NumberField source="orderNo" />
        <TextField source="entryEN" sortable={false} />
        <TextField source="entryPT" sortable={false} />
      </Datagrid>
    </List>
  );
};

export const BrBannerPhraseCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput multiline source="entryEN" fullWidth />
          <TextInput multiline source="entryPT" fullWidth />
          <CustomNumberInput source="orderNo" step={1} fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
