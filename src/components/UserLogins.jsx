import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Create,
  Datagrid,
  DateField,
  DateTimeInput,
  Edit,
  Filter,
  Labeled,
  List,
  ReferenceInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import {
  Alert,
  CircularProgress,
  Grid,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import MuiList from '@mui/material/List';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'User Login';

export const UserLoginEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <Labeled fullWidth>
              <LinkField
                reference="User"
                linkSource="user.id"
                labelSource="user.fullName"
                label="User"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                label="IP Address"
                linkSource="ipAddress.id"
                labelSource="ipAddress.label"
                reference="IpAddress"
              />
            </Labeled>
            <Labeled fullWidth>
              <LinkField
                label="Device"
                linkSource="device.id"
                labelSource="device.label"
                reference="Device"
              />
            </Labeled>
            <DateTimeInput source="loginDt" disabled fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const UserLoginFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name, Last Name, or Email"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const UserLoginList = () => {
  const [potentialFraudData, setPotentialFraudData] = useState(null);
  const dataProvider = useDataProvider();
  const { permissions } = usePermissions();

  if (!potentialFraudData) {
    dataProvider.getOne('PotentialFraudData', {}).then((res) => {
      setPotentialFraudData(res.data);
    });
  }

  return (
    <>
      {!potentialFraudData ? (
        <CircularProgress />
      ) : (
        <Grid
          container
          style={{ padding: '1rem', maxHeight: '400px', overflow: 'scroll' }}
        >
          <Grid item xs={12} md={6}>
            <Grid item xs={12}>
              <Typography variant="h6">Devices with multiple users:</Typography>
            </Grid>
            <Grid item xs={12}>
              {potentialFraudData?.devicesUsedByMultipleUsers?.length > 0 ? (
                <MuiList dense>
                  {potentialFraudData?.devicesUsedByMultipleUsers
                    ?.sort((a, b) => (a.users.length > b.users.length ? -1 : 1))
                    .map((device) => (
                      <ListItem key={`device-${device.id}`}>
                        <ListItemText
                          primary={device.label}
                          secondary={device.users
                            .map((user) => user.fullName)
                            .join(', ')}
                        />
                      </ListItem>
                    ))}
                </MuiList>
              ) : (
                <Alert severity="info">
                  No devices found with multiple users logging in
                </Alert>
              )}
            </Grid>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid item xs={12}>
              <Typography variant="h6">
                IP Addresses with multiple users:
              </Typography>
            </Grid>
            <Grid item xs={12}>
              {potentialFraudData?.ipsUsedByMultipleUsers?.length > 0 ? (
                <MuiList dense>
                  {potentialFraudData?.ipsUsedByMultipleUsers
                    ?.sort((a, b) => (a.users.length > b.users.length ? -1 : 1))
                    .map((ip) => (
                      <ListItem key={`ip-${ip.id}`}>
                        <ListItemText
                          primary={ip.label}
                          secondary={ip.users
                            .map((user) => user.fullName)
                            .join(', ')}
                        />
                      </ListItem>
                    ))}
                </MuiList>
              ) : (
                <Alert severity="info">
                  No IPs found with multiple users logging in
                </Alert>
              )}
            </Grid>
          </Grid>
        </Grid>
      )}
      <List
        title={entityName}
        perPage={25}
        sort={{ field: 'id', order: 'DESC' }}
        filters={<UserLoginFilter />}
      >
        <Datagrid
          rowClick={
            getEditable(useResourceDefinition().name, permissions)
              ? 'edit'
              : 'show'
          }
        >
          <TextField source="id" />
          <LinkField
            reference="User"
            linkSource="user.id"
            labelSource="user.fullName"
            label="User"
          />
          <LinkField
            label="IP Address"
            linkSource="ipAddress.id"
            labelSource="ipAddress.label"
            reference="IpAddress"
          />
          <LinkField
            label="Device"
            linkSource="device.id"
            labelSource="device.label"
            reference="Device"
          />
          <DateField source="loginDt" showTime />
        </Datagrid>
      </List>
    </>
  );
};

export const UserLoginCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <ReferenceInput
            perPage={10000}
            source="user.id"
            sortable={false}
            reference="UserLite"
          >
            <AutocompleteInput
              label="User (Investor)"
              required
              fullWidth
              allowEmpty
              optionText="fullName"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <ReferenceInput
            perPage={100000}
            source="ipAddress.id"
            sortable={false}
            reference="IpAddress"
          >
            <AutocompleteInput
              label="IP Address"
              required
              fullWidth
              allowEmpty
              optionText="label"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <ReferenceInput
            perPage={100000}
            source="device.id"
            sortable={false}
            reference="Device"
          >
            <AutocompleteInput
              label="Device"
              required
              fullWidth
              allowEmpty
              optionText="label"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <DateTimeInput source="loginDt" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
