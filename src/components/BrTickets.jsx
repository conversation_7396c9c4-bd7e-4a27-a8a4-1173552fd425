import React from 'react';
import { useParams } from 'react-router-dom';

import {
  AutocompleteInput,
  Button,
  Create,
  Datagrid,
  DateField,
  DateInput,
  DateTimeInput,
  Edit,
  Filter,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  usePermissions,
  useRefresh,
  useResourceDefinition,
} from 'react-admin';

import {
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { Add, CloudDownload, Delete } from '@mui/icons-material';

import { CustomNumberInput, DetailField, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';
import { uploadObjectToS3 } from '../utils/aws';
import { lintAwsObjectKey } from '../utils/global';

const entityName = 'Ticket';

export const BrTicketEdit = () => {
  const { id } = useParams();
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();

  const createDocument = (event) => {
    const {
      target: {
        validity,
        files: [file],
      },
    } = event;
    if (validity.valid) {
      const fileSize = file.size / 1024 / 1024; // in MiB
      if (fileSize > 10) {
        notify('File must be less than 10MB', { type: 'error' });
        return;
      }
      const awsObjectKey = lintAwsObjectKey(
        `TicketDocuments/${id}/${file.name}`
      );
      uploadObjectToS3(
        file,
        awsObjectKey,
        process.env.REACT_APP_S3_CREDIT_MGMT_BUCKET
      ).then(
        () => {
          dataProvider
            .create('BrTicketDocument', {
              data: {
                brTicketId: parseInt(id, 10),
                awsObjectKey: awsObjectKey,
              },
            })
            .then(
              (res) => {
                notify('Document uploaded', { type: 'success' });
                refresh();
              },
              (err) => {
                console.error(err);
                notify('Error uploading document', {
                  type: 'error',
                });
              }
            );
        },
        (e) => {
          notify('Error uploading document', { type: 'error' });
        }
      );
    }
  };

  const deleteDocument = (documentId) => {
    dataProvider
      .delete('BrTicketDocument', {
        id: documentId,
      })
      .then(
        (res) => {
          notify('Document deleted', { type: 'success' });
          refresh();
        },
        (err) => {
          console.error(err);
          notify('Error deleting document', {
            type: 'error',
          });
        }
      );
  };

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h5">Details:</Typography>
            <TextInput source="ticketNumber" disabled fullWidth />
            <TextInput source="description" fullWidth multiline />
            <DateTimeInput source="openedDt" fullWidth />
            <DateTimeInput source="closedDt" fullWidth />
            <ReferenceInput
              source="brConsumerUnit.id"
              reference="BrConsumerUnitLite"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <AutocompleteInput
                optionText="name"
                label="Consumer unit"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="brSalesPerson.id"
              reference="BrSalesPerson"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Sales partner"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="brTicketType.id"
              reference="BrTicketType"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Ticket type"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <ReferenceInput
              source="brTicketCommunicationChannel.id"
              reference="BrTicketCommunicationChannel"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Communication channel"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
            <Typography variant="h5" style={{ marginTop: '1rem' }}>
              Notes:
            </Typography>
            <TextInput source="additionalNotes" fullWidth multiline />
            <Typography variant="h5" style={{ marginTop: '1rem' }}>
              Documents:
            </Typography>
            <FunctionField
              label="Documents"
              render={(record) => {
                return (
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Download</TableCell>
                        <TableCell>Delete</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {record?.brTicketDocuments?.map((document) => (
                        <TableRow key={document.id}>
                          <TableCell>{document.name}</TableCell>
                          <TableCell>
                            <Button
                              variant="contained"
                              color="primary"
                              endIcon={<CloudDownload />}
                              style={{ textTransform: 'none' }}
                              onClick={() => {
                                window.location.assign(document.downloadUrl);
                              }}
                            >
                              Download
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="contained"
                              color="secondary"
                              endIcon={<Delete />}
                              onClick={() => deleteDocument(document.id)}
                              style={{ textTransform: 'none' }}
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={3}>
                          <Button
                            variant="contained"
                            color="primary"
                            endIcon={<Add />}
                            component="label" // https://stackoverflow.com/a/54043619
                            style={{ textTransform: 'none' }}
                          >
                            Upload document
                            <input
                              type="file"
                              hidden
                              onChange={(event) => createDocument(event)}
                            />
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                );
              }}
            />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

const CustomFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by ticket description"
      source="q"
      alwaysOn
    />
    <SelectInput
      style={{ minWidth: '200px' }}
      label="Ticket status"
      source="status"
      alwaysOn
      choices={[
        { id: 'open', name: 'Open' },
        { id: 'closed', name: 'Closed' },
      ]}
    />
    <ReferenceInput
      label="Consumer unit"
      source="brConsumerUnit.id"
      reference="BrConsumerUnitLite"
      perPage={10000}
      sort={{ field: 'name', order: 'ASC' }}
    >
      <AutocompleteInput label="Consumer unit" optionText="name" />
    </ReferenceInput>
  </Filter>
);

export const BrTicketList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} filters={<CustomFilter />}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="ticketNumber" />
        <TextField source="description" />
        <DateField source="openedDt" showTime />
        <DateField source="closedDt" showTime />
        <LinkField
          reference="BrConsumerUnit"
          linkSource="brConsumerUnit.id"
          labelSource="brConsumerUnit.name"
          label="Consumer unit"
        />
        <LinkField
          reference="BrSalesPerson"
          linkSource="brSalesPerson.id"
          labelSource="brSalesPerson.name"
          label="Sales partner"
        />
        <LinkField
          reference="BrTicketType"
          linkSource="brTicketType.id"
          labelSource="brTicketType.name"
          label="Ticket type"
        />
        <LinkField
          reference="BrTicketCommunicationChannel"
          linkSource="brTicketCommunicationChannel.id"
          labelSource="brTicketCommunicationChannel.name"
          label="Communication channel"
        />
        <DetailField source="additionalNotes" />
      </Datagrid>
    </List>
  );
};

export const BrTicketCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="description" required fullWidth multiline />
          <DateTimeInput source="openedDt" required fullWidth />
          <ReferenceInput
            source="brTicketType.id"
            reference="BrTicketType"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Ticket type"
              fullWidth
              required
              helperText={
                <Typography variant="caption">
                  To add a new ticket type,{' '}
                  <a href="/BrTicketType/create" style={{ cursor: 'pointer' }}>
                    click here.
                  </a>
                </Typography>
              }
            />
          </ReferenceInput>
          <ReferenceInput
            source="brTicketCommunicationChannel.id"
            reference="BrTicketCommunicationChannel"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput
              optionText="name"
              label="Communication channel"
              fullWidth
              required
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
