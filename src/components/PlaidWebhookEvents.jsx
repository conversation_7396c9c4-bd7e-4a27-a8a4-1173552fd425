import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Datagrid,
  DateField,
  DateInput,
  Edit,
  FormDataConsumer,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid, Table, TableBody, TableCell, TableRow } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { DetailField } from './CustomFields';

const entityName = 'Plaid Webhook Event';

export const PlaidWebhookEventEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="webhookType" fullWidth disabled />
            <TextInput source="webhookCode" fullWidth disabled />
            <DateInput source="createdAt" fullWidth disabled />
            <FormDataConsumer fullWidth>
              {({ formData, ...rest }) => {
                const obj = JSON.parse(formData.body);
                return (
                  <Table dense>
                    <TableBody>
                      {Object.keys(obj).map((key) => (
                        <TableRow>
                          <TableCell>{key}</TableCell>
                          <TableCell>
                            {JSON.stringify(obj[String(key)])}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                );
              }}
            </FormDataConsumer>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const PlaidWebhookEventList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="webhookType" />
        <TextField source="webhookCode" />
        <TextField source="itemId" />
        <DetailField source="body" />
        <DateField source="createdAt" showTime={true} />
      </Datagrid>
    </List>
  );
};
