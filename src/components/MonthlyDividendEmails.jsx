import React from 'react';
import moment from 'moment';
import {
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  DateInput,
  FormDataConsumer,
  List,
  SelectInput,
  SimpleForm,
  TextField,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { <PERSON>ert, AlertTitle, Grid, Typography } from '@mui/material';

import { CustomNumberInput } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Monthly Dividend Email';

export const MonthlyDividendEmailList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="month" />
        <TextField source="year" />
        <TextField source="status" />
        <DateField source="createdAt" />
      </Datagrid>
    </List>
  );
};

export const MonthlyDividendEmailCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <BooleanInput source="previewMode" fullWidth defaultValue={true} />
          <FormDataConsumer>
            {({ formData, ...rest }) => {
              if (formData.previewMode) {
                return (
                  <>
                    <Typography variant="h5">Preview Mode</Typography>
                    <CustomNumberInput
                      source="previewUserId"
                      required
                      fullWidth
                      helperText="Supplying a preview user ID will send an <NAME_EMAIL> for the user id supplied."
                    />
                  </>
                );
              } else {
                return (
                  <Alert severity="warning">
                    <AlertTitle>
                      Make sure all dividend reinvestments have been executed
                      before clicking 'Create'.
                    </AlertTitle>
                    Clicking 'Create' below is what triggers all dividend emails
                    to be sent. The email will be incorrect for each user that
                    the reinvestment is still processing for.
                  </Alert>
                );
              }
            }}
          </FormDataConsumer>
          <SelectInput
            source="month"
            label="Month"
            fullWidth
            required
            helperText="This is the month the dividends were paid out. (ie: if you distribute the dividends on January 24th, the month would be January)"
            choices={[
              { id: 1, name: 'January' },
              { id: 2, name: 'February' },
              { id: 3, name: 'March' },
              { id: 4, name: 'April' },
              { id: 5, name: 'May' },
              { id: 6, name: 'June' },
              { id: 7, name: 'July' },
              { id: 8, name: 'August' },
              { id: 9, name: 'September' },
              { id: 10, name: 'October' },
              { id: 11, name: 'November' },
              { id: 12, name: 'December' },
            ]}
          />
          <SelectInput
            source="year"
            label="Year"
            fullWidth
            required
            choices={[
              // { id: moment().year() - 1, name: moment().year() - 1 },
              { id: moment().year(), name: moment().year() },
              // { id: moment().year() + 1, name: moment().year() + 1 },
            ]}
          />
          <DateInput
            source="dividendRangeStartDt"
            required
            fullWidth
            helperText="The start date of the range of dividends to include in the transactional email. Add some padding on both ends of the range for timezone safety."
          />
          <DateInput
            source="dividendRangeEndDt"
            required
            fullWidth
            helperText="The end date of the range of dividends to include in the transactional email. Add some padding on both ends of the range for timezone safety."
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
