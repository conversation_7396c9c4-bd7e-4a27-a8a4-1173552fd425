import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  DateField,
  Edit,
  Filter,
  List,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { getEditable } from '../utils/applyRoleAuth';
import { LinkField } from './CustomFields';

const entityName = 'Event Email';

export const EventEmailEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="event.title" label="Event" />
      </SimpleForm>
    </Edit>
  );
};

const EventEmailFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by User Name"
      source="q"
      alwaysOn
    />
    <ReferenceInput source="event.id" label="Event" reference="Event">
      <SelectInput label="Event" optionText="title" />
    </ReferenceInput>
  </Filter>
);

export const EventEmailList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      perPage={25}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<EventEmailFilter />}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <TextField source="event.title" label="Event" sortable={false} />
        <DateField source="createdAt" />
        <DateField source="updatedAt" />
      </Datagrid>
    </List>
  );
};

export const EventEmailCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm></SimpleForm>
  </Create>
);
