import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON><PERSON>,
  <PERSON>grid,
  DateField,
  DateInput,
  Edit,
  List,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';

const entityName = 'Br Consortium';

export const BrConsortiumEdit = () => {
  const { id } = useParams();
  const { permissions } = usePermissions();

  const isIT = permissions?.roles?.indexOf('ITWrite') > -1;

  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="legalName" fullWidth required />
            <TextInput source="internalName" fullWidth required />
            <DateInput source="registrationDt" fullWidth />
            <TextInput source="registrationNumber" fullWidth />
            <TextInput source="cnpj" fullWidth />
            <TextInput source="address1" fullWidth />
            <TextInput source="address2" fullWidth />
            <TextInput source="city" fullWidth />
            <TextInput source="state" fullWidth />
            <TextInput source="district" fullWidth />
            <TextInput source="postalCode" fullWidth />
            {isIT && (
              <>
                <TextInput source="stripeAccountKey" fullWidth />
                <TextInput source="stripeWebhookKey" fullWidth />
              </>
            )}
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const BrConsortiumList = () => {
  const { permissions } = usePermissions();
  const isIT = permissions?.roles?.map((el) => el.name)?.indexOf('ITRead') > -1;

  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <TextField source="legalName" />
        <TextField source="internalName" />
        <DateField source="registrationDt" />
        <TextField source="registrationNumber" />
        <TextField source="cnpj" />
        <TextField source="address" />
        {isIT && <TextField source="stripeAccountKey" />}
        {isIT && <TextField source="stripeWebhookKey" />}
      </Datagrid>
    </List>
  );
};

export const BrConsortiumCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="legalName" fullWidth required />
          <TextInput source="internalName" fullWidth required />
          <DateInput source="registrationDt" fullWidth />
          <TextInput source="registrationNumber" fullWidth />
          <TextInput source="cnpj" fullWidth />
          <TextInput source="address1" fullWidth />
          <TextInput source="address2" fullWidth />
          <TextInput source="city" fullWidth />
          <TextInput source="state" fullWidth />
          <TextInput source="district" fullWidth />
          <TextInput source="postalCode" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
