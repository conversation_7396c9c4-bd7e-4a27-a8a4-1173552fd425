import React from 'react';
import { useParams } from 'react-router-dom';
import {
  Create,
  Datagrid,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Portfolio Tax Year';

export const PortfolioTaxYearEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <CustomNumberInput required source="taxYear" fullWidth />
            <ReferenceInput source="portfolio.id" reference="PortfolioLite">
              <SelectInput
                label="Portfolio"
                required
                fullWidth
                optionText="name"
              />
            </ReferenceInput>
            <CustomNumberInput source="profitLoss" fullWidth />
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
};

export const PortfolioTaxYearList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <NumberField source="taxYear" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <NumberField source="profitLoss" />
      </Datagrid>
    </List>
  );
};

export const PortfolioTaxYearCreate = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <CustomNumberInput source="taxYear" fullWidth step={1} />
          <ReferenceInput source="portfolio.id" reference="PortfolioLite">
            <SelectInput label="Portfolio" fullWidth optionText="name" />
          </ReferenceInput>
          <CustomNumberInput source="profitLoss" fullWidth />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
