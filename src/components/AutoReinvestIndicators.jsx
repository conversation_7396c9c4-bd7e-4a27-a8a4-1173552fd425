import React from 'react';
import { useParams } from 'react-router-dom';
import {
  AutocompleteInput,
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  DateField,
  Edit,
  Filter,
  FunctionField,
  Labeled,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid } from '@mui/material';
import { CloudDownload } from '@mui/icons-material';

import { getEditable } from '../utils/applyRoleAuth';
import { CustomNumberInput, LinkField } from './CustomFields';

const entityName = 'Auto Reinvest Indicator';

export const AutoReinvestIndicatorEdit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <BooleanInput source="isActive" />
        <CustomNumberInput source="percentage" />
        <Labeled fullWidth>
          <LinkField
            reference="User"
            linkSource="user.id"
            labelSource="user.fullName"
            label="User"
          />
        </Labeled>
        <Labeled fullWidth>
          <LinkField
            reference="SubAccount"
            linkSource="subAccount.id"
            labelSource="subAccount.name"
            label="SubAccount"
          />
        </Labeled>
        <Labeled fullWidth>
          <LinkField
            reference="Portfolio"
            linkSource="portfolio.id"
            labelSource="portfolio.name"
            label="Portfolio"
          />
        </Labeled>
        {/* <Labeled fullWidth>
          <LinkField
            reference="Portfolio"
            linkSource="reinvestIntoPortfolio.id"
            labelSource="reinvestIntoPortfolio.name"
            label="Reinvest Into Portfolio"
          />
        </Labeled> */}
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <ReferenceInput
              source="reinvestIntoPortfolio.id"
              reference="Portfolio"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput
                optionText="name"
                label="Reinvest into Portfolio (override)"
                fullWidth
                allowEmpty
              />
            </ReferenceInput>
          </Grid>
        </Grid>
        <FunctionField
          label="Agreement"
          render={(record) => {
            if (record.agreementDownloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={() =>
                    window.location.assign(record.agreementDownloadUrl)
                  }
                >
                  Download Agreement
                </Button>
              );
            }
            return null;
          }}
        />
      </SimpleForm>
    </Edit>
  );
};

const AutoReinvestIndicatorFilter = (props) => (
  <Filter {...props}>
    <TextInput
      style={{ minWidth: '420px' }}
      label="Search by First Name or Last Name"
      source="q"
      alwaysOn
    />
  </Filter>
);

export const AutoReinvestIndicatorList = () => {
  const { permissions } = usePermissions();
  return (
    <List
      title={entityName}
      sort={{ field: 'id', order: 'DESC' }}
      filters={<AutoReinvestIndicatorFilter />}
      perPage={25}
    >
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <LinkField
          reference="User"
          linkSource="user.id"
          labelSource="user.fullName"
          label="User"
        />
        <LinkField
          reference="SubAccount"
          linkSource="subAccount.id"
          labelSource="subAccount.name"
          label="SubAccount"
        />
        <TextField source="user.type" label="User Type" />
        <LinkField
          reference="Portfolio"
          linkSource="portfolio.id"
          labelSource="portfolio.name"
          label="Portfolio"
        />
        <BooleanField source="isActive" />
        <NumberField source="percentage" />
        <LinkField
          reference="Portfolio"
          linkSource="reinvestIntoPortfolio.id"
          labelSource="reinvestIntoPortfolio.name"
          label="Reinvest Into Portfolio"
        />
        <FunctionField
          label="Agreement"
          render={(record) => {
            if (record.agreementDownloadUrl) {
              return (
                <Button
                  variant="contained"
                  startIcon={<CloudDownload />}
                  style={{ textTransform: 'none' }}
                  onClick={() =>
                    window.location.assign(record.agreementDownloadUrl)
                  }
                >
                  Download Agreement
                </Button>
              );
            }
            return null;
          }}
        />
        <DateField source="updatedAt" />
        <DateField source="createdAt" showTime={true} />
      </Datagrid>
    </List>
  );
};

export const AutoReinvestIndicatorCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <BooleanInput
            defaultValue={true}
            fullWidth
            source="isActive"
            helperText="Leave this on to enable the auto reinvestment feature. Turn it off to disable auto reinvesting."
          />
          <CustomNumberInput
            defaultValue={100}
            required
            fullWidth
            source="percentage"
          />
          <ReferenceInput
            perPage={10_000}
            source="user.id"
            reference="UserLite"
          >
            <AutocompleteInput
              label="User (Investor)"
              required
              fullWidth
              allowEmpty={true}
              optionText="fullName"
              shouldRenderSuggestions={(value) => value.trim().length > 0}
            />
          </ReferenceInput>
          <ReferenceInput source="subAccount.id" reference="SubAccountLite">
            <SelectInput label="SubAccount" fullWidth source="name" />
          </ReferenceInput>
          <ReferenceInput source="portfolio.id" reference="PortfolioLite">
            <SelectInput
              label="Portfolio"
              required
              fullWidth
              optionText="name"
            />
          </ReferenceInput>
          <BooleanInput
            source="createSignedSubscriptionAgreement"
            fullWidth
            defaultValue={false}
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
