import React from 'react';
import { useParams } from 'react-router-dom';

import {
  BooleanField,
  BooleanInput,
  Create,
  Datagrid,
  Edit,
  FunctionField,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  useDataProvider,
  useNotify,
  useRefresh,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Button, Grid } from '@mui/material';

import { Image, Video } from 'cloudinary-react';
import { Delete } from '@mui/icons-material';
import { getEditable } from '../utils/applyRoleAuth';

import { openUploadWidget } from '../utils/CloudinaryService';
import { CustomNumberInput, LinkField } from './CustomFields';

import Config from '../config/config';

const entityName = 'Investor Resource';

export const InvestorResourceEdit = (props) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  const onVideoUploaded = (video) => {
    dataProvider
      .update('InvestorResource', {
        data: {
          id: parseInt(id, 10),
          videoPublicId: video.public_id,
        },
      })
      .then(
        () => {
          notify('Successfully uploaded video');
          refresh();
        },
        (e) => {
          console.error('ERROR', e);
        }
      );
  };

  const uploadVideoWithCloudinary = () => {
    const uploadOptions = {
      cloudName: Config.cloud_name,
      // eager: [
      //   { width: 200, crop: 'scale' },
      //   { width: 960, crop: 'scale' },
      //   { width: 1280, crop: 'scale' },
      //   { width: 1920, crop: 'scale' },
      // ],
      // eslint-disable-next-line camelcase
      eager_async: true,
      multiple: false,
      // resourceType: 'video',
      showPoweredBy: false,
      uploadPreset: Config.investor_resource_video_upload_preset,
    };
    openUploadWidget(uploadOptions, (error, resp) => {
      if (!error && resp.event === 'success') {
        onVideoUploaded(resp.info);
      }
    });
  };

  const handleRemoveVideo = () => {
    dataProvider
      .update('InvestorResource', {
        data: {
          videoPublicId: null,
          id: parseInt(id, 10),
        },
      })
      .catch((e) => {
        console.error('ERROR', e);
        notify('Error removing video', { type: 'error' });
      })
      .then(() => {
        notify('Video successfully removed');
        refresh();
      });
  };
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput
              multiline
              source="title"
              helperText="Title text of resource"
              fullWidth
            />
            <TextInput
              multiline
              fullWidth
              source="summary"
              helperText="1 to 2 sentences max. This shows below the article tile on the investment 101 page."
            />
            <TextInput
              source="length"
              helperText="Estimated article read time (ex: '5 min read'"
              fullWidth
            />
            {/* TODO: REPLACE WITH SELECT */}
            <TextInput
              source="fileType"
              helperText="Must be Image, Video, PDF, Audio, or Link"
              fullWidth
            />
            <TextInput
              fullWidth
              source="externalLink"
              helperText="Used if the resource media is an externally hosted asset such as a Demio webinar."
            />
            <TextInput
              fullWidth
              source="spotifyEmbedTag"
              helperText="Spotify Embed Tag. This should be an iframe tag copied from spotify's embed share functionality."
            />
            <CustomNumberInput source="orderNo" fullWidth step={1} />
            <BooleanInput source="inactive" />
            <ReferenceInput source="type.id" reference="InvestorResourceType">
              <SelectInput
                label="Type"
                fullWidth
                helperText="If no type is selected, resource will not appear on frontend"
                optionText="name"
              />
            </ReferenceInput>
          </Grid>
        </Grid>
        <FunctionField
          align="center"
          label="Video"
          style={{ width: '100%' }}
          render={(record) => {
            if (!record.videoPublicId) return null;
            if (record.fileType === 'Video') {
              return (
                <>
                  <Video
                    cloud_name={Config.cloud_name}
                    publicId={record.videoPublicId}
                    muted
                    width="200"
                    crop="scale"
                    sourceTypes={['mp4']}
                    controls
                  >
                    {/* <Transformation width={200} crop="scale" /> */}
                  </Video>
                  <Button
                    style={{ float: 'right' }}
                    onClick={handleRemoveVideo}
                  >
                    <Delete />
                  </Button>
                </>
              );
            }
            return (
              <>
                <Image
                  cloud_name={Config.cloud_name}
                  publicId={record.videoPublicId}
                  muted
                  width="200"
                  crop="scale"
                  // sourceTypes={['mp4']}
                  controls
                >
                  {/* <Transformation width={200} crop="scale" /> */}
                </Image>
                <Button style={{ float: 'right' }} onClick={handleRemoveVideo}>
                  <Delete />
                </Button>
              </>
            );
          }}
        />
        <div className="actions">
          <Button
            variant="contained"
            color="primary"
            onClick={uploadVideoWithCloudinary}
          >
            Add file
          </Button>
        </div>
      </SimpleForm>
    </Edit>
  );
};

export const InvestorResourceList = () => {
  const { permissions } = usePermissions();
  return (
    <List title={entityName} perPage={25} sort={{ field: 'id', order: 'DESC' }}>
      <Datagrid
        rowClick={
          getEditable(useResourceDefinition().name, permissions)
            ? 'edit'
            : 'show'
        }
      >
        <TextField source="id" />
        <NumberField source="orderNo" />
        <TextField source="title" />
        <TextField source="summary" />
        <TextField source="length" sortable={false} />
        <BooleanField source="inactive" />
        <LinkField
          label="Type"
          linkSource="type.id"
          labelSource="type.name"
          reference="InvestorResourceType"
        />
      </Datagrid>
    </List>
  );
};

export const InvestorResourceCreate = () => (
  <Create title={`Create ${entityName}`}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput
            source="title"
            helperText="Title text of an investor resource entry. This can be updated at any time."
            fullWidth
          />
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
