import React from 'react';
import { useParams } from 'react-router-dom';

import {
  <PERSON>reate,
  Datagrid,
  DateField,
  DateInput,
  Edit,
  List,
  NumberField,
  ReferenceInput,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  usePermissions,
  useResourceDefinition,
} from 'react-admin';

import { Grid } from '@mui/material';

import { CustomNumberInput, LinkField } from './CustomFields';
import { getEditable } from '../utils/applyRoleAuth';

const entityName = '[SINGULAR_LABEL]';

export const [SINGULAR_NAME]Edit = () => {
  const { id } = useParams();
  return (
    <Edit title={`${entityName} #${id}`} undoable={false}>
      <SimpleForm>
        <Grid container style={{ width: '100%' }}>
          <Grid item xs={12} md={6}>
            <TextInput source="name" fullWidth />
            <DateInput source="date" fullWidth />
            <CustomNumberInput source="amount" fullWidth />
            <ReferenceInput
              source="example.id"
              reference="Example"
              perPage={10_000}
              sort={{ field: 'id', order: 'ASC' }}
            >
              <SelectInput optionText="name" label="Example" fullWidth allowEmpty/>
            </ReferenceInput>
          </Grid>
        </Grid>
      </SimpleForm>
    </Edit>
  );
}

export const [SINGULAR_NAME]List = () => {
  const { permissions }  = usePermissions();
  return (
  <List title={entityName} perPage={25}>
    <Datagrid
      rowClick={
        getEditable(useResourceDefinition().name, permissions) ? 'edit' : 'show'
      }
    >
      <TextField source="id" />
      <LinkField
        reference="Example"
        linkSource="example.id"
        labelSource="example.label"
        label="Example"
      />
      <TextField source="name" />
      <DateField source="date" />
      <NumberField source="amount" />
    </Datagrid>
  </List>
);
    }

export const [SINGULAR_NAME]Create = () => (
  <Create title={`Create ${entityName}`} undoable={false}>
    <SimpleForm>
      <Grid container style={{ width: '100%' }}>
        <Grid item xs={12} md={6}>
          <TextInput source="name" required fullWidth />
          <DateInput source="date" required fullWidth />
          <CustomNumberInput source="amount" fullWidth />
          <ReferenceInput
            source="example.id"
            reference="Example"
            perPage={10_000}
            sort={{ field: 'id', order: 'ASC' }}
          >
            <SelectInput 
              optionText="name"  
              label="Example" 
              fullWidth 
              allowEmpty
            />
          </ReferenceInput>
        </Grid>
      </Grid>
    </SimpleForm>
  </Create>
);
