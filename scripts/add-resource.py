import sys
import os
import shutil

args = sys.argv
resource_name_singular = args[1]
resource_name_plural = args[2]
resource_name_singular_label = args[3]
resource_name_plural_label = args[4]
resource_category = args[5]
resource_name_lower_singular = resource_name_singular[0].lower() + resource_name_singular[1:]

print('\n')
print('Singular Resource Name: ' + resource_name_singular)
print('Plural Resource Name: ' + resource_name_plural)
print('Singular Resource Label: ' + resource_name_singular_label)
print('Plural Resource Label: ' + resource_name_plural_label)
print('Resource Category: ' + resource_category)
print('\n')

plural_placeholder = '[PLURAL_NAME]'
singular_placeholder = '[SINGULAR_NAME]'
lower_singular_placeholder = '[LOWER_SINGULAR_NAME]'
singular_label_placeholder = '[SINGULAR_LABEL]'
plural_label_placeholder = '[PLURAL_LABEL]'
category_placeholder = '[RESOURCE_CATEGORY]'

files_created = []

def file_string_replace(path):
	with open(path, "r") as f:
		text = f.read()
		updated_text = text.replace(plural_placeholder, resource_name_plural)
		updated_text = updated_text.replace(singular_placeholder, resource_name_singular)
		updated_text = updated_text.replace(lower_singular_placeholder, resource_name_lower_singular)
		updated_text = updated_text.replace(singular_label_placeholder, resource_name_singular_label)
	with open(path, "w") as f:
		f.write(updated_text)

def createQueries():
	print('Creating queries...')
	query_types = ['Many', 'Edit', 'List']
	for query_type in query_types:
		src_path = './scripts/add-resource-templates/template{}.graphql'.format(query_type)
		dest_path = './src/queries/{}.graphql'.format(resource_name_singular + query_type)
		if os.path.exists(dest_path):
			raise Exception("File {} already exists.".format(dest_path))
		shutil.copy2(src_path, dest_path)
		files_created.append(dest_path)
		file_string_replace(dest_path)
	print('Successfully created queries.')


def createMutations():
	print('Creating mutations...')
	mutation_types = ['Create', 'Update', 'Delete']
	for mutation_type in mutation_types:
		src_path = './scripts/add-resource-templates/template{}.graphql'.format(mutation_type)
		dest_path = './src/mutations/{}.graphql'.format(resource_name_singular + mutation_type)
		if os.path.exists(dest_path):
			raise Exception("File {} already exists.".format(dest_path))
		shutil.copy2(src_path, dest_path)
		files_created.append(dest_path)
		file_string_replace(dest_path)
	print('Successfully created mutations.')


def createViews():
	print('Creating List/Edit/Create view...')
	src_path = './scripts/add-resource-templates/template.jsx'
	dest_path = './src/components/{}.jsx'.format(resource_name_plural)
	if os.path.exists(dest_path):
		raise Exception("File {} already exists.".format(dest_path))
	shutil.copy2(src_path, dest_path)
	files_created.append(dest_path)
	file_string_replace(dest_path)
	print('Successfully created List/Edit/Create view...')


def updateApplyRoleAuth():
	print('Updating applyRoleAuth.js...')
	apply_role_auth_place_marker = '// !Dont change me. I am used by the add-resource script!'
	replacing_text = apply_role_auth_place_marker + '\n\t' + resource_name_singular + ": {\n\t\treadAccess: [],\n\t\twriteAccess: [],\n\t},"
	src_path = './src/utils/applyRoleAuth.js'
	with open(src_path, "r") as f:
		text = f.read()
		updated_text = text.replace(apply_role_auth_place_marker, replacing_text)
	with open(src_path, "w") as f:
		f.write(updated_text)
	print('Successfully updated applyRoleAuth.js')
	print('Make sure changes to applyRoleAuth.js are in the desired order.')


def updateApp():
	print('Updating App.jsx...')
	src_path = './src/components/App.jsx'

	query_import_marker = '// !Dont change me. I am used by the add-resource script to import queries!'
	jsx_import_marker = '// !Dont change me. I am used by the add-resource script to import List/Edit/Create views!'
	case_marker = '// !Dont change me. I am used by the add-resource script to setup queries!'
	resource_marker = '// !Dont change me. I am used by the add-resource script to create the resource!'

	with open(src_path, "r") as f:
		text = f.read()

		query_import_replacing_text = query_import_marker + "\n// {0} queries\nimport {0}ListQuery from '../queries/{0}List.graphql';\nimport {0}ManyQuery from '../queries/{0}Many.graphql';\nimport {0}EditQuery from '../queries/{0}Edit.graphql';\nimport {0}UpdateMutation from '../mutations/{0}Update.graphql';\nimport {0}CreateMutation from '../mutations/{0}Create.graphql';\nimport {0}DeleteMutation from '../mutations/{0}Delete.graphql';".format(resource_name_singular)
		updated_text = text.replace(query_import_marker, query_import_replacing_text)

		jsx_import_replacing_text = jsx_import_marker + "\nconst {0}List = withSuspense(\n\tReact.lazy(() =>\n\t\timport('./{1}').then((module) => ({{\n\t\t\tdefault: module.{0}List,\n\t\t}}))\n\t)\n);\nconst {0}Edit = withSuspense(\n\tReact.lazy(() =>\n\t\timport('./{1}').then((module) => ({{\n\t\t\tdefault: module.{0}Edit,\n\t\t}}))\n\t)\n);\nconst {0}Create = withSuspense(\n\tReact.lazy(() =>\n\t\timport('./{1}').then((module) => ({{\n\t\t\tdefault: module.{0}Create,\n\t\t}}))\n\t)\n);".format(resource_name_singular, resource_name_plural)

		updated_text = updated_text.replace(jsx_import_marker, jsx_import_replacing_text)

		resource_replacing_text = resource_marker + """
				<Resource
				name="[SINGULAR_NAME]"
				edit={
					getEditable('[SINGULAR_NAME]', permissions)
					? [SINGULAR_NAME]Edit
					: null
				}
				show={ShowGuesser}
				list={[SINGULAR_NAME]List}
				create={[SINGULAR_NAME]Create}
				options={{
					dataProvider,
					category: '[RESOURCE_CATEGORY]',
					label: '[PLURAL_LABEL]',
				}}
				/>,""".replace(plural_label_placeholder, resource_name_plural_label).replace(singular_placeholder, resource_name_singular).replace(category_placeholder, resource_category)
		updated_text = updated_text.replace(resource_marker, resource_replacing_text)
		
		case_replacing_text = case_marker + """\n\t\tcase '[SINGULAR_NAME]':
		switch (fetchType) {
			case 'GET_LIST':
			returnObj = {
				query: [SINGULAR_NAME]ListQuery,
				variables: params, // params = { id: ... }
				parseResponse: (response) => ({
				data: response.data.all[PLURAL_NAME].rows,
				total: response.data.all[PLURAL_NAME].count,
				}),
			};
			break;
			case 'GET_MANY':
			returnObj = {
				query: [SINGULAR_NAME]ManyQuery,
				parseResponse: (response) => ({
				data: response.data.all[PLURAL_NAME],
				total: response.data.all[PLURAL_NAME].length,
				}),
			};
			break;
			case 'GET_ONE':
			returnObj = {
				query: [SINGULAR_NAME]EditQuery,
				variables: { id: parseInt(params.id, 10) },
				parseResponse: (response) => {
				return {
					data: response.data.[LOWER_SINGULAR_NAME],
				};
				},
			};
			break;
			case 'CREATE':
			input = filterInput(
				'Create[SINGULAR_NAME]Input',
				data,
				introspection.types
			);
			returnObj = {
				query: [SINGULAR_NAME]CreateMutation,
				variables: {
				input,
				},
				parseResponse: (response) => ({
				data: response.data.create[SINGULAR_NAME],
				}),
			};
			break;
			case 'UPDATE':
			input = filterInput(
				'Update[SINGULAR_NAME]Input',
				data,
				introspection.types
			);
			returnObj = {
				query: [SINGULAR_NAME]UpdateMutation,
				variables: {
				input,
				},
				parseResponse: (response) => {
				return {
					data: response.data.update[SINGULAR_NAME],
				};
				},
			};
			break;
			case 'DELETE':
			returnObj = {
				query: [SINGULAR_NAME]DeleteMutation,
				variables: {
				id: params.id,
				},
				parseResponse: (response) => ({
				data: response.data.delete[SINGULAR_NAME],
				}),
			};
			break;
			default:
			console.log('query not set up!', resourceName, fetchType);
			break;
		}
		break;""".replace(plural_placeholder, resource_name_plural).replace(singular_placeholder, resource_name_singular).replace(lower_singular_placeholder, resource_name_lower_singular)
		updated_text = updated_text.replace(case_marker, case_replacing_text)

	with open(src_path, "w") as f:
		f.write(updated_text)

	print("Completed updating App.jsx")
	print('Make sure changes to App.jsx are in the desired order.')


def deleteCreatedFiles():
	print("Removing created files...")
	for file in files_created:
		os.remove(file)
		print(file + ' removed.')

try:
	createQueries()
	createMutations()
	createViews()
	updateApplyRoleAuth()
	updateApp()
except Exception as e:
	print('Error adding resource: ' + str(e))
	deleteCreatedFiles()
	print('Changes to App.jsx or applyRoleAuth.js may need to be reverted.')
