read -p "Singular Resource Name (Ex: FaqEntry): " resource_name_singular
read -p "Plural Resource Name (Ex: FaqEntries): " resource_name_plural
read -p "Singular Resource Label (Ex: Faq Entry): " resource_name_singular_label
read -p "Plural Resource Label (Ex: Faq Entries): " resource_name_plural_label
read -p "Resource Category (options: 'content', 'documents', 'financials', 'itUtilities', 'brazil', 'assets', 'users', 'accounting', 'monitoring', 'marketing', 'ira', 'assetMgmt', 'security', 'hr', 'creditMgmt'): " resource_category
python3 ./scripts/add-resource.py "$resource_name_singular" "$resource_name_plural" "$resource_name_singular_label" "$resource_name_plural_label" "$resource_category"