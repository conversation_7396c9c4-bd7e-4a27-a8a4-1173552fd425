{"editor.insertSpaces": false, "jshint.enable": false, "eslint.alwaysShowStatus": true, "editor.formatOnSave": true, "workbench.colorCustomizations": {"activityBar.activeBackground": "#254c78", "activityBar.background": "#254c78", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#ca548c", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#254c78", "statusBar.background": "#193351", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#254c78", "statusBarItem.remoteBackground": "#193351", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#193351", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#19335199", "titleBar.inactiveForeground": "#e7e7e799"}, "cSpell.words": ["ACAT", "actuals", "Agrmt", "AMMP", "apos", "bday", "Boleto", "btns", "cachebust", "Cafd", "CAFDs", "camelcase", "chartjs", "CHEATSHEET", "clickjacking", "cloudinary", "<PERSON><PERSON><PERSON>", "Colorstr", "comms", "Conergy", "countup", "createdb", "creds", "crossorigin", "Datagrid", "datasets", "dateonly", "DBUSER", "decarbonization", "deserialization", "devtool", "dropdb", "dwolla", "dyno", "dynos", "Energea", "Energea’s", "eversign", "faturamento", "Faturamentos", "financials", "fname", "gsap", "herokuapp", "hocs", "iframe", "Intelbras", "jsonexport", "keyframes", "kpis", "Laerskool", "<PERSON><PERSON>", "lazyload", "LDAP", "LFDI", "linkedin", "loaderio", "lsof", "mapbox", "marketingautomation", "materialui", "mathjax", "maxatte<PERSON>", "Merch", "Mgmt", "microdeposits", "MTSFTP", "MWAC", "MWDC", "NGOs", "Nico", "no", "noct", "noinfo", "noopen", "nosniff", "offtaker", "Offtakers", "OIDS", "okta", "<PERSON><PERSON>'s", "openidconnect", "owasp", "periodicities", "pgdatabase", "pghost", "PGPASSWORD", "PGPORT", "PGUSER", "pkce", "pkey", "plusplus", "Pmax", "<PERSON><PERSON>", "postbuild", "preconnect", "presigner", "progid", "progressbar", "psql", "Punchlist", "pvsyst", "Quickbooks", "Rateio", "Rateios", "readonly", "refetched", "<PERSON><PERSON><PERSON>", "Reinvestments", "Roboto", "salesforce", "<PERSON><PERSON><PERSON>", "scada", "scrollable", "scrollmagic", "sendgrid", "SERASA", "sess", "SHARPSPRING", "signin", "signup", "<PERSON><PERSON><PERSON><PERSON>", "SMSMFA", "starlink", "styleguide", "Suncast", "sungrow", "swipeable", "<PERSON><PERSON><PERSON>", "Texeira", "<PERSON>id", "unacc", "Uncollectible", "unconfirmedemail", "undoable", "unlevered", "unmount", "unregisteredemail", "untrusted", "updatepasswordsuccess", "Uploader", "<PERSON><PERSON><PERSON>", "vbls", "<PERSON><PERSON>", "WCOH", "webcrypto", "xargs", "xirr", "xmark"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "never"}, "cSpell.language": "en,pt,pt_BR", "cSpell.ignoreWords": ["browserslist", "dwelloptimal", "inmemory", "subaccount"], "peacock.color": "#193351"}