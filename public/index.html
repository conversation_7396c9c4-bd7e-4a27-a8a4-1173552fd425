<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="robots" content="noindex,nofollow">
  <link rel="shortcut icon" href="%PUBLIC_URL%/favicons/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <meta name="theme-color" content="#000000" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <link rel="stylesheet" href="%PUBLIC_URL%/css/weather-icons.min.css" />
  <link rel="stylesheet" href="%PUBLIC_URL%/font/fonts.css" />
  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->

  <!-- <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css"
    integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous"> -->

  <link href="https://fonts.googleapis.com/css?family=Lato:400,700|Montserrat:400,800" rel="stylesheet">
  <script defer src="https://cdn.plaid.com/link/v2/stable/link-initialize.js"></script>
  <script defer type="text/javascript" src="https://static.eversign.com/js/embedded-signing.js"></script>
  <title>Starlight</title>
  <script async preconnect src="https://kit.fontawesome.com/823f9560d7.js" crossorigin="anonymous"></script>

</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <script src="https://widget.cloudinary.com/v2.0/global/all.js" type="text/javascript"></script>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>
